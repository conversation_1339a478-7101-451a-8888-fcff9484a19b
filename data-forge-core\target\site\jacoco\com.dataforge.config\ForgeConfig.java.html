<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ForgeConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.config</a> &gt; <span class="el_source">ForgeConfig.java</span></div><h1>ForgeConfig.java</h1><pre class="source lang-java linenums">package com.dataforge.config;

import java.util.ArrayList;
import java.util.List;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * DataForge主配置类。
 * 
 * &lt;p&gt;
 * 使用Spring Boot的@ConfigurationProperties注解，
 * 支持从application.yml或外部配置文件加载配置。
 * 
 * &lt;p&gt;
 * 配置示例：
 * 
 * &lt;pre&gt;
 * dataforge:
 *   count: 100
 *   output:
 *     format: csv
 *     file: &quot;output/test-data.csv&quot;
 *   fields:
 *     - name: &quot;userId&quot;
 *       type: &quot;uuid&quot;
 *       params:
 *         type: &quot;UUID4&quot;
 *     - name: &quot;name&quot;
 *       type: &quot;name&quot;
 *       params:
 *         type: &quot;CN&quot;
 *         gender: &quot;ANY&quot;
 * &lt;/pre&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = &quot;dataforge&quot;)
@Validated
public class ForgeConfig {

    /**
     * 要生成的记录数量，默认为10。
     */
<span class="nc" id="L49">    @Min(value = 1, message = &quot;Count must be at least 1&quot;)</span>
    private int count = 10;

    /**
     * 输出配置。
     */
<span class="nc" id="L55">    @NotNull(message = &quot;Output configuration cannot be null&quot;)</span>
    @Valid
    private OutputConfig output = new OutputConfig();

    /**
     * 字段配置列表。
     */
<span class="nc" id="L62">    @NotNull(message = &quot;Fields configuration cannot be null&quot;)</span>
    @Valid
    private List&lt;FieldConfigWrapper&gt; fields = new ArrayList&lt;&gt;();

    /**
     * 是否启用数据校验，默认为true。
     */
<span class="nc" id="L69">    private boolean validate = true;</span>

    /**
     * 并发线程数，默认为1（单线程）。
     */
<span class="nc" id="L74">    @Min(value = 1, message = &quot;Thread count must be at least 1&quot;)</span>
    private int threads = 1;

    /**
     * 随机种子，用于可重现的数据生成。如果不设置，使用系统时间作为种子。
     */
    private Long seed;

    /**
     * 默认构造函数。
     */
<span class="nc" id="L85">    public ForgeConfig() {</span>
<span class="nc" id="L86">    }</span>

    /**
     * 获取要生成的记录数量。
     * 
     * @return 记录数量
     */
    public int getCount() {
<span class="nc" id="L94">        return count;</span>
    }

    /**
     * 设置要生成的记录数量。
     * 
     * @param count 记录数量
     */
    public void setCount(int count) {
<span class="nc" id="L103">        this.count = count;</span>
<span class="nc" id="L104">    }</span>

    /**
     * 获取输出配置。
     * 
     * @return 输出配置
     */
    public OutputConfig getOutput() {
<span class="nc" id="L112">        return output;</span>
    }

    /**
     * 设置输出配置。
     * 
     * @param output 输出配置
     */
    public void setOutput(OutputConfig output) {
<span class="nc" id="L121">        this.output = output;</span>
<span class="nc" id="L122">    }</span>

    /**
     * 获取字段配置列表。
     * 
     * @return 字段配置列表
     */
    public List&lt;FieldConfigWrapper&gt; getFields() {
<span class="nc" id="L130">        return fields;</span>
    }

    /**
     * 设置字段配置列表。
     * 
     * @param fields 字段配置列表
     */
    public void setFields(List&lt;FieldConfigWrapper&gt; fields) {
<span class="nc bnc" id="L139" title="All 2 branches missed.">        this.fields = fields != null ? fields : new ArrayList&lt;&gt;();</span>
<span class="nc" id="L140">    }</span>

    /**
     * 检查是否启用数据校验。
     * 
     * @return 如果启用校验返回true，否则返回false
     */
    public boolean isValidate() {
<span class="nc" id="L148">        return validate;</span>
    }

    /**
     * 设置是否启用数据校验。
     * 
     * @param validate 是否启用校验
     */
    public void setValidate(boolean validate) {
<span class="nc" id="L157">        this.validate = validate;</span>
<span class="nc" id="L158">    }</span>

    /**
     * 获取并发线程数。
     * 
     * @return 线程数
     */
    public int getThreads() {
<span class="nc" id="L166">        return threads;</span>
    }

    /**
     * 设置并发线程数。
     * 
     * @param threads 线程数
     */
    public void setThreads(int threads) {
<span class="nc" id="L175">        this.threads = threads;</span>
<span class="nc" id="L176">    }</span>

    /**
     * 获取随机种子。
     * 
     * @return 随机种子，如果未设置返回null
     */
    public Long getSeed() {
<span class="nc" id="L184">        return seed;</span>
    }

    /**
     * 设置随机种子。
     * 
     * @param seed 随机种子
     */
    public void setSeed(Long seed) {
<span class="nc" id="L193">        this.seed = seed;</span>
<span class="nc" id="L194">    }</span>

    /**
     * 添加字段配置。
     * 
     * @param field 字段配置
     */
    public void addField(FieldConfigWrapper field) {
<span class="nc bnc" id="L202" title="All 2 branches missed.">        if (field != null) {</span>
<span class="nc" id="L203">            this.fields.add(field);</span>
        }
<span class="nc" id="L205">    }</span>

    /**
     * 检查配置是否有效。
     * 
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
<span class="nc bnc" id="L213" title="All 10 branches missed.">        return count &gt; 0 &amp;&amp; output != null &amp;&amp; fields != null &amp;&amp; !fields.isEmpty() &amp;&amp; threads &gt; 0;</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L218">        return String.format(&quot;ForgeConfig{count=%d, output=%s, fields=%d, validate=%s, threads=%d, seed=%s}&quot;,</span>
<span class="nc" id="L219">                count, output, fields.size(), validate, threads, seed);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>