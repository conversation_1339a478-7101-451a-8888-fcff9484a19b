<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PhoneGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">PhoneGenerator.java</span></div><h1>PhoneGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 手机号码生成器。
 * 
 * &lt;p&gt;
 * 生成符合中国大陆运营商号段规则的11位手机号码。
 * 支持指定运营商前缀和生成有效/无效号码。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L24">public class PhoneGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(PhoneGenerator.class);</span>

    /**
     * 中国移动号段前缀。
     */
<span class="nc" id="L31">    private static final List&lt;String&gt; CHINA_MOBILE_PREFIXES = Arrays.asList(</span>
            &quot;134&quot;, &quot;135&quot;, &quot;136&quot;, &quot;137&quot;, &quot;138&quot;, &quot;139&quot;, &quot;147&quot;, &quot;148&quot;, &quot;150&quot;, &quot;151&quot;,
            &quot;152&quot;, &quot;157&quot;, &quot;158&quot;, &quot;159&quot;, &quot;172&quot;, &quot;178&quot;, &quot;182&quot;, &quot;183&quot;, &quot;184&quot;, &quot;187&quot;,
            &quot;188&quot;, &quot;195&quot;, &quot;197&quot;, &quot;198&quot;);

    /**
     * 中国联通号段前缀。
     */
<span class="nc" id="L39">    private static final List&lt;String&gt; CHINA_UNICOM_PREFIXES = Arrays.asList(</span>
            &quot;130&quot;, &quot;131&quot;, &quot;132&quot;, &quot;145&quot;, &quot;146&quot;, &quot;155&quot;, &quot;156&quot;, &quot;166&quot;, &quot;167&quot;, &quot;171&quot;,
            &quot;175&quot;, &quot;176&quot;, &quot;185&quot;, &quot;186&quot;, &quot;196&quot;);

    /**
     * 中国电信号段前缀。
     */
<span class="nc" id="L46">    private static final List&lt;String&gt; CHINA_TELECOM_PREFIXES = Arrays.asList(</span>
            &quot;133&quot;, &quot;149&quot;, &quot;153&quot;, &quot;173&quot;, &quot;174&quot;, &quot;177&quot;, &quot;180&quot;, &quot;181&quot;, &quot;189&quot;, &quot;191&quot;,
            &quot;193&quot;, &quot;199&quot;);

    /**
     * 虚拟运营商号段前缀。
     */
<span class="nc" id="L53">    private static final List&lt;String&gt; VIRTUAL_OPERATOR_PREFIXES = Arrays.asList(</span>
            &quot;162&quot;, &quot;165&quot;, &quot;167&quot;, &quot;170&quot;, &quot;171&quot;);

    /**
     * 所有有效号段前缀。
     */
    private static final List&lt;String&gt; ALL_VALID_PREFIXES;

    static {
<span class="nc" id="L62">        ALL_VALID_PREFIXES = new java.util.ArrayList&lt;&gt;();</span>
<span class="nc" id="L63">        ALL_VALID_PREFIXES.addAll(CHINA_MOBILE_PREFIXES);</span>
<span class="nc" id="L64">        ALL_VALID_PREFIXES.addAll(CHINA_UNICOM_PREFIXES);</span>
<span class="nc" id="L65">        ALL_VALID_PREFIXES.addAll(CHINA_TELECOM_PREFIXES);</span>
<span class="nc" id="L66">        ALL_VALID_PREFIXES.addAll(VIRTUAL_OPERATOR_PREFIXES);</span>
<span class="nc" id="L67">    }</span>

<span class="nc" id="L69">    private final Random random = new Random();</span>

    @Override
    public String getType() {
<span class="nc" id="L73">        return &quot;phone&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 从参数中获取地区，默认为中国大陆
<span class="nc" id="L80">            String region = getStringParam(config, &quot;region&quot;, &quot;CN&quot;);</span>

<span class="nc bnc" id="L82" title="All 2 branches missed.">            if (!&quot;CN&quot;.equalsIgnoreCase(region)) {</span>
<span class="nc" id="L83">                logger.warn(&quot;Unsupported region: {}, using CN&quot;, region);</span>
            }

            // 从参数中获取是否生成有效号码
<span class="nc" id="L87">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>

<span class="nc bnc" id="L89" title="All 2 branches missed.">            if (!valid) {</span>
<span class="nc" id="L90">                return generateInvalidPhone();</span>
            }

            // 从参数中获取指定的前缀
<span class="nc" id="L94">            String prefixParam = getStringParam(config, &quot;prefix&quot;, null);</span>
<span class="nc" id="L95">            List&lt;String&gt; allowedPrefixes = parseAllowedPrefixes(prefixParam);</span>

            // 从参数中获取运营商类型
<span class="nc" id="L98">            String operator = getStringParam(config, &quot;operator&quot;, &quot;ANY&quot;);</span>

<span class="nc" id="L100">            return generateValidPhone(allowedPrefixes, operator);</span>

<span class="nc" id="L102">        } catch (Exception e) {</span>
<span class="nc" id="L103">            logger.error(&quot;Failed to generate phone number&quot;, e);</span>
            // 返回一个默认手机号作为fallback
<span class="nc" id="L105">            return &quot;13800138000&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L111">        return FieldConfig.class;</span>
    }

    /**
     * 生成有效的手机号码。
     * 
     * @param allowedPrefixes 允许的前缀列表
     * @param operator        运营商类型
     * @return 有效的手机号码
     */
    private String generateValidPhone(List&lt;String&gt; allowedPrefixes, String operator) {
<span class="nc" id="L122">        List&lt;String&gt; prefixes = selectPrefixesByOperator(operator);</span>

        // 如果指定了允许的前缀，取交集
<span class="nc bnc" id="L125" title="All 4 branches missed.">        if (allowedPrefixes != null &amp;&amp; !allowedPrefixes.isEmpty()) {</span>
<span class="nc" id="L126">            prefixes = prefixes.stream()</span>
<span class="nc" id="L127">                    .filter(allowedPrefixes::contains)</span>
<span class="nc" id="L128">                    .collect(java.util.stream.Collectors.toList());</span>
        }

<span class="nc bnc" id="L131" title="All 2 branches missed.">        if (prefixes.isEmpty()) {</span>
<span class="nc" id="L132">            logger.warn(&quot;No valid prefixes found, using all valid prefixes&quot;);</span>
<span class="nc" id="L133">            prefixes = ALL_VALID_PREFIXES;</span>
        }

        // 随机选择前缀
<span class="nc" id="L137">        String prefix = prefixes.get(random.nextInt(prefixes.size()));</span>

        // 生成后8位数字
<span class="nc" id="L140">        StringBuilder phone = new StringBuilder(prefix);</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc" id="L142">            phone.append(random.nextInt(10));</span>
        }

<span class="nc" id="L145">        return phone.toString();</span>
    }

    /**
     * 生成无效的手机号码。
     * 
     * @return 无效的手机号码
     */
    private String generateInvalidPhone() {
<span class="nc" id="L154">        int type = random.nextInt(4);</span>

<span class="nc bnc" id="L156" title="All 4 branches missed.">        return switch (type) {</span>
<span class="nc" id="L157">            case 0 -&gt; generateWrongLengthPhone();</span>
<span class="nc" id="L158">            case 1 -&gt; generateWrongPrefixPhone();</span>
<span class="nc" id="L159">            case 2 -&gt; generateNonNumericPhone();</span>
<span class="nc" id="L160">            default -&gt; generateOtherInvalidPhone();</span>
        };
    }

    /**
     * 生成长度错误的手机号码。
     * 
     * @return 长度错误的手机号码
     */
    private String generateWrongLengthPhone() {
<span class="nc bnc" id="L170" title="All 2 branches missed.">        int length = random.nextBoolean() ? random.nextInt(5) + 5 : // 5-9位</span>
<span class="nc" id="L171">                random.nextInt(5) + 12; // 12-16位</span>

<span class="nc" id="L173">        StringBuilder phone = new StringBuilder();</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L175">            phone.append(random.nextInt(10));</span>
        }

<span class="nc" id="L178">        return phone.toString();</span>
    }

    /**
     * 生成前缀错误的手机号码。
     * 
     * @return 前缀错误的手机号码
     */
    private String generateWrongPrefixPhone() {
        // 生成不存在的前缀
<span class="nc" id="L188">        String[] invalidPrefixes = { &quot;100&quot;, &quot;101&quot;, &quot;102&quot;, &quot;110&quot;, &quot;111&quot;, &quot;120&quot;, &quot;121&quot;, &quot;122&quot;, &quot;123&quot;, &quot;124&quot; };</span>
<span class="nc" id="L189">        String prefix = invalidPrefixes[random.nextInt(invalidPrefixes.length)];</span>

<span class="nc" id="L191">        StringBuilder phone = new StringBuilder(prefix);</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc" id="L193">            phone.append(random.nextInt(10));</span>
        }

<span class="nc" id="L196">        return phone.toString();</span>
    }

    /**
     * 生成包含非数字字符的手机号码。
     * 
     * @return 包含非数字字符的手机号码
     */
    private String generateNonNumericPhone() {
<span class="nc" id="L205">        String validPrefix = ALL_VALID_PREFIXES.get(random.nextInt(ALL_VALID_PREFIXES.size()));</span>
<span class="nc" id="L206">        StringBuilder phone = new StringBuilder(validPrefix);</span>

        // 在后8位中随机插入字母
<span class="nc bnc" id="L209" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">            if (random.nextInt(4) == 0) { // 25%概率插入字母</span>
<span class="nc" id="L211">                phone.append((char) ('A' + random.nextInt(26)));</span>
            } else {
<span class="nc" id="L213">                phone.append(random.nextInt(10));</span>
            }
        }

<span class="nc" id="L217">        return phone.toString();</span>
    }

    /**
     * 生成其他类型的无效手机号码。
     * 
     * @return 其他无效手机号码
     */
    private String generateOtherInvalidPhone() {
        // 生成全0或全相同数字的号码
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L228">            return &quot;00000000000&quot;;</span>
        } else {
<span class="nc" id="L230">            int digit = random.nextInt(10);</span>
<span class="nc" id="L231">            return String.valueOf(digit).repeat(11);</span>
        }
    }

    /**
     * 根据运营商类型选择前缀。
     * 
     * @param operator 运营商类型
     * @return 前缀列表
     */
    private List&lt;String&gt; selectPrefixesByOperator(String operator) {
<span class="nc bnc" id="L242" title="All 5 branches missed.">        return switch (operator.toUpperCase()) {</span>
<span class="nc" id="L243">            case &quot;MOBILE&quot;, &quot;CHINA_MOBILE&quot; -&gt; CHINA_MOBILE_PREFIXES;</span>
<span class="nc" id="L244">            case &quot;UNICOM&quot;, &quot;CHINA_UNICOM&quot; -&gt; CHINA_UNICOM_PREFIXES;</span>
<span class="nc" id="L245">            case &quot;TELECOM&quot;, &quot;CHINA_TELECOM&quot; -&gt; CHINA_TELECOM_PREFIXES;</span>
<span class="nc" id="L246">            case &quot;VIRTUAL&quot; -&gt; VIRTUAL_OPERATOR_PREFIXES;</span>
<span class="nc" id="L247">            default -&gt; ALL_VALID_PREFIXES;</span>
        };
    }

    /**
     * 解析允许的前缀参数。
     * 
     * @param prefixParam 前缀参数字符串
     * @return 前缀列表
     */
    private List&lt;String&gt; parseAllowedPrefixes(String prefixParam) {
<span class="nc bnc" id="L258" title="All 4 branches missed.">        if (prefixParam == null || prefixParam.trim().isEmpty()) {</span>
<span class="nc" id="L259">            return null;</span>
        }

<span class="nc" id="L262">        return Arrays.stream(prefixParam.split(&quot;,&quot;))</span>
<span class="nc" id="L263">                .map(String::trim)</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="nc" id="L265">                .collect(java.util.stream.Collectors.toList());</span>
    }

    /**
     * 从配置中获取字符串参数。
     * 
     * @param config       字段配置
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L277" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L278">            return defaultValue;</span>
        }

<span class="nc" id="L281">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     * 
     * @param config       字段配置
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L294" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L295">            return defaultValue;</span>
        }

<span class="nc" id="L298">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L299" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L300">            return defaultValue;</span>
        }

<span class="nc bnc" id="L303" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L304">            return (Boolean) value;</span>
        }

<span class="nc" id="L307">        return Boolean.parseBoolean(value.toString());</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L312">        return &quot;Phone number generator - generates Chinese mobile phone numbers with operator support&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>