<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BaseGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">BaseGenerator</span></div><h1>BaseGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">63 of 139</td><td class="ctr2">54%</td><td class="bar">21 of 38</td><td class="ctr2">44%</td><td class="ctr1">17</td><td class="ctr2">25</td><td class="ctr1">23</td><td class="ctr2">43</td><td class="ctr1">1</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a4"><a href="BaseGenerator.java.html#L86" class="el_method">getLongParam(FieldConfig, String, long)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="30" alt="30"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="BaseGenerator.java.html#L65" class="el_method">getDoubleParam(FieldConfig, String, double)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="14" alt="14"/></td><td class="ctr2" id="c4">46%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">37%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h1">6</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="BaseGenerator.java.html#L44" class="el_method">getIntParam(FieldConfig, String, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="21" alt="21"/></td><td class="ctr2" id="c3">70%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">62%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="BaseGenerator.java.html#L27" class="el_method">getBooleanParam(FieldConfig, String, boolean)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="21" alt="21"/></td><td class="ctr2" id="c2">77%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="5" alt="5"/></td><td class="ctr2" id="e2">62%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="BaseGenerator.java.html#L16" class="el_method">getStringParam(FieldConfig, String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="17" alt="17"/></td><td class="ctr2" id="c1">89%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">66%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="BaseGenerator.java.html#L10" class="el_method">BaseGenerator()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>