<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LicensePlateGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">LicensePlateGenerator.java</span></div><h1>LicensePlateGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 车牌号生成器。
 * 
 * &lt;p&gt;
 * 生成符合中国大陆车牌号规则的车牌号码。
 * 支持燃油车牌和新能源车牌，可指定省份和城市。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L26">public class LicensePlateGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L28">    private static final Logger logger = LoggerFactory.getLogger(LicensePlateGenerator.class);</span>

    /**
     * 省份简称映射。
     */
<span class="nc" id="L33">    private static final Map&lt;String, String&gt; PROVINCE_CODES = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L36">        PROVINCE_CODES.put(&quot;北京&quot;, &quot;京&quot;);</span>
<span class="nc" id="L37">        PROVINCE_CODES.put(&quot;天津&quot;, &quot;津&quot;);</span>
<span class="nc" id="L38">        PROVINCE_CODES.put(&quot;河北&quot;, &quot;冀&quot;);</span>
<span class="nc" id="L39">        PROVINCE_CODES.put(&quot;山西&quot;, &quot;晋&quot;);</span>
<span class="nc" id="L40">        PROVINCE_CODES.put(&quot;内蒙古&quot;, &quot;蒙&quot;);</span>
<span class="nc" id="L41">        PROVINCE_CODES.put(&quot;辽宁&quot;, &quot;辽&quot;);</span>
<span class="nc" id="L42">        PROVINCE_CODES.put(&quot;吉林&quot;, &quot;吉&quot;);</span>
<span class="nc" id="L43">        PROVINCE_CODES.put(&quot;黑龙江&quot;, &quot;黑&quot;);</span>
<span class="nc" id="L44">        PROVINCE_CODES.put(&quot;上海&quot;, &quot;沪&quot;);</span>
<span class="nc" id="L45">        PROVINCE_CODES.put(&quot;江苏&quot;, &quot;苏&quot;);</span>
<span class="nc" id="L46">        PROVINCE_CODES.put(&quot;浙江&quot;, &quot;浙&quot;);</span>
<span class="nc" id="L47">        PROVINCE_CODES.put(&quot;安徽&quot;, &quot;皖&quot;);</span>
<span class="nc" id="L48">        PROVINCE_CODES.put(&quot;福建&quot;, &quot;闽&quot;);</span>
<span class="nc" id="L49">        PROVINCE_CODES.put(&quot;江西&quot;, &quot;赣&quot;);</span>
<span class="nc" id="L50">        PROVINCE_CODES.put(&quot;山东&quot;, &quot;鲁&quot;);</span>
<span class="nc" id="L51">        PROVINCE_CODES.put(&quot;河南&quot;, &quot;豫&quot;);</span>
<span class="nc" id="L52">        PROVINCE_CODES.put(&quot;湖北&quot;, &quot;鄂&quot;);</span>
<span class="nc" id="L53">        PROVINCE_CODES.put(&quot;湖南&quot;, &quot;湘&quot;);</span>
<span class="nc" id="L54">        PROVINCE_CODES.put(&quot;广东&quot;, &quot;粤&quot;);</span>
<span class="nc" id="L55">        PROVINCE_CODES.put(&quot;广西&quot;, &quot;桂&quot;);</span>
<span class="nc" id="L56">        PROVINCE_CODES.put(&quot;海南&quot;, &quot;琼&quot;);</span>
<span class="nc" id="L57">        PROVINCE_CODES.put(&quot;重庆&quot;, &quot;渝&quot;);</span>
<span class="nc" id="L58">        PROVINCE_CODES.put(&quot;四川&quot;, &quot;川&quot;);</span>
<span class="nc" id="L59">        PROVINCE_CODES.put(&quot;贵州&quot;, &quot;贵&quot;);</span>
<span class="nc" id="L60">        PROVINCE_CODES.put(&quot;云南&quot;, &quot;云&quot;);</span>
<span class="nc" id="L61">        PROVINCE_CODES.put(&quot;西藏&quot;, &quot;藏&quot;);</span>
<span class="nc" id="L62">        PROVINCE_CODES.put(&quot;陕西&quot;, &quot;陕&quot;);</span>
<span class="nc" id="L63">        PROVINCE_CODES.put(&quot;甘肃&quot;, &quot;甘&quot;);</span>
<span class="nc" id="L64">        PROVINCE_CODES.put(&quot;青海&quot;, &quot;青&quot;);</span>
<span class="nc" id="L65">        PROVINCE_CODES.put(&quot;宁夏&quot;, &quot;宁&quot;);</span>
<span class="nc" id="L66">        PROVINCE_CODES.put(&quot;新疆&quot;, &quot;新&quot;);</span>
<span class="nc" id="L67">        PROVINCE_CODES.put(&quot;香港&quot;, &quot;港&quot;);</span>
<span class="nc" id="L68">        PROVINCE_CODES.put(&quot;澳门&quot;, &quot;澳&quot;);</span>
<span class="nc" id="L69">        PROVINCE_CODES.put(&quot;台湾&quot;, &quot;台&quot;);</span>
    }

    /**
     * 常用省份简称。
     */
<span class="nc" id="L75">    private static final List&lt;String&gt; COMMON_PROVINCES = Arrays.asList(</span>
            &quot;京&quot;, &quot;津&quot;, &quot;沪&quot;, &quot;渝&quot;, &quot;冀&quot;, &quot;豫&quot;, &quot;云&quot;, &quot;辽&quot;, &quot;黑&quot;, &quot;湘&quot;,
            &quot;皖&quot;, &quot;鲁&quot;, &quot;新&quot;, &quot;苏&quot;, &quot;浙&quot;, &quot;赣&quot;, &quot;鄂&quot;, &quot;桂&quot;, &quot;甘&quot;, &quot;晋&quot;,
            &quot;蒙&quot;, &quot;陕&quot;, &quot;吉&quot;, &quot;闽&quot;, &quot;贵&quot;, &quot;粤&quot;, &quot;青&quot;, &quot;藏&quot;, &quot;川&quot;, &quot;宁&quot;,
            &quot;琼&quot;, &quot;港&quot;, &quot;澳&quot;, &quot;台&quot;);

    /**
     * 城市代码字母。
     */
<span class="nc" id="L84">    private static final List&lt;String&gt; CITY_CODES = Arrays.asList(</span>
            &quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;, &quot;E&quot;, &quot;F&quot;, &quot;G&quot;, &quot;H&quot;, &quot;J&quot;, &quot;K&quot;,
            &quot;L&quot;, &quot;M&quot;, &quot;N&quot;, &quot;P&quot;, &quot;Q&quot;, &quot;R&quot;, &quot;S&quot;, &quot;T&quot;, &quot;U&quot;, &quot;V&quot;,
            &quot;W&quot;, &quot;X&quot;, &quot;Y&quot;, &quot;Z&quot;);

    /**
     * 车牌字符集（不包含I和O）。
     */
    private static final String PLATE_CHARS = &quot;ABCDEFGHJKLMNPQRSTUVWXYZ0123456789&quot;;

    /**
     * 新能源车牌专用字符。
     */
<span class="nc" id="L97">    private static final List&lt;String&gt; NEW_ENERGY_PREFIXES = Arrays.asList(&quot;D&quot;, &quot;F&quot;);</span>

<span class="nc" id="L99">    private final Random random = new Random();</span>

    @Override
    public String getType() {
<span class="nc" id="L103">        return &quot;licenseplate&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 从参数中获取车牌类型
<span class="nc" id="L110">            String plateType = getStringParam(config, &quot;type&quot;, &quot;BOTH&quot;);</span>

            // 从参数中获取省份
<span class="nc" id="L113">            String province = getStringParam(config, &quot;province&quot;, null);</span>

            // 从参数中获取城市代码
<span class="nc" id="L116">            String city = getStringParam(config, &quot;city&quot;, null);</span>

            // 从参数中获取是否包含I和O
<span class="nc" id="L119">            boolean includeIO = getBooleanParam(config, &quot;include_io&quot;, false);</span>

            // 从参数中获取是否生成有效车牌
<span class="nc" id="L122">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>

<span class="nc bnc" id="L124" title="All 2 branches missed.">            if (!valid) {</span>
<span class="nc" id="L125">                return generateInvalidLicensePlate();</span>
            }

<span class="nc" id="L128">            return generateValidLicensePlate(plateType, province, city, includeIO);</span>

<span class="nc" id="L130">        } catch (Exception e) {</span>
<span class="nc" id="L131">            logger.error(&quot;Failed to generate license plate&quot;, e);</span>
            // 返回一个默认车牌号作为fallback
<span class="nc" id="L133">            return &quot;京A12345&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L139">        return FieldConfig.class;</span>
    }

    /**
     * 生成有效的车牌号。
     * 
     * @param plateType 车牌类型
     * @param province  省份
     * @param city      城市代码
     * @param includeIO 是否包含I和O
     * @return 有效的车牌号
     */
    private String generateValidLicensePlate(String plateType, String province, String city, boolean includeIO) {
        // 确定车牌类型
<span class="nc" id="L153">        boolean isNewEnergy = determineNewEnergyType(plateType);</span>

        // 生成省份简称
<span class="nc" id="L156">        String provinceCode = selectProvinceCode(province);</span>

        // 生成城市代码
<span class="nc" id="L159">        String cityCode = selectCityCode(city);</span>

        // 生成车牌号码部分
<span class="nc" id="L162">        String numberPart = generateNumberPart(isNewEnergy, includeIO);</span>

<span class="nc" id="L164">        return provinceCode + cityCode + numberPart;</span>
    }

    /**
     * 确定是否为新能源车牌。
     * 
     * @param plateType 车牌类型参数
     * @return 是否为新能源车牌
     */
    private boolean determineNewEnergyType(String plateType) {
<span class="nc bnc" id="L174" title="All 3 branches missed.">        return switch (plateType.toUpperCase()) {</span>
<span class="nc" id="L175">            case &quot;NEW_ENERGY&quot;, &quot;ELECTRIC&quot;, &quot;GREEN&quot; -&gt; true;</span>
<span class="nc" id="L176">            case &quot;FUEL&quot;, &quot;TRADITIONAL&quot;, &quot;BLUE&quot; -&gt; false;</span>
<span class="nc" id="L177">            default -&gt; random.nextBoolean(); // BOTH或其他情况随机选择</span>
        };
    }

    /**
     * 选择省份代码。
     * 
     * @param province 指定的省份
     * @return 省份简称
     */
    private String selectProvinceCode(String province) {
<span class="nc bnc" id="L188" title="All 4 branches missed.">        if (province != null &amp;&amp; !province.trim().isEmpty()) {</span>
<span class="nc" id="L189">            String cleanProvince = province.trim();</span>

            // 如果直接是省份简称
<span class="nc bnc" id="L192" title="All 4 branches missed.">            if (cleanProvince.length() == 1 &amp;&amp; COMMON_PROVINCES.contains(cleanProvince)) {</span>
<span class="nc" id="L193">                return cleanProvince;</span>
            }

            // 如果是省份全名，查找对应简称
<span class="nc" id="L197">            String code = PROVINCE_CODES.get(cleanProvince);</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">            if (code != null) {</span>
<span class="nc" id="L199">                return code;</span>
            }

            // 如果是省份全名的一部分，模糊匹配
<span class="nc bnc" id="L203" title="All 2 branches missed.">            for (Map.Entry&lt;String, String&gt; entry : PROVINCE_CODES.entrySet()) {</span>
<span class="nc bnc" id="L204" title="All 4 branches missed.">                if (entry.getKey().contains(cleanProvince) || cleanProvince.contains(entry.getKey())) {</span>
<span class="nc" id="L205">                    return entry.getValue();</span>
                }
<span class="nc" id="L207">            }</span>

<span class="nc" id="L209">            logger.warn(&quot;Unknown province: {}, using random province&quot;, province);</span>
        }

        // 随机选择一个常用省份
<span class="nc" id="L213">        return COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size()));</span>
    }

    /**
     * 选择城市代码。
     * 
     * @param city 指定的城市代码
     * @return 城市代码字母
     */
    private String selectCityCode(String city) {
<span class="nc bnc" id="L223" title="All 4 branches missed.">        if (city != null &amp;&amp; !city.trim().isEmpty()) {</span>
<span class="nc" id="L224">            String cleanCity = city.trim().toUpperCase();</span>

            // 如果是单个字母且在有效范围内
<span class="nc bnc" id="L227" title="All 4 branches missed.">            if (cleanCity.length() == 1 &amp;&amp; CITY_CODES.contains(cleanCity)) {</span>
<span class="nc" id="L228">                return cleanCity;</span>
            }

<span class="nc" id="L231">            logger.warn(&quot;Invalid city code: {}, using random city code&quot;, city);</span>
        }

        // 随机选择一个城市代码
<span class="nc" id="L235">        return CITY_CODES.get(random.nextInt(CITY_CODES.size()));</span>
    }

    /**
     * 生成车牌号码部分。
     * 
     * @param isNewEnergy 是否为新能源车牌
     * @param includeIO   是否包含I和O
     * @return 号码部分
     */
    private String generateNumberPart(boolean isNewEnergy, boolean includeIO) {
<span class="nc bnc" id="L246" title="All 2 branches missed.">        if (isNewEnergy) {</span>
<span class="nc" id="L247">            return generateNewEnergyNumberPart(includeIO);</span>
        } else {
<span class="nc" id="L249">            return generateTraditionalNumberPart(includeIO);</span>
        }
    }

    /**
     * 生成新能源车牌号码部分（6位）。
     * 
     * @param includeIO 是否包含I和O
     * @return 新能源车牌号码部分
     */
    private String generateNewEnergyNumberPart(boolean includeIO) {
<span class="nc" id="L260">        StringBuilder numberPart = new StringBuilder();</span>
<span class="nc bnc" id="L261" title="All 2 branches missed.">        String charSet = includeIO ? PLATE_CHARS + &quot;IO&quot; : PLATE_CHARS;</span>

        // 新能源车牌：D或F开头，后面5位字母数字
<span class="nc" id="L264">        String prefix = NEW_ENERGY_PREFIXES.get(random.nextInt(NEW_ENERGY_PREFIXES.size()));</span>
<span class="nc" id="L265">        numberPart.append(prefix);</span>

        // 生成后5位
<span class="nc bnc" id="L268" title="All 2 branches missed.">        for (int i = 0; i &lt; 5; i++) {</span>
<span class="nc" id="L269">            numberPart.append(charSet.charAt(random.nextInt(charSet.length())));</span>
        }

<span class="nc" id="L272">        return numberPart.toString();</span>
    }

    /**
     * 生成传统车牌号码部分（5位）。
     * 
     * @param includeIO 是否包含I和O
     * @return 传统车牌号码部分
     */
    private String generateTraditionalNumberPart(boolean includeIO) {
<span class="nc" id="L282">        StringBuilder numberPart = new StringBuilder();</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">        String charSet = includeIO ? PLATE_CHARS + &quot;IO&quot; : PLATE_CHARS;</span>

        // 传统车牌：5位字母数字组合
<span class="nc bnc" id="L286" title="All 2 branches missed.">        for (int i = 0; i &lt; 5; i++) {</span>
<span class="nc" id="L287">            numberPart.append(charSet.charAt(random.nextInt(charSet.length())));</span>
        }

<span class="nc" id="L290">        return numberPart.toString();</span>
    }

    /**
     * 生成无效的车牌号。
     * 
     * @return 无效的车牌号
     */
    private String generateInvalidLicensePlate() {
<span class="nc" id="L299">        int type = random.nextInt(4);</span>

<span class="nc bnc" id="L301" title="All 4 branches missed.">        return switch (type) {</span>
<span class="nc" id="L302">            case 0 -&gt; generateWrongLengthPlate();</span>
<span class="nc" id="L303">            case 1 -&gt; generateWrongFormatPlate();</span>
<span class="nc" id="L304">            case 2 -&gt; generateInvalidCharsPlate();</span>
<span class="nc" id="L305">            default -&gt; generateOtherInvalidPlate();</span>
        };
    }

    /**
     * 生成长度错误的车牌号。
     * 
     * @return 长度错误的车牌号
     */
    private String generateWrongLengthPlate() {
        // 生成过短或过长的车牌号
<span class="nc bnc" id="L316" title="All 2 branches missed.">        int length = random.nextBoolean() ? random.nextInt(4) + 2 : // 2-5位</span>
<span class="nc" id="L317">                random.nextInt(5) + 9; // 9-13位</span>

<span class="nc" id="L319">        StringBuilder plate = new StringBuilder();</span>
<span class="nc bnc" id="L320" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc bnc" id="L321" title="All 2 branches missed.">            if (i == 0) {</span>
                // 第一位用省份简称
<span class="nc" id="L323">                plate.append(COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size())));</span>
            } else {
<span class="nc" id="L325">                plate.append(PLATE_CHARS.charAt(random.nextInt(PLATE_CHARS.length())));</span>
            }
        }

<span class="nc" id="L329">        return plate.toString();</span>
    }

    /**
     * 生成格式错误的车牌号。
     * 
     * @return 格式错误的车牌号
     */
    private String generateWrongFormatPlate() {
        // 生成不符合省份+城市+号码格式的车牌
<span class="nc" id="L339">        return &quot;1A12345&quot;; // 第一位不是汉字</span>
    }

    /**
     * 生成包含非法字符的车牌号。
     * 
     * @return 包含非法字符的车牌号
     */
    private String generateInvalidCharsPlate() {
<span class="nc" id="L348">        String province = COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size()));</span>
<span class="nc" id="L349">        String city = CITY_CODES.get(random.nextInt(CITY_CODES.size()));</span>

        // 在号码部分包含非法字符
<span class="nc" id="L352">        String[] invalidChars = { &quot;I&quot;, &quot;O&quot;, &quot;!&quot;, &quot;@&quot;, &quot;#&quot;, &quot;$&quot;, &quot;%&quot; };</span>
<span class="nc" id="L353">        String invalidChar = invalidChars[random.nextInt(invalidChars.length)];</span>

<span class="nc" id="L355">        return province + city + &quot;123&quot; + invalidChar + &quot;5&quot;;</span>
    }

    /**
     * 生成其他类型的无效车牌号。
     * 
     * @return 其他无效车牌号
     */
    private String generateOtherInvalidPlate() {
        // 生成全数字或全字母的车牌号
<span class="nc bnc" id="L365" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L366">            return &quot;1234567&quot;; // 全数字</span>
        } else {
<span class="nc" id="L368">            return &quot;ABCDEFG&quot;; // 全字母</span>
        }
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L376" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L377">            return defaultValue;</span>
        }

<span class="nc" id="L380">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L381" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L388" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L389">            return defaultValue;</span>
        }

<span class="nc" id="L392">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L393" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L394">            return defaultValue;</span>
        }

<span class="nc bnc" id="L397" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L398">            return (Boolean) value;</span>
        }

<span class="nc" id="L401">        return Boolean.parseBoolean(value.toString());</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L406">        return &quot;License plate generator - generates Chinese license plate numbers with fuel/electric vehicle support&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>