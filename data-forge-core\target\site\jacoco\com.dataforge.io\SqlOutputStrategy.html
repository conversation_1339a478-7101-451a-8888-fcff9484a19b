<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SqlOutputStrategy</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.io</a> &gt; <span class="el_class">SqlOutputStrategy</span></div><h1>SqlOutputStrategy</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">730 of 730</td><td class="ctr2">0%</td><td class="bar">80 of 80</td><td class="ctr2">0%</td><td class="ctr1">56</td><td class="ctr2">56</td><td class="ctr1">171</td><td class="ctr2">171</td><td class="ctr1">16</td><td class="ctr2">16</td></tr></tfoot><tbody><tr><td id="a12"><a href="SqlOutputStrategy.java.html#L112" class="el_method">writeRecord(Map)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="136" alt="136"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h1">29</td><td class="ctr2" id="i1">29</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="SqlOutputStrategy.java.html#L280" class="el_method">writeBatchInsert(List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="114" height="10" title="130" alt="130"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h2">27</td><td class="ctr2" id="i2">27</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="SqlOutputStrategy.java.html#L346" class="el_method">formatSqlValue(Object)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="120" alt="120"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="24" alt="24"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h0">31</td><td class="ctr2" id="i0">31</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="SqlOutputStrategy.java.html#L221" class="el_method">initializeWriter()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="95" alt="95"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h4">18</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="SqlOutputStrategy.java.html#L76" class="el_method">initialize(OutputConfig, List)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="80" alt="80"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="12" alt="12"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a15"><a href="SqlOutputStrategy.java.html#L254" class="el_method">writeSqlHeader()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="37" alt="37"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="SqlOutputStrategy.java.html#L182" class="el_method">finish()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="32" alt="32"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a13"><a href="SqlOutputStrategy.java.html#L162" class="el_method">writeRecords(List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="29" alt="29"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="SqlOutputStrategy.java.html#L267" class="el_method">writeSqlFooter()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="25" alt="25"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a0"><a href="SqlOutputStrategy.java.html#L328" class="el_method">escapeIdentifier(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="13" alt="13"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a10"><a href="SqlOutputStrategy.java.html#L35" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="13" alt="13"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="SqlOutputStrategy.java.html#L205" class="el_method">flush()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="7" alt="7"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h11">3</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="SqlOutputStrategy.java.html#L33" class="el_method">SqlOutputStrategy()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="SqlOutputStrategy.java.html#L212" class="el_method">getWrittenRecordCount()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a5"><a href="SqlOutputStrategy.java.html#L71" class="el_method">getSupportedFormat()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a4"><a href="SqlOutputStrategy.java.html#L414" class="el_method">getDescription()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>