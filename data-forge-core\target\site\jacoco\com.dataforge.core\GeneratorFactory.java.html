<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GeneratorFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.core</a> &gt; <span class="el_source">GeneratorFactory.java</span></div><h1>GeneratorFactory.java</h1><pre class="source lang-java linenums">package com.dataforge.core;

import com.dataforge.generators.spi.DataGenerator;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 数据生成器工厂类。
 * 
 * &lt;p&gt;
 * 使用Java ServiceLoader (SPI) 机制动态加载所有DataGenerator实现。
 * 该工厂负责管理和提供数据生成器实例，支持运行时发现和注册新的生成器。
 * 
 * &lt;p&gt;
 * 工厂采用单例模式，确保生成器的统一管理和高效访问。
 * 所有注册的生成器都会被缓存，避免重复创建实例。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class GeneratorFactory {

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(GeneratorFactory.class);</span>

    /**
     * 生成器缓存，键为类型名称，值为生成器实例。
     * 使用ConcurrentHashMap确保线程安全。
     */
<span class="nc" id="L35">    private final Map&lt;String, DataGenerator&lt;?, ?&gt;&gt; generators = new ConcurrentHashMap&lt;&gt;();</span>

    /**
     * 是否已初始化标志。
     */
<span class="nc" id="L40">    private volatile boolean initialized = false;</span>

    /**
     * 构造函数，自动初始化生成器。
     */
<span class="nc" id="L45">    public GeneratorFactory() {</span>
<span class="nc" id="L46">        initialize();</span>
<span class="nc" id="L47">    }</span>

    /**
     * 初始化生成器工厂，加载所有可用的数据生成器。
     * 
     * &lt;p&gt;
     * 该方法使用ServiceLoader机制扫描classpath中所有实现了
     * DataGenerator接口的类，并将其注册到工厂中。
     */
    private synchronized void initialize() {
<span class="nc bnc" id="L57" title="All 2 branches missed.">        if (initialized) {</span>
<span class="nc" id="L58">            return;</span>
        }

<span class="nc" id="L61">        logger.info(&quot;Initializing GeneratorFactory...&quot;);</span>

        try {
            // 使用ServiceLoader加载所有DataGenerator实现
            @SuppressWarnings(&quot;rawtypes&quot;)
<span class="nc" id="L66">            ServiceLoader&lt;DataGenerator&gt; serviceLoader = ServiceLoader.load(DataGenerator.class);</span>

<span class="nc" id="L68">            int loadedCount = 0;</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">            for (DataGenerator&lt;?, ?&gt; generator : serviceLoader) {</span>
                try {
<span class="nc" id="L71">                    String type = generator.getType();</span>
<span class="nc bnc" id="L72" title="All 4 branches missed.">                    if (type == null || type.trim().isEmpty()) {</span>
<span class="nc" id="L73">                        logger.warn(&quot;Generator {} returned null or empty type, skipping&quot;,</span>
<span class="nc" id="L74">                                generator.getClass().getName());</span>
<span class="nc" id="L75">                        continue;</span>
                    }

                    // 检查是否已存在相同类型的生成器
<span class="nc bnc" id="L79" title="All 2 branches missed.">                    if (generators.containsKey(type)) {</span>
<span class="nc" id="L80">                        logger.warn(&quot;Duplicate generator type '{}' found. Existing: {}, New: {}. Keeping existing.&quot;,</span>
<span class="nc" id="L81">                                type, generators.get(type).getClass().getName(), generator.getClass().getName());</span>
<span class="nc" id="L82">                        continue;</span>
                    }

<span class="nc" id="L85">                    generators.put(type, generator);</span>
<span class="nc" id="L86">                    loadedCount++;</span>
<span class="nc" id="L87">                    logger.debug(&quot;Registered generator: type='{}', class='{}'&quot;, type, generator.getClass().getName());</span>

<span class="nc" id="L89">                } catch (Exception e) {</span>
<span class="nc" id="L90">                    logger.error(&quot;Failed to register generator: {}&quot;, generator.getClass().getName(), e);</span>
<span class="nc" id="L91">                }</span>
<span class="nc" id="L92">            }</span>

<span class="nc" id="L94">            initialized = true;</span>
<span class="nc" id="L95">            logger.info(&quot;GeneratorFactory initialized successfully. Loaded {} generators: {}&quot;,</span>
<span class="nc" id="L96">                    loadedCount, generators.keySet());</span>

<span class="nc" id="L98">        } catch (Exception e) {</span>
<span class="nc" id="L99">            logger.error(&quot;Failed to initialize GeneratorFactory&quot;, e);</span>
<span class="nc" id="L100">            throw new RuntimeException(&quot;Failed to initialize GeneratorFactory&quot;, e);</span>
<span class="nc" id="L101">        }</span>
<span class="nc" id="L102">    }</span>

    /**
     * 根据类型获取数据生成器。
     * 
     * @param type 数据类型标识符
     * @return 对应的数据生成器，如果不存在返回null
     * @throws IllegalArgumentException 当type为null或空字符串时
     */
    public DataGenerator&lt;?, ?&gt; getGenerator(String type) {
<span class="nc bnc" id="L112" title="All 4 branches missed.">        if (type == null || type.trim().isEmpty()) {</span>
<span class="nc" id="L113">            throw new IllegalArgumentException(&quot;Generator type cannot be null or empty&quot;);</span>
        }

<span class="nc" id="L116">        DataGenerator&lt;?, ?&gt; generator = generators.get(type.trim());</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">        if (generator == null) {</span>
<span class="nc" id="L118">            logger.debug(&quot;No generator found for type: {}&quot;, type);</span>
        }

<span class="nc" id="L121">        return generator;</span>
    }

    /**
     * 检查是否存在指定类型的生成器。
     * 
     * @param type 数据类型标识符
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasGenerator(String type) {
<span class="nc bnc" id="L131" title="All 4 branches missed.">        if (type == null || type.trim().isEmpty()) {</span>
<span class="nc" id="L132">            return false;</span>
        }
<span class="nc" id="L134">        return generators.containsKey(type.trim());</span>
    }

    /**
     * 获取所有已注册的生成器类型。
     * 
     * @return 生成器类型集合的副本
     */
    public java.util.Set&lt;String&gt; getAvailableTypes() {
<span class="nc" id="L143">        return new java.util.HashSet&lt;&gt;(generators.keySet());</span>
    }

    /**
     * 获取已注册的生成器数量。
     * 
     * @return 生成器数量
     */
    public int getGeneratorCount() {
<span class="nc" id="L152">        return generators.size();</span>
    }

    /**
     * 手动注册数据生成器。
     * 
     * &lt;p&gt;
     * 该方法主要用于测试或动态注册生成器的场景。
     * 在正常情况下，生成器应该通过SPI机制自动发现和注册。
     * 
     * @param generator 要注册的数据生成器
     * @throws IllegalArgumentException 当generator为null或其类型为null/空字符串时
     * @throws IllegalStateException    当指定类型的生成器已存在时
     */
    public void registerGenerator(DataGenerator&lt;?, ?&gt; generator) {
<span class="nc bnc" id="L167" title="All 2 branches missed.">        if (generator == null) {</span>
<span class="nc" id="L168">            throw new IllegalArgumentException(&quot;Generator cannot be null&quot;);</span>
        }

<span class="nc" id="L171">        String type = generator.getType();</span>
<span class="nc bnc" id="L172" title="All 4 branches missed.">        if (type == null || type.trim().isEmpty()) {</span>
<span class="nc" id="L173">            throw new IllegalArgumentException(&quot;Generator type cannot be null or empty&quot;);</span>
        }

<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (generators.containsKey(type)) {</span>
<span class="nc" id="L177">            throw new IllegalStateException(&quot;Generator for type '&quot; + type + &quot;' already exists&quot;);</span>
        }

<span class="nc" id="L180">        generators.put(type, generator);</span>
<span class="nc" id="L181">        logger.info(&quot;Manually registered generator: type='{}', class='{}'&quot;, type, generator.getClass().getName());</span>
<span class="nc" id="L182">    }</span>

    /**
     * 注销指定类型的数据生成器。
     * 
     * &lt;p&gt;
     * 该方法主要用于测试或动态管理生成器的场景。
     * 
     * @param type 要注销的生成器类型
     * @return 被注销的生成器，如果不存在返回null
     */
    public DataGenerator&lt;?, ?&gt; unregisterGenerator(String type) {
<span class="nc bnc" id="L194" title="All 4 branches missed.">        if (type == null || type.trim().isEmpty()) {</span>
<span class="nc" id="L195">            return null;</span>
        }

<span class="nc" id="L198">        DataGenerator&lt;?, ?&gt; removed = generators.remove(type.trim());</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (removed != null) {</span>
<span class="nc" id="L200">            logger.info(&quot;Unregistered generator: type='{}', class='{}'&quot;, type, removed.getClass().getName());</span>
        }

<span class="nc" id="L203">        return removed;</span>
    }

    /**
     * 重新初始化生成器工厂。
     * 
     * &lt;p&gt;
     * 清除所有已注册的生成器，重新扫描和加载。
     * 该方法主要用于开发和测试场景。
     */
    public synchronized void reinitialize() {
<span class="nc" id="L214">        logger.info(&quot;Reinitializing GeneratorFactory...&quot;);</span>
<span class="nc" id="L215">        generators.clear();</span>
<span class="nc" id="L216">        initialized = false;</span>
<span class="nc" id="L217">        initialize();</span>
<span class="nc" id="L218">    }</span>

    /**
     * 获取生成器的详细信息。
     * 
     * @return 包含所有生成器信息的映射
     */
    public Map&lt;String, String&gt; getGeneratorInfo() {
<span class="nc" id="L226">        Map&lt;String, String&gt; info = new HashMap&lt;&gt;();</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">        for (Map.Entry&lt;String, DataGenerator&lt;?, ?&gt;&gt; entry : generators.entrySet()) {</span>
<span class="nc" id="L228">            DataGenerator&lt;?, ?&gt; generator = entry.getValue();</span>
<span class="nc" id="L229">            info.put(entry.getKey(), String.format(&quot;%s - %s&quot;,</span>
<span class="nc" id="L230">                    generator.getClass().getSimpleName(),</span>
<span class="nc" id="L231">                    generator.getDescription()));</span>
<span class="nc" id="L232">        }</span>
<span class="nc" id="L233">        return info;</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L238">        return String.format(&quot;GeneratorFactory{initialized=%s, generatorCount=%d, types=%s}&quot;,</span>
<span class="nc" id="L239">                initialized, generators.size(), generators.keySet());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>