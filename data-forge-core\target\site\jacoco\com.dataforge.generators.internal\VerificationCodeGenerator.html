<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VerificationCodeGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">VerificationCodeGenerator</span></div><h1>VerificationCodeGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">598 of 598</td><td class="ctr2">0%</td><td class="bar">57 of 57</td><td class="ctr2">0%</td><td class="ctr1">48</td><td class="ctr2">48</td><td class="ctr1">113</td><td class="ctr2">113</td><td class="ctr1">17</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a4"><a href="VerificationCodeGenerator.java.html#L96" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="124" alt="124"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="VerificationCodeGenerator.java.html#L293" class="el_method">generateMathCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="67" alt="67"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="VerificationCodeGenerator.java.html#L314" class="el_method">generateChineseCode(int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="66" alt="66"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="VerificationCodeGenerator.java.html#L132" class="el_method">generateVerificationCode(String, int, String, String, boolean, boolean)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="62" alt="62"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="VerificationCodeGenerator.java.html#L62" class="el_method">initializeCodeTemplates()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="51" alt="51"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="VerificationCodeGenerator.java.html#L172" class="el_method">determineCharset(String, String, VerificationCodeGenerator.CodeTemplate, boolean, boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="51" alt="51"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="13" alt="13"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h1">18</td><td class="ctr2" id="i1">18</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="VerificationCodeGenerator.java.html#L272" class="el_method">generateFormattedCode(String, String, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="48" alt="48"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="92" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a13"><a href="VerificationCodeGenerator.java.html#L206" class="el_method">removeAmbiguousChars(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="33" alt="33"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="VerificationCodeGenerator.java.html#L216" class="el_method">containsAlpha(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="24" alt="24"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h11">4</td><td class="ctr2" id="i11">4</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a14"><a href="VerificationCodeGenerator.java.html#L29" class="el_method">static {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="17" alt="17"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a15"><a href="VerificationCodeGenerator.java.html#L250" class="el_method">validateCode(String, String, boolean)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="16" alt="16"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="VerificationCodeGenerator.java.html#L225" class="el_method">applyCase(String, VerificationCodeGenerator.CodeType)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="13" alt="13"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h10">5</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="VerificationCodeGenerator.java.html#L160" class="el_method">determineCodeType(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="11" alt="11"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="VerificationCodeGenerator.java.html#L265" class="el_method">isExpired(long)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="8" alt="8"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="VerificationCodeGenerator.java.html#L27" class="el_method">VerificationCodeGenerator()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a10"><a href="VerificationCodeGenerator.java.html#L84" class="el_method">getType()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a9"><a href="VerificationCodeGenerator.java.html#L89" class="el_method">getConfigClass()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>