<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
------------------------------------------------------------------------------------->
#### **A. 核心技术栈版本规约**

1. **Java 版本:** 项目**必须**使用 **Java 17 (LTS)**。CI/CD 环境和所有开发人员的本地环境都必须统一到此版本。

2. **Spring Boot 版本:** 项目**必须**使用 **Spring Boot 3.2.x**。具体的小版本号在顶层 pom.xml 中定义，并随官方稳定版发布进行升级。

3. **构建工具版本:** 项目**必须**使用 **Apache Maven 3.8.x** 或更高版本。

#### **B. 编码规约 (Coding Conventions)**

1. **代码风格:**

    - **基础规范:** **必须**严格遵循 **Google Java Style Guide**。

    - **自动化工具:**

        - **Checkstyle:** 项目**必须**集成 Checkstyle 插件，并使用 google_checks.xml 作为规则集。构建时自动检查，任何违反都将导致构建失败。

        - **PMD:** 项目**必须**集成 PMD 插件进行静态代码分析。任何 Priority 1 或 Priority 2 的问题都将导致构建失败。

    - **格式化:** 所有代码在提交前**必须**使用 google-java-format 工具进行格式化。

2. **命名规约:**

    - **包 (Package):** 全小写，连续单词用点.分隔。

    - **类/接口 (Class/Interface):** 大驼峰命名法 (UpperCamelCase)。

    - **方法 (Method):** 小驼峰命名法 (lowerCamelCase)。

    - **常量 (Constant):** 全大写，单词间用下划线_分隔。

    - **变量 (Variable):** 小驼峰命名法。布尔型变量**必须**以 is, has, can 开头。

    - **配置类:** **必须**以 Config 或 Properties 结尾。

    - **DTO (Data Transfer Object):** **必须**以 Dto, Request, Response 之一结尾。

3. **注释规约:**

    - **Javadoc:** 所有 public 的类和方法都**必须**提供完整的 Javadoc 注释，包含功能描述、@param、@return 和 @throws。

    - **实现注释:** 对复杂的业务逻辑、算法或非直观的代码块，**必须**使用 // 或 /*...*/ 添加注释，解释其设计意图（"The Why"）。

    - **TODO 注释:** **必须**遵循格式 // TODO: [YYYY-MM-DD, Author] Description.。

4. **日志规约:**

    - **框架:** **必须**统一使用 **SLF4J** 作为日志门面，实现为 Logback。

    - **日志级别:** **必须**严格按照以下定义使用：

        - ERROR: 系统关键功能无法正常提供服务，例如数据库连接中断、核心依赖服务宕机。**必须**记录异常堆栈。

        - WARN: 发生非预期情况，但不影响当前核心流程，例如配置降级、重试操作。

        - INFO: 记录应用生命周期中的关键事件和业务流程的关键节点，例如应用启动、收到生成请求、任务完成。

        - DEBUG: 仅用于开发和调试阶段，记录详细的程序执行状态。

        - TRACE: 最细粒度的日志，仅在深度问题排查时使用。

    - **禁止项:** **严禁**使用 System.out.println()。**严禁**在日志中以明文形式输出任何敏感信息（密码、密钥、身份证号、银行卡号等）。

#### **C. 版本控制规约 (Version Control Conventions)**

1. **分支模型:** **必须**采用 **Git Flow** 模型。

    - main: 存放稳定、可发布版本。只接受来自 release/*或 hotfix/* 分支的合并请求 (Pull Request)。

    - develop: 开发主分支。所有 feature/* 分支的终点。

    - feature/*: 功能分支，从 develop 创建，**必须**遵循命名 feature/issue-id-short-description (例如: feature/DF-25-add-idcard-generator)。

    - release/*: 发布分支，从 develop 创建，用于版本发布准备。

    - hotfix/*: 热修复分支，从 main 创建，用于线上紧急修复。

2. **提交信息 (Commit Message) 规约:**

    - **格式:** **必须**严格遵循 **Conventional Commits 1.0.0** 规范。

    - **示例:**

        codeCode

        ```
        feat(generator): add support for Luhn algorithm validation
        fix(config): correct yaml parsing for nested field parameters
        ```

    - **强制策略:** 在 CI 中集成 commit-lint 工具，对合并到 develop 和 main 的提交信息进行格式校验，不符合规范的 PR 将被拒绝。

#### **D. 构建与依赖管理规约**

1. **构建工具:** **必须**使用 **Maven**。

2. **依赖管理:**

    - **BOM (Bill of Materials):** **必须**通过 spring-boot-dependencies BOM 管理 Spring Boot 及相关依赖版本。

    - **版本定义:** 所有非 BOM 管理的依赖版本**必须**在顶层 pom.xml 的 <properties> 中统一定义。

    - **依赖范围 (Scope):** **必须**为每个依赖明确指定 scope。测试库**必须**使用 test scope。

#### **E. 测试规约 (Testing Conventions)**

1. **单元测试 (Unit Tests):**

    - **框架:** **必须**使用 JUnit 5, Mockito, AssertJ。

    - **要求:** 所有 Service 层、DataGenerator 实现、Validator 等核心业务逻辑类，其新增或修改的 public 方法**必须**有对应的单元测试。

    - **命名:** 测试类**必须**以 Test 结尾。测试方法**必须**以 should_ExpectedBehavior_When_StateUnderTest 格式命名。

2. **集成测试 (Integration Tests):**

    - **框架:** **必须**使用 Spring Boot Test (@SpringBootTest)。

    - **要求:** 所有 CLI 命令和未来新增的 Controller **必须**有集成测试覆盖其主要流程。

    - **命名:** 测试类**必须**以 IT 结尾。

3. **代码覆盖率:**

    - **工具:** **必须**使用 JaCoCo。

    - **目标:** data-forge-core 模块的行覆盖率**不得低于 85%**。CI 流水线将强制检查此指标。

#### **F. API 设计与文档规约**

1. **RESTful 风格:**

    - **资源:** **必须**使用名词复数表示资源。

    - **HTTP 方法:** **必须**严格使用 GET, POST, PUT, DELETE 等标准方法。

    - **状态码:** **必须**返回准确的 HTTP 状态码。

2. **数据格式:** API 的请求和响应体**必须**使用 application/json 格式，并设置正确的 Content-Type 头。

3. **API 文档:**

    - **工具:** **必须**使用 springdoc-openapi 生成 OpenAPI 3.0 文档。

    - **注解:** 所有暴露的 API 端点和 DTO **必须**使用 @Operation, @Parameter, @Schema 等注解提供清晰的文档说明。

4. **DTOs:** **严禁**将领域模型 (Domain Model) 或数据库实体 (Entity) 直接作为 API 的输入或输出。**必须**使用 DTOs 进行数据传输。

#### **G. 异常处理规约**

1. **自定义异常:** **必须**为可预见的业务异常创建继承自 RuntimeException 的自定义异常类。

2. **全局异常处理器:** **必须**使用 @RestControllerAdvice (或相应的CLI异常处理机制) 实现全局异常处理器，以统一的格式返回错误信息。

#### **H. 安全规约**

1. **依赖扫描:** CI 流水线**必须**集成 **Snyk** 或 **OWASP Dependency-Check**，并设置规则，发现高危漏洞 (High Severity) 时中断构建。

2. **输入校验:** 所有外部输入**必须**在进入 Service 层之前完成校验。**必须**使用 **Jakarta Bean Validation** (@Valid) 进行声明式校验。

3. **安全编码:** **必须**遵循 OWASP Top 10 的指导原则进行编码，以防止 SQL 注入、XSS、命令注入等常见漏洞。

### **3. 风险、最佳实践与后续步骤 (Risks, Best Practices & Next Steps)**

- **潜在风险:**

    1. **执行成本:** 严格的规约在初期会增加开发者的学习和适应成本。

    2. **工具链维护:** 维护 CI/CD 流水线中集成的各种检查工具需要持续投入。

- **最佳实践建议:**

    1. **自动化一切 (Automate Everything):** 规约的生命力在于其自动化执行。通过 CI/CD 流水线，将人的审查负担转移给机器，保证规约的刚性。

    2. **提供模板和示例 (Provide Templates & Examples):** 为团队提供符合规约的代码模板、IDE 配置模板和示例项目，降低上手门槛。
