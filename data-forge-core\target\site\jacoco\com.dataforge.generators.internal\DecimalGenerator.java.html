<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DecimalGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">DecimalGenerator.java</span></div><h1>DecimalGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 小数生成器
 * 
 * &lt;p&gt;
 * 支持生成各种精度和格式的小数，包括浮点数、双精度数、
 * 高精度小数等，用于财务计算、科学计算、精度测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;type: 数值类型 (FLOAT|DOUBLE|BIGDECIMAL) 默认: DOUBLE&lt;/li&gt;
 * &lt;li&gt;min: 最小值 默认: 0.0&lt;/li&gt;
 * &lt;li&gt;max: 最大值 默认: 100.0&lt;/li&gt;
 * &lt;li&gt;precision: 总精度位数 默认: 10&lt;/li&gt;
 * &lt;li&gt;scale: 小数位数 默认: 2&lt;/li&gt;
 * &lt;li&gt;rounding: 舍入模式 (UP|DOWN|CEILING|FLOOR|HALF_UP|HALF_DOWN|HALF_EVEN) 默认: HALF_UP&lt;/li&gt;
 * &lt;li&gt;format: 输出格式 (PLAIN|SCIENTIFIC|CURRENCY|PERCENTAGE|CUSTOM) 默认: PLAIN&lt;/li&gt;
 * &lt;li&gt;locale: 本地化设置 默认: en_US&lt;/li&gt;
 * &lt;li&gt;currency_code: 货币代码（仅对CURRENCY格式有效）默认: USD&lt;/li&gt;
 * &lt;li&gt;pattern: 自定义格式模式（仅对CUSTOM格式有效）&lt;/li&gt;
 * &lt;li&gt;positive_only: 是否只生成正数 默认: false&lt;/li&gt;
 * &lt;li&gt;exclude_zero: 是否排除零 默认: false&lt;/li&gt;
 * &lt;li&gt;distribution: 分布类型 (UNIFORM|NORMAL|EXPONENTIAL) 默认: UNIFORM&lt;/li&gt;
 * &lt;li&gt;mean: 正态分布的均值 默认: (min+max)/2&lt;/li&gt;
 * &lt;li&gt;stddev: 正态分布的标准差 默认: (max-min)/6&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L46">public class DecimalGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L48">    private static final Logger logger = LoggerFactory.getLogger(DecimalGenerator.class);</span>
<span class="fc" id="L49">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 数值类型枚举
<span class="fc" id="L52">    public enum DecimalType {</span>
<span class="fc" id="L53">        FLOAT(&quot;32位浮点数&quot;),</span>
<span class="fc" id="L54">        DOUBLE(&quot;64位双精度数&quot;),</span>
<span class="fc" id="L55">        BIGDECIMAL(&quot;高精度小数&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L59">        DecimalType(String description) {</span>
<span class="fc" id="L60">            this.description = description;</span>
<span class="fc" id="L61">        }</span>
        
        public String getDescription() {
<span class="nc" id="L64">            return description;</span>
        }
    }
    
    // 输出格式枚举
<span class="fc" id="L69">    public enum OutputFormat {</span>
<span class="fc" id="L70">        PLAIN(&quot;普通格式&quot;),</span>
<span class="fc" id="L71">        SCIENTIFIC(&quot;科学计数法&quot;),</span>
<span class="fc" id="L72">        CURRENCY(&quot;货币格式&quot;),</span>
<span class="fc" id="L73">        PERCENTAGE(&quot;百分比格式&quot;),</span>
<span class="fc" id="L74">        CUSTOM(&quot;自定义格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L78">        OutputFormat(String description) {</span>
<span class="fc" id="L79">            this.description = description;</span>
<span class="fc" id="L80">        }</span>
        
        public String getDescription() {
<span class="nc" id="L83">            return description;</span>
        }
    }
    
    // 分布类型枚举
<span class="fc" id="L88">    public enum DistributionType {</span>
<span class="fc" id="L89">        UNIFORM(&quot;均匀分布&quot;),</span>
<span class="fc" id="L90">        NORMAL(&quot;正态分布&quot;),</span>
<span class="fc" id="L91">        EXPONENTIAL(&quot;指数分布&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L95">        DistributionType(String description) {</span>
<span class="fc" id="L96">            this.description = description;</span>
<span class="fc" id="L97">        }</span>
        
        public String getDescription() {
<span class="nc" id="L100">            return description;</span>
        }
    }

    @Override
    public String getType() {
<span class="fc" id="L106">        return &quot;decimal&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L111">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取数值类型
<span class="fc" id="L118">            String typeStr = getStringParam(config, &quot;type&quot;, &quot;DOUBLE&quot;);</span>
<span class="fc" id="L119">            DecimalType decimalType = parseDecimalType(typeStr);</span>
            
            // 获取分布类型
<span class="fc" id="L122">            String distributionStr = getStringParam(config, &quot;distribution&quot;, &quot;UNIFORM&quot;);</span>
<span class="fc" id="L123">            DistributionType distribution = parseDistributionType(distributionStr);</span>
            
            // 获取输出格式
<span class="fc" id="L126">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;PLAIN&quot;);</span>
<span class="fc" id="L127">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 生成小数
<span class="fc" id="L130">            BigDecimal decimal = generateDecimal(distribution, config);</span>
            
            // 转换为指定类型
<span class="fc" id="L133">            Number number = convertToDecimalType(decimal, decimalType);</span>
            
            // 格式化输出
<span class="fc" id="L136">            return formatDecimal(number, format, config);</span>
            
<span class="nc" id="L138">        } catch (Exception e) {</span>
<span class="nc" id="L139">            logger.error(&quot;Failed to generate decimal number&quot;, e);</span>
            // 返回一个默认的小数作为fallback
<span class="nc" id="L141">            return String.format(&quot;%.2f&quot;, random.nextDouble() * 100);</span>
        }
    }

    /**
     * 解析小数类型
     */
    private DecimalType parseDecimalType(String typeStr) {
        try {
<span class="fc" id="L150">            return DecimalType.valueOf(typeStr.toUpperCase());</span>
<span class="fc" id="L151">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L152">            logger.warn(&quot;Invalid decimal type: {}, using DOUBLE as default&quot;, typeStr);</span>
<span class="fc" id="L153">            return DecimalType.DOUBLE;</span>
        }
    }

    /**
     * 解析分布类型
     */
    private DistributionType parseDistributionType(String distributionStr) {
        try {
<span class="fc" id="L162">            return DistributionType.valueOf(distributionStr.toUpperCase());</span>
<span class="nc" id="L163">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L164">            logger.warn(&quot;Invalid distribution type: {}, using UNIFORM as default&quot;, distributionStr);</span>
<span class="nc" id="L165">            return DistributionType.UNIFORM;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L174">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="fc" id="L175">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L176">            logger.warn(&quot;Invalid output format: {}, using PLAIN as default&quot;, formatStr);</span>
<span class="fc" id="L177">            return OutputFormat.PLAIN;</span>
        }
    }

    /**
     * 生成小数
     */
    private BigDecimal generateDecimal(DistributionType distribution, FieldConfig config) {
        // 获取范围参数
<span class="fc" id="L186">        double min = getDoubleParam(config, &quot;min&quot;, 0.0);</span>
<span class="fc" id="L187">        double max = getDoubleParam(config, &quot;max&quot;, 100.0);</span>
        
        // 获取其他参数
<span class="fc" id="L190">        boolean positiveOnly = getBooleanParam(config, &quot;positive_only&quot;, false);</span>
<span class="fc" id="L191">        boolean excludeZero = getBooleanParam(config, &quot;exclude_zero&quot;, false);</span>
        
        // 调整范围
<span class="pc bpc" id="L194" title="1 of 4 branches missed.">        if (positiveOnly &amp;&amp; min &lt;= 0) {</span>
<span class="fc" id="L195">            min = 0.01;</span>
        }
        
<span class="pc bpc" id="L198" title="5 of 6 branches missed.">        if (excludeZero &amp;&amp; min &lt;= 0 &amp;&amp; max &gt;= 0) {</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">            if (min == 0) min = 0.01;</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">            if (max == 0) max = 0.01;</span>
        }
        
        // 根据分布类型生成数值
        double value;
<span class="pc bpc" id="L205" title="2 of 4 branches missed.">        switch (distribution) {</span>
            case UNIFORM:
<span class="fc" id="L207">                value = generateUniform(min, max);</span>
<span class="fc" id="L208">                break;</span>
            case NORMAL:
<span class="fc" id="L210">                value = generateNormal(config, min, max);</span>
<span class="fc" id="L211">                break;</span>
            case EXPONENTIAL:
<span class="nc" id="L213">                value = generateExponential(config, min, max);</span>
<span class="nc" id="L214">                break;</span>
            default:
<span class="nc" id="L216">                value = generateUniform(min, max);</span>
                break;
        }
        
        // 应用精度和舍入
<span class="fc" id="L221">        int scale = getIntParam(config, &quot;scale&quot;, 2);</span>
<span class="fc" id="L222">        String roundingStr = getStringParam(config, &quot;rounding&quot;, &quot;HALF_UP&quot;);</span>
<span class="fc" id="L223">        RoundingMode rounding = parseRoundingMode(roundingStr);</span>
        
<span class="fc" id="L225">        return BigDecimal.valueOf(value).setScale(scale, rounding);</span>
    }

    /**
     * 生成均匀分布随机数
     */
    private double generateUniform(double min, double max) {
<span class="pc bpc" id="L232" title="1 of 2 branches missed.">        if (min &gt;= max) {</span>
<span class="nc" id="L233">            return min;</span>
        }
<span class="fc" id="L235">        return min + random.nextDouble() * (max - min);</span>
    }

    /**
     * 生成正态分布随机数
     */
    private double generateNormal(FieldConfig config, double min, double max) {
<span class="fc" id="L242">        double mean = getDoubleParam(config, &quot;mean&quot;, (min + max) / 2.0);</span>
<span class="fc" id="L243">        double stddev = getDoubleParam(config, &quot;stddev&quot;, (max - min) / 6.0);</span>
        
        double value;
        do {
<span class="fc" id="L247">            value = random.nextGaussian() * stddev + mean;</span>
<span class="pc bpc" id="L248" title="2 of 4 branches missed.">        } while (value &lt; min || value &gt; max);</span>
        
<span class="fc" id="L250">        return value;</span>
    }

    /**
     * 生成指数分布随机数
     */
    private double generateExponential(FieldConfig config, double min, double max) {
<span class="nc" id="L257">        double lambda = getDoubleParam(config, &quot;lambda&quot;, 1.0);</span>
        
        double value;
        do {
<span class="nc" id="L261">            value = -Math.log(1 - random.nextDouble()) / lambda + min;</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">        } while (value &gt; max);</span>
        
<span class="nc" id="L264">        return value;</span>
    }

    /**
     * 解析舍入模式
     */
    private RoundingMode parseRoundingMode(String roundingStr) {
        try {
<span class="fc" id="L272">            return RoundingMode.valueOf(roundingStr.toUpperCase());</span>
<span class="nc" id="L273">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L274">            logger.warn(&quot;Invalid rounding mode: {}, using HALF_UP as default&quot;, roundingStr);</span>
<span class="nc" id="L275">            return RoundingMode.HALF_UP;</span>
        }
    }

    /**
     * 转换为指定的小数类型
     */
    private Number convertToDecimalType(BigDecimal decimal, DecimalType decimalType) {
<span class="pc bpc" id="L283" title="2 of 4 branches missed.">        switch (decimalType) {</span>
            case FLOAT:
<span class="nc" id="L285">                return decimal.floatValue();</span>
            case DOUBLE:
<span class="fc" id="L287">                return decimal.doubleValue();</span>
            case BIGDECIMAL:
<span class="fc" id="L289">                return decimal;</span>
            default:
<span class="nc" id="L291">                return decimal.doubleValue();</span>
        }
    }

    /**
     * 格式化小数
     */
    private String formatDecimal(Number number, OutputFormat format, FieldConfig config) {
<span class="pc bpc" id="L299" title="1 of 6 branches missed.">        switch (format) {</span>
            case PLAIN:
<span class="fc" id="L301">                return formatAsPlain(number, config);</span>
            case SCIENTIFIC:
<span class="fc" id="L303">                return formatAsScientific(number);</span>
            case CURRENCY:
<span class="fc" id="L305">                return formatAsCurrency(number, config);</span>
            case PERCENTAGE:
<span class="fc" id="L307">                return formatAsPercentage(number, config);</span>
            case CUSTOM:
<span class="fc" id="L309">                return formatAsCustom(number, config);</span>
            default:
<span class="nc" id="L311">                return number.toString();</span>
        }
    }

    /**
     * 格式化为普通格式
     */
    private String formatAsPlain(Number number, FieldConfig config) {
<span class="fc" id="L319">        int scale = getIntParam(config, &quot;scale&quot;, 2);</span>
        
<span class="fc bfc" id="L321" title="All 2 branches covered.">        if (number instanceof BigDecimal) {</span>
<span class="fc" id="L322">            return ((BigDecimal) number).toPlainString();</span>
        } else {
<span class="fc" id="L324">            DecimalFormat df = new DecimalFormat();</span>
<span class="fc" id="L325">            df.setMaximumFractionDigits(scale);</span>
<span class="fc" id="L326">            df.setMinimumFractionDigits(scale);</span>
<span class="fc" id="L327">            df.setGroupingUsed(false);</span>
<span class="fc" id="L328">            return df.format(number);</span>
        }
    }

    /**
     * 格式化为科学计数法
     */
    private String formatAsScientific(Number number) {
<span class="fc" id="L336">        return String.format(&quot;%.3E&quot;, number.doubleValue());</span>
    }

    /**
     * 格式化为货币格式
     */
    private String formatAsCurrency(Number number, FieldConfig config) {
<span class="fc" id="L343">        String localeStr = getStringParam(config, &quot;locale&quot;, &quot;en_US&quot;);</span>
<span class="fc" id="L344">        String currencyCode = getStringParam(config, &quot;currency_code&quot;, &quot;USD&quot;);</span>
        
<span class="fc" id="L346">        Locale locale = parseLocale(localeStr);</span>
<span class="fc" id="L347">        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(locale);</span>
        
        try {
<span class="fc" id="L350">            java.util.Currency currency = java.util.Currency.getInstance(currencyCode);</span>
<span class="fc" id="L351">            currencyFormat.setCurrency(currency);</span>
<span class="nc" id="L352">        } catch (Exception e) {</span>
<span class="nc" id="L353">            logger.warn(&quot;Invalid currency code: {}, using default&quot;, currencyCode);</span>
<span class="fc" id="L354">        }</span>
        
<span class="fc" id="L356">        return currencyFormat.format(number);</span>
    }

    /**
     * 格式化为百分比格式
     */
    private String formatAsPercentage(Number number, FieldConfig config) {
<span class="fc" id="L363">        String localeStr = getStringParam(config, &quot;locale&quot;, &quot;en_US&quot;);</span>
<span class="fc" id="L364">        Locale locale = parseLocale(localeStr);</span>
        
<span class="fc" id="L366">        NumberFormat percentFormat = NumberFormat.getPercentInstance(locale);</span>
<span class="fc" id="L367">        int scale = getIntParam(config, &quot;scale&quot;, 2);</span>
<span class="fc" id="L368">        percentFormat.setMaximumFractionDigits(scale);</span>
<span class="fc" id="L369">        percentFormat.setMinimumFractionDigits(scale);</span>
        
        // 将数值转换为百分比（除以100）
<span class="fc" id="L372">        double percentValue = number.doubleValue() / 100.0;</span>
<span class="fc" id="L373">        return percentFormat.format(percentValue);</span>
    }

    /**
     * 格式化为自定义格式
     */
    private String formatAsCustom(Number number, FieldConfig config) {
<span class="fc" id="L380">        String pattern = getStringParam(config, &quot;pattern&quot;, &quot;#,##0.00&quot;);</span>
        
        try {
<span class="fc" id="L383">            DecimalFormat customFormat = new DecimalFormat(pattern);</span>
<span class="fc" id="L384">            return customFormat.format(number);</span>
<span class="nc" id="L385">        } catch (Exception e) {</span>
<span class="nc" id="L386">            logger.warn(&quot;Invalid custom pattern: {}, using default format&quot;, pattern);</span>
<span class="nc" id="L387">            return number.toString();</span>
        }
    }

    /**
     * 解析本地化设置
     */
    private Locale parseLocale(String localeStr) {
        try {
<span class="pc bpc" id="L396" title="1 of 2 branches missed.">            if (localeStr.contains(&quot;_&quot;)) {</span>
<span class="fc" id="L397">                String[] parts = localeStr.split(&quot;_&quot;);</span>
<span class="pc bpc" id="L398" title="1 of 2 branches missed.">                if (parts.length == 2) {</span>
<span class="fc" id="L399">                    return new Locale(parts[0], parts[1]);</span>
<span class="nc bnc" id="L400" title="All 2 branches missed.">                } else if (parts.length == 3) {</span>
<span class="nc" id="L401">                    return new Locale(parts[0], parts[1], parts[2]);</span>
                }
            }
<span class="nc" id="L404">            return Locale.forLanguageTag(localeStr.replace(&quot;_&quot;, &quot;-&quot;));</span>
<span class="nc" id="L405">        } catch (Exception e) {</span>
<span class="nc" id="L406">            logger.warn(&quot;Invalid locale: {}, using English as default&quot;, localeStr);</span>
<span class="nc" id="L407">            return Locale.ENGLISH;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>