<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FaxGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">FaxGenerator.java</span></div><h1>FaxGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 传真号码生成器
 * 
 * 支持的参数：
 * - region: 地区 (CN|US|UK|JP|ANY)
 * - area_code: 区号 (指定区号或随机)
 * - format: 输出格式 (STANDARD|INTERNATIONAL|COMPACT)
 * - include_extension: 是否包含分机号 (true|false)
 * - extension_length: 分机号长度 (2-6)
 * - prefix: 自定义前缀
 * 
 * <AUTHOR>
 */
<span class="nc" id="L25">public class FaxGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(FaxGenerator.class);</span>
<span class="nc" id="L28">    private static final Random random = new Random();</span>

    // 输出格式枚举
<span class="nc" id="L31">    private enum FaxFormat {</span>
<span class="nc" id="L32">        STANDARD, // 标准格式：区号-号码</span>
<span class="nc" id="L33">        INTERNATIONAL, // 国际格式：+国家码-区号-号码</span>
<span class="nc" id="L34">        COMPACT // 紧凑格式：区号号码（无分隔符）</span>
    }

    // 国家代码和区号映射
<span class="nc" id="L38">    private static final Map&lt;String, CountryInfo&gt; COUNTRY_INFO = new HashMap&lt;&gt;();</span>

    // 中国常用区号
<span class="nc" id="L41">    private static final List&lt;String&gt; CHINA_AREA_CODES = Arrays.asList(</span>
            &quot;010&quot;, &quot;021&quot;, &quot;022&quot;, &quot;023&quot;, &quot;024&quot;, &quot;025&quot;, &quot;027&quot;, &quot;028&quot;, &quot;029&quot;,
            &quot;0311&quot;, &quot;0312&quot;, &quot;0313&quot;, &quot;0314&quot;, &quot;0315&quot;, &quot;0316&quot;, &quot;0317&quot;, &quot;0318&quot;, &quot;0319&quot;,
            &quot;0335&quot;, &quot;0349&quot;, &quot;0351&quot;, &quot;0352&quot;, &quot;0353&quot;, &quot;0354&quot;, &quot;0355&quot;, &quot;0356&quot;, &quot;0357&quot;, &quot;0358&quot;, &quot;0359&quot;,
            &quot;0371&quot;, &quot;0372&quot;, &quot;0373&quot;, &quot;0374&quot;, &quot;0375&quot;, &quot;0376&quot;, &quot;0377&quot;, &quot;0378&quot;, &quot;0379&quot;,
            &quot;0391&quot;, &quot;0392&quot;, &quot;0393&quot;, &quot;0394&quot;, &quot;0395&quot;, &quot;0396&quot;, &quot;0398&quot;,
            &quot;0411&quot;, &quot;0412&quot;, &quot;0413&quot;, &quot;0414&quot;, &quot;0415&quot;, &quot;0416&quot;, &quot;0417&quot;, &quot;0418&quot;, &quot;0419&quot;,
            &quot;0421&quot;, &quot;0427&quot;, &quot;0429&quot;,
            &quot;0431&quot;, &quot;0432&quot;, &quot;0433&quot;, &quot;0434&quot;, &quot;0435&quot;, &quot;0436&quot;, &quot;0437&quot;, &quot;0438&quot;, &quot;0439&quot;,
            &quot;0451&quot;, &quot;0452&quot;, &quot;0453&quot;, &quot;0454&quot;, &quot;0455&quot;, &quot;0456&quot;, &quot;0457&quot;, &quot;0458&quot;, &quot;0459&quot;,
            &quot;0464&quot;, &quot;0467&quot;, &quot;0468&quot;, &quot;0469&quot;,
            &quot;0471&quot;, &quot;0472&quot;, &quot;0473&quot;, &quot;0474&quot;, &quot;0475&quot;, &quot;0476&quot;, &quot;0477&quot;, &quot;0478&quot;, &quot;0479&quot;,
            &quot;0482&quot;, &quot;0483&quot;,
            &quot;0511&quot;, &quot;0512&quot;, &quot;0513&quot;, &quot;0514&quot;, &quot;0515&quot;, &quot;0516&quot;, &quot;0517&quot;, &quot;0518&quot;, &quot;0519&quot;,
            &quot;0523&quot;, &quot;0527&quot;, &quot;0528&quot;,
            &quot;0531&quot;, &quot;0532&quot;, &quot;0533&quot;, &quot;0534&quot;, &quot;0535&quot;, &quot;0536&quot;, &quot;0537&quot;, &quot;0538&quot;, &quot;0539&quot;,
            &quot;0543&quot;, &quot;0546&quot;, &quot;0547&quot;, &quot;0548&quot;, &quot;0549&quot;,
            &quot;0551&quot;, &quot;0552&quot;, &quot;0553&quot;, &quot;0554&quot;, &quot;0555&quot;, &quot;0556&quot;, &quot;0557&quot;, &quot;0558&quot;, &quot;0559&quot;,
            &quot;0561&quot;, &quot;0562&quot;, &quot;0563&quot;, &quot;0564&quot;, &quot;0565&quot;, &quot;0566&quot;);

    // 国家信息类
    private static class CountryInfo {
        final String countryCode;
        final List&lt;String&gt; areaCodes;
        final int numberLength;

<span class="nc" id="L67">        CountryInfo(String countryCode, List&lt;String&gt; areaCodes, int numberLength) {</span>
<span class="nc" id="L68">            this.countryCode = countryCode;</span>
<span class="nc" id="L69">            this.areaCodes = areaCodes;</span>
<span class="nc" id="L70">            this.numberLength = numberLength;</span>
<span class="nc" id="L71">        }</span>
    }

    static {
<span class="nc" id="L75">        initializeCountryInfo();</span>
<span class="nc" id="L76">    }</span>

    private static void initializeCountryInfo() {
        // 中国
<span class="nc" id="L80">        COUNTRY_INFO.put(&quot;CN&quot;, new CountryInfo(</span>
                &quot;+86&quot;,
                CHINA_AREA_CODES,
                8));

        // 美国
<span class="nc" id="L86">        COUNTRY_INFO.put(&quot;US&quot;, new CountryInfo(</span>
                &quot;+1&quot;,
<span class="nc" id="L88">                Arrays.asList(&quot;212&quot;, &quot;213&quot;, &quot;214&quot;, &quot;215&quot;, &quot;216&quot;, &quot;217&quot;, &quot;218&quot;, &quot;219&quot;, &quot;301&quot;, &quot;302&quot;, &quot;303&quot;, &quot;304&quot;, &quot;305&quot;,</span>
                        &quot;307&quot;, &quot;308&quot;, &quot;309&quot;, &quot;310&quot;, &quot;312&quot;, &quot;313&quot;, &quot;314&quot;, &quot;315&quot;, &quot;316&quot;, &quot;317&quot;, &quot;318&quot;, &quot;319&quot;, &quot;320&quot;,
                        &quot;321&quot;, &quot;323&quot;, &quot;330&quot;, &quot;334&quot;, &quot;336&quot;, &quot;337&quot;, &quot;339&quot;, &quot;347&quot;, &quot;351&quot;, &quot;352&quot;, &quot;360&quot;, &quot;361&quot;, &quot;386&quot;,
                        &quot;401&quot;, &quot;402&quot;, &quot;404&quot;, &quot;405&quot;, &quot;406&quot;, &quot;407&quot;, &quot;408&quot;, &quot;409&quot;, &quot;410&quot;, &quot;412&quot;, &quot;413&quot;, &quot;414&quot;, &quot;415&quot;,
                        &quot;417&quot;, &quot;419&quot;, &quot;423&quot;, &quot;424&quot;, &quot;425&quot;, &quot;430&quot;, &quot;432&quot;, &quot;434&quot;, &quot;435&quot;, &quot;440&quot;, &quot;443&quot;, &quot;469&quot;, &quot;470&quot;,
                        &quot;475&quot;, &quot;478&quot;, &quot;479&quot;, &quot;480&quot;, &quot;484&quot;, &quot;501&quot;, &quot;502&quot;, &quot;503&quot;, &quot;504&quot;, &quot;505&quot;, &quot;507&quot;, &quot;508&quot;, &quot;509&quot;,
                        &quot;510&quot;, &quot;512&quot;, &quot;513&quot;, &quot;515&quot;, &quot;516&quot;, &quot;517&quot;, &quot;518&quot;, &quot;520&quot;, &quot;530&quot;, &quot;540&quot;, &quot;541&quot;, &quot;551&quot;, &quot;559&quot;,
                        &quot;561&quot;, &quot;562&quot;, &quot;563&quot;, &quot;564&quot;, &quot;567&quot;, &quot;570&quot;, &quot;571&quot;, &quot;573&quot;, &quot;574&quot;, &quot;575&quot;, &quot;580&quot;, &quot;585&quot;, &quot;586&quot;,
                        &quot;601&quot;, &quot;602&quot;, &quot;603&quot;, &quot;605&quot;, &quot;606&quot;, &quot;607&quot;, &quot;608&quot;, &quot;609&quot;, &quot;610&quot;, &quot;612&quot;, &quot;614&quot;, &quot;615&quot;, &quot;616&quot;,
                        &quot;617&quot;, &quot;618&quot;, &quot;619&quot;, &quot;620&quot;, &quot;623&quot;, &quot;626&quot;, &quot;630&quot;, &quot;631&quot;, &quot;636&quot;, &quot;641&quot;, &quot;646&quot;, &quot;650&quot;, &quot;651&quot;,
                        &quot;660&quot;, &quot;661&quot;, &quot;662&quot;, &quot;667&quot;, &quot;678&quot;, &quot;682&quot;, &quot;701&quot;, &quot;702&quot;, &quot;703&quot;, &quot;704&quot;, &quot;706&quot;, &quot;707&quot;, &quot;708&quot;,
                        &quot;712&quot;, &quot;713&quot;, &quot;714&quot;, &quot;715&quot;, &quot;716&quot;, &quot;717&quot;, &quot;718&quot;, &quot;719&quot;, &quot;720&quot;, &quot;724&quot;, &quot;727&quot;, &quot;731&quot;, &quot;732&quot;,
                        &quot;734&quot;, &quot;737&quot;, &quot;740&quot;, &quot;754&quot;, &quot;757&quot;, &quot;760&quot;, &quot;763&quot;, &quot;765&quot;, &quot;770&quot;, &quot;772&quot;, &quot;773&quot;, &quot;774&quot;, &quot;775&quot;,
                        &quot;781&quot;, &quot;785&quot;, &quot;786&quot;, &quot;801&quot;, &quot;802&quot;, &quot;803&quot;, &quot;804&quot;, &quot;805&quot;, &quot;806&quot;, &quot;808&quot;, &quot;810&quot;, &quot;812&quot;, &quot;813&quot;,
                        &quot;814&quot;, &quot;815&quot;, &quot;816&quot;, &quot;817&quot;, &quot;818&quot;, &quot;828&quot;, &quot;830&quot;, &quot;831&quot;, &quot;832&quot;, &quot;843&quot;, &quot;845&quot;, &quot;847&quot;, &quot;848&quot;,
                        &quot;850&quot;, &quot;856&quot;, &quot;857&quot;, &quot;858&quot;, &quot;859&quot;, &quot;860&quot;, &quot;862&quot;, &quot;863&quot;, &quot;864&quot;, &quot;865&quot;, &quot;870&quot;, &quot;878&quot;, &quot;901&quot;,
                        &quot;903&quot;, &quot;904&quot;, &quot;906&quot;, &quot;907&quot;, &quot;908&quot;, &quot;909&quot;, &quot;910&quot;, &quot;912&quot;, &quot;913&quot;, &quot;914&quot;, &quot;915&quot;, &quot;916&quot;, &quot;917&quot;,
                        &quot;918&quot;, &quot;919&quot;, &quot;920&quot;, &quot;925&quot;, &quot;928&quot;, &quot;929&quot;, &quot;931&quot;, &quot;936&quot;, &quot;937&quot;, &quot;940&quot;, &quot;941&quot;, &quot;947&quot;, &quot;949&quot;,
                        &quot;951&quot;, &quot;952&quot;, &quot;954&quot;, &quot;956&quot;, &quot;970&quot;, &quot;971&quot;, &quot;972&quot;, &quot;973&quot;, &quot;978&quot;, &quot;979&quot;, &quot;980&quot;, &quot;985&quot;, &quot;989&quot;),
                7));

        // 英国
<span class="nc" id="L110">        COUNTRY_INFO.put(&quot;UK&quot;, new CountryInfo(</span>
                &quot;+44&quot;,
<span class="nc" id="L112">                Arrays.asList(&quot;20&quot;, &quot;121&quot;, &quot;131&quot;, &quot;141&quot;, &quot;151&quot;, &quot;161&quot;, &quot;113&quot;, &quot;114&quot;, &quot;115&quot;, &quot;116&quot;, &quot;117&quot;, &quot;118&quot;, &quot;1204&quot;,</span>
                        &quot;1223&quot;, &quot;1224&quot;, &quot;1225&quot;, &quot;1226&quot;, &quot;1227&quot;, &quot;1228&quot;, &quot;1229&quot;, &quot;1233&quot;, &quot;1234&quot;, &quot;1235&quot;, &quot;1236&quot;, &quot;1237&quot;,
                        &quot;1239&quot;, &quot;1241&quot;, &quot;1242&quot;, &quot;1243&quot;, &quot;1244&quot;, &quot;1245&quot;, &quot;1246&quot;, &quot;1248&quot;, &quot;1249&quot;, &quot;1250&quot;, &quot;1252&quot;, &quot;1253&quot;,
                        &quot;1254&quot;, &quot;1255&quot;, &quot;1256&quot;, &quot;1257&quot;, &quot;1258&quot;, &quot;1259&quot;, &quot;1260&quot;, &quot;1261&quot;, &quot;1262&quot;, &quot;1263&quot;, &quot;1264&quot;, &quot;1267&quot;,
                        &quot;1268&quot;, &quot;1269&quot;, &quot;1270&quot;, &quot;1271&quot;, &quot;1272&quot;, &quot;1273&quot;, &quot;1274&quot;, &quot;1275&quot;, &quot;1276&quot;, &quot;1277&quot;, &quot;1278&quot;, &quot;1279&quot;,
                        &quot;1280&quot;, &quot;1282&quot;, &quot;1283&quot;, &quot;1284&quot;, &quot;1285&quot;, &quot;1286&quot;, &quot;1287&quot;, &quot;1288&quot;, &quot;1289&quot;, &quot;1290&quot;, &quot;1291&quot;, &quot;1292&quot;,
                        &quot;1293&quot;, &quot;1294&quot;, &quot;1295&quot;, &quot;1296&quot;, &quot;1297&quot;, &quot;1298&quot;, &quot;1299&quot;),
                6));

        // 日本
<span class="nc" id="L122">        COUNTRY_INFO.put(&quot;JP&quot;, new CountryInfo(</span>
                &quot;+81&quot;,
<span class="nc" id="L124">                Arrays.asList(&quot;3&quot;, &quot;6&quot;, &quot;11&quot;, &quot;43&quot;, &quot;44&quot;, &quot;45&quot;, &quot;48&quot;, &quot;52&quot;, &quot;53&quot;, &quot;54&quot;, &quot;55&quot;, &quot;58&quot;, &quot;72&quot;, &quot;75&quot;, &quot;76&quot;,</span>
                        &quot;78&quot;, &quot;82&quot;, &quot;86&quot;, &quot;92&quot;, &quot;95&quot;, &quot;96&quot;, &quot;98&quot;, &quot;99&quot;, &quot;11&quot;, &quot;123&quot;, &quot;124&quot;, &quot;125&quot;, &quot;126&quot;, &quot;133&quot;, &quot;134&quot;,
                        &quot;135&quot;, &quot;136&quot;, &quot;137&quot;, &quot;138&quot;, &quot;139&quot;, &quot;142&quot;, &quot;143&quot;, &quot;144&quot;, &quot;145&quot;, &quot;146&quot;, &quot;152&quot;, &quot;153&quot;, &quot;154&quot;,
                        &quot;155&quot;, &quot;156&quot;, &quot;157&quot;, &quot;158&quot;, &quot;162&quot;, &quot;163&quot;, &quot;164&quot;, &quot;165&quot;, &quot;166&quot;, &quot;167&quot;, &quot;172&quot;, &quot;173&quot;, &quot;174&quot;,
                        &quot;175&quot;, &quot;176&quot;, &quot;178&quot;, &quot;179&quot;, &quot;182&quot;, &quot;183&quot;, &quot;184&quot;, &quot;185&quot;, &quot;186&quot;, &quot;187&quot;, &quot;191&quot;, &quot;192&quot;, &quot;193&quot;,
                        &quot;194&quot;, &quot;195&quot;, &quot;220&quot;, &quot;223&quot;, &quot;224&quot;, &quot;225&quot;, &quot;226&quot;, &quot;228&quot;, &quot;229&quot;, &quot;233&quot;, &quot;234&quot;, &quot;235&quot;, &quot;237&quot;,
                        &quot;238&quot;, &quot;240&quot;, &quot;241&quot;, &quot;242&quot;, &quot;243&quot;, &quot;244&quot;, &quot;246&quot;, &quot;247&quot;, &quot;248&quot;, &quot;250&quot;, &quot;254&quot;, &quot;255&quot;, &quot;256&quot;,
                        &quot;258&quot;, &quot;260&quot;, &quot;261&quot;, &quot;263&quot;, &quot;264&quot;, &quot;265&quot;, &quot;266&quot;, &quot;267&quot;, &quot;268&quot;, &quot;269&quot;, &quot;270&quot;, &quot;274&quot;, &quot;276&quot;,
                        &quot;277&quot;, &quot;278&quot;, &quot;279&quot;, &quot;280&quot;, &quot;282&quot;, &quot;283&quot;, &quot;284&quot;, &quot;285&quot;, &quot;287&quot;, &quot;288&quot;, &quot;289&quot;, &quot;291&quot;, &quot;293&quot;,
                        &quot;294&quot;, &quot;295&quot;, &quot;296&quot;, &quot;297&quot;, &quot;299&quot;),
                8));
<span class="nc" id="L135">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L139">        return &quot;fax&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L144">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L151">            String region = config.getParam(&quot;region&quot;, String.class, &quot;CN&quot;);</span>
<span class="nc" id="L152">            String areaCode = config.getParam(&quot;area_code&quot;, String.class, null);</span>
<span class="nc" id="L153">            String format = config.getParam(&quot;format&quot;, String.class, &quot;STANDARD&quot;);</span>
<span class="nc" id="L154">            boolean includeExtension = Boolean</span>
<span class="nc" id="L155">                    .parseBoolean(config.getParam(&quot;include_extension&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L156">            int extensionLength = Integer.parseInt(config.getParam(&quot;extension_length&quot;, String.class, &quot;3&quot;));</span>
<span class="nc" id="L157">            String prefix = config.getParam(&quot;prefix&quot;, String.class, null);</span>

            // 生成传真号码
<span class="nc" id="L160">            String faxNumber = generateFaxNumber(region, areaCode, format, includeExtension, extensionLength, prefix);</span>

            // 将传真号码信息存入上下文
<span class="nc" id="L163">            context.put(&quot;fax_number&quot;, faxNumber);</span>
<span class="nc" id="L164">            context.put(&quot;fax_region&quot;, region);</span>
<span class="nc" id="L165">            context.put(&quot;fax_area_code&quot;, extractAreaCode(faxNumber, region));</span>

<span class="nc" id="L167">            logger.debug(&quot;Generated fax number: {}&quot;, faxNumber);</span>
<span class="nc" id="L168">            return faxNumber;</span>

<span class="nc" id="L170">        } catch (Exception e) {</span>
<span class="nc" id="L171">            logger.error(&quot;Error generating fax number&quot;, e);</span>
<span class="nc" id="L172">            return &quot;010-12345678&quot;;</span>
        }
    }

    private String generateFaxNumber(String region, String areaCode, String format,
            boolean includeExtension, int extensionLength, String prefix) {

        // 获取国家信息
<span class="nc" id="L180">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L182">            countryInfo = COUNTRY_INFO.get(&quot;CN&quot;); // 默认使用中国</span>
        }

        // 确定区号
<span class="nc bnc" id="L186" title="All 2 branches missed.">        String finalAreaCode = areaCode != null ? areaCode : selectRandomAreaCode(countryInfo);</span>

        // 生成号码主体
<span class="nc" id="L189">        String mainNumber = generateMainNumber(countryInfo);</span>

        // 生成分机号
<span class="nc bnc" id="L192" title="All 2 branches missed.">        String extension = includeExtension ? generateExtension(extensionLength) : null;</span>

        // 应用格式
<span class="nc" id="L195">        return formatFaxNumber(countryInfo, finalAreaCode, mainNumber, extension, format, prefix);</span>
    }

    private String selectRandomAreaCode(CountryInfo countryInfo) {
<span class="nc" id="L199">        List&lt;String&gt; areaCodes = countryInfo.areaCodes;</span>
<span class="nc" id="L200">        return areaCodes.get(random.nextInt(areaCodes.size()));</span>
    }

    private String generateMainNumber(CountryInfo countryInfo) {
<span class="nc" id="L204">        StringBuilder number = new StringBuilder();</span>

<span class="nc bnc" id="L206" title="All 2 branches missed.">        for (int i = 0; i &lt; countryInfo.numberLength; i++) {</span>
<span class="nc" id="L207">            number.append(random.nextInt(10));</span>
        }

<span class="nc" id="L210">        return number.toString();</span>
    }

    private String generateExtension(int length) {
<span class="nc" id="L214">        StringBuilder extension = new StringBuilder();</span>

<span class="nc bnc" id="L216" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L217">            extension.append(random.nextInt(10));</span>
        }

<span class="nc" id="L220">        return extension.toString();</span>
    }

    private String formatFaxNumber(CountryInfo countryInfo, String areaCode, String mainNumber,
            String extension, String format, String prefix) {

<span class="nc" id="L226">        StringBuilder faxNumber = new StringBuilder();</span>

        // 添加自定义前缀
<span class="nc bnc" id="L229" title="All 4 branches missed.">        if (prefix != null &amp;&amp; !prefix.isEmpty()) {</span>
<span class="nc" id="L230">            faxNumber.append(prefix);</span>
        }

<span class="nc" id="L233">        FaxFormat faxFormat = parseFaxFormat(format);</span>

<span class="nc bnc" id="L235" title="All 3 branches missed.">        switch (faxFormat) {</span>
            case INTERNATIONAL:
<span class="nc" id="L237">                faxNumber.append(countryInfo.countryCode).append(&quot;-&quot;);</span>
<span class="nc" id="L238">                faxNumber.append(areaCode).append(&quot;-&quot;);</span>
<span class="nc" id="L239">                faxNumber.append(mainNumber);</span>
<span class="nc" id="L240">                break;</span>

            case COMPACT:
<span class="nc" id="L243">                faxNumber.append(areaCode).append(mainNumber);</span>
<span class="nc" id="L244">                break;</span>

            case STANDARD:
            default:
<span class="nc" id="L248">                faxNumber.append(areaCode).append(&quot;-&quot;).append(mainNumber);</span>
                break;
        }

        // 添加分机号
<span class="nc bnc" id="L253" title="All 2 branches missed.">        if (extension != null) {</span>
<span class="nc" id="L254">            faxNumber.append(&quot; ext.&quot;).append(extension);</span>
        }

<span class="nc" id="L257">        return faxNumber.toString();</span>
    }

    private FaxFormat parseFaxFormat(String format) {
        try {
<span class="nc" id="L262">            return FaxFormat.valueOf(format.toUpperCase());</span>
<span class="nc" id="L263">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L264">            logger.warn(&quot;Unknown fax format: {}. Using STANDARD.&quot;, format);</span>
<span class="nc" id="L265">            return FaxFormat.STANDARD;</span>
        }
    }

    private String extractAreaCode(String faxNumber, String region) {
        try {
<span class="nc" id="L271">            CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">            if (countryInfo == null) {</span>
<span class="nc" id="L273">                return &quot;unknown&quot;;</span>
            }

            // 移除国家代码和前缀
<span class="nc" id="L277">            String cleanNumber = faxNumber;</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">            if (cleanNumber.startsWith(countryInfo.countryCode)) {</span>
<span class="nc" id="L279">                cleanNumber = cleanNumber.substring(countryInfo.countryCode.length());</span>
            }

            // 移除分隔符
<span class="nc" id="L283">            cleanNumber = cleanNumber.replaceAll(&quot;[^0-9]&quot;, &quot;&quot;);</span>

            // 根据区号长度提取
<span class="nc bnc" id="L286" title="All 2 branches missed.">            for (String code : countryInfo.areaCodes) {</span>
<span class="nc bnc" id="L287" title="All 2 branches missed.">                if (cleanNumber.startsWith(code)) {</span>
<span class="nc" id="L288">                    return code;</span>
                }
<span class="nc" id="L290">            }</span>

<span class="nc" id="L292">            return &quot;unknown&quot;;</span>
<span class="nc" id="L293">        } catch (Exception e) {</span>
<span class="nc" id="L294">            return &quot;unknown&quot;;</span>
        }
    }

    /**
     * 验证传真号码格式
     */
    public boolean validateFaxNumber(String faxNumber, String region) {
<span class="nc bnc" id="L302" title="All 4 branches missed.">        if (faxNumber == null || faxNumber.isEmpty()) {</span>
<span class="nc" id="L303">            return false;</span>
        }

<span class="nc" id="L306">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L308">            return false;</span>
        }

        // 移除所有非数字字符（除了+号）
<span class="nc" id="L312">        String cleanNumber = faxNumber.replaceAll(&quot;[^0-9+]&quot;, &quot;&quot;);</span>

        // 检查是否包含有效的区号
<span class="nc bnc" id="L315" title="All 2 branches missed.">        for (String areaCode : countryInfo.areaCodes) {</span>
<span class="nc bnc" id="L316" title="All 2 branches missed.">            if (cleanNumber.contains(areaCode)) {</span>
<span class="nc" id="L317">                return true;</span>
            }
<span class="nc" id="L319">        }</span>

<span class="nc" id="L321">        return false;</span>
    }

    /**
     * 生成企业传真号码（通常比个人传真号码更正式）
     */
    public String generateCorporateFax(String region) {
<span class="nc" id="L328">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L329" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L330">            countryInfo = COUNTRY_INFO.get(&quot;CN&quot;);</span>
        }

        // 企业传真通常使用主要城市的区号
        String areaCode;
<span class="nc bnc" id="L335" title="All 2 branches missed.">        if (&quot;CN&quot;.equals(region.toUpperCase())) {</span>
<span class="nc" id="L336">            String[] majorCities = { &quot;010&quot;, &quot;021&quot;, &quot;022&quot;, &quot;020&quot;, &quot;0755&quot;, &quot;0571&quot;, &quot;025&quot;, &quot;027&quot;, &quot;028&quot; };</span>
<span class="nc" id="L337">            areaCode = majorCities[random.nextInt(majorCities.length)];</span>
<span class="nc" id="L338">        } else {</span>
<span class="nc" id="L339">            areaCode = selectRandomAreaCode(countryInfo);</span>
        }

<span class="nc" id="L342">        String mainNumber = generateMainNumber(countryInfo);</span>

<span class="nc" id="L344">        return formatFaxNumber(countryInfo, areaCode, mainNumber, null, &quot;INTERNATIONAL&quot;, &quot;Fax: &quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>