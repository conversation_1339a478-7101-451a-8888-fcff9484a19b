<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UsernameGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">UsernameGenerator</span></div><h1>UsernameGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,206 of 1,206</td><td class="ctr2">0%</td><td class="bar">124 of 124</td><td class="ctr2">0%</td><td class="ctr1">82</td><td class="ctr2">82</td><td class="ctr1">215</td><td class="ctr2">215</td><td class="ctr1">17</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a15"><a href="UsernameGenerator.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="330" alt="330"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h0">46</td><td class="ctr2" id="i0">46</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a12"><a href="UsernameGenerator.java.html#L381" class="el_method">processChineseName(String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="208" alt="208"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="31" alt="31"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h1">36</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="UsernameGenerator.java.html#L271" class="el_method">generateNameBasedUsername(int, int, String, String, String, String, DataForgeContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="116" alt="116"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="22" alt="22"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h2">25</td><td class="ctr2" id="i2">25</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a13"><a href="UsernameGenerator.java.html#L345" class="el_method">processEnglishName(String, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="96" alt="96"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h5">14</td><td class="ctr2" id="i5">14</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="UsernameGenerator.java.html#L131" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="93" alt="93"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="UsernameGenerator.java.html#L229" class="el_method">generateUsername(int, int, String, String, String, boolean, boolean, String, Set, DataForgeContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="71" alt="71"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="92" height="10" title="24" alt="24"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">13</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h4">16</td><td class="ctr2" id="i4">16</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a11"><a href="UsernameGenerator.java.html#L167" class="el_method">parseLengthRange(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="70" alt="70"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="UsernameGenerator.java.html#L450" class="el_method">generateRandomUsername(int, int, String, String, String, ThreadLocalRandom)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="66" alt="66"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="UsernameGenerator.java.html#L207" class="el_method">buildBlacklist(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="41" alt="41"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="UsernameGenerator.java.html#L186" class="el_method">buildCharSet(String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="30" alt="30"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="UsernameGenerator.java.html#L326" class="el_method">processName(String, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="25" alt="25"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a10"><a href="UsernameGenerator.java.html#L477" class="el_method">isBlacklisted(String, Set)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="22" alt="22"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h11">6</td><td class="ctr2" id="i11">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a6"><a href="UsernameGenerator.java.html#L495" class="el_method">getBooleanParam(Map, String, boolean)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="20" alt="20"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h12">6</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="UsernameGenerator.java.html#L490" class="el_method">getStringParam(Map, String, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="UsernameGenerator.java.html#L40" class="el_method">UsernameGenerator()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a9"><a href="UsernameGenerator.java.html#L121" class="el_method">getType()</a></td><td class="bar" id="b15"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a7"><a href="UsernameGenerator.java.html#L126" class="el_method">getConfigClass()</a></td><td class="bar" id="b16"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>