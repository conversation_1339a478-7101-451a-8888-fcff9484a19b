package com.dataforge.cli.commands;

import com.dataforge.config.FieldConfigWrapper;
import com.dataforge.config.ForgeConfig;
import com.dataforge.config.OutputConfig;
import com.dataforge.service.DataForgeException;
import com.dataforge.service.DataForgeService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;

/**
 * 数据生成命令。
 * 
 * <p>
 * 使用Picocli框架实现的CLI命令，负责解析命令行参数并调用核心服务生成数据。
 * 该命令支持通过配置文件或命令行参数指定生成规则。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@Command(name = "generate", mixinStandardHelpOptions = true, version = "DataForge 1.0.0", description = "Generates test data based on configuration.", headerHeading = "%n@|bold,underline DataForge Test Data Generator|@%n%n", descriptionHeading = "%n@|bold,underline Description:|@%n%n", parameterListHeading = "%n@|bold,underline Parameters:|@%n", optionListHeading = "%n@|bold,underline Options:|@%n", commandListHeading = "%n@|bold,underline Commands:|@%n")
public class GenerateCommand implements CommandLineRunner, Callable<Integer> {

    private static final Logger logger = LoggerFactory.getLogger(GenerateCommand.class);

    @Autowired
    private DataForgeService dataForgeService;

    // === 核心配置选项 ===

    @Option(names = { "-c", "--config" }, description = "Path to the configuration file (YAML/JSON).")
    private File configFile;

    @Option(names = { "-n", "--count" }, description = "Number of records to generate (default: ${DEFAULT-VALUE}).")
    private int count = 10;

    @Option(names = { "-t",
            "--threads" }, description = "Number of threads for concurrent generation (default: ${DEFAULT-VALUE}).")
    private int threads = 1;

    @Option(names = { "--seed" }, description = "Random seed for reproducible generation.")
    private Long seed;

    // === 输出配置选项 ===

    @Option(names = { "-f",
            "--format" }, description = "Output format: ${COMPLETION-CANDIDATES} (default: ${DEFAULT-VALUE}).")
    private OutputConfig.Format outputFormat = OutputConfig.Format.CONSOLE;

    @Option(names = { "-o", "--output" }, description = "Output file path (required for non-console formats).")
    private String outputFile;

    @Option(names = {
            "--append" }, description = "Append to output file instead of overwriting (default: ${DEFAULT-VALUE}).")
    private boolean append = false;

    @Option(names = { "--encoding" }, description = "Output file encoding (default: ${DEFAULT-VALUE}).")
    private String encoding = "UTF-8";

    // === CSV特定选项 ===

    @Option(names = { "--csv-delimiter" }, description = "CSV field delimiter (default: ${DEFAULT-VALUE}).")
    private String csvDelimiter = ",";

    @Option(names = { "--csv-no-header" }, description = "Do not include header row in CSV output.")
    private boolean csvNoHeader = false;

    // === JSON特定选项 ===

    @Option(names = { "--json-compact" }, description = "Output compact JSON without pretty printing.")
    private boolean jsonCompact = false;

    // === SQL特定选项 ===

    @Option(names = {
            "--sql-table" }, description = "SQL table name for INSERT statements (default: ${DEFAULT-VALUE}).")
    private String sqlTableName = "test_data";

    // === 快速字段定义选项 ===

    @Option(names = { "--fields" }, description = "Quick field definition: field1:type1,field2:type2,... " +
            "Example: --fields name:name,phone:phone,email:email")
    private String quickFields;

    // === 调试和帮助选项 ===

    @Option(names = { "--list-generators" }, description = "List all available data generators and exit.")
    private boolean listGenerators = false;

    @Option(names = { "--list-formats" }, description = "List all available output formats and exit.")
    private boolean listFormats = false;

    @Option(names = { "--validate" }, description = "Enable data validation (default: ${DEFAULT-VALUE}).")
    private boolean validate = true;

    @Option(names = { "-v", "--verbose" }, description = "Enable verbose output.")
    private boolean verbose = false;

    @Override
    public void run(String... args) {
        // Spring Boot启动时调用，使用Picocli解析参数并执行命令
        int exitCode = new CommandLine(this).execute(args);
        if (exitCode != 0) {
            System.exit(exitCode);
        }
    }

    @Override
    public Integer call() throws Exception {
        try {
            // 设置日志级别
            if (verbose) {
                System.setProperty("logging.level.com.dataforge", "DEBUG");
            }

            logger.info("DataForge CLI starting...");

            // 处理信息查询命令
            if (listGenerators) {
                printAvailableGenerators();
                return 0;
            }

            if (listFormats) {
                printAvailableFormats();
                return 0;
            }

            // 构建配置
            ForgeConfig config = buildConfig();

            // 验证配置
            if (!config.isValid()) {
                System.err.println("Error: Invalid configuration. Please check your parameters.");
                return 1;
            }

            // 执行数据生成
            dataForgeService.generateData(config);

            logger.info("DataForge CLI completed successfully.");
            return 0;

        } catch (DataForgeException e) {
            System.err.println("Error: " + e.getMessage());
            logger.error("DataForge execution failed", e);
            return 1;
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            logger.error("Unexpected error during execution", e);
            return 2;
        }
    }

    /**
     * 构建配置对象。
     * 
     * <p>
     * 优先使用CLI参数，其次使用配置文件。
     * CLI参数会覆盖配置文件中的相应设置。
     * 
     * @return 构建的配置对象
     * @throws Exception 当配置构建失败时
     */
    private ForgeConfig buildConfig() throws Exception {
        ForgeConfig config = new ForgeConfig();

        // 从配置文件加载配置
        if (configFile != null) {
            logger.info("Loading configuration from file: {}", configFile.getAbsolutePath());
            config = loadConfigFromFile(configFile);
        }

        // 应用CLI参数覆盖（只有当CLI参数被明确指定时）
        // 注意：Picocli会为未指定的参数设置默认值，所以我们需要检查是否真的指定了参数

        // 对于数值参数，如果等于默认值且没有配置文件，则使用默认值
        // 如果有配置文件，则只有明确指定时才覆盖
        if (configFile == null || count != 10) { // 10是默认值
            config.setCount(count);
        }

        if (configFile == null || threads != 1) { // 1是默认值
            config.setThreads(threads);
        }

        // validate默认为true，总是设置
        config.setValidate(validate);

        if (seed != null) {
            config.setSeed(seed);
        }

        // 构建输出配置
        OutputConfig outputConfig = config.getOutput();
        if (outputConfig == null) {
            outputConfig = new OutputConfig();
        }

        // 只有当CLI参数被明确指定时才覆盖
        if (configFile == null || outputFormat != OutputConfig.Format.CONSOLE) { // CONSOLE是默认值
            outputConfig.setFormat(outputFormat);
        }

        if (outputFile != null) {
            outputConfig.setFile(outputFile);
        }

        if (configFile == null || append) { // append默认为false
            outputConfig.setAppend(append);
        }

        if (configFile == null || !encoding.equals("UTF-8")) { // UTF-8是默认值
            outputConfig.setEncoding(encoding);
        }

        if (configFile == null || !csvDelimiter.equals(",")) { // ,是默认值
            outputConfig.setCsvDelimiter(csvDelimiter);
        }

        if (configFile == null || csvNoHeader) { // csvNoHeader默认为false
            outputConfig.setCsvIncludeHeader(!csvNoHeader);
        }

        if (configFile == null || jsonCompact) { // jsonCompact默认为false
            outputConfig.setJsonPrettyPrint(!jsonCompact);
        }

        if (configFile == null || !sqlTableName.equals("test_data")) { // test_data是默认值
            outputConfig.setSqlTableName(sqlTableName);
        }

        config.setOutput(outputConfig);

        // 构建字段配置（只有当没有配置文件或CLI明确指定了字段时）
        if (configFile == null || (quickFields != null && !quickFields.trim().isEmpty())) {
            List<FieldConfigWrapper> fields = buildFieldConfigs();
            config.setFields(fields);
        }

        logger.debug("Built configuration: {}", config);
        return config;
    }

    /**
     * 构建字段配置列表。
     * 
     * @return 字段配置列表
     */
    private List<FieldConfigWrapper> buildFieldConfigs() {
        List<FieldConfigWrapper> fields = new ArrayList<>();

        // 处理快速字段定义
        if (quickFields != null && !quickFields.trim().isEmpty()) {
            String[] fieldDefs = quickFields.split(",");
            for (String fieldDef : fieldDefs) {
                String[] parts = fieldDef.trim().split(":");
                if (parts.length >= 2) {
                    String fieldName = parts[0].trim();
                    String fieldType = parts[1].trim();
                    fields.add(FieldConfigWrapper.of(fieldName, fieldType));
                }
            }
        }

        // 如果没有定义字段，使用默认字段
        if (fields.isEmpty()) {
            fields.add(FieldConfigWrapper.of("id", "uuid"));
            fields.add(FieldConfigWrapper.of("name", "name"));
            fields.add(FieldConfigWrapper.of("phone", "phone"));
            fields.add(FieldConfigWrapper.of("email", "email"));
        }

        return fields;
    }

    /**
     * 打印可用的数据生成器。
     */
    private void printAvailableGenerators() {
        System.out.println("Available Data Generators:");
        System.out.println("==========================");

        Map<String, String> generatorInfo = dataForgeService.getGeneratorInfo();
        if (generatorInfo.isEmpty()) {
            System.out.println("No generators available.");
            return;
        }

        for (Map.Entry<String, String> entry : generatorInfo.entrySet()) {
            System.out.printf("  %-15s : %s%n", entry.getKey(), entry.getValue());
        }

        System.out.println();
        System.out.println("Usage example:");
        System.out.println("  --fields name:name,phone:phone,email:email");
    }

    /**
     * 打印可用的输出格式。
     */
    private void printAvailableFormats() {
        System.out.println("Available Output Formats:");
        System.out.println("=========================");

        List<OutputConfig.Format> formats = dataForgeService.getAvailableOutputFormats();
        if (formats.isEmpty()) {
            System.out.println("No output formats available.");
            return;
        }

        for (OutputConfig.Format format : formats) {
            System.out.printf("  %-10s : %s%n", format.name().toLowerCase(), getFormatDescription(format));
        }

        System.out.println();
        System.out.println("Usage example:");
        System.out.println("  --format csv --output data.csv");
    }

    /**
     * 获取输出格式的描述。
     * 
     * @param format 输出格式
     * @return 格式描述
     */
    private String getFormatDescription(OutputConfig.Format format) {
        return switch (format) {
            case CONSOLE -> "Display data in tabular format to console";
            case CSV -> "Export data as Comma Separated Values file";
            case JSON -> "Export data as JSON format";
            case XML -> "Export data as XML format";
            case SQL -> "Generate SQL INSERT statements";
        };
    }

    /**
     * 从配置文件加载配置
     */
    private ForgeConfig loadConfigFromFile(File configFile) throws Exception {
        if (!configFile.exists()) {
            throw new IllegalArgumentException("Configuration file not found: " + configFile.getAbsolutePath());
        }

        try {
            ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            mapper.configure(DeserializationFeature.READ_ENUMS_USING_TO_STRING, true);
            mapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
            // 配置枚举大小写不敏感
            mapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, false);

            // 先读取为通用对象，查看原始内容
            Object rawConfig = mapper.readValue(configFile, Object.class);
            logger.info("Raw config content: {}", rawConfig);

            // 读取配置文件，需要从dataforge节点开始
            @SuppressWarnings("unchecked")
            Map<String, Object> rootMap = mapper.readValue(configFile, Map.class);
            Object dataforgeNode = rootMap.get("dataforge");

            ForgeConfig config;
            if (dataforgeNode != null) {
                // 将dataforge节点转换为ForgeConfig对象
                config = mapper.convertValue(dataforgeNode, ForgeConfig.class);
            } else {
                // 如果没有dataforge节点，直接解析整个文件
                config = mapper.readValue(configFile, ForgeConfig.class);
            }

            logger.info("Configuration loaded from file: {} - count: {}, fields: {}, output: {}",
                    configFile.getAbsolutePath(), config.getCount(),
                    config.getFields().size(), config.getOutput().getFormat());
            return config;

        } catch (Exception e) {
            throw new Exception("Failed to load configuration from file: " + configFile.getAbsolutePath(), e);
        }
    }
}