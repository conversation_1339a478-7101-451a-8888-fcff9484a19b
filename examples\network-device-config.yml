dataforge:
  count: 10
  output:
    format: csv
    file: "output/network-device-test.csv"
  fields:
    # 现有网络设备类生成器测试
    - name: "ip_address"
      type: "ip"
      params:
        version: "ipv4"
        type: "public"
    
    - name: "mac_address"
      type: "mac"
      params:
        format: "colon"
        oui: "random"
    
    - name: "url"
      type: "url"
      params:
        protocol: "https"
        include_query: "true"
    
    - name: "domain"
      type: "domain"
      params:
        type: "common"
        tld: "com"
    
    - name: "api_key"
      type: "apikey"
      params:
        type: "bearer"
        length: "32"
    
    - name: "port"
      type: "port"
      params:
        type: "registered"
        min: "1024"
        max: "49151"
    
    # 新增HTTP头生成器测试
    - name: "http_header"
      type: "http_header"
      params:
        type: "common"
        format: "header"
    
    - name: "user_agent_header"
      type: "http_header"
      params:
        name: "User-Agent"
        format: "header"
    
    - name: "auth_header"
      type: "http_header"
      params:
        name: "Authorization"
        format: "header"
    
    - name: "content_type_header"
      type: "http_header"
      params:
        name: "Content-Type"
        format: "json"
    
    # 新增网络设备类生成器测试
    - name: "session_token"
      type: "session_token"
      params:
        type: "JWT"
        length: "32"
        encoding: "BASE64_URL_SAFE"