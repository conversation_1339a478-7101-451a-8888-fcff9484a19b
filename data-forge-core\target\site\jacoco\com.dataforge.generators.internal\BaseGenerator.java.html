<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BaseGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">BaseGenerator.java</span></div><h1>BaseGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.model.FieldConfig;

/**
 * 生成器基类，提供通用的参数获取方法
 * 
 * <AUTHOR>
 */
<span class="fc" id="L10">public abstract class BaseGenerator {</span>

    /**
     * 从配置中获取字符串参数。
     */
    protected String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="pc bpc" id="L16" title="2 of 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L17">            return defaultValue;</span>
        }
<span class="fc" id="L19">        Object value = config.getParams().get(key);</span>
<span class="fc bfc" id="L20" title="All 2 branches covered.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    protected boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="pc bpc" id="L27" title="2 of 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L28">            return defaultValue;</span>
        }
<span class="fc" id="L30">        Object value = config.getParams().get(key);</span>
<span class="fc bfc" id="L31" title="All 2 branches covered.">        if (value == null) {</span>
<span class="fc" id="L32">            return defaultValue;</span>
        }
<span class="pc bpc" id="L34" title="1 of 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L35">            return (Boolean) value;</span>
        }
<span class="fc" id="L37">        return Boolean.parseBoolean(value.toString());</span>
    }

    /**
     * 从配置中获取整数参数。
     */
    protected int getIntParam(FieldConfig config, String key, int defaultValue) {
<span class="pc bpc" id="L44" title="2 of 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L45">            return defaultValue;</span>
        }
<span class="fc" id="L47">        Object value = config.getParams().get(key);</span>
<span class="fc bfc" id="L48" title="All 2 branches covered.">        if (value == null) {</span>
<span class="fc" id="L49">            return defaultValue;</span>
        }
<span class="pc bpc" id="L51" title="1 of 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L52">            return ((Number) value).intValue();</span>
        }
        try {
<span class="fc" id="L55">            return Integer.parseInt(value.toString());</span>
<span class="nc" id="L56">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L57">            return defaultValue;</span>
        }
    }

    /**
     * 从配置中获取双精度浮点数参数。
     */
    protected double getDoubleParam(FieldConfig config, String key, double defaultValue) {
<span class="pc bpc" id="L65" title="2 of 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L66">            return defaultValue;</span>
        }
<span class="fc" id="L68">        Object value = config.getParams().get(key);</span>
<span class="fc bfc" id="L69" title="All 2 branches covered.">        if (value == null) {</span>
<span class="fc" id="L70">            return defaultValue;</span>
        }
<span class="pc bpc" id="L72" title="1 of 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L73">            return ((Number) value).doubleValue();</span>
        }
        try {
<span class="fc" id="L76">            return Double.parseDouble(value.toString());</span>
<span class="nc" id="L77">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L78">            return defaultValue;</span>
        }
    }

    /**
     * 从配置中获取长整数参数。
     */
    protected long getLongParam(FieldConfig config, String key, long defaultValue) {
<span class="pc bpc" id="L86" title="2 of 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L87">            return defaultValue;</span>
        }
<span class="fc" id="L89">        Object value = config.getParams().get(key);</span>
<span class="fc bfc" id="L90" title="All 2 branches covered.">        if (value == null) {</span>
<span class="fc" id="L91">            return defaultValue;</span>
        }
<span class="pc bpc" id="L93" title="1 of 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L94">            return ((Number) value).longValue();</span>
        }
        try {
<span class="fc" id="L97">            return Long.parseLong(value.toString());</span>
<span class="nc" id="L98">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L99">            return defaultValue;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>