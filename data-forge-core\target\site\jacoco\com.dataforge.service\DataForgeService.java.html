<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataForgeService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.service</a> &gt; <span class="el_source">DataForgeService.java</span></div><h1>DataForgeService.java</h1><pre class="source lang-java linenums">package com.dataforge.service;

import com.dataforge.config.FieldConfigWrapper;
import com.dataforge.config.ForgeConfig;
import com.dataforge.config.OutputConfig;
import com.dataforge.core.DataForgeContext;
import com.dataforge.core.GeneratorFactory;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.io.OutputStrategy;
import com.dataforge.model.FieldConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DataForge核心服务类。
 * 
 * &lt;p&gt;
 * 作为编排核心逻辑的主服务，负责协调数据生成器、输出策略和配置管理。
 * 该服务是整个数据生成流程的控制中心。
 * 
 * &lt;p&gt;
 * 主要职责：
 * &lt;ul&gt;
 * &lt;li&gt;解析和验证配置&lt;/li&gt;
 * &lt;li&gt;协调数据生成器执行数据生成&lt;/li&gt;
 * &lt;li&gt;管理数据生成上下文和字段关联&lt;/li&gt;
 * &lt;li&gt;控制数据输出流程&lt;/li&gt;
 * &lt;li&gt;支持并发数据生成&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
public class DataForgeService {

<span class="nc" id="L48">    private static final Logger logger = LoggerFactory.getLogger(DataForgeService.class);</span>

    private final GeneratorFactory generatorFactory;
    private final List&lt;OutputStrategy&gt; outputStrategies;

    /**
     * 构造函数，注入依赖。
     * 
     * @param generatorFactory 生成器工厂
     * @param outputStrategies 输出策略列表
     */
    @Autowired
<span class="nc" id="L60">    public DataForgeService(GeneratorFactory generatorFactory, List&lt;OutputStrategy&gt; outputStrategies) {</span>
<span class="nc" id="L61">        this.generatorFactory = generatorFactory;</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">        this.outputStrategies = outputStrategies != null ? outputStrategies : new ArrayList&lt;&gt;();</span>
<span class="nc" id="L63">        logger.info(&quot;DataForgeService initialized with {} generators and {} output strategies&quot;,</span>
<span class="nc" id="L64">                generatorFactory.getGeneratorCount(), this.outputStrategies.size());</span>
<span class="nc" id="L65">    }</span>

    /**
     * 根据配置生成数据。
     * 
     * &lt;p&gt;
     * 这是数据生成的主入口方法，负责整个生成流程的协调和控制。
     * 
     * @param config 生成配置
     * @throws DataForgeException 当生成过程中发生错误时
     */
    public void generateData(ForgeConfig config) throws DataForgeException {
<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (config == null) {</span>
<span class="nc" id="L78">            throw new DataForgeException(&quot;Configuration cannot be null&quot;);</span>
        }

<span class="nc" id="L81">        logger.info(&quot;Starting data generation with config: {}&quot;, config);</span>

        try {
            // 1. 验证配置
<span class="nc" id="L85">            validateConfig(config);</span>

            // 2. 准备输出策略
<span class="nc" id="L88">            OutputStrategy outputStrategy = prepareOutputStrategy(config.getOutput());</span>

            // 3. 提取字段名称
<span class="nc" id="L91">            List&lt;String&gt; fieldNames = extractFieldNames(config.getFields());</span>

            // 4. 初始化输出策略
<span class="nc" id="L94">            outputStrategy.initialize(config.getOutput(), fieldNames);</span>

            // 5. 执行数据生成
<span class="nc bnc" id="L97" title="All 2 branches missed.">            if (config.getThreads() &gt; 1) {</span>
<span class="nc" id="L98">                generateDataConcurrently(config, outputStrategy, fieldNames);</span>
            } else {
<span class="nc" id="L100">                generateDataSequentially(config, outputStrategy, fieldNames);</span>
            }

            // 6. 完成输出
<span class="nc" id="L104">            outputStrategy.finish();</span>

<span class="nc" id="L106">            logger.info(&quot;Data generation completed successfully. Generated {} records.&quot;, config.getCount());</span>

<span class="nc" id="L108">        } catch (Exception e) {</span>
<span class="nc" id="L109">            logger.error(&quot;Data generation failed&quot;, e);</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">            if (e instanceof DataForgeException) {</span>
<span class="nc" id="L111">                throw e;</span>
            }
<span class="nc" id="L113">            throw new DataForgeException(&quot;Data generation failed: &quot; + e.getMessage(), e);</span>
<span class="nc" id="L114">        }</span>
<span class="nc" id="L115">    }</span>

    /**
     * 验证配置的有效性。
     * 
     * @param config 配置对象
     * @throws DataForgeException 当配置无效时
     */
    private void validateConfig(ForgeConfig config) throws DataForgeException {
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (!config.isValid()) {</span>
<span class="nc" id="L125">            throw new DataForgeException(&quot;Invalid configuration: &quot; + config);</span>
        }

<span class="nc bnc" id="L128" title="All 2 branches missed.">        if (config.getFields().isEmpty()) {</span>
<span class="nc" id="L129">            throw new DataForgeException(&quot;No fields configured for data generation&quot;);</span>
        }

        // 验证所有字段都有对应的生成器
<span class="nc bnc" id="L133" title="All 2 branches missed.">        for (FieldConfigWrapper field : config.getFields()) {</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">            if (!generatorFactory.hasGenerator(field.getType())) {</span>
<span class="nc" id="L135">                throw new DataForgeException(&quot;No generator found for field type: &quot; + field.getType());</span>
            }
<span class="nc" id="L137">        }</span>

<span class="nc" id="L139">        logger.debug(&quot;Configuration validation passed&quot;);</span>
<span class="nc" id="L140">    }</span>

    /**
     * 准备输出策略。
     * 
     * @param outputConfig 输出配置
     * @return 匹配的输出策略
     * @throws DataForgeException 当找不到匹配的输出策略时
     */
    private OutputStrategy prepareOutputStrategy(OutputConfig outputConfig) throws DataForgeException {
<span class="nc bnc" id="L150" title="All 2 branches missed.">        for (OutputStrategy strategy : outputStrategies) {</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">            if (strategy.supports(outputConfig)) {</span>
<span class="nc" id="L152">                logger.debug(&quot;Selected output strategy: {} for format: {}&quot;,</span>
<span class="nc" id="L153">                        strategy.getClass().getSimpleName(), outputConfig.getFormat());</span>
<span class="nc" id="L154">                return strategy;</span>
            }
<span class="nc" id="L156">        }</span>

<span class="nc" id="L158">        throw new DataForgeException(&quot;No output strategy found for format: &quot; + outputConfig.getFormat());</span>
    }

    /**
     * 提取字段名称列表。
     * 
     * @param fields 字段配置列表
     * @return 字段名称列表
     */
    private List&lt;String&gt; extractFieldNames(List&lt;FieldConfigWrapper&gt; fields) {
<span class="nc" id="L168">        List&lt;String&gt; fieldNames = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">        for (FieldConfigWrapper field : fields) {</span>
<span class="nc" id="L170">            fieldNames.add(field.getName());</span>
<span class="nc" id="L171">        }</span>
<span class="nc" id="L172">        return fieldNames;</span>
    }

    /**
     * 顺序生成数据。
     * 
     * @param config         生成配置
     * @param outputStrategy 输出策略
     * @param fieldNames     字段名称列表
     * @throws DataForgeException 当生成失败时
     */
    private void generateDataSequentially(ForgeConfig config, OutputStrategy outputStrategy,
            List&lt;String&gt; fieldNames) throws DataForgeException {

<span class="nc" id="L186">        logger.debug(&quot;Starting sequential data generation for {} records&quot;, config.getCount());</span>

        // 初始化随机数生成器
<span class="nc bnc" id="L189" title="All 2 branches missed.">        Random random = config.getSeed() != null ? new Random(config.getSeed()) : new Random();</span>

<span class="nc bnc" id="L191" title="All 2 branches missed.">        for (int i = 0; i &lt; config.getCount(); i++) {</span>
            try {
                // 创建新的上下文
<span class="nc" id="L194">                DataForgeContext context = new DataForgeContext();</span>
<span class="nc" id="L195">                context.setCurrentRecordIndex(i);</span>

                // 生成单条记录
<span class="nc" id="L198">                Map&lt;String, Object&gt; record = generateSingleRecord(config.getFields(), context, random);</span>

                // 输出记录
<span class="nc" id="L201">                outputStrategy.writeRecord(record);</span>

                // 定期刷新输出（每100条记录）
<span class="nc bnc" id="L204" title="All 2 branches missed.">                if ((i + 1) % 100 == 0) {</span>
<span class="nc" id="L205">                    outputStrategy.flush();</span>
<span class="nc" id="L206">                    logger.debug(&quot;Generated {} records&quot;, i + 1);</span>
                }

<span class="nc" id="L209">            } catch (Exception e) {</span>
<span class="nc" id="L210">                throw new DataForgeException(&quot;Failed to generate record at index &quot; + i, e);</span>
<span class="nc" id="L211">            }</span>
        }
<span class="nc" id="L213">    }</span>

    /**
     * 并发生成数据。
     * 
     * @param config         生成配置
     * @param outputStrategy 输出策略
     * @param fieldNames     字段名称列表
     * @throws DataForgeException 当生成失败时
     */
    private void generateDataConcurrently(ForgeConfig config, OutputStrategy outputStrategy,
            List&lt;String&gt; fieldNames) throws DataForgeException {

<span class="nc" id="L226">        logger.debug(&quot;Starting concurrent data generation for {} records with {} threads&quot;,</span>
<span class="nc" id="L227">                config.getCount(), config.getThreads());</span>

<span class="nc" id="L229">        ExecutorService executor = Executors.newFixedThreadPool(config.getThreads());</span>

        try {
            // 计算每个线程处理的记录数
<span class="nc" id="L233">            int recordsPerThread = config.getCount() / config.getThreads();</span>
<span class="nc" id="L234">            int remainingRecords = config.getCount() % config.getThreads();</span>

<span class="nc" id="L236">            List&lt;CompletableFuture&lt;List&lt;Map&lt;String, Object&gt;&gt;&gt;&gt; futures = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L238">            int startIndex = 0;</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">            for (int threadIndex = 0; threadIndex &lt; config.getThreads(); threadIndex++) {</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">                int recordCount = recordsPerThread + (threadIndex &lt; remainingRecords ? 1 : 0);</span>
<span class="nc" id="L241">                int finalStartIndex = startIndex;</span>

                // 为每个线程创建独立的随机数生成器
<span class="nc bnc" id="L244" title="All 2 branches missed.">                Random threadRandom = config.getSeed() != null ? new Random(config.getSeed() + threadIndex)</span>
<span class="nc" id="L245">                        : new Random();</span>

<span class="nc" id="L247">                CompletableFuture&lt;List&lt;Map&lt;String, Object&gt;&gt;&gt; future = CompletableFuture.supplyAsync(() -&gt; {</span>
<span class="nc" id="L248">                    return generateRecordsBatch(config.getFields(), finalStartIndex, recordCount, threadRandom);</span>
                }, executor);

<span class="nc" id="L251">                futures.add(future);</span>
<span class="nc" id="L252">                startIndex += recordCount;</span>
            }

            // 等待所有线程完成并收集结果
<span class="nc bnc" id="L256" title="All 2 branches missed.">            for (CompletableFuture&lt;List&lt;Map&lt;String, Object&gt;&gt;&gt; future : futures) {</span>
<span class="nc" id="L257">                List&lt;Map&lt;String, Object&gt;&gt; records = future.get();</span>
<span class="nc" id="L258">                outputStrategy.writeRecords(records);</span>
<span class="nc" id="L259">            }</span>

<span class="nc" id="L261">        } catch (Exception e) {</span>
<span class="nc" id="L262">            throw new DataForgeException(&quot;Concurrent data generation failed&quot;, e);</span>
        } finally {
<span class="nc" id="L264">            executor.shutdown();</span>
            try {
<span class="nc bnc" id="L266" title="All 2 branches missed.">                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L267">                    executor.shutdownNow();</span>
                }
<span class="nc" id="L269">            } catch (InterruptedException e) {</span>
<span class="nc" id="L270">                executor.shutdownNow();</span>
<span class="nc" id="L271">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L272">            }</span>
        }
<span class="nc" id="L274">    }</span>

    /**
     * 批量生成记录。
     * 
     * @param fields     字段配置列表
     * @param startIndex 起始索引
     * @param count      记录数量
     * @param random     随机数生成器
     * @return 生成的记录列表
     */
    private List&lt;Map&lt;String, Object&gt;&gt; generateRecordsBatch(List&lt;FieldConfigWrapper&gt; fields,
            int startIndex, int count, Random random) {
<span class="nc" id="L287">        List&lt;Map&lt;String, Object&gt;&gt; records = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L289" title="All 2 branches missed.">        for (int i = 0; i &lt; count; i++) {</span>
<span class="nc" id="L290">            DataForgeContext context = new DataForgeContext();</span>
<span class="nc" id="L291">            context.setCurrentRecordIndex(startIndex + i);</span>

<span class="nc" id="L293">            Map&lt;String, Object&gt; record = generateSingleRecord(fields, context, random);</span>
<span class="nc" id="L294">            records.add(record);</span>
        }

<span class="nc" id="L297">        return records;</span>
    }

    /**
     * 生成单条记录。
     * 
     * @param fields  字段配置列表
     * @param context 生成上下文
     * @param random  随机数生成器（可选，用于可重现的生成）
     * @return 生成的记录
     */
    @SuppressWarnings(&quot;unchecked&quot;)
    private Map&lt;String, Object&gt; generateSingleRecord(List&lt;FieldConfigWrapper&gt; fields,
            DataForgeContext context, Random random) {
<span class="nc" id="L311">        Map&lt;String, Object&gt; record = new HashMap&lt;&gt;();</span>

        // 按字段配置顺序生成数据，确保关联性正确处理
<span class="nc bnc" id="L314" title="All 2 branches missed.">        for (FieldConfigWrapper field : fields) {</span>
            try {
<span class="nc" id="L316">                DataGenerator&lt;Object, FieldConfig&gt; generator = (DataGenerator&lt;Object, FieldConfig&gt;) generatorFactory</span>
<span class="nc" id="L317">                        .getGenerator(field.getType());</span>

<span class="nc bnc" id="L319" title="All 2 branches missed.">                if (generator == null) {</span>
<span class="nc" id="L320">                    logger.warn(&quot;No generator found for field type: {}, using null value&quot;, field.getType());</span>
<span class="nc" id="L321">                    record.put(field.getName(), null);</span>
<span class="nc" id="L322">                    continue;</span>
                }

                // 生成字段值
<span class="nc" id="L326">                Object value = generator.generate(field, context);</span>
<span class="nc" id="L327">                record.put(field.getName(), value);</span>

<span class="nc" id="L329">                logger.trace(&quot;Generated field: {}={}&quot;, field.getName(), value);</span>

<span class="nc" id="L331">            } catch (Exception e) {</span>
<span class="nc" id="L332">                logger.error(&quot;Failed to generate field: {}&quot;, field.getName(), e);</span>
                // 继续处理其他字段，将当前字段设为null
<span class="nc" id="L334">                record.put(field.getName(), null);</span>
<span class="nc" id="L335">            }</span>
<span class="nc" id="L336">        }</span>

<span class="nc" id="L338">        return record;</span>
    }

    /**
     * 获取可用的数据生成器类型。
     * 
     * @return 生成器类型集合
     */
    public java.util.Set&lt;String&gt; getAvailableGeneratorTypes() {
<span class="nc" id="L347">        return generatorFactory.getAvailableTypes();</span>
    }

    /**
     * 获取生成器的详细信息。
     * 
     * @return 生成器信息映射
     */
    public Map&lt;String, String&gt; getGeneratorInfo() {
<span class="nc" id="L356">        return generatorFactory.getGeneratorInfo();</span>
    }

    /**
     * 获取可用的输出格式。
     * 
     * @return 输出格式列表
     */
    public List&lt;OutputConfig.Format&gt; getAvailableOutputFormats() {
<span class="nc" id="L365">        List&lt;OutputConfig.Format&gt; formats = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L366" title="All 2 branches missed.">        for (OutputStrategy strategy : outputStrategies) {</span>
<span class="nc" id="L367">            formats.add(strategy.getSupportedFormat());</span>
<span class="nc" id="L368">        }</span>
<span class="nc" id="L369">        return formats;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>