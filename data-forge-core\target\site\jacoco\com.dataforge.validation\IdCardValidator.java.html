<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IdCardValidator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_source">IdCardValidator.java</span></div><h1>IdCardValidator.java</h1><pre class="source lang-java linenums">package com.dataforge.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * 中国大陆居民身份证号码校验器。
 * 
 * &lt;p&gt;
 * 实现中国大陆18位身份证号码的校验算法，包括：
 * 1. 长度校验（18位）
 * 2. 地区代码校验
 * 3. 出生日期校验
 * 4. 校验位算法校验
 * 
 * &lt;p&gt;
 * 身份证号码结构：XXXXXX YYYYMMDD SSS C
 * - 前6位：地区代码
 * - 第7-14位：出生日期（YYYYMMDD）
 * - 第15-17位：顺序码SSS（奇数为男性，偶数为女性）
 * - 第18位：校验码C
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L33">public class IdCardValidator implements Validator&lt;String&gt; {</span>

<span class="nc" id="L35">    private static final Logger logger = LoggerFactory.getLogger(IdCardValidator.class);</span>

    /**
     * 校验位权重数组。
     */
<span class="nc" id="L40">    private static final int[] WEIGHTS = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };</span>

    /**
     * 校验位对应表。
     */
<span class="nc" id="L45">    private static final char[] CHECK_CODES = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };</span>

    /**
     * 常见地区代码映射（部分）。
     * 实际应用中应该使用完整的行政区划代码表。
     */
<span class="nc" id="L51">    private static final Map&lt;String, String&gt; REGION_CODES = new HashMap&lt;&gt;();</span>

    static {
        // 初始化部分地区代码（实际应用中应该加载完整的行政区划代码表）
<span class="nc" id="L55">        REGION_CODES.put(&quot;110000&quot;, &quot;北京市&quot;);</span>
<span class="nc" id="L56">        REGION_CODES.put(&quot;110100&quot;, &quot;北京市市辖区&quot;);</span>
<span class="nc" id="L57">        REGION_CODES.put(&quot;110101&quot;, &quot;北京市东城区&quot;);</span>
<span class="nc" id="L58">        REGION_CODES.put(&quot;110102&quot;, &quot;北京市西城区&quot;);</span>
<span class="nc" id="L59">        REGION_CODES.put(&quot;120000&quot;, &quot;天津市&quot;);</span>
<span class="nc" id="L60">        REGION_CODES.put(&quot;130000&quot;, &quot;河北省&quot;);</span>
<span class="nc" id="L61">        REGION_CODES.put(&quot;140000&quot;, &quot;山西省&quot;);</span>
<span class="nc" id="L62">        REGION_CODES.put(&quot;150000&quot;, &quot;内蒙古自治区&quot;);</span>
<span class="nc" id="L63">        REGION_CODES.put(&quot;210000&quot;, &quot;辽宁省&quot;);</span>
<span class="nc" id="L64">        REGION_CODES.put(&quot;220000&quot;, &quot;吉林省&quot;);</span>
<span class="nc" id="L65">        REGION_CODES.put(&quot;230000&quot;, &quot;黑龙江省&quot;);</span>
<span class="nc" id="L66">        REGION_CODES.put(&quot;310000&quot;, &quot;上海市&quot;);</span>
<span class="nc" id="L67">        REGION_CODES.put(&quot;320000&quot;, &quot;江苏省&quot;);</span>
<span class="nc" id="L68">        REGION_CODES.put(&quot;330000&quot;, &quot;浙江省&quot;);</span>
<span class="nc" id="L69">        REGION_CODES.put(&quot;330100&quot;, &quot;浙江省杭州市&quot;);</span>
<span class="nc" id="L70">        REGION_CODES.put(&quot;330106&quot;, &quot;浙江省杭州市西湖区&quot;);</span>
<span class="nc" id="L71">        REGION_CODES.put(&quot;340000&quot;, &quot;安徽省&quot;);</span>
<span class="nc" id="L72">        REGION_CODES.put(&quot;350000&quot;, &quot;福建省&quot;);</span>
<span class="nc" id="L73">        REGION_CODES.put(&quot;360000&quot;, &quot;江西省&quot;);</span>
<span class="nc" id="L74">        REGION_CODES.put(&quot;370000&quot;, &quot;山东省&quot;);</span>
<span class="nc" id="L75">        REGION_CODES.put(&quot;410000&quot;, &quot;河南省&quot;);</span>
<span class="nc" id="L76">        REGION_CODES.put(&quot;420000&quot;, &quot;湖北省&quot;);</span>
<span class="nc" id="L77">        REGION_CODES.put(&quot;430000&quot;, &quot;湖南省&quot;);</span>
<span class="nc" id="L78">        REGION_CODES.put(&quot;440000&quot;, &quot;广东省&quot;);</span>
<span class="nc" id="L79">        REGION_CODES.put(&quot;450000&quot;, &quot;广西壮族自治区&quot;);</span>
<span class="nc" id="L80">        REGION_CODES.put(&quot;460000&quot;, &quot;海南省&quot;);</span>
<span class="nc" id="L81">        REGION_CODES.put(&quot;500000&quot;, &quot;重庆市&quot;);</span>
<span class="nc" id="L82">        REGION_CODES.put(&quot;510000&quot;, &quot;四川省&quot;);</span>
<span class="nc" id="L83">        REGION_CODES.put(&quot;520000&quot;, &quot;贵州省&quot;);</span>
<span class="nc" id="L84">        REGION_CODES.put(&quot;530000&quot;, &quot;云南省&quot;);</span>
<span class="nc" id="L85">        REGION_CODES.put(&quot;540000&quot;, &quot;西藏自治区&quot;);</span>
<span class="nc" id="L86">        REGION_CODES.put(&quot;610000&quot;, &quot;陕西省&quot;);</span>
<span class="nc" id="L87">        REGION_CODES.put(&quot;620000&quot;, &quot;甘肃省&quot;);</span>
<span class="nc" id="L88">        REGION_CODES.put(&quot;630000&quot;, &quot;青海省&quot;);</span>
<span class="nc" id="L89">        REGION_CODES.put(&quot;640000&quot;, &quot;宁夏回族自治区&quot;);</span>
<span class="nc" id="L90">        REGION_CODES.put(&quot;650000&quot;, &quot;新疆维吾尔自治区&quot;);</span>
<span class="nc" id="L91">        REGION_CODES.put(&quot;710000&quot;, &quot;台湾省&quot;);</span>
<span class="nc" id="L92">        REGION_CODES.put(&quot;810000&quot;, &quot;香港特别行政区&quot;);</span>
<span class="nc" id="L93">        REGION_CODES.put(&quot;820000&quot;, &quot;澳门特别行政区&quot;);</span>
<span class="nc" id="L94">    }</span>

    @Override
    public boolean isValid(String data) {
<span class="nc" id="L98">        return validate(data).isValid();</span>
    }

    @Override
    public ValidationResult validate(String data) {
<span class="nc bnc" id="L103" title="All 2 branches missed.">        if (data == null) {</span>
<span class="nc" id="L104">            return ValidationResult.failure(&quot;ID card number cannot be null&quot;);</span>
        }

        // 移除所有非字母数字字符
<span class="nc" id="L108">        String cleanData = data.replaceAll(&quot;[^0-9Xx]&quot;, &quot;&quot;).toUpperCase();</span>

<span class="nc bnc" id="L110" title="All 2 branches missed.">        if (cleanData.isEmpty()) {</span>
<span class="nc" id="L111">            return ValidationResult.failure(&quot;ID card number cannot be empty&quot;);</span>
        }

        // 长度校验
<span class="nc bnc" id="L115" title="All 2 branches missed.">        if (cleanData.length() != 18) {</span>
<span class="nc" id="L116">            return ValidationResult.failure(&quot;ID card number must be exactly 18 characters long&quot;);</span>
        }

        try {
            // 前17位必须是数字
<span class="nc" id="L121">            String first17 = cleanData.substring(0, 17);</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">            if (!first17.matches(&quot;\\d{17}&quot;)) {</span>
<span class="nc" id="L123">                return ValidationResult.failure(&quot;First 17 characters must be digits&quot;);</span>
            }

            // 地区代码校验
<span class="nc" id="L127">            ValidationResult regionResult = validateRegionCode(first17.substring(0, 6));</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">            if (!regionResult.isValid()) {</span>
<span class="nc" id="L129">                return regionResult;</span>
            }

            // 出生日期校验
<span class="nc" id="L133">            ValidationResult birthDateResult = validateBirthDate(first17.substring(6, 14));</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">            if (!birthDateResult.isValid()) {</span>
<span class="nc" id="L135">                return birthDateResult;</span>
            }

            // 校验位校验
<span class="nc" id="L139">            ValidationResult checkCodeResult = validateCheckCode(cleanData);</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">            if (!checkCodeResult.isValid()) {</span>
<span class="nc" id="L141">                return checkCodeResult;</span>
            }

<span class="nc" id="L144">            logger.debug(&quot;ID card validation passed for: {}&quot;, maskIdCard(data));</span>
<span class="nc" id="L145">            return ValidationResult.success();</span>

<span class="nc" id="L147">        } catch (Exception e) {</span>
<span class="nc" id="L148">            logger.error(&quot;Error during ID card validation for: {}&quot;, maskIdCard(data), e);</span>
<span class="nc" id="L149">            return ValidationResult.failure(&quot;Error during ID card validation: &quot; + e.getMessage());</span>
        }
    }

    /**
     * 校验地区代码。
     * 
     * @param regionCode 6位地区代码
     * @return 校验结果
     */
    private ValidationResult validateRegionCode(String regionCode) {
<span class="nc bnc" id="L160" title="All 2 branches missed.">        if (regionCode.length() != 6) {</span>
<span class="nc" id="L161">            return ValidationResult.failure(&quot;Region code must be 6 digits&quot;);</span>
        }

<span class="nc bnc" id="L164" title="All 2 branches missed.">        if (!regionCode.matches(&quot;\\d{6}&quot;)) {</span>
<span class="nc" id="L165">            return ValidationResult.failure(&quot;Region code must contain only digits&quot;);</span>
        }

        // 基本的地区代码格式校验
        // 第1-2位：省、自治区、直辖市代码（11-82）
<span class="nc" id="L170">        int provinceCode = Integer.parseInt(regionCode.substring(0, 2));</span>
<span class="nc bnc" id="L171" title="All 4 branches missed.">        if (provinceCode &lt; 11 || provinceCode &gt; 82) {</span>
<span class="nc" id="L172">            return ValidationResult.failure(&quot;Invalid province code: &quot; + provinceCode);</span>
        }

        // 检查是否为已知的地区代码（可选，因为完整的地区代码表很大）
<span class="nc" id="L176">        String provincePrefix = regionCode.substring(0, 2) + &quot;0000&quot;;</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">        if (REGION_CODES.containsKey(provincePrefix)) {</span>
<span class="nc" id="L178">            logger.debug(&quot;Region code {} belongs to {}&quot;, regionCode, REGION_CODES.get(provincePrefix));</span>
        }

<span class="nc" id="L181">        return ValidationResult.success();</span>
    }

    /**
     * 校验出生日期。
     * 
     * @param birthDateStr 8位出生日期字符串（YYYYMMDD）
     * @return 校验结果
     */
    private ValidationResult validateBirthDate(String birthDateStr) {
<span class="nc bnc" id="L191" title="All 2 branches missed.">        if (birthDateStr.length() != 8) {</span>
<span class="nc" id="L192">            return ValidationResult.failure(&quot;Birth date must be 8 digits&quot;);</span>
        }

<span class="nc bnc" id="L195" title="All 2 branches missed.">        if (!birthDateStr.matches(&quot;\\d{8}&quot;)) {</span>
<span class="nc" id="L196">            return ValidationResult.failure(&quot;Birth date must contain only digits&quot;);</span>
        }

        try {
            // 解析日期
<span class="nc" id="L201">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;);</span>
<span class="nc" id="L202">            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);</span>

            // 检查日期范围（1900年1月1日到当前日期）
<span class="nc" id="L205">            LocalDate minDate = LocalDate.of(1900, 1, 1);</span>
<span class="nc" id="L206">            LocalDate maxDate = LocalDate.now();</span>

<span class="nc bnc" id="L208" title="All 2 branches missed.">            if (birthDate.isBefore(minDate)) {</span>
<span class="nc" id="L209">                return ValidationResult.failure(&quot;Birth date cannot be before &quot; + minDate);</span>
            }

<span class="nc bnc" id="L212" title="All 2 branches missed.">            if (birthDate.isAfter(maxDate)) {</span>
<span class="nc" id="L213">                return ValidationResult.failure(&quot;Birth date cannot be in the future&quot;);</span>
            }

<span class="nc" id="L216">            logger.debug(&quot;Birth date validation passed: {}&quot;, birthDate);</span>
<span class="nc" id="L217">            return ValidationResult.success();</span>

<span class="nc" id="L219">        } catch (DateTimeParseException e) {</span>
<span class="nc" id="L220">            return ValidationResult.failure(&quot;Invalid birth date format: &quot; + birthDateStr);</span>
        }
    }

    /**
     * 校验校验位。
     * 
     * @param idCard 完整的18位身份证号码
     * @return 校验结果
     */
    private ValidationResult validateCheckCode(String idCard) {
<span class="nc" id="L231">        String first17 = idCard.substring(0, 17);</span>
<span class="nc" id="L232">        char actualCheckCode = idCard.charAt(17);</span>
<span class="nc" id="L233">        char expectedCheckCode = calculateCheckCode(first17);</span>

<span class="nc bnc" id="L235" title="All 2 branches missed.">        if (actualCheckCode == expectedCheckCode) {</span>
<span class="nc" id="L236">            return ValidationResult.success();</span>
        } else {
<span class="nc" id="L238">            return ValidationResult.failure(</span>
<span class="nc" id="L239">                    String.format(&quot;Check code mismatch. Expected: %c, Actual: %c&quot;,</span>
<span class="nc" id="L240">                            expectedCheckCode, actualCheckCode));</span>
        }
    }

    /**
     * 计算身份证号码的校验位。
     * 
     * @param first17 前17位数字
     * @return 校验位字符
     */
    public char calculateCheckCode(String first17) {
<span class="nc bnc" id="L251" title="All 6 branches missed.">        if (first17 == null || first17.length() != 17 || !first17.matches(&quot;\\d{17}&quot;)) {</span>
<span class="nc" id="L252">            throw new IllegalArgumentException(&quot;First 17 characters must be exactly 17 digits&quot;);</span>
        }

<span class="nc" id="L255">        int sum = 0;</span>
<span class="nc bnc" id="L256" title="All 2 branches missed.">        for (int i = 0; i &lt; 17; i++) {</span>
<span class="nc" id="L257">            int digit = Character.getNumericValue(first17.charAt(i));</span>
<span class="nc" id="L258">            sum += digit * WEIGHTS[i];</span>
        }

<span class="nc" id="L261">        int remainder = sum % 11;</span>
<span class="nc" id="L262">        return CHECK_CODES[remainder];</span>
    }

    /**
     * 生成完整的有效身份证号码。
     * 
     * @param first17 前17位数字
     * @return 完整的18位身份证号码
     */
    public String generateValidIdCard(String first17) {
<span class="nc" id="L272">        char checkCode = calculateCheckCode(first17);</span>
<span class="nc" id="L273">        return first17 + checkCode;</span>
    }

    /**
     * 从身份证号码中提取性别。
     * 
     * @param idCard 身份证号码
     * @return &quot;M&quot;表示男性，&quot;F&quot;表示女性，null表示无法确定
     */
    public String extractGender(String idCard) {
<span class="nc bnc" id="L283" title="All 4 branches missed.">        if (idCard == null || idCard.length() &lt; 17) {</span>
<span class="nc" id="L284">            return null;</span>
        }

        try {
            // 第17位数字决定性别
<span class="nc" id="L289">            int genderDigit = Character.getNumericValue(idCard.charAt(16));</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">            return (genderDigit % 2 == 1) ? &quot;M&quot; : &quot;F&quot;;</span>
<span class="nc" id="L291">        } catch (Exception e) {</span>
<span class="nc" id="L292">            logger.warn(&quot;Failed to extract gender from ID card: {}&quot;, maskIdCard(idCard), e);</span>
<span class="nc" id="L293">            return null;</span>
        }
    }

    /**
     * 从身份证号码中提取出生日期。
     * 
     * @param idCard 身份证号码
     * @return 出生日期，如果无法解析则返回null
     */
    public LocalDate extractBirthDate(String idCard) {
<span class="nc bnc" id="L304" title="All 4 branches missed.">        if (idCard == null || idCard.length() &lt; 14) {</span>
<span class="nc" id="L305">            return null;</span>
        }

        try {
<span class="nc" id="L309">            String birthDateStr = idCard.substring(6, 14);</span>
<span class="nc" id="L310">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;);</span>
<span class="nc" id="L311">            return LocalDate.parse(birthDateStr, formatter);</span>
<span class="nc" id="L312">        } catch (Exception e) {</span>
<span class="nc" id="L313">            logger.warn(&quot;Failed to extract birth date from ID card: {}&quot;, maskIdCard(idCard), e);</span>
<span class="nc" id="L314">            return null;</span>
        }
    }

    /**
     * 从身份证号码中提取地区代码。
     * 
     * @param idCard 身份证号码
     * @return 6位地区代码，如果无法提取则返回null
     */
    public String extractRegionCode(String idCard) {
<span class="nc bnc" id="L325" title="All 4 branches missed.">        if (idCard == null || idCard.length() &lt; 6) {</span>
<span class="nc" id="L326">            return null;</span>
        }

<span class="nc" id="L329">        return idCard.substring(0, 6);</span>
    }

    /**
     * 掩码身份证号码用于日志记录。
     * 
     * @param idCard 原始身份证号码
     * @return 掩码后的身份证号码
     */
    private String maskIdCard(String idCard) {
<span class="nc bnc" id="L339" title="All 4 branches missed.">        if (idCard == null || idCard.length() &lt; 8) {</span>
<span class="nc" id="L340">            return &quot;****&quot;;</span>
        }

        // 显示前4位和后4位，中间用*代替
<span class="nc" id="L344">        String prefix = idCard.substring(0, 4);</span>
<span class="nc" id="L345">        String suffix = idCard.substring(idCard.length() - 4);</span>
<span class="nc" id="L346">        int maskLength = idCard.length() - 8;</span>
<span class="nc" id="L347">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="nc" id="L349">        return prefix + mask + suffix;</span>
    }

    @Override
    public String getName() {
<span class="nc" id="L354">        return &quot;IdCard&quot;;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L359">        return &quot;Chinese mainland resident ID card validator (18-digit)&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>