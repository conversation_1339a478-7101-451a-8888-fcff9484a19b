<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.dataforge.core</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <span class="el_package">com.dataforge.core</span></div><h1>com.dataforge.core</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">558 of 581</td><td class="ctr2">3%</td><td class="bar">64 of 64</td><td class="ctr2">0%</td><td class="ctr1">57</td><td class="ctr2">59</td><td class="ctr1">123</td><td class="ctr2">130</td><td class="ctr1">25</td><td class="ctr2">27</td><td class="ctr1">1</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a1"><a href="GeneratorFactory.java.html" class="el_source">GeneratorFactory.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="337" alt="337"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">30</td><td class="ctr2" id="g0">30</td><td class="ctr1" id="h0">78</td><td class="ctr2" id="i0">78</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k1">12</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="DataForgeContext.java.html" class="el_source">DataForgeContext.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="221" alt="221"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="23" alt="23"/></td><td class="ctr2" id="c0">9%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="28" alt="28"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">27</td><td class="ctr2" id="g1">29</td><td class="ctr1" id="h1">45</td><td class="ctr2" id="i1">52</td><td class="ctr1" id="j0">13</td><td class="ctr2" id="k0">15</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>