package com.dataforge.io;

import com.dataforge.config.OutputConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

/**
 * CSV输出策略实现
 * 
 * 支持功能：
 * 1. 流式写入CSV文件
 * 2. 自定义分隔符
 * 3. 可选的标题行
 * 4. 字符编码支持
 * 5. 追加模式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class CsvOutputStrategy implements OutputStrategy {

    private static final Logger log = LoggerFactory.getLogger(CsvOutputStrategy.class);

    private BufferedWriter writer;
    private String delimiter;
    private boolean includeHeader;
    private boolean headerWritten = false;
    private int recordCount = 0;

    @Override
    public OutputConfig.Format getSupportedFormat() {
        return OutputConfig.Format.CSV;
    }

    @Override
    public void initialize(OutputConfig config, List<String> fieldNames) throws OutputException {
        try {
            // 解析配置
            this.delimiter = config.getCsvDelimiter() != null ? config.getCsvDelimiter() : ",";
            this.includeHeader = config.isCsvIncludeHeader();

            // 创建文件写入器
            String encoding = config.getEncoding() != null ? config.getEncoding() : "UTF-8";
            Charset charset = Charset.forName(encoding);

            this.writer = new BufferedWriter(
                    new FileWriter(config.getFile(), charset, config.isAppend()));

            log.info("CSV output initialized - file: {}, delimiter: '{}', header: {}, encoding: {}",
                    config.getFile(), delimiter, includeHeader, encoding);

        } catch (IOException e) {
            throw new OutputException("Failed to initialize CSV output: " + e.getMessage(), e);
        }
    }

    @Override
    public void writeRecord(Map<String, Object> record) throws OutputException {
        try {
            // 写入标题行（仅第一次）
            if (includeHeader && !headerWritten) {
                writeHeader(record);
                headerWritten = true;
            }

            // 写入数据行
            writeDataRow(record);
            recordCount++;

        } catch (IOException e) {
            throw new OutputException("Failed to write CSV record: " + e.getMessage(), e);
        }
    }

    @Override
    public void writeRecords(List<Map<String, Object>> records) throws OutputException {
        for (Map<String, Object> record : records) {
            writeRecord(record);
        }
    }

    @Override
    public void finish() throws OutputException {
        try {
            if (writer != null) {
                writer.flush();
                writer.close();
                log.info("CSV output completed. Total records: {}", recordCount);
            }
        } catch (IOException e) {
            throw new OutputException("Failed to close CSV output: " + e.getMessage(), e);
        }
    }

    /**
     * 写入标题行
     */
    private void writeHeader(Map<String, Object> record) throws IOException {
        boolean first = true;
        for (String fieldName : record.keySet()) {
            if (!first) {
                writer.write(delimiter);
            }
            writer.write(escapeCsvField(fieldName));
            first = false;
        }
        writer.newLine();
    }

    /**
     * 写入数据行
     */
    private void writeDataRow(Map<String, Object> record) throws IOException {
        boolean first = true;
        for (Object value : record.values()) {
            if (!first) {
                writer.write(delimiter);
            }

            String stringValue = value != null ? value.toString() : "";
            writer.write(escapeCsvField(stringValue));
            first = false;
        }
        writer.newLine();
    }

    /**
     * 转义CSV字段
     * 
     * 规则：
     * 1. 如果字段包含分隔符、双引号或换行符，则用双引号包围
     * 2. 字段内的双引号需要转义为两个双引号
     */
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }

        // 检查是否需要转义
        boolean needsEscaping = field.contains(delimiter) ||
                field.contains("\"") ||
                field.contains("\n") ||
                field.contains("\r");

        if (needsEscaping) {
            // 转义双引号
            String escaped = field.replace("\"", "\"\"");
            // 用双引号包围
            return "\"" + escaped + "\"";
        }

        return field;
    }
}