<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebSocketGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">WebSocketGenerator</span></div><h1>WebSocketGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">190 of 917</td><td class="ctr2">79%</td><td class="bar">34 of 76</td><td class="ctr2">55%</td><td class="ctr1">29</td><td class="ctr2">62</td><td class="ctr1">38</td><td class="ctr2">151</td><td class="ctr1">1</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a1"><a href="WebSocketGenerator.java.html#L432" class="el_method">formatAsJson(WebSocketGenerator.WebSocketConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="56" alt="56"/><img src="../jacoco-resources/greenbar.gif" width="77" height="10" title="102" alt="102"/></td><td class="ctr2" id="c12">64%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="3" alt="3"/></td><td class="ctr2" id="e7">30%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="WebSocketGenerator.java.html#L351" class="el_method">generateHeaders()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="41" alt="41"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="WebSocketGenerator.java.html#L323" class="el_method">generateQueryParamValue(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="38" alt="38"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="29" alt="29"/></td><td class="ctr2" id="c13">43%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="5" alt="5"/></td><td class="ctr2" id="e6">35%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h2">6</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="WebSocketGenerator.java.html#L287" class="el_method">generateSubProtocol(WebSocketGenerator.SubProtocol)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c16">31%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="1" alt="1"/></td><td class="ctr2" id="e8">25%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="WebSocketGenerator.java.html#L208" class="el_method">generateWebSocketConfig(FieldConfig)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="104" height="10" title="137" alt="137"/></td><td class="ctr2" id="c8">93%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="9" alt="9"/></td><td class="ctr2" id="e4">64%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="WebSocketGenerator.java.html#L466" class="el_method">formatAsConfig(WebSocketGenerator.WebSocketConfig)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="97" alt="97"/></td><td class="ctr2" id="c9">91%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="WebSocketGenerator.java.html#L175" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="19" alt="19"/></td><td class="ctr2" id="c11">73%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a14"><a href="WebSocketGenerator.java.html#L264" class="el_method">parseProtocol(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">36%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="WebSocketGenerator.java.html#L276" class="el_method">parseSubProtocol(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">36%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="WebSocketGenerator.java.html#L385" class="el_method">formatWebSocketConfig(WebSocketGenerator.WebSocketConfig, WebSocketGenerator.OutputFormat)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="15" alt="15"/></td><td class="ctr2" id="c10">78%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a16"><a href="WebSocketGenerator.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="97" height="10" title="128" alt="128"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="WebSocketGenerator.java.html#L401" class="el_method">formatAsUrl(WebSocketGenerator.WebSocketConfig)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="101" alt="101"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="15" alt="15"/></td><td class="ctr2" id="e2">83%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a6"><a href="WebSocketGenerator.java.html#L304" class="el_method">generateQueryParams()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="43" alt="43"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="WebSocketGenerator.java.html#L371" class="el_method">generateRandomString(int)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="25" alt="25"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a13"><a href="WebSocketGenerator.java.html#L196" class="el_method">parseOutputFormat(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">4</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a17"><a href="WebSocketGenerator.java.html#L40" class="el_method">WebSocketGenerator()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a12"><a href="WebSocketGenerator.java.html#L163" class="el_method">getType()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a11"><a href="WebSocketGenerator.java.html#L168" class="el_method">getConfigClass()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>