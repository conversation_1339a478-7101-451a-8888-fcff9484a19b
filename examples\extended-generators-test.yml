dataforge:
  count: 10
  output:
    format: csv
    file: "output/extended-test.csv"
  fields:
    # 基础信息类
    - name: "name"
      type: "name"
      params:
        type: "CN"
        gender: "ANY"
    
    - name: "gender"
      type: "gender"
      params:
        format: "CHINESE"
        male_ratio: 0.6
        link_idcard: true
    
    - name: "age"
      type: "age"
      params:
        min: 18
        max: 65
        distribution: "NORMAL"
        link_birth_date: true
    
    - name: "idcard"
      type: "idcard"
      params:
        region: "330100"
        birth_date_range: "1980-01-01,2005-12-31"
        valid: true
    
    - name: "phone"
      type: "phone"
      params:
        prefix: "138,139,150,151"
        valid: true
    
    - name: "email"
      type: "email"
      params:
        domains: "qq.com,163.com,gmail.com"
        prefix_name: true
    
    - name: "address"
      type: "address"
      params:
        detail_level: "FULL"
        include_zipcode: true
        link_idcard: true
    
    - name: "username"
      type: "username"
      params:
        length: "6,12"
        chars: "ALPHANUMERIC"
        link_name: true
        name_style: "MIXED"
        unique: true
    
    - name: "password"
      type: "password"
      params:
        length: "8,16"
        complexity: "HIGH"
        include_weak: false
        require_uppercase: true
        require_lowercase: true
        require_digits: true
        require_special: true
    
    - name: "bankcard"
      type: "bankcard"
      params:
        type: "DEBIT"
        valid: true
    
    - name: "license_plate"
      type: "licenseplate"
      params:
        type: "FUEL"
        province: "浙"
    
    - name: "uuid"
      type: "uuid"
      params:
        type: "UUID4"