<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UsernameGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">UsernameGenerator.java</span></div><h1>UsernameGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 账号名/用户名生成器
 * 
 * 支持功能：
 * 1. 指定长度和字符集的账号名生成
 * 2. 与姓名关联的智能用户名生成
 * 3. 支持前缀和后缀
 * 4. 支持唯一性保证
 * 5. 支持黑名单过滤
 * 
 * 参数配置：
 * - length: 账号名长度范围 &quot;min,max&quot;（默认&quot;6,16&quot;）
 * - chars: 字符集类型
 * ALPHANUMERIC|ALPHANUMERIC_SPECIAL|NUMERIC|ALPHA|CUSTOM（默认ALPHANUMERIC）
 * - custom_chars: 自定义字符集（当chars=CUSTOM时使用）
 * - prefix: 可选的账号名前缀
 * - suffix: 可选的账号名后缀
 * - unique: 是否在生成批次中保证唯一性（默认true）
 * - link_name: 是否关联姓名（默认true）
 * - name_style: 姓名关联风格 PINYIN|INITIALS|MIXED（默认MIXED）
 * - blacklist: 黑名单词汇，逗号分隔
 * 
 * 关联字段：
 * - name: 从上下文中获取姓名，生成基于姓名的用户名
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L40">public class UsernameGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L42">    private static final Logger log = LoggerFactory.getLogger(UsernameGenerator.class);</span>

    private static final String TYPE = &quot;username&quot;;
    private static final String DEFAULT_LENGTH = &quot;6,16&quot;;
    private static final String DEFAULT_CHARS = &quot;ALPHANUMERIC&quot;;
    private static final boolean DEFAULT_UNIQUE = true;
    private static final boolean DEFAULT_LINK_NAME = true;
    private static final String DEFAULT_NAME_STYLE = &quot;MIXED&quot;;

    // 字符集定义
    private static final String NUMERIC_CHARS = &quot;0123456789&quot;;
    private static final String ALPHA_CHARS = &quot;abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;;
    private static final String ALPHANUMERIC_CHARS = ALPHA_CHARS + NUMERIC_CHARS;
    private static final String SPECIAL_CHARS = &quot;_-&quot;;
    private static final String ALPHANUMERIC_SPECIAL_CHARS = ALPHANUMERIC_CHARS + SPECIAL_CHARS;

    // 上下文键名
    private static final String CONTEXT_NAME = &quot;name&quot;;

    // 生成的用户名缓存（用于唯一性检查）
<span class="nc" id="L62">    private static final Set&lt;String&gt; generatedUsernames = Collections.synchronizedSet(new HashSet&lt;&gt;());</span>

    // 默认黑名单
<span class="nc" id="L65">    private static final Set&lt;String&gt; DEFAULT_BLACKLIST = Set.of(</span>
            &quot;admin&quot;, &quot;root&quot;, &quot;administrator&quot;, &quot;system&quot;, &quot;test&quot;, &quot;guest&quot;, &quot;user&quot;, &quot;null&quot;, &quot;undefined&quot;,
            &quot;password&quot;, &quot;login&quot;, &quot;register&quot;, &quot;signin&quot;, &quot;signup&quot;, &quot;logout&quot;, &quot;delete&quot;, &quot;remove&quot;,
            &quot;fuck&quot;, &quot;shit&quot;, &quot;damn&quot;, &quot;hell&quot;, &quot;sex&quot;, &quot;porn&quot;, &quot;xxx&quot;, &quot;666&quot;, &quot;888&quot;, &quot;999&quot;);

    // 中文姓名拼音映射（简化版）
<span class="nc" id="L71">    private static final Map&lt;String, String&gt; PINYIN_MAP = new HashMap&lt;&gt;();</span>
    static {
        // 常见姓氏拼音
<span class="nc" id="L74">        PINYIN_MAP.put(&quot;王&quot;, &quot;wang&quot;);</span>
<span class="nc" id="L75">        PINYIN_MAP.put(&quot;李&quot;, &quot;li&quot;);</span>
<span class="nc" id="L76">        PINYIN_MAP.put(&quot;张&quot;, &quot;zhang&quot;);</span>
<span class="nc" id="L77">        PINYIN_MAP.put(&quot;刘&quot;, &quot;liu&quot;);</span>
<span class="nc" id="L78">        PINYIN_MAP.put(&quot;陈&quot;, &quot;chen&quot;);</span>
<span class="nc" id="L79">        PINYIN_MAP.put(&quot;杨&quot;, &quot;yang&quot;);</span>
<span class="nc" id="L80">        PINYIN_MAP.put(&quot;赵&quot;, &quot;zhao&quot;);</span>
<span class="nc" id="L81">        PINYIN_MAP.put(&quot;黄&quot;, &quot;huang&quot;);</span>
<span class="nc" id="L82">        PINYIN_MAP.put(&quot;周&quot;, &quot;zhou&quot;);</span>
<span class="nc" id="L83">        PINYIN_MAP.put(&quot;吴&quot;, &quot;wu&quot;);</span>
<span class="nc" id="L84">        PINYIN_MAP.put(&quot;徐&quot;, &quot;xu&quot;);</span>
<span class="nc" id="L85">        PINYIN_MAP.put(&quot;孙&quot;, &quot;sun&quot;);</span>
<span class="nc" id="L86">        PINYIN_MAP.put(&quot;胡&quot;, &quot;hu&quot;);</span>
<span class="nc" id="L87">        PINYIN_MAP.put(&quot;朱&quot;, &quot;zhu&quot;);</span>
<span class="nc" id="L88">        PINYIN_MAP.put(&quot;高&quot;, &quot;gao&quot;);</span>
<span class="nc" id="L89">        PINYIN_MAP.put(&quot;林&quot;, &quot;lin&quot;);</span>
<span class="nc" id="L90">        PINYIN_MAP.put(&quot;何&quot;, &quot;he&quot;);</span>
<span class="nc" id="L91">        PINYIN_MAP.put(&quot;郭&quot;, &quot;guo&quot;);</span>
<span class="nc" id="L92">        PINYIN_MAP.put(&quot;马&quot;, &quot;ma&quot;);</span>
<span class="nc" id="L93">        PINYIN_MAP.put(&quot;罗&quot;, &quot;luo&quot;);</span>
<span class="nc" id="L94">        PINYIN_MAP.put(&quot;梁&quot;, &quot;liang&quot;);</span>

        // 常见名字拼音
<span class="nc" id="L97">        PINYIN_MAP.put(&quot;伟&quot;, &quot;wei&quot;);</span>
<span class="nc" id="L98">        PINYIN_MAP.put(&quot;芳&quot;, &quot;fang&quot;);</span>
<span class="nc" id="L99">        PINYIN_MAP.put(&quot;娜&quot;, &quot;na&quot;);</span>
<span class="nc" id="L100">        PINYIN_MAP.put(&quot;秀&quot;, &quot;xiu&quot;);</span>
<span class="nc" id="L101">        PINYIN_MAP.put(&quot;敏&quot;, &quot;min&quot;);</span>
<span class="nc" id="L102">        PINYIN_MAP.put(&quot;静&quot;, &quot;jing&quot;);</span>
<span class="nc" id="L103">        PINYIN_MAP.put(&quot;丽&quot;, &quot;li&quot;);</span>
<span class="nc" id="L104">        PINYIN_MAP.put(&quot;强&quot;, &quot;qiang&quot;);</span>
<span class="nc" id="L105">        PINYIN_MAP.put(&quot;磊&quot;, &quot;lei&quot;);</span>
<span class="nc" id="L106">        PINYIN_MAP.put(&quot;军&quot;, &quot;jun&quot;);</span>
<span class="nc" id="L107">        PINYIN_MAP.put(&quot;洋&quot;, &quot;yang&quot;);</span>
<span class="nc" id="L108">        PINYIN_MAP.put(&quot;勇&quot;, &quot;yong&quot;);</span>
<span class="nc" id="L109">        PINYIN_MAP.put(&quot;艳&quot;, &quot;yan&quot;);</span>
<span class="nc" id="L110">        PINYIN_MAP.put(&quot;杰&quot;, &quot;jie&quot;);</span>
<span class="nc" id="L111">        PINYIN_MAP.put(&quot;娟&quot;, &quot;juan&quot;);</span>
<span class="nc" id="L112">        PINYIN_MAP.put(&quot;涛&quot;, &quot;tao&quot;);</span>
<span class="nc" id="L113">        PINYIN_MAP.put(&quot;明&quot;, &quot;ming&quot;);</span>
<span class="nc" id="L114">        PINYIN_MAP.put(&quot;超&quot;, &quot;chao&quot;);</span>
<span class="nc" id="L115">        PINYIN_MAP.put(&quot;秀英&quot;, &quot;xiuying&quot;);</span>
<span class="nc" id="L116">        PINYIN_MAP.put(&quot;桂英&quot;, &quot;guiying&quot;);</span>
<span class="nc" id="L117">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L121">        return TYPE;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L126">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L131">        Map&lt;String, Object&gt; params = config.getParams();</span>

        // 解析配置参数
<span class="nc" id="L134">        String lengthStr = getStringParam(params, &quot;length&quot;, DEFAULT_LENGTH);</span>
<span class="nc" id="L135">        String charsType = getStringParam(params, &quot;chars&quot;, DEFAULT_CHARS);</span>
<span class="nc" id="L136">        String customChars = getStringParam(params, &quot;custom_chars&quot;, &quot;&quot;);</span>
<span class="nc" id="L137">        String prefix = getStringParam(params, &quot;prefix&quot;, &quot;&quot;);</span>
<span class="nc" id="L138">        String suffix = getStringParam(params, &quot;suffix&quot;, &quot;&quot;);</span>
<span class="nc" id="L139">        boolean unique = getBooleanParam(params, &quot;unique&quot;, DEFAULT_UNIQUE);</span>
<span class="nc" id="L140">        boolean linkName = getBooleanParam(params, &quot;link_name&quot;, DEFAULT_LINK_NAME);</span>
<span class="nc" id="L141">        String nameStyle = getStringParam(params, &quot;name_style&quot;, DEFAULT_NAME_STYLE);</span>
<span class="nc" id="L142">        String blacklistStr = getStringParam(params, &quot;blacklist&quot;, &quot;&quot;);</span>

        // 解析长度范围
<span class="nc" id="L145">        int[] lengthRange = parseLengthRange(lengthStr);</span>
<span class="nc" id="L146">        int minLength = lengthRange[0];</span>
<span class="nc" id="L147">        int maxLength = lengthRange[1];</span>

        // 构建字符集
<span class="nc" id="L150">        String charSet = buildCharSet(charsType, customChars);</span>

        // 构建黑名单
<span class="nc" id="L153">        Set&lt;String&gt; blacklist = buildBlacklist(blacklistStr);</span>

        // 生成用户名
<span class="nc" id="L156">        String username = generateUsername(minLength, maxLength, charSet, prefix, suffix,</span>
                unique, linkName, nameStyle, blacklist, context);

<span class="nc" id="L159">        return username;</span>
    }

    /**
     * 解析长度范围
     */
    private int[] parseLengthRange(String lengthStr) {
        try {
<span class="nc bnc" id="L167" title="All 2 branches missed.">            if (lengthStr.contains(&quot;,&quot;)) {</span>
<span class="nc" id="L168">                String[] parts = lengthStr.split(&quot;,&quot;);</span>
<span class="nc" id="L169">                int min = Integer.parseInt(parts[0].trim());</span>
<span class="nc" id="L170">                int max = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L171">                return new int[] { Math.max(1, min), Math.max(min, max) };</span>
            } else {
<span class="nc" id="L173">                int length = Integer.parseInt(lengthStr.trim());</span>
<span class="nc" id="L174">                return new int[] { Math.max(1, length), Math.max(1, length) };</span>
            }
<span class="nc" id="L176">        } catch (Exception e) {</span>
<span class="nc" id="L177">            log.warn(&quot;Invalid length parameter: {}. Using default.&quot;, lengthStr);</span>
<span class="nc" id="L178">            return new int[] { 6, 16 };</span>
        }
    }

    /**
     * 构建字符集
     */
    private String buildCharSet(String charsType, String customChars) {
<span class="nc bnc" id="L186" title="All 6 branches missed.">        switch (charsType.toUpperCase()) {</span>
            case &quot;NUMERIC&quot;:
<span class="nc" id="L188">                return NUMERIC_CHARS;</span>
            case &quot;ALPHA&quot;:
<span class="nc" id="L190">                return ALPHA_CHARS;</span>
            case &quot;ALPHANUMERIC&quot;:
<span class="nc" id="L192">                return ALPHANUMERIC_CHARS;</span>
            case &quot;ALPHANUMERIC_SPECIAL&quot;:
<span class="nc" id="L194">                return ALPHANUMERIC_SPECIAL_CHARS;</span>
            case &quot;CUSTOM&quot;:
<span class="nc bnc" id="L196" title="All 2 branches missed.">                return customChars.isEmpty() ? ALPHANUMERIC_CHARS : customChars;</span>
            default:
<span class="nc" id="L198">                log.warn(&quot;Unknown chars type: {}. Using ALPHANUMERIC.&quot;, charsType);</span>
<span class="nc" id="L199">                return ALPHANUMERIC_CHARS;</span>
        }
    }

    /**
     * 构建黑名单
     */
    private Set&lt;String&gt; buildBlacklist(String blacklistStr) {
<span class="nc" id="L207">        Set&lt;String&gt; blacklist = new HashSet&lt;&gt;(DEFAULT_BLACKLIST);</span>

<span class="nc bnc" id="L209" title="All 2 branches missed.">        if (!blacklistStr.isEmpty()) {</span>
<span class="nc" id="L210">            String[] words = blacklistStr.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L211" title="All 2 branches missed.">            for (String word : words) {</span>
<span class="nc" id="L212">                String trimmed = word.trim().toLowerCase();</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">                if (!trimmed.isEmpty()) {</span>
<span class="nc" id="L214">                    blacklist.add(trimmed);</span>
                }
            }
        }

<span class="nc" id="L219">        return blacklist;</span>
    }

    /**
     * 生成用户名
     */
    private String generateUsername(int minLength, int maxLength, String charSet, String prefix, String suffix,
            boolean unique, boolean linkName, String nameStyle, Set&lt;String&gt; blacklist,
            DataForgeContext context) {

<span class="nc" id="L229">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
        String username;
<span class="nc" id="L231">        int attempts = 0;</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">        int maxAttempts = unique ? 100 : 1;</span>

        do {
<span class="nc" id="L235">            attempts++;</span>

            // 尝试基于姓名生成
<span class="nc bnc" id="L238" title="All 4 branches missed.">            if (linkName &amp;&amp; attempts &lt;= 10) {</span>
<span class="nc" id="L239">                username = generateNameBasedUsername(minLength, maxLength, charSet, prefix, suffix,</span>
                        nameStyle, context);
<span class="nc bnc" id="L241" title="All 2 branches missed.">                if (username != null) {</span>
<span class="nc bnc" id="L242" title="All 4 branches missed.">                    if (!isBlacklisted(username, blacklist) &amp;&amp;</span>
<span class="nc bnc" id="L243" title="All 2 branches missed.">                            (!unique || !generatedUsernames.contains(username))) {</span>
<span class="nc" id="L244">                        break;</span>
                    }
                }
            }

            // 随机生成
<span class="nc" id="L250">            username = generateRandomUsername(minLength, maxLength, charSet, prefix, suffix, random);</span>

<span class="nc bnc" id="L252" title="All 4 branches missed.">        } while ((isBlacklisted(username, blacklist) ||</span>
<span class="nc bnc" id="L253" title="All 4 branches missed.">                (unique &amp;&amp; generatedUsernames.contains(username))) &amp;&amp;</span>
                attempts &lt; maxAttempts);

        // 记录生成的用户名（用于唯一性检查）
<span class="nc bnc" id="L257" title="All 2 branches missed.">        if (unique) {</span>
<span class="nc" id="L258">            generatedUsernames.add(username);</span>
        }

<span class="nc" id="L261">        return username;</span>
    }

    /**
     * 基于姓名生成用户名
     */
    private String generateNameBasedUsername(int minLength, int maxLength, String charSet,
            String prefix, String suffix, String nameStyle,
            DataForgeContext context) {

<span class="nc" id="L271">        String name = context.get(CONTEXT_NAME, String.class).orElse(null);</span>
<span class="nc bnc" id="L272" title="All 4 branches missed.">        if (name == null || name.trim().isEmpty()) {</span>
<span class="nc" id="L273">            return null;</span>
        }

<span class="nc" id="L276">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L277">        StringBuilder username = new StringBuilder();</span>

        // 添加前缀
<span class="nc bnc" id="L280" title="All 2 branches missed.">        if (!prefix.isEmpty()) {</span>
<span class="nc" id="L281">            username.append(prefix);</span>
        }

        // 处理姓名
<span class="nc" id="L285">        String nameBase = processName(name, nameStyle);</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">        if (nameBase.isEmpty()) {</span>
<span class="nc" id="L287">            return null;</span>
        }

<span class="nc" id="L290">        username.append(nameBase);</span>

        // 添加随机数字或字符
<span class="nc" id="L293">        int remainingLength = maxLength - username.length() - suffix.length();</span>
<span class="nc bnc" id="L294" title="All 2 branches missed.">        if (remainingLength &gt; 0) {</span>
<span class="nc" id="L295">            int addLength = random.nextInt(1, Math.min(remainingLength + 1, 5));</span>
<span class="nc bnc" id="L296" title="All 2 branches missed.">            for (int i = 0; i &lt; addLength; i++) {</span>
<span class="nc bnc" id="L297" title="All 4 branches missed.">                if (random.nextBoolean() &amp;&amp; charSet.contains(&quot;0&quot;)) {</span>
                    // 添加数字
<span class="nc" id="L299">                    username.append(random.nextInt(10));</span>
                } else {
                    // 添加字符
<span class="nc" id="L302">                    char c = charSet.charAt(random.nextInt(charSet.length()));</span>
<span class="nc" id="L303">                    username.append(c);</span>
                }
            }
        }

        // 添加后缀
<span class="nc bnc" id="L309" title="All 2 branches missed.">        if (!suffix.isEmpty()) {</span>
<span class="nc" id="L310">            username.append(suffix);</span>
        }

        // 长度检查
<span class="nc" id="L314">        String result = username.toString();</span>
<span class="nc bnc" id="L315" title="All 4 branches missed.">        if (result.length() &lt; minLength || result.length() &gt; maxLength) {</span>
<span class="nc" id="L316">            return null;</span>
        }

<span class="nc" id="L319">        return result;</span>
    }

    /**
     * 处理姓名
     */
    private String processName(String name, String nameStyle) {
<span class="nc bnc" id="L326" title="All 4 branches missed.">        if (name == null || name.trim().isEmpty()) {</span>
<span class="nc" id="L327">            return &quot;&quot;;</span>
        }

<span class="nc" id="L330">        name = name.trim();</span>

        // 如果是英文名，直接处理
<span class="nc bnc" id="L333" title="All 2 branches missed.">        if (name.matches(&quot;[a-zA-Z\\s]+&quot;)) {</span>
<span class="nc" id="L334">            return processEnglishName(name, nameStyle);</span>
        }

        // 如果是中文名，转换为拼音
<span class="nc" id="L338">        return processChineseName(name, nameStyle);</span>
    }

    /**
     * 处理英文姓名
     */
    private String processEnglishName(String name, String nameStyle) {
<span class="nc" id="L345">        String[] parts = name.split(&quot;\\s+&quot;);</span>
<span class="nc" id="L346">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

<span class="nc bnc" id="L348" title="All 3 branches missed.">        switch (nameStyle.toUpperCase()) {</span>
            case &quot;INITIALS&quot;:
<span class="nc" id="L350">                StringBuilder initials = new StringBuilder();</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">                for (String part : parts) {</span>
<span class="nc bnc" id="L352" title="All 2 branches missed.">                    if (!part.isEmpty()) {</span>
<span class="nc" id="L353">                        initials.append(Character.toLowerCase(part.charAt(0)));</span>
                    }
                }
<span class="nc" id="L356">                return initials.toString();</span>

            case &quot;PINYIN&quot;:
                // 对于英文名，PINYIN等同于全名
<span class="nc" id="L360">                return name.toLowerCase().replaceAll(&quot;\\s+&quot;, &quot;&quot;);</span>

            case &quot;MIXED&quot;:
            default:
<span class="nc bnc" id="L364" title="All 2 branches missed.">                if (parts.length &gt;= 2) {</span>
                    // 随机选择：首字母+姓氏 或 名字+姓氏首字母
<span class="nc bnc" id="L366" title="All 2 branches missed.">                    if (random.nextBoolean()) {</span>
<span class="nc" id="L367">                        return Character.toLowerCase(parts[0].charAt(0)) + parts[parts.length - 1].toLowerCase();</span>
                    } else {
<span class="nc" id="L369">                        return parts[0].toLowerCase() + Character.toLowerCase(parts[parts.length - 1].charAt(0));</span>
                    }
                } else {
<span class="nc" id="L372">                    return parts[0].toLowerCase();</span>
                }
        }
    }

    /**
     * 处理中文姓名
     */
    private String processChineseName(String name, String nameStyle) {
<span class="nc" id="L381">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

<span class="nc bnc" id="L383" title="All 3 branches missed.">        switch (nameStyle.toUpperCase()) {</span>
            case &quot;INITIALS&quot;:
<span class="nc" id="L385">                StringBuilder initials = new StringBuilder();</span>
<span class="nc bnc" id="L386" title="All 2 branches missed.">                for (char c : name.toCharArray()) {</span>
<span class="nc" id="L387">                    String pinyin = PINYIN_MAP.get(String.valueOf(c));</span>
<span class="nc bnc" id="L388" title="All 4 branches missed.">                    if (pinyin != null &amp;&amp; !pinyin.isEmpty()) {</span>
<span class="nc" id="L389">                        initials.append(pinyin.charAt(0));</span>
                    }
                }
<span class="nc" id="L392">                return initials.toString();</span>

            case &quot;PINYIN&quot;:
<span class="nc" id="L395">                StringBuilder fullPinyin = new StringBuilder();</span>
<span class="nc bnc" id="L396" title="All 2 branches missed.">                for (char c : name.toCharArray()) {</span>
<span class="nc" id="L397">                    String pinyin = PINYIN_MAP.get(String.valueOf(c));</span>
<span class="nc bnc" id="L398" title="All 2 branches missed.">                    if (pinyin != null) {</span>
<span class="nc" id="L399">                        fullPinyin.append(pinyin);</span>
                    }
                }
<span class="nc" id="L402">                return fullPinyin.toString();</span>

            case &quot;MIXED&quot;:
            default:
<span class="nc bnc" id="L406" title="All 2 branches missed.">                if (name.length() &gt;= 2) {</span>
                    // 随机选择：姓氏拼音+名字首字母 或 姓氏首字母+名字拼音
<span class="nc" id="L408">                    String surname = String.valueOf(name.charAt(0));</span>
<span class="nc" id="L409">                    String givenName = name.substring(1);</span>

<span class="nc" id="L411">                    String surnamePinyin = PINYIN_MAP.get(surname);</span>
<span class="nc bnc" id="L412" title="All 2 branches missed.">                    if (surnamePinyin == null)</span>
<span class="nc" id="L413">                        surnamePinyin = surname;</span>

<span class="nc bnc" id="L415" title="All 2 branches missed.">                    if (random.nextBoolean()) {</span>
                        // 姓氏拼音+名字首字母
<span class="nc" id="L417">                        StringBuilder result = new StringBuilder(surnamePinyin);</span>
<span class="nc bnc" id="L418" title="All 2 branches missed.">                        for (char c : givenName.toCharArray()) {</span>
<span class="nc" id="L419">                            String pinyin = PINYIN_MAP.get(String.valueOf(c));</span>
<span class="nc bnc" id="L420" title="All 4 branches missed.">                            if (pinyin != null &amp;&amp; !pinyin.isEmpty()) {</span>
<span class="nc" id="L421">                                result.append(pinyin.charAt(0));</span>
                            }
                        }
<span class="nc" id="L424">                        return result.toString();</span>
                    } else {
                        // 姓氏首字母+名字拼音
<span class="nc" id="L427">                        StringBuilder result = new StringBuilder();</span>
<span class="nc" id="L428">                        result.append(surnamePinyin.charAt(0));</span>
<span class="nc bnc" id="L429" title="All 2 branches missed.">                        for (char c : givenName.toCharArray()) {</span>
<span class="nc" id="L430">                            String pinyin = PINYIN_MAP.get(String.valueOf(c));</span>
<span class="nc bnc" id="L431" title="All 2 branches missed.">                            if (pinyin != null) {</span>
<span class="nc" id="L432">                                result.append(pinyin);</span>
                            }
                        }
<span class="nc" id="L435">                        return result.toString();</span>
                    }
                } else {
<span class="nc" id="L438">                    String pinyin = PINYIN_MAP.get(name);</span>
<span class="nc bnc" id="L439" title="All 2 branches missed.">                    return pinyin != null ? pinyin : name;</span>
                }
        }
    }

    /**
     * 生成随机用户名
     */
    private String generateRandomUsername(int minLength, int maxLength, String charSet,
            String prefix, String suffix, ThreadLocalRandom random) {

<span class="nc" id="L450">        int baseLength = random.nextInt(minLength, maxLength + 1) - prefix.length() - suffix.length();</span>
<span class="nc" id="L451">        baseLength = Math.max(1, baseLength);</span>

<span class="nc" id="L453">        StringBuilder username = new StringBuilder();</span>
<span class="nc" id="L454">        username.append(prefix);</span>

        // 确保第一个字符是字母（如果字符集包含字母）
<span class="nc" id="L457">        String alphaChars = charSet.replaceAll(&quot;[^a-zA-Z]&quot;, &quot;&quot;);</span>
<span class="nc bnc" id="L458" title="All 2 branches missed.">        if (!alphaChars.isEmpty()) {</span>
<span class="nc" id="L459">            username.append(alphaChars.charAt(random.nextInt(alphaChars.length())));</span>
<span class="nc" id="L460">            baseLength--;</span>
        }

        // 生成剩余字符
<span class="nc bnc" id="L464" title="All 2 branches missed.">        for (int i = 0; i &lt; baseLength; i++) {</span>
<span class="nc" id="L465">            username.append(charSet.charAt(random.nextInt(charSet.length())));</span>
        }

<span class="nc" id="L468">        username.append(suffix);</span>

<span class="nc" id="L470">        return username.toString();</span>
    }

    /**
     * 检查是否在黑名单中
     */
    private boolean isBlacklisted(String username, Set&lt;String&gt; blacklist) {
<span class="nc" id="L477">        String lowerUsername = username.toLowerCase();</span>

<span class="nc bnc" id="L479" title="All 2 branches missed.">        for (String blackWord : blacklist) {</span>
<span class="nc bnc" id="L480" title="All 2 branches missed.">            if (lowerUsername.contains(blackWord)) {</span>
<span class="nc" id="L481">                return true;</span>
            }
<span class="nc" id="L483">        }</span>

<span class="nc" id="L485">        return false;</span>
    }

    // 工具方法
    private String getStringParam(Map&lt;String, Object&gt; params, String key, String defaultValue) {
<span class="nc" id="L490">        Object value = params.get(key);</span>
<span class="nc bnc" id="L491" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    private boolean getBooleanParam(Map&lt;String, Object&gt; params, String key, boolean defaultValue) {
<span class="nc" id="L495">        Object value = params.get(key);</span>
<span class="nc bnc" id="L496" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L497">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L499" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L500">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L502">        return defaultValue;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>