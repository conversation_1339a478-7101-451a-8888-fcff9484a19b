dataforge:
  count: 10
  output:
    format: csv
    file: "output/business-test.csv"
    encoding: "UTF-8"
  fields:
    - name: "id"
      type: "uuid"
      params:
        type: "UUID4"
    
    - name: "name"
      type: "name"
      params:
        type: "CN"
        gender_related: "true"
    
    - name: "gender"
      type: "gender"
      params:
        format: "CN"
        male_ratio: "0.6"
    
    - name: "age"
      type: "age"
      params:
        min: "22"
        max: "65"
        distribution: "NORMAL"
    
    - name: "education"
      type: "education"
      params:
        levels: "HIGH_SCHOOL|COLLEGE|BACHELOR|MASTER|PHD"
        distribution: "REALISTIC"
        age_related: "true"
    
    - name: "occupation"
      type: "occupation"
      params:
        industry: "ANY"
        level: "ANY"
    
    - name: "company"
      type: "company"
      params:
        industry: "ANY"
        type: "ANY"
        prefix_region: "true"
        length_range: "8,25"
    
    - name: "uscc"
      type: "uscc"
      params:
        org_type: "ENTERPRISE"
        valid: "true"
    
    - name: "orgcode"
      type: "orgcode"
      params:
        valid: "true"
    
    - name: "phone"
      type: "phone"
      params:
        region: "CN"
        valid: "true"
    
    - name: "email"
      type: "email"
      params:
        domains: "qq.com,163.com,gmail.com,sina.com"
        prefix_name: "true"
    
    - name: "address"
      type: "address"
      params:
        detail_level: "FULL"
        include_zipcode: "true"