<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CsvOutputStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.io</a> &gt; <span class="el_source">CsvOutputStrategy.java</span></div><h1>CsvOutputStrategy.java</h1><pre class="source lang-java linenums">package com.dataforge.io;

import com.dataforge.config.OutputConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

/**
 * CSV输出策略实现
 * 
 * 支持功能：
 * 1. 流式写入CSV文件
 * 2. 自定义分隔符
 * 3. 可选的标题行
 * 4. 字符编码支持
 * 5. 追加模式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
<span class="nc" id="L29">public class CsvOutputStrategy implements OutputStrategy {</span>

<span class="nc" id="L31">    private static final Logger log = LoggerFactory.getLogger(CsvOutputStrategy.class);</span>

    private BufferedWriter writer;
    private String delimiter;
    private boolean includeHeader;
<span class="nc" id="L36">    private boolean headerWritten = false;</span>
<span class="nc" id="L37">    private int recordCount = 0;</span>

    @Override
    public OutputConfig.Format getSupportedFormat() {
<span class="nc" id="L41">        return OutputConfig.Format.CSV;</span>
    }

    @Override
    public void initialize(OutputConfig config, List&lt;String&gt; fieldNames) throws OutputException {
        try {
            // 解析配置
<span class="nc bnc" id="L48" title="All 2 branches missed.">            this.delimiter = config.getCsvDelimiter() != null ? config.getCsvDelimiter() : &quot;,&quot;;</span>
<span class="nc" id="L49">            this.includeHeader = config.isCsvIncludeHeader();</span>

            // 创建文件写入器
<span class="nc bnc" id="L52" title="All 2 branches missed.">            String encoding = config.getEncoding() != null ? config.getEncoding() : &quot;UTF-8&quot;;</span>
<span class="nc" id="L53">            Charset charset = Charset.forName(encoding);</span>

<span class="nc" id="L55">            this.writer = new BufferedWriter(</span>
<span class="nc" id="L56">                    new FileWriter(config.getFile(), charset, config.isAppend()));</span>

<span class="nc" id="L58">            log.info(&quot;CSV output initialized - file: {}, delimiter: '{}', header: {}, encoding: {}&quot;,</span>
<span class="nc" id="L59">                    config.getFile(), delimiter, includeHeader, encoding);</span>

<span class="nc" id="L61">        } catch (IOException e) {</span>
<span class="nc" id="L62">            throw new OutputException(&quot;Failed to initialize CSV output: &quot; + e.getMessage(), e);</span>
<span class="nc" id="L63">        }</span>
<span class="nc" id="L64">    }</span>

    @Override
    public void writeRecord(Map&lt;String, Object&gt; record) throws OutputException {
        try {
            // 写入标题行（仅第一次）
<span class="nc bnc" id="L70" title="All 4 branches missed.">            if (includeHeader &amp;&amp; !headerWritten) {</span>
<span class="nc" id="L71">                writeHeader(record);</span>
<span class="nc" id="L72">                headerWritten = true;</span>
            }

            // 写入数据行
<span class="nc" id="L76">            writeDataRow(record);</span>
<span class="nc" id="L77">            recordCount++;</span>

<span class="nc" id="L79">        } catch (IOException e) {</span>
<span class="nc" id="L80">            throw new OutputException(&quot;Failed to write CSV record: &quot; + e.getMessage(), e);</span>
<span class="nc" id="L81">        }</span>
<span class="nc" id="L82">    }</span>

    @Override
    public void writeRecords(List&lt;Map&lt;String, Object&gt;&gt; records) throws OutputException {
<span class="nc bnc" id="L86" title="All 2 branches missed.">        for (Map&lt;String, Object&gt; record : records) {</span>
<span class="nc" id="L87">            writeRecord(record);</span>
<span class="nc" id="L88">        }</span>
<span class="nc" id="L89">    }</span>

    @Override
    public void finish() throws OutputException {
        try {
<span class="nc bnc" id="L94" title="All 2 branches missed.">            if (writer != null) {</span>
<span class="nc" id="L95">                writer.flush();</span>
<span class="nc" id="L96">                writer.close();</span>
<span class="nc" id="L97">                log.info(&quot;CSV output completed. Total records: {}&quot;, recordCount);</span>
            }
<span class="nc" id="L99">        } catch (IOException e) {</span>
<span class="nc" id="L100">            throw new OutputException(&quot;Failed to close CSV output: &quot; + e.getMessage(), e);</span>
<span class="nc" id="L101">        }</span>
<span class="nc" id="L102">    }</span>

    /**
     * 写入标题行
     */
    private void writeHeader(Map&lt;String, Object&gt; record) throws IOException {
<span class="nc" id="L108">        boolean first = true;</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">        for (String fieldName : record.keySet()) {</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">            if (!first) {</span>
<span class="nc" id="L111">                writer.write(delimiter);</span>
            }
<span class="nc" id="L113">            writer.write(escapeCsvField(fieldName));</span>
<span class="nc" id="L114">            first = false;</span>
<span class="nc" id="L115">        }</span>
<span class="nc" id="L116">        writer.newLine();</span>
<span class="nc" id="L117">    }</span>

    /**
     * 写入数据行
     */
    private void writeDataRow(Map&lt;String, Object&gt; record) throws IOException {
<span class="nc" id="L123">        boolean first = true;</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">        for (Object value : record.values()) {</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">            if (!first) {</span>
<span class="nc" id="L126">                writer.write(delimiter);</span>
            }

<span class="nc bnc" id="L129" title="All 2 branches missed.">            String stringValue = value != null ? value.toString() : &quot;&quot;;</span>
<span class="nc" id="L130">            writer.write(escapeCsvField(stringValue));</span>
<span class="nc" id="L131">            first = false;</span>
<span class="nc" id="L132">        }</span>
<span class="nc" id="L133">        writer.newLine();</span>
<span class="nc" id="L134">    }</span>

    /**
     * 转义CSV字段
     * 
     * 规则：
     * 1. 如果字段包含分隔符、双引号或换行符，则用双引号包围
     * 2. 字段内的双引号需要转义为两个双引号
     */
    private String escapeCsvField(String field) {
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (field == null) {</span>
<span class="nc" id="L145">            return &quot;&quot;;</span>
        }

        // 检查是否需要转义
<span class="nc bnc" id="L149" title="All 2 branches missed.">        boolean needsEscaping = field.contains(delimiter) ||</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">                field.contains(&quot;\&quot;&quot;) ||</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">                field.contains(&quot;\n&quot;) ||</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">                field.contains(&quot;\r&quot;);</span>

<span class="nc bnc" id="L154" title="All 2 branches missed.">        if (needsEscaping) {</span>
            // 转义双引号
<span class="nc" id="L156">            String escaped = field.replace(&quot;\&quot;&quot;, &quot;\&quot;\&quot;&quot;);</span>
            // 用双引号包围
<span class="nc" id="L158">            return &quot;\&quot;&quot; + escaped + &quot;\&quot;&quot;;</span>
        }

<span class="nc" id="L161">        return field;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>