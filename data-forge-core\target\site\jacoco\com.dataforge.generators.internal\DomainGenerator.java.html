<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DomainGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">DomainGenerator.java</span></div><h1>DomainGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 域名生成器
 * 
 * 支持的参数：
 * - tld: 顶级域名 (com|org|net|cn|ANY)
 * - include_subdomain: 是否包含子域名 (true|false)
 * - length: 域名长度范围 (如 &quot;5,15&quot;)
 * - type: 域名类型 (GENERIC|BRAND|DICTIONARY|RANDOM)
 * - file: 自定义域名词典文件路径
 * - international: 是否支持国际化域名 (true|false)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L25">public class DomainGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(DomainGenerator.class);</span>
<span class="nc" id="L28">    private static final Random random = new Random();</span>

    // 顶级域名分类
<span class="nc" id="L31">    private static final Map&lt;String, List&lt;String&gt;&gt; TLD_CATEGORIES = new HashMap&lt;&gt;();</span>

    // 通用域名词汇
<span class="nc" id="L34">    private static final List&lt;String&gt; GENERIC_WORDS = Arrays.asList(</span>
            &quot;tech&quot;, &quot;web&quot;, &quot;app&quot;, &quot;digital&quot;, &quot;online&quot;, &quot;net&quot;, &quot;data&quot;, &quot;cloud&quot;,
            &quot;smart&quot;, &quot;global&quot;, &quot;world&quot;, &quot;international&quot;, &quot;universal&quot;, &quot;mega&quot;,
            &quot;super&quot;, &quot;ultra&quot;, &quot;pro&quot;, &quot;expert&quot;, &quot;master&quot;, &quot;premium&quot;, &quot;elite&quot;,
            &quot;advanced&quot;, &quot;modern&quot;, &quot;future&quot;, &quot;next&quot;, &quot;new&quot;, &quot;best&quot;, &quot;top&quot;);

    // 品牌相关词汇
<span class="nc" id="L41">    private static final List&lt;String&gt; BRAND_WORDS = Arrays.asList(</span>
            &quot;corp&quot;, &quot;inc&quot;, &quot;ltd&quot;, &quot;group&quot;, &quot;company&quot;, &quot;enterprise&quot;, &quot;solutions&quot;,
            &quot;systems&quot;, &quot;services&quot;, &quot;consulting&quot;, &quot;partners&quot;, &quot;associates&quot;,
            &quot;ventures&quot;, &quot;capital&quot;, &quot;holdings&quot;, &quot;industries&quot;, &quot;technologies&quot;,
            &quot;innovations&quot;, &quot;dynamics&quot;, &quot;synergy&quot;, &quot;nexus&quot;, &quot;matrix&quot;, &quot;vertex&quot;);

    // 字典词汇
<span class="nc" id="L48">    private static final List&lt;String&gt; DICTIONARY_WORDS = Arrays.asList(</span>
            &quot;apple&quot;, &quot;google&quot;, &quot;amazon&quot;, &quot;microsoft&quot;, &quot;facebook&quot;, &quot;twitter&quot;,
            &quot;linkedin&quot;, &quot;youtube&quot;, &quot;instagram&quot;, &quot;pinterest&quot;, &quot;reddit&quot;, &quot;github&quot;,
            &quot;stackoverflow&quot;, &quot;wikipedia&quot;, &quot;mozilla&quot;, &quot;adobe&quot;, &quot;oracle&quot;, &quot;ibm&quot;,
            &quot;intel&quot;, &quot;nvidia&quot;, &quot;samsung&quot;, &quot;sony&quot;, &quot;canon&quot;, &quot;nikon&quot;, &quot;tesla&quot;);

    // 子域名前缀
<span class="nc" id="L55">    private static final List&lt;String&gt; SUBDOMAIN_PREFIXES = Arrays.asList(</span>
            &quot;www&quot;, &quot;api&quot;, &quot;app&quot;, &quot;mobile&quot;, &quot;m&quot;, &quot;admin&quot;, &quot;blog&quot;, &quot;shop&quot;, &quot;store&quot;,
            &quot;mail&quot;, &quot;email&quot;, &quot;ftp&quot;, &quot;cdn&quot;, &quot;static&quot;, &quot;img&quot;, &quot;images&quot;, &quot;media&quot;,
            &quot;assets&quot;, &quot;files&quot;, &quot;docs&quot;, &quot;help&quot;, &quot;support&quot;, &quot;dev&quot;, &quot;test&quot;, &quot;staging&quot;,
            &quot;beta&quot;, &quot;alpha&quot;, &quot;demo&quot;, &quot;preview&quot;, &quot;secure&quot;, &quot;ssl&quot;, &quot;vpn&quot;);

    static {
<span class="nc" id="L62">        initializeTldCategories();</span>
<span class="nc" id="L63">    }</span>

    private static void initializeTldCategories() {
        // 通用顶级域名
<span class="nc" id="L67">        TLD_CATEGORIES.put(&quot;GENERIC&quot;, Arrays.asList(</span>
                &quot;com&quot;, &quot;org&quot;, &quot;net&quot;, &quot;edu&quot;, &quot;gov&quot;, &quot;mil&quot;, &quot;int&quot;,
                &quot;info&quot;, &quot;biz&quot;, &quot;name&quot;, &quot;pro&quot;, &quot;museum&quot;, &quot;travel&quot;, &quot;jobs&quot;));

        // 国家顶级域名
<span class="nc" id="L72">        TLD_CATEGORIES.put(&quot;COUNTRY&quot;, Arrays.asList(</span>
                &quot;cn&quot;, &quot;us&quot;, &quot;uk&quot;, &quot;de&quot;, &quot;fr&quot;, &quot;jp&quot;, &quot;au&quot;, &quot;ca&quot;, &quot;ru&quot;, &quot;br&quot;,
                &quot;in&quot;, &quot;kr&quot;, &quot;it&quot;, &quot;es&quot;, &quot;nl&quot;, &quot;se&quot;, &quot;no&quot;, &quot;dk&quot;, &quot;fi&quot;, &quot;pl&quot;));

        // 新通用顶级域名
<span class="nc" id="L77">        TLD_CATEGORIES.put(&quot;NEW_GTLD&quot;, Arrays.asList(</span>
                &quot;tech&quot;, &quot;app&quot;, &quot;web&quot;, &quot;online&quot;, &quot;site&quot;, &quot;store&quot;, &quot;shop&quot;, &quot;blog&quot;,
                &quot;news&quot;, &quot;media&quot;, &quot;photo&quot;, &quot;video&quot;, &quot;music&quot;, &quot;game&quot;, &quot;sport&quot;,
                &quot;health&quot;, &quot;food&quot;, &quot;travel&quot;, &quot;hotel&quot;, &quot;car&quot;, &quot;auto&quot;, &quot;finance&quot;));

        // 中文顶级域名
<span class="nc" id="L83">        TLD_CATEGORIES.put(&quot;CHINESE&quot;, Arrays.asList(</span>
                &quot;中国&quot;, &quot;公司&quot;, &quot;网络&quot;, &quot;组织&quot;, &quot;政府&quot;, &quot;教育&quot;, &quot;商业&quot;, &quot;信息&quot;));
<span class="nc" id="L85">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L89">        return &quot;domain&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L94">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L101">            String tld = config.getParam(&quot;tld&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L102">            boolean includeSubdomain = Boolean</span>
<span class="nc" id="L103">                    .parseBoolean(config.getParam(&quot;include_subdomain&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L104">            String lengthRange = config.getParam(&quot;length&quot;, String.class, &quot;5,15&quot;);</span>
<span class="nc" id="L105">            String type = config.getParam(&quot;type&quot;, String.class, &quot;GENERIC&quot;);</span>
<span class="nc" id="L106">            boolean international = Boolean.parseBoolean(config.getParam(&quot;international&quot;, String.class, &quot;false&quot;));</span>

            // 解析长度范围
<span class="nc" id="L109">            String[] lengthParts = lengthRange.split(&quot;,&quot;);</span>
<span class="nc" id="L110">            int minLength = Integer.parseInt(lengthParts[0].trim());</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">            int maxLength = lengthParts.length &gt; 1 ? Integer.parseInt(lengthParts[1].trim()) : minLength;</span>

            // 生成域名
<span class="nc" id="L114">            String domain = generateDomain(tld, includeSubdomain, minLength, maxLength, type, international, config);</span>

            // 将域名信息存入上下文
<span class="nc" id="L117">            context.put(&quot;domain&quot;, domain);</span>
<span class="nc" id="L118">            context.put(&quot;domain_tld&quot;, extractTld(domain));</span>
<span class="nc" id="L119">            context.put(&quot;domain_sld&quot;, extractSld(domain));</span>

<span class="nc" id="L121">            logger.debug(&quot;Generated domain: {}&quot;, domain);</span>
<span class="nc" id="L122">            return domain;</span>

<span class="nc" id="L124">        } catch (Exception e) {</span>
<span class="nc" id="L125">            logger.error(&quot;Error generating domain&quot;, e);</span>
<span class="nc" id="L126">            return &quot;example.com&quot;;</span>
        }
    }

    private String generateDomain(String tld, boolean includeSubdomain, int minLength, int maxLength,
            String type, boolean international, FieldConfig config) {

<span class="nc" id="L133">        StringBuilder domain = new StringBuilder();</span>

        // 1. 生成子域名（如果需要）
<span class="nc bnc" id="L136" title="All 4 branches missed.">        if (includeSubdomain &amp;&amp; random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L137">            String subdomain = generateSubdomain();</span>
<span class="nc" id="L138">            domain.append(subdomain).append(&quot;.&quot;);</span>
        }

        // 2. 生成二级域名
<span class="nc" id="L142">        String sld = generateSecondLevelDomain(type, minLength, maxLength, config);</span>
<span class="nc" id="L143">        domain.append(sld);</span>

        // 3. 生成顶级域名
<span class="nc" id="L146">        String topLevelDomain = generateTopLevelDomain(tld, international);</span>
<span class="nc" id="L147">        domain.append(&quot;.&quot;).append(topLevelDomain);</span>

<span class="nc" id="L149">        return domain.toString();</span>
    }

    private String generateSubdomain() {
<span class="nc" id="L153">        return SUBDOMAIN_PREFIXES.get(random.nextInt(SUBDOMAIN_PREFIXES.size()));</span>
    }

    private String generateSecondLevelDomain(String type, int minLength, int maxLength, FieldConfig config) {
        // 加载自定义词典
<span class="nc" id="L158">        List&lt;String&gt; customWords = loadCustomWords(config);</span>
<span class="nc bnc" id="L159" title="All 2 branches missed.">        if (!customWords.isEmpty()) {</span>
<span class="nc" id="L160">            return selectFromWords(customWords, minLength, maxLength);</span>
        }

        // 根据类型生成
<span class="nc bnc" id="L164" title="All 4 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;GENERIC&quot;:
<span class="nc" id="L166">                return generateGenericDomain(minLength, maxLength);</span>

            case &quot;BRAND&quot;:
<span class="nc" id="L169">                return generateBrandDomain(minLength, maxLength);</span>

            case &quot;DICTIONARY&quot;:
<span class="nc" id="L172">                return generateDictionaryDomain(minLength, maxLength);</span>

            case &quot;RANDOM&quot;:
            default:
<span class="nc" id="L176">                return generateRandomDomain(minLength, maxLength);</span>
        }
    }

    private List&lt;String&gt; loadCustomWords(FieldConfig config) {
<span class="nc" id="L181">        String customFile = config.getParam(&quot;file&quot;, String.class, null);</span>
<span class="nc bnc" id="L182" title="All 2 branches missed.">        if (customFile != null) {</span>
            try {
<span class="nc" id="L184">                return DataLoader.loadDataFromFile(customFile);</span>
<span class="nc" id="L185">            } catch (Exception e) {</span>
<span class="nc" id="L186">                logger.warn(&quot;Failed to load custom domain file: {}&quot;, customFile, e);</span>
            }
        }
<span class="nc" id="L189">        return new ArrayList&lt;&gt;();</span>
    }

    private String selectFromWords(List&lt;String&gt; words, int minLength, int maxLength) {
        // 过滤符合长度要求的词汇
<span class="nc" id="L194">        List&lt;String&gt; validWords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L195" title="All 2 branches missed.">        for (String word : words) {</span>
<span class="nc bnc" id="L196" title="All 4 branches missed.">            if (word.length() &gt;= minLength &amp;&amp; word.length() &lt;= maxLength) {</span>
<span class="nc" id="L197">                validWords.add(word);</span>
            }
<span class="nc" id="L199">        }</span>

<span class="nc bnc" id="L201" title="All 2 branches missed.">        if (validWords.isEmpty()) {</span>
<span class="nc" id="L202">            return generateRandomDomain(minLength, maxLength);</span>
        }

<span class="nc" id="L205">        return validWords.get(random.nextInt(validWords.size()));</span>
    }

    private String generateGenericDomain(int minLength, int maxLength) {
        // 组合通用词汇
<span class="nc bnc" id="L210" title="All 4 branches missed.">        if (random.nextBoolean() &amp;&amp; GENERIC_WORDS.size() &gt; 1) {</span>
<span class="nc" id="L211">            String word1 = GENERIC_WORDS.get(random.nextInt(GENERIC_WORDS.size()));</span>
<span class="nc" id="L212">            String word2 = GENERIC_WORDS.get(random.nextInt(GENERIC_WORDS.size()));</span>
<span class="nc" id="L213">            String combined = word1 + word2;</span>

<span class="nc bnc" id="L215" title="All 4 branches missed.">            if (combined.length() &gt;= minLength &amp;&amp; combined.length() &lt;= maxLength) {</span>
<span class="nc" id="L216">                return combined;</span>
            }
        }

        // 单个通用词汇
<span class="nc" id="L221">        List&lt;String&gt; validWords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L222" title="All 2 branches missed.">        for (String word : GENERIC_WORDS) {</span>
<span class="nc bnc" id="L223" title="All 4 branches missed.">            if (word.length() &gt;= minLength &amp;&amp; word.length() &lt;= maxLength) {</span>
<span class="nc" id="L224">                validWords.add(word);</span>
            }
<span class="nc" id="L226">        }</span>

<span class="nc bnc" id="L228" title="All 2 branches missed.">        if (!validWords.isEmpty()) {</span>
<span class="nc" id="L229">            return validWords.get(random.nextInt(validWords.size()));</span>
        }

<span class="nc" id="L232">        return generateRandomDomain(minLength, maxLength);</span>
    }

    private String generateBrandDomain(int minLength, int maxLength) {
        // 组合品牌词汇
<span class="nc bnc" id="L237" title="All 4 branches missed.">        if (random.nextBoolean() &amp;&amp; BRAND_WORDS.size() &gt; 1) {</span>
<span class="nc" id="L238">            String word1 = BRAND_WORDS.get(random.nextInt(BRAND_WORDS.size()));</span>
<span class="nc" id="L239">            String word2 = BRAND_WORDS.get(random.nextInt(BRAND_WORDS.size()));</span>
<span class="nc" id="L240">            String combined = word1 + word2;</span>

<span class="nc bnc" id="L242" title="All 4 branches missed.">            if (combined.length() &gt;= minLength &amp;&amp; combined.length() &lt;= maxLength) {</span>
<span class="nc" id="L243">                return combined;</span>
            }
        }

        // 单个品牌词汇
<span class="nc" id="L248">        List&lt;String&gt; validWords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L249" title="All 2 branches missed.">        for (String word : BRAND_WORDS) {</span>
<span class="nc bnc" id="L250" title="All 4 branches missed.">            if (word.length() &gt;= minLength &amp;&amp; word.length() &lt;= maxLength) {</span>
<span class="nc" id="L251">                validWords.add(word);</span>
            }
<span class="nc" id="L253">        }</span>

<span class="nc bnc" id="L255" title="All 2 branches missed.">        if (!validWords.isEmpty()) {</span>
<span class="nc" id="L256">            return validWords.get(random.nextInt(validWords.size()));</span>
        }

<span class="nc" id="L259">        return generateRandomDomain(minLength, maxLength);</span>
    }

    private String generateDictionaryDomain(int minLength, int maxLength) {
        // 使用字典词汇
<span class="nc" id="L264">        List&lt;String&gt; validWords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L265" title="All 2 branches missed.">        for (String word : DICTIONARY_WORDS) {</span>
<span class="nc bnc" id="L266" title="All 4 branches missed.">            if (word.length() &gt;= minLength &amp;&amp; word.length() &lt;= maxLength) {</span>
<span class="nc" id="L267">                validWords.add(word);</span>
            }
<span class="nc" id="L269">        }</span>

<span class="nc bnc" id="L271" title="All 2 branches missed.">        if (!validWords.isEmpty()) {</span>
<span class="nc" id="L272">            return validWords.get(random.nextInt(validWords.size()));</span>
        }

<span class="nc" id="L275">        return generateRandomDomain(minLength, maxLength);</span>
    }

    private String generateRandomDomain(int minLength, int maxLength) {
<span class="nc" id="L279">        int length = minLength + random.nextInt(maxLength - minLength + 1);</span>
<span class="nc" id="L280">        StringBuilder domain = new StringBuilder();</span>

        // 确保第一个字符是字母
<span class="nc" id="L283">        domain.append((char) ('a' + random.nextInt(26)));</span>

        // 生成剩余字符
<span class="nc bnc" id="L286" title="All 2 branches missed.">        for (int i = 1; i &lt; length; i++) {</span>
<span class="nc bnc" id="L287" title="All 6 branches missed.">            if (random.nextDouble() &lt; 0.1 &amp;&amp; i &gt; 1 &amp;&amp; i &lt; length - 1) {</span>
                // 10%概率添加连字符（不在开头和结尾）
<span class="nc" id="L289">                domain.append('-');</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">            } else if (random.nextDouble() &lt; 0.2) {</span>
                // 20%概率添加数字
<span class="nc" id="L292">                domain.append(random.nextInt(10));</span>
            } else {
                // 添加字母
<span class="nc" id="L295">                domain.append((char) ('a' + random.nextInt(26)));</span>
            }
        }

        // 确保不以连字符结尾
<span class="nc" id="L300">        String result = domain.toString();</span>
<span class="nc bnc" id="L301" title="All 2 branches missed.">        if (result.endsWith(&quot;-&quot;)) {</span>
<span class="nc" id="L302">            result = result.substring(0, result.length() - 1) + (char) ('a' + random.nextInt(26));</span>
        }

<span class="nc" id="L305">        return result;</span>
    }

    private String generateTopLevelDomain(String tld, boolean international) {
<span class="nc bnc" id="L309" title="All 2 branches missed.">        if (!&quot;ANY&quot;.equalsIgnoreCase(tld)) {</span>
<span class="nc" id="L310">            return tld.toLowerCase();</span>
        }

        // 选择TLD类别
<span class="nc" id="L314">        List&lt;String&gt; categories = new ArrayList&lt;&gt;(TLD_CATEGORIES.keySet());</span>
<span class="nc bnc" id="L315" title="All 2 branches missed.">        if (!international) {</span>
<span class="nc" id="L316">            categories.remove(&quot;CHINESE&quot;); // 如果不支持国际化，移除中文TLD</span>
        }

<span class="nc" id="L319">        String category = categories.get(random.nextInt(categories.size()));</span>
<span class="nc" id="L320">        List&lt;String&gt; tlds = TLD_CATEGORIES.get(category);</span>

<span class="nc" id="L322">        return tlds.get(random.nextInt(tlds.size()));</span>
    }

    private String extractTld(String domain) {
<span class="nc" id="L326">        int lastDot = domain.lastIndexOf('.');</span>
<span class="nc bnc" id="L327" title="All 4 branches missed.">        if (lastDot &gt; 0 &amp;&amp; lastDot &lt; domain.length() - 1) {</span>
<span class="nc" id="L328">            return domain.substring(lastDot + 1);</span>
        }
<span class="nc" id="L330">        return &quot;unknown&quot;;</span>
    }

    private String extractSld(String domain) {
        // 移除子域名
<span class="nc" id="L335">        String withoutSubdomain = domain;</span>
<span class="nc" id="L336">        String[] parts = domain.split(&quot;\\.&quot;);</span>
<span class="nc bnc" id="L337" title="All 2 branches missed.">        if (parts.length &gt; 2) {</span>
            // 假设最后两部分是SLD.TLD
<span class="nc" id="L339">            withoutSubdomain = parts[parts.length - 2] + &quot;.&quot; + parts[parts.length - 1];</span>
        }

<span class="nc" id="L342">        int lastDot = withoutSubdomain.lastIndexOf('.');</span>
<span class="nc bnc" id="L343" title="All 2 branches missed.">        if (lastDot &gt; 0) {</span>
<span class="nc" id="L344">            return withoutSubdomain.substring(0, lastDot);</span>
        }
<span class="nc" id="L346">        return withoutSubdomain;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>