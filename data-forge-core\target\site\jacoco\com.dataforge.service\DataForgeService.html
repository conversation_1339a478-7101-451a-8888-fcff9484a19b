<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataForgeService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.service</a> &gt; <span class="el_class">DataForgeService</span></div><h1>DataForgeService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">541 of 541</td><td class="ctr2">0%</td><td class="bar">46 of 46</td><td class="ctr2">0%</td><td class="ctr1">37</td><td class="ctr2">37</td><td class="ctr1">123</td><td class="ctr2">123</td><td class="ctr1">14</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a3"><a href="DataForgeService.java.html#L226" class="el_method">generateDataConcurrently(ForgeConfig, OutputStrategy, List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="127" alt="127"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h0">29</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="DataForgeService.java.html#L77" class="el_method">generateData(ForgeConfig)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="72" alt="72"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="DataForgeService.java.html#L186" class="el_method">generateDataSequentially(ForgeConfig, OutputStrategy, List)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="70" alt="70"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">14</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="DataForgeService.java.html#L311" class="el_method">generateSingleRecord(List, DataForgeContext, Random)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="68" alt="68"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h2">17</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a13"><a href="DataForgeService.java.html#L124" class="el_method">validateConfig(ForgeConfig)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="48" alt="48"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a11"><a href="DataForgeService.java.html#L150" class="el_method">prepareOutputStrategy(OutputConfig)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="34" alt="34"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="DataForgeService.java.html#L287" class="el_method">generateRecordsBatch(List, int, int, Random)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="32" alt="32"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="DataForgeService.java.html#L60" class="el_method">DataForgeService(GeneratorFactory, List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="25" alt="25"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="DataForgeService.java.html#L365" class="el_method">getAvailableOutputFormats()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="23" alt="23"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="DataForgeService.java.html#L168" class="el_method">extractFieldNames(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="22" alt="22"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a10"><a href="DataForgeService.java.html#L248" class="el_method">lambda$generateDataConcurrently$0(ForgeConfig, int, int, Random)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a7"><a href="DataForgeService.java.html#L347" class="el_method">getAvailableGeneratorTypes()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="DataForgeService.java.html#L356" class="el_method">getGeneratorInfo()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="DataForgeService.java.html#L48" class="el_method">static {...}</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>