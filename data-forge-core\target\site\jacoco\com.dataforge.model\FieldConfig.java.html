<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.model</a> &gt; <span class="el_source">FieldConfig.java</span></div><h1>FieldConfig.java</h1><pre class="source lang-java linenums">package com.dataforge.model;

import java.util.HashMap;
import java.util.Map;
import jakarta.validation.constraints.NotBlank;

/**
 * 字段配置基类。
 * 
 * &lt;p&gt;
 * 所有具体的字段配置类都应该继承此基类。
 * 提供了通用的字段配置属性和参数处理机制。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public abstract class FieldConfig {

    /**
     * 字段名称，在输出中作为列名或字段名使用。
     */
    @NotBlank(message = &quot;Field name cannot be blank&quot;)
    private String name;

    /**
     * 数据类型标识符，用于匹配对应的数据生成器。
     */
    @NotBlank(message = &quot;Field type cannot be blank&quot;)
    private String type;

    /**
     * 字段描述，可选，用于文档和调试。
     */
    private String description;

    /**
     * 是否为必填字段，默认为true。
     */
<span class="pc" id="L39">    private boolean required = true;</span>

    /**
     * 生成参数映射，用于存储生成器特定的配置参数。
     * 使用 Map&lt;String, Object&gt; 来接收灵活的参数配置。
     */
<span class="pc" id="L45">    private Map&lt;String, Object&gt; params = new HashMap&lt;&gt;();</span>

    /**
     * 默认构造函数。
     */
<span class="nc" id="L50">    protected FieldConfig() {</span>
<span class="nc" id="L51">    }</span>

    /**
     * 构造函数。
     * 
     * @param name 字段名称
     * @param type 数据类型标识符
     */
<span class="fc" id="L59">    protected FieldConfig(String name, String type) {</span>
<span class="fc" id="L60">        this.name = name;</span>
<span class="fc" id="L61">        this.type = type;</span>
<span class="fc" id="L62">    }</span>

    /**
     * 获取字段名称。
     * 
     * @return 字段名称
     */
    public String getName() {
<span class="nc" id="L70">        return name;</span>
    }

    /**
     * 设置字段名称。
     * 
     * @param name 字段名称
     */
    public void setName(String name) {
<span class="nc" id="L79">        this.name = name;</span>
<span class="nc" id="L80">    }</span>

    /**
     * 获取数据类型标识符。
     * 
     * @return 数据类型标识符
     */
    public String getType() {
<span class="nc" id="L88">        return type;</span>
    }

    /**
     * 设置数据类型标识符。
     * 
     * @param type 数据类型标识符
     */
    public void setType(String type) {
<span class="nc" id="L97">        this.type = type;</span>
<span class="nc" id="L98">    }</span>

    /**
     * 获取字段描述。
     * 
     * @return 字段描述
     */
    public String getDescription() {
<span class="nc" id="L106">        return description;</span>
    }

    /**
     * 设置字段描述。
     * 
     * @param description 字段描述
     */
    public void setDescription(String description) {
<span class="nc" id="L115">        this.description = description;</span>
<span class="nc" id="L116">    }</span>

    /**
     * 检查是否为必填字段。
     * 
     * @return 如果是必填字段返回true，否则返回false
     */
    public boolean isRequired() {
<span class="nc" id="L124">        return required;</span>
    }

    /**
     * 设置是否为必填字段。
     * 
     * @param required 是否必填
     */
    public void setRequired(boolean required) {
<span class="nc" id="L133">        this.required = required;</span>
<span class="nc" id="L134">    }</span>

    /**
     * 获取生成参数映射。
     * 
     * @return 参数映射
     */
    public Map&lt;String, Object&gt; getParams() {
<span class="fc" id="L142">        return params;</span>
    }

    /**
     * 设置生成参数映射。
     * 
     * @param params 参数映射
     */
    public void setParams(Map&lt;String, Object&gt; params) {
<span class="pc bpc" id="L151" title="1 of 2 branches missed.">        this.params = params != null ? params : new HashMap&lt;&gt;();</span>
<span class="fc" id="L152">    }</span>

    /**
     * 获取指定参数的值。
     * 
     * @param key 参数键
     * @return 参数值，如果不存在返回null
     */
    public Object getParam(String key) {
<span class="nc" id="L161">        return params.get(key);</span>
    }

    /**
     * 获取指定参数的值，并转换为指定类型。
     * 
     * @param &lt;T&gt;          期望的参数类型
     * @param key          参数键
     * @param type         期望的参数类型
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或类型不匹配返回默认值
     */
    @SuppressWarnings(&quot;unchecked&quot;)
    public &lt;T&gt; T getParam(String key, Class&lt;T&gt; type, T defaultValue) {
<span class="nc" id="L175">        Object value = params.get(key);</span>
<span class="nc bnc" id="L176" title="All 4 branches missed.">        if (value != null &amp;&amp; type.isInstance(value)) {</span>
<span class="nc" id="L177">            return (T) value;</span>
        }
<span class="nc" id="L179">        return defaultValue;</span>
    }

    /**
     * 设置参数值。
     * 
     * @param key   参数键
     * @param value 参数值
     */
    public void setParam(String key, Object value) {
<span class="nc" id="L189">        params.put(key, value);</span>
<span class="nc" id="L190">    }</span>

    /**
     * 检查是否包含指定参数。
     * 
     * @param key 参数键
     * @return 如果包含该参数返回true，否则返回false
     */
    public boolean hasParam(String key) {
<span class="nc" id="L199">        return params.containsKey(key);</span>
    }

    /**
     * 移除指定参数。
     * 
     * @param key 参数键
     * @return 被移除的参数值，如果不存在返回null
     */
    public Object removeParam(String key) {
<span class="nc" id="L209">        return params.remove(key);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L214">        return String.format(&quot;%s{name='%s', type='%s', required=%s, params=%s}&quot;,</span>
<span class="nc" id="L215">                getClass().getSimpleName(), name, type, required, params);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>