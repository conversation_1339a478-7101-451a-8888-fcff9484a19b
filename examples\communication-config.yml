dataforge:
  count: 10
  output:
    format: csv
    file: "output/communication-test.csv"
  fields:
    # 验证码生成器测试
    - name: "email_code"
      type: "verification_code"
      params:
        type: "EMAIL"
        length: 6
        chars: "NUMERIC"
        
    - name: "sms_code"
      type: "verification_code"
      params:
        type: "SMS"
        length: 6
        exclude_ambiguous: true
        
    - name: "captcha_code"
      type: "verification_code"
      params:
        type: "CAPTCHA"
        length: 4
        chars: "ALPHANUMERIC"
        case_sensitive: false
        
    - name: "alphanumeric_code"
      type: "verification_code"
      params:
        type: "ALPHANUMERIC"
        length: 8
        chars: "ALPHANUMERIC_UPPER"
        
    # 传真号码生成器测试
    - name: "fax_china"
      type: "fax"
      params:
        region: "CN"
        format: "STANDARD"
        include_extension: false
        
    - name: "fax_us"
      type: "fax"
      params:
        region: "US"
        format: "INTERNATIONAL"
        include_extension: true
        extension_length: 4
        
    - name: "fax_compact"
      type: "fax"
      params:
        region: "CN"
        area_code: "010"
        format: "COMPACT"
        
    # 固话号码生成器测试
    - name: "landline_beijing"
      type: "landline"
      params:
        region: "CN"
        area_code: "010"
        format: "STANDARD"
        
    - name: "landline_us"
      type: "landline"
      params:
        region: "US"
        format: "PARENTHESES"
        include_extension: true
        extension_length: 3
        
    - name: "landline_uk"
      type: "landline"
      params:
        region: "UK"
        format: "INTERNATIONAL"
        number_length: 7
        
    # 文件路径生成器测试
    - name: "windows_path"
      type: "filepath"
      params:
        os: "WINDOWS"
        type: "ABSOLUTE"
        depth: 4
        include_filename: true
        extension: "DOCUMENT"
        
    - name: "unix_path"
      type: "filepath"
      params:
        os: "UNIX"
        type: "ABSOLUTE"
        depth: 3
        include_filename: true
        extension: "CODE"
        
    - name: "relative_path"
      type: "filepath"
      params:
        os: "ANY"
        type: "RELATIVE"
        depth: 2
        include_filename: true
        extension: "IMAGE"
        include_spaces: true
        
    - name: "unc_path"
      type: "filepath"
      params:
        os: "WINDOWS"
        type: "UNC"
        depth: 2
        include_filename: true
        extension: "DATA"
        
    # MIME类型生成器测试
    - name: "text_mime"
      type: "mimetype"
      params:
        category: "TEXT"
        include_parameters: true
        charset: "UTF-8"
        
    - name: "image_mime"
      type: "mimetype"
      params:
        category: "IMAGE"
        
    - name: "application_mime"
      type: "mimetype"
      params:
        category: "APPLICATION"
        include_parameters: true
        
    - name: "random_mime"
      type: "mimetype"
      params:
        category: "ANY"
        include_parameters: false
        
    # 端口号生成器测试
    - name: "well_known_port"
      type: "port"
      params:
        type: "WELL_KNOWN"
        exclude_reserved: true
        
    - name: "http_port"
      type: "port"
      params:
        service: "HTTP"
        protocol: "TCP"
        
    - name: "database_port"
      type: "port"
      params:
        service: "DATABASE"
        
    - name: "dynamic_port"
      type: "port"
      params:
        type: "DYNAMIC"
        min: 50000
        max: 60000
        
    - name: "common_port"
      type: "port"
      params:
        type: "COMMON"
        exclude_reserved: true