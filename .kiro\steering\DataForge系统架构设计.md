<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
------------------------------------------------------------------------------------->
### **1. 诊断与分析 (Diagnosis & Analysis)**

- **核心问题定位：** 核心任务是将这份详细的**功能规格说明 (Functional Specification)** 转化为一套具体、可落地的 **Java Spring Boot 技术架构**，确保架构能够支撑当前所有功能，并为未来的扩展留下充足空间。

- **上下文澄清：** 该设计文档描述了一个双重用途的工具：

    1. **命令行工具 (CLI):** 供测试人员和CI/CD流水线直接调用。

    2. **可编程库 (Library):** 可被其他Java项目（如自动化测试框架）依赖和调用。  
        我们的架构设计必须同时优雅地支持这两种使用场景。

- **当前阶段判定：** 我们目前正处于软件开发生命周期（SDLC）的 **“2. 技术设计”** 阶段。此阶段的核心产出物就是一份清晰的架构蓝图和关键模块的设计方案。

### **2. 解决方案与设计 (Solution & Design)**

基于**模块化、可扩展性、关注点分离**的设计哲学，我为你规划了如下的分层架构。

#### **2.1. 整体架构分层**

我们将采用经典的四层架构模型，确保各层职责单一，边界清晰。

codeCode

```
+-------------------------------------------------+
|               Presentation Layer                |  <-- CLI接口层 (Spring Shell / Picocli)
| (接口层: 负责与外部交互)                        |
+-------------------------------------------------+
|               Application Layer                 |  <-- 应用服务层 (DataForgeService)
| (应用层: 编排业务逻辑，协调领域层和基础设施层)    |
+-------------------------------------------------+
|                  Domain Layer                   |  <-- 领域核心层 (Generators, Factories, Models)
| (领域层: 包含核心业务逻辑和规则)                |
+-------------------------------------------------+
|              Infrastructure Layer               |  <-- 基础设施层 (Config, IO, DB)
| (基础设施层: 提供技术实现，如文件读写、配置解析)  |
+-------------------------------------------------+
```

#### **2.2. 项目模块与包结构设计**

一个清晰的包结构是项目可维护性的基石。我建议采用如下的 Maven/Gradle 模块结构：

codeCode

```
data-forge/
├── pom.xml
├── data-forge-core/                    # 核心库，可被其他项目独立依赖
│   ├── src/main/java/com/dataforge/
│   │   ├── core/                       # 核心引擎、上下文、工厂
│   │   │   ├── DataForgeContext.java   # 生成上下文，用于处理数据关联
│   │   │   ├── GeneratorFactory.java   # 生成器工厂
│   │   │   └── ...
│   │   ├── model/                      # 数据模型
│   │   │   ├── FieldConfig.java        # 字段配置模型
│   │   │   └── ...
│   │   ├── generators/                 # 所有数据生成器的接口与实现
│   │   │   ├── spi/                    # SPI (Service Provider Interface)
│   │   │   │   └── DataGenerator.java  # 统一的生成器接口
│   │   │   ├── internal/               # 内置生成器实现
│   │   │   │   ├── IdCardGenerator.java
│   │   │   │   ├── NameGenerator.java
│   │   │   │   └── ...
│   │   │   └── ...
│   │   ├── config/                     # 配置加载与解析
│   │   │   ├── AppConfig.java          # Spring @Configuration
│   │   │   └── ForgeConfig.java        # @ConfigurationProperties
│   │   ├── io/                         # 数据输出
│   │   │   ├── OutputStrategy.java     # 输出策略接口
│   │   │   ├── CsvOutputStrategy.java  # CSV输出实现
│   │   │   └── JsonOutputStrategy.java # JSON输出实现
│   │   └── validation/                 # 数据校验
│   │       ├── Validator.java          # 校验器接口
│   │       └── LuhnValidator.java      # Luhn算法校验器
│   └── ...
└── data-forge-cli/                     # CLI应用，依赖 data-forge-core
    └── src/main/java/com/dataforge/cli/
        ├── DataForgeCliApplication.java# Spring Boot 启动类
        ├── commands/                   # CLI 命令
        │   └── GenerateCommand.java    # 主生成命令
        └── ...
```

**设计阐述 (The "Why"):**

- **data-forge-core 模块化:** 将核心逻辑封装成一个独立的core模块，它不依赖于任何展示层技术（如CLI）。这使得core模块可以轻松地被发布为一个独立的JAR包，供其他测试框架或Java应用作为库来使用，完美满足了双重用途的需求。

- **data-forge-cli 模块:** 仅负责命令行交互的逻辑。它依赖core模块，并调用其服务来完成实际工作。这种分离使得未来如果需要提供Web界面（例如一个data-forge-web模块），我们只需更换展示层，核心逻辑保持不变。

#### **2.3. 核心组件设计与代码示例**

这是整个架构的灵魂，直接决定了系统的可扩展性。我们将使用Java的 ServiceLoader (SPI) 机制。

- **DataGenerator.java 接口:** 定义所有生成器的统一契约。

codeJava

```
// package: com.dataforge.generators.spi

/**
 * 所有数据生成器的统一接口。
 * @param <T> 生成的数据类型 (e.g., String, Integer)
 * @param <C> 该生成器特定的配置类
 */
public interface DataGenerator<T, C extends FieldConfig> {

    /**
     * 返回该生成器能处理的类型名称，用于CLI或配置文件中的匹配。
     * 例如: "idcard", "name"
     */
    String getType();

    /**
     * 根据配置和上下文生成数据。
     * @param config 特定于此生成器的配置对象
     * @param context 生成上下文，用于处理字段间的关联
     * @return 生成的数据
     */
    T generate(C config, DataForgeContext context);
    
    /**
     * 返回此生成器对应的配置类类型。
     * 用于配置反序列化。
     */
    Class<C> getConfigClass();
}
```

- **SPI 配置:** 在 data-forge-core 的 resources/META-INF/services/ 目录下，创建一个文件名为 com.dataforge.generators.spi.DataGenerator，文件内容为所有内置生成器的全限定类名，每行一个。

codeCode

```
com.dataforge.generators.internal.IdCardGenerator
com.dataforge.generators.internal.NameGenerator
...
```

**设计阐述 (The "Why"):**

- **解耦:** GeneratorFactory 无需硬编码知道所有生成器的实现类。

- **高扩展性:** 第三方开发者可以实现DataGenerator接口，并将他们的实现打包成一个JAR。用户只需将这个JAR放到classpath下，DataForge就能自动发现并使用新的生成器，无需修改任何核心代码。这完全符合设计文档中对扩展性的要求。

要实现“身份证号与年龄、性别、地址的关联”，我们需要一个在单次生成任务中传递状态的载体。

codeJava

```
// package: com.dataforge.core

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据生成上下文。
 * 在一次生成请求的生命周期内共享数据，用于解决字段间的关联性问题。
 */
public class DataForgeContext {
    // 使用 ConcurrentHashMap 保证多线程生成时的线程安全
    private final Map<String, Object> contextMap = new ConcurrentHashMap<>();

    public void put(String key, Object value) {
        contextMap.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <V> Optional<V> get(String key, Class<V> type) {
        Object value = contextMap.get(key);
        if (value != null && type.isInstance(value)) {
            return Optional.of((V) value);
        }
        return Optional.empty();
    }
    
    // ... 其他辅助方法
}
```

**使用示例:**

1. **IdCardGenerator** 在生成身份证后，会将 birthDate, gender, regionCode 放入 DataForgeContext。

2. **AgeGenerator** 会先检查 Context 中是否有 birthDate，如果有，则直接计算年龄，否则再随机生成。

3. **AddressGenerator** 会检查 Context 中是否有 regionCode，如果有，则生成该地区下的地址。

利用 Spring Boot 的强项来优雅地处理复杂的 YAML/JSON 配置。

- **application.yml (或用户指定的配置文件):**

codeYaml

```
dataforge:
  count: 10
  output:
    format: csv
    file: "output/users.csv"
  fields:
    - name: "userId"
      type: "uuid"
      params:
        type: "UUID4"
    - name: "idCard"
      type: "idcard"
      params:
        region: "330100"
        birth_date_range: "1990-01-01,2000-12-31"
    - name: "age"
      type: "age"
      # age 会自动关联 idCard 的出生日期
    - name: "email"
      type: "email"
      params:
        prefix_name: true # 尝试使用姓名拼音
```

- **对应的 Java 配置类:**

codeJava

```
// package: com.dataforge.config

@ConfigurationProperties(prefix = "dataforge")
public class ForgeConfig {
    private int count;
    private OutputConfig output;
    private List<FieldConfigWrapper> fields;
    // Getters and Setters...
}

// ... OutputConfig.java, FieldConfigWrapper.java 等

// FieldConfigWrapper 用于处理不同类型的 params
public class FieldConfigWrapper {
    private String name;
    private String type;
    // 使用 Map<String, Object> 来接收灵活的 params
    private Map<String, Object> params = new HashMap<>(); 
    // Getters and Setters...
}
```

**设计阐述 (The "Why"):** Spring Boot 的配置绑定能力非常强大，能自动将 YAML/JSON 映射到 POJO，大大简化了配置解析代码，使我们能更专注于业务逻辑。

虽然 Spring Shell 是一个选择，但对于功能复杂的CLI工具，Picocli 提供了更强大的功能和与 Spring Boot 的无缝集成。

codeJava

```
// package: com.dataforge.cli.commands

@Component
@Command(name = "generate", mixinStandardHelpOptions = true,
         description = "Generates test data based on configuration.")
public class GenerateCommand implements CommandLineRunner, Callable<Integer> {

    @Autowired
    private DataForgeService dataForgeService;

    @Option(names = {"-c", "--config"}, description = "Path to the configuration file (YAML/JSON).")
    private File configFile;

    @Option(names = {"-n", "--count"}, description = "Number of records to generate.")
    private int count = 10;
    
    // ... 其他直接在命令行中指定的参数，可覆盖配置文件 ...

    @Override
    public void run(String... args) {
        // Spring Boot 启动时，如果这个类是主类或被扫描到，run方法会被执行
        // 我们通过 Picocli 的 execute 方法来解析命令行参数并调用 call()
        new CommandLine(this).execute(args);
    }
    
    @Override
    public Integer call() throws Exception {
        // 这是命令的核心执行逻辑
        System.out.println("DataForge starting generation...");
        
        // 1. 加载配置（优先CLI参数，其次是配置文件）
        ForgeConfig config = buildConfig(); 
        
        // 2. 调用应用层服务
        dataForgeService.generateData(config);
        
        System.out.println("Generation completed successfully.");
        return 0; // Exit code
    }
    
    private ForgeConfig buildConfig() {
        // ... 逻辑：合并 CLI 参数和配置文件内容 ...
        return new ForgeConfig();
    }
}
```

### **3. 风险、最佳实践与后续步骤 (Risks, Best Practices & Next Steps)**

- **潜在风险：**

    1. **性能瓶颈:** 生成海量数据（如百万级）时，简单的循环+写入会非常慢且消耗内存。**对策：** 数据输出模块必须采用流式API（Stream）和缓冲写入（BufferedWriter），避免将所有数据一次性加载到内存中。

    2. **外部数据源维护:** 身份证地区代码、姓氏库等数据会过时。**对策：** 架构上应支持这些数据源的热更新，例如从指定的URL或本地文件中定期加载，而不是硬编码。

    3. **配置复杂性:** 随着功能增多，application.yml 或 CLI 参数会变得极其复杂。**对策：** 强化“配置模板化”功能，允许用户保存和复用复杂的配置片段。

- **最佳实践建议：**

    1. **拥抱SOLID原则:** 我们的设计处处体现了SOLID。例如，DataGenerator 接口遵循**开闭原则 (OCP)**；GeneratorFactory 依赖 DataGenerator 抽象而非具体实现，遵循**依赖倒置原则 (DIP)**。在后续开发中应持续坚守。

    2. **测试驱动开发 (TDD):** 对于一个测试数据生成工具，其自身的质量至关重要。每个DataGenerator都必须有对应的单元测试，覆盖其所有配置参数和边界条件。CLI命令也应有集成测试，验证端到端的流程。
