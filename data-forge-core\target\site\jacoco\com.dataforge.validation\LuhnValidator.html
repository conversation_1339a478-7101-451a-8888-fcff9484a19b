<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LuhnValidator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_class">LuhnValidator</span></div><h1>LuhnValidator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">192 of 319</td><td class="ctr2">39%</td><td class="bar">34 of 48</td><td class="ctr2">29%</td><td class="ctr1">28</td><td class="ctr2">36</td><td class="ctr1">49</td><td class="ctr2">77</td><td class="ctr1">8</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a9"><a href="LuhnValidator.java.html#L36" class="el_method">validate(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="59" alt="59"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h0">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="LuhnValidator.java.html#L75" class="el_method">performLuhnCheck(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="51" alt="51"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="LuhnValidator.java.html#L166" class="el_method">validateBankCard(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="25" alt="25"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="LuhnValidator.java.html#L189" class="el_method">validateIMEI(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="21" alt="21"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h3">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="LuhnValidator.java.html#L151" class="el_method">generateValidNumber(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="LuhnValidator.java.html#L103" class="el_method">generateCheckDigit(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="106" height="10" title="77" alt="77"/></td><td class="ctr2" id="c3">88%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="94" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">78%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="LuhnValidator.java.html#L31" class="el_method">isValid(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="LuhnValidator.java.html#L209" class="el_method">maskSensitiveData(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="59" height="10" title="43" alt="43"/></td><td class="ctr2" id="c2">91%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="LuhnValidator.java.html#L229" class="el_method">getName()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="LuhnValidator.java.html#L234" class="el_method">getDescription()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="LuhnValidator.java.html#L27" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="LuhnValidator.java.html#L25" class="el_method">LuhnValidator()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>