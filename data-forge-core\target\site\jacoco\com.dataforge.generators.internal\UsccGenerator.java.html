<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UsccGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">UsccGenerator.java</span></div><h1>UsccGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.validation.UsccValidator;
import com.dataforge.validation.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 统一社会信用代码生成器
 * 
 * 基于GB32100-2015标准生成18位统一社会信用代码
 * 结构：登记管理部门码(1位) + 机构类别码(1位) + 行政区划码(6位) + 主体标识码(9位) + 校验码(1位)
 * 
 * 支持的参数：
 * - region: 行政区划码 (6位数字，如 &quot;110000&quot; 表示北京)
 * - org_type: 机构类别 (ENTERPRISE|INDIVIDUAL|SOCIAL_ORG|INSTITUTION|ANY)
 * - valid: 是否生成有效代码 (true|false)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L28">public class UsccGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L30">    private static final Logger logger = LoggerFactory.getLogger(UsccGenerator.class);</span>
<span class="nc" id="L31">    private static final Random random = new Random();</span>

    // 登记管理部门码
<span class="nc" id="L34">    private static final List&lt;String&gt; REGISTRATION_AUTHORITIES = Arrays.asList(</span>
            &quot;1&quot;, // 机构编制
            &quot;5&quot;, // 民政
            &quot;9&quot;, // 工商
            &quot;Y&quot; // 其他
    );

    // 机构类别码
    private static final String ORG_TYPE_ENTERPRISE = &quot;1&quot;; // 企业
    private static final String ORG_TYPE_INDIVIDUAL = &quot;2&quot;; // 个体工商户
    private static final String ORG_TYPE_SOCIAL_ORG = &quot;3&quot;; // 农民专业合作社
    private static final String ORG_TYPE_INSTITUTION = &quot;9&quot;; // 事业单位

    // 常用行政区划码
<span class="nc" id="L48">    private static final List&lt;String&gt; COMMON_REGIONS = Arrays.asList(</span>
            &quot;110000&quot;, // 北京市
            &quot;120000&quot;, // 天津市
            &quot;130000&quot;, // 河北省
            &quot;140000&quot;, // 山西省
            &quot;150000&quot;, // 内蒙古自治区
            &quot;210000&quot;, // 辽宁省
            &quot;220000&quot;, // 吉林省
            &quot;230000&quot;, // 黑龙江省
            &quot;310000&quot;, // 上海市
            &quot;320000&quot;, // 江苏省
            &quot;330000&quot;, // 浙江省
            &quot;340000&quot;, // 安徽省
            &quot;350000&quot;, // 福建省
            &quot;360000&quot;, // 江西省
            &quot;370000&quot;, // 山东省
            &quot;410000&quot;, // 河南省
            &quot;420000&quot;, // 湖北省
            &quot;430000&quot;, // 湖南省
            &quot;440000&quot;, // 广东省
            &quot;450000&quot;, // 广西壮族自治区
            &quot;460000&quot;, // 海南省
            &quot;500000&quot;, // 重庆市
            &quot;510000&quot;, // 四川省
            &quot;520000&quot;, // 贵州省
            &quot;530000&quot;, // 云南省
            &quot;540000&quot;, // 西藏自治区
            &quot;610000&quot;, // 陕西省
            &quot;620000&quot;, // 甘肃省
            &quot;630000&quot;, // 青海省
            &quot;640000&quot;, // 宁夏回族自治区
            &quot;650000&quot; // 新疆维吾尔自治区
    );

    // 校验码字符集
    private static final String CHECK_CODE_CHARS = &quot;0123456789ABCDEFGHJKLMNPQRTUWXY&quot;;

    // 校验码权重
<span class="nc" id="L86">    private static final int[] WEIGHTS = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };</span>

    @Override
    public String getType() {
<span class="nc" id="L90">        return &quot;uscc&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L95">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L102">            String region = config.getParam(&quot;region&quot;, String.class, null);</span>
<span class="nc" id="L103">            String orgType = config.getParam(&quot;org_type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L104">            boolean valid = Boolean.parseBoolean(config.getParam(&quot;valid&quot;, String.class, &quot;true&quot;));</span>

            // 生成统一社会信用代码
<span class="nc" id="L107">            String uscc = generateUscc(region, orgType, valid);</span>

            // 验证生成的代码
<span class="nc bnc" id="L110" title="All 2 branches missed.">            if (valid) {</span>
<span class="nc" id="L111">                UsccValidator validator = new UsccValidator();</span>
<span class="nc" id="L112">                ValidationResult validation = validator.validate(uscc);</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">                if (!validation.isValid()) {</span>
<span class="nc" id="L114">                    logger.warn(&quot;Generated invalid USCC: {}, errors: {}&quot;, uscc, validation.getErrorMessages());</span>
                    // 重新生成
<span class="nc" id="L116">                    uscc = generateUscc(region, orgType, true);</span>
                }
            }

<span class="nc" id="L120">            logger.debug(&quot;Generated USCC: {}&quot;, uscc);</span>
<span class="nc" id="L121">            return uscc;</span>

<span class="nc" id="L123">        } catch (Exception e) {</span>
<span class="nc" id="L124">            logger.error(&quot;Error generating USCC&quot;, e);</span>
<span class="nc" id="L125">            return &quot;91110000000000000A&quot;; // 默认有效代码</span>
        }
    }

    private String generateUscc(String region, String orgType, boolean valid) {
<span class="nc" id="L130">        StringBuilder uscc = new StringBuilder();</span>

        // 1. 登记管理部门码 (1位)
<span class="nc" id="L133">        String registrationAuthority = REGISTRATION_AUTHORITIES.get(random.nextInt(REGISTRATION_AUTHORITIES.size()));</span>
<span class="nc" id="L134">        uscc.append(registrationAuthority);</span>

        // 2. 机构类别码 (1位)
<span class="nc" id="L137">        String orgTypeCode = getOrgTypeCode(orgType);</span>
<span class="nc" id="L138">        uscc.append(orgTypeCode);</span>

        // 3. 行政区划码 (6位)
<span class="nc" id="L141">        String regionCode = getRegionCode(region);</span>
<span class="nc" id="L142">        uscc.append(regionCode);</span>

        // 4. 主体标识码 (9位)
<span class="nc" id="L145">        String entityCode = generateEntityCode();</span>
<span class="nc" id="L146">        uscc.append(entityCode);</span>

        // 5. 校验码 (1位)
        String checkCode;
<span class="nc bnc" id="L150" title="All 2 branches missed.">        if (valid) {</span>
<span class="nc" id="L151">            checkCode = calculateCheckCode(uscc.toString());</span>
        } else {
            // 生成错误的校验码
            do {
<span class="nc" id="L155">                checkCode = String.valueOf(CHECK_CODE_CHARS.charAt(random.nextInt(CHECK_CODE_CHARS.length())));</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">            } while (checkCode.equals(calculateCheckCode(uscc.toString())));</span>
        }
<span class="nc" id="L158">        uscc.append(checkCode);</span>

<span class="nc" id="L160">        return uscc.toString();</span>
    }

    private String getOrgTypeCode(String orgType) {
<span class="nc bnc" id="L164" title="All 5 branches missed.">        switch (orgType) {</span>
            case &quot;ENTERPRISE&quot;:
<span class="nc" id="L166">                return ORG_TYPE_ENTERPRISE;</span>
            case &quot;INDIVIDUAL&quot;:
<span class="nc" id="L168">                return ORG_TYPE_INDIVIDUAL;</span>
            case &quot;SOCIAL_ORG&quot;:
<span class="nc" id="L170">                return ORG_TYPE_SOCIAL_ORG;</span>
            case &quot;INSTITUTION&quot;:
<span class="nc" id="L172">                return ORG_TYPE_INSTITUTION;</span>
            case &quot;ANY&quot;:
            default:
<span class="nc" id="L175">                return Arrays.asList(ORG_TYPE_ENTERPRISE, ORG_TYPE_INDIVIDUAL,</span>
                        ORG_TYPE_SOCIAL_ORG, ORG_TYPE_INSTITUTION)
<span class="nc" id="L177">                        .get(random.nextInt(4));</span>
        }
    }

    private String getRegionCode(String region) {
<span class="nc bnc" id="L182" title="All 4 branches missed.">        if (region != null &amp;&amp; region.matches(&quot;\\d{6}&quot;)) {</span>
<span class="nc" id="L183">            return region;</span>
        }

        // 使用常用行政区划码
<span class="nc" id="L187">        return COMMON_REGIONS.get(random.nextInt(COMMON_REGIONS.size()));</span>
    }

    private String generateEntityCode() {
<span class="nc" id="L191">        StringBuilder entityCode = new StringBuilder();</span>

        // 生成9位主体标识码
<span class="nc bnc" id="L194" title="All 2 branches missed.">        for (int i = 0; i &lt; 9; i++) {</span>
            // 使用数字和大写字母（排除I、O、S、V、Z）
<span class="nc" id="L196">            String chars = &quot;0123456789ABCDEFGHJKLMNPQRTUWXY&quot;;</span>
<span class="nc" id="L197">            entityCode.append(chars.charAt(random.nextInt(chars.length())));</span>
        }

<span class="nc" id="L200">        return entityCode.toString();</span>
    }

    private String calculateCheckCode(String code17) {
<span class="nc" id="L204">        int sum = 0;</span>

        // 计算加权和
<span class="nc bnc" id="L207" title="All 2 branches missed.">        for (int i = 0; i &lt; 17; i++) {</span>
<span class="nc" id="L208">            char c = code17.charAt(i);</span>
            int value;

<span class="nc bnc" id="L211" title="All 2 branches missed.">            if (Character.isDigit(c)) {</span>
<span class="nc" id="L212">                value = c - '0';</span>
            } else {
                // 字母转换为数值
<span class="nc" id="L215">                value = CHECK_CODE_CHARS.indexOf(c);</span>
            }

<span class="nc" id="L218">            sum += value * WEIGHTS[i];</span>
        }

        // 计算校验码
<span class="nc" id="L222">        int remainder = sum % 31;</span>
<span class="nc" id="L223">        int checkValue = (31 - remainder) % 31;</span>
<span class="nc" id="L224">        return String.valueOf(CHECK_CODE_CHARS.charAt(checkValue));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>