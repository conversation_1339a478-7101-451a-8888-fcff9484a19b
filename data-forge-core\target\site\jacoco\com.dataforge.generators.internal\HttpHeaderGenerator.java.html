<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HttpHeaderGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">HttpHeaderGenerator.java</span></div><h1>HttpHeaderGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * HTTP头生成器
 * 
 * 支持的参数：
 * - type: 头类型 (request|response|common) 默认: common
 * - name: 指定头名称
 * - format: 输出格式 (header|json) 默认: header
 * 
 * <AUTHOR>
 */
<span class="nc" id="L22">public class HttpHeaderGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(HttpHeaderGenerator.class);</span>
<span class="nc" id="L25">    private static final SecureRandom random = new SecureRandom();</span>

    // 常见HTTP头
<span class="nc" id="L28">    private static final Map&lt;String, List&lt;String&gt;&gt; COMMON_HEADERS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L31">        COMMON_HEADERS.put(&quot;User-Agent&quot;, Arrays.asList(</span>
                &quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36&quot;,
                &quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36&quot;,
                &quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36&quot;));

<span class="nc" id="L36">        COMMON_HEADERS.put(&quot;Accept&quot;, Arrays.asList(</span>
                &quot;text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8&quot;,
                &quot;application/json, text/plain, */*&quot;,
                &quot;application/json&quot;));

<span class="nc" id="L41">        COMMON_HEADERS.put(&quot;Content-Type&quot;, Arrays.asList(</span>
                &quot;text/html; charset=utf-8&quot;,
                &quot;application/json; charset=utf-8&quot;,
                &quot;application/xml; charset=utf-8&quot;));

<span class="nc" id="L46">        COMMON_HEADERS.put(&quot;Authorization&quot;, Arrays.asList(</span>
                &quot;Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&quot;,
                &quot;Basic dXNlcm5hbWU6cGFzc3dvcmQ=&quot;,
                &quot;API-Key sk-1234567890abcdef&quot;));
<span class="nc" id="L50">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L54">        return &quot;http_header&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L59">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
<span class="nc" id="L65">            String name = getStringParam(config, &quot;name&quot;, null);</span>
<span class="nc" id="L66">            String format = getStringParam(config, &quot;format&quot;, &quot;header&quot;);</span>

            String headerName;
            String headerValue;

<span class="nc bnc" id="L71" title="All 4 branches missed.">            if (name != null &amp;&amp; !name.trim().isEmpty()) {</span>
<span class="nc" id="L72">                headerName = name.trim();</span>
<span class="nc" id="L73">                headerValue = generateValueForHeader(headerName);</span>
            } else {
<span class="nc" id="L75">                Map.Entry&lt;String, List&lt;String&gt;&gt; entry = getRandomHeader();</span>
<span class="nc" id="L76">                headerName = entry.getKey();</span>
<span class="nc" id="L77">                List&lt;String&gt; values = entry.getValue();</span>
<span class="nc" id="L78">                headerValue = values.get(random.nextInt(values.size()));</span>
            }

<span class="nc" id="L81">            return formatOutput(headerName, headerValue, format);</span>

<span class="nc" id="L83">        } catch (Exception e) {</span>
<span class="nc" id="L84">            logger.error(&quot;生成HTTP头时发生错误&quot;, e);</span>
<span class="nc" id="L85">            return &quot;Content-Type: text/plain&quot;;</span>
        }
    }

    private Map.Entry&lt;String, List&lt;String&gt;&gt; getRandomHeader() {
<span class="nc" id="L90">        List&lt;Map.Entry&lt;String, List&lt;String&gt;&gt;&gt; entries = new ArrayList&lt;&gt;(COMMON_HEADERS.entrySet());</span>
<span class="nc" id="L91">        return entries.get(random.nextInt(entries.size()));</span>
    }

    private String generateValueForHeader(String headerName) {
<span class="nc bnc" id="L95" title="All 3 branches missed.">        switch (headerName.toLowerCase()) {</span>
            case &quot;content-length&quot;:
<span class="nc" id="L97">                return String.valueOf(random.nextInt(65536));</span>
            case &quot;x-request-id&quot;:
<span class="nc" id="L99">                return UUID.randomUUID().toString();</span>
            default:
<span class="nc" id="L101">                return &quot;test-value-&quot; + random.nextInt(1000);</span>
        }
    }

    private String formatOutput(String name, String value, String format) {
<span class="nc bnc" id="L106" title="All 2 branches missed.">        switch (format.toLowerCase()) {</span>
            case &quot;json&quot;:
<span class="nc" id="L108">                return String.format(&quot;{\&quot;%s\&quot;: \&quot;%s\&quot;}&quot;, name, value.replace(&quot;\&quot;&quot;, &quot;\\\&quot;&quot;));</span>
            case &quot;header&quot;:
            default:
<span class="nc" id="L111">                return String.format(&quot;%s: %s&quot;, name, value);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>