<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RandomNumberGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">RandomNumberGenerator</span></div><h1>RandomNumberGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">214 of 496</td><td class="ctr2">56%</td><td class="bar">33 of 51</td><td class="ctr2">35%</td><td class="ctr1">29</td><td class="ctr2">51</td><td class="ctr1">43</td><td class="ctr2">103</td><td class="ctr1">4</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a11"><a href="RandomNumberGenerator.java.html#L180" class="el_method">generateRandomNumber(RandomNumberGenerator.NumberType, RandomNumberGenerator.DistributionType, FieldConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="45" alt="45"/><img src="../jacoco-resources/greenbar.gif" width="63" height="10" title="50" alt="50"/></td><td class="ctr2" id="c14">52%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">21%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="RandomNumberGenerator.java.html#L264" class="el_method">generatePoisson(FieldConfig, long, long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="42" alt="42"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="RandomNumberGenerator.java.html#L250" class="el_method">generateExponential(FieldConfig, long, long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="25" alt="25"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="RandomNumberGenerator.java.html#L284" class="el_method">convertToNumberType(double, RandomNumberGenerator.NumberType, FieldConfig)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="27" alt="27"/></td><td class="ctr2" id="c13">52%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="RandomNumberGenerator.java.html#L360" class="el_method">formatAsOctal(Number)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="14" alt="14"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="RandomNumberGenerator.java.html#L382" class="el_method">formatAsScientific(Number)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="13" alt="13"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="RandomNumberGenerator.java.html#L329" class="el_method">formatNumber(Number, RandomNumberGenerator.OutputFormat, RandomNumberGenerator.NumberType)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="14" alt="14"/></td><td class="ctr2" id="c12">56%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="RandomNumberGenerator.java.html#L115" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="42" alt="42"/></td><td class="ctr2" id="c9">80%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="RandomNumberGenerator.java.html#L306" class="el_method">generateBigInteger(double, int)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="35" alt="35"/></td><td class="ctr2" id="c7">83%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="3" alt="3"/></td><td class="ctr2" id="e0">75%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="RandomNumberGenerator.java.html#L349" class="el_method">formatAsHex(Number)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">56%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a17"><a href="RandomNumberGenerator.java.html#L168" class="el_method">parseOutputFormat(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">36%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a1"><a href="RandomNumberGenerator.java.html#L371" class="el_method">formatAsBinary(Number)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">57%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a12"><a href="RandomNumberGenerator.java.html#L225" class="el_method">generateUniform(long, long)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="15" alt="15"/></td><td class="ctr2" id="c8">83%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="RandomNumberGenerator.java.html#L235" class="el_method">generateNormal(FieldConfig, long, long)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="41" alt="41"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="RandomNumberGenerator.java.html#L144" class="el_method">parseNumberType(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="11" alt="11"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a15"><a href="RandomNumberGenerator.java.html#L156" class="el_method">parseDistributionType(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a19"><a href="RandomNumberGenerator.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a18"><a href="RandomNumberGenerator.java.html#L40" class="el_method">RandomNumberGenerator()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a14"><a href="RandomNumberGenerator.java.html#L103" class="el_method">getType()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a13"><a href="RandomNumberGenerator.java.html#L108" class="el_method">getConfigClass()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>