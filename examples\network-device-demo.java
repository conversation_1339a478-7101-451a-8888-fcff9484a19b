import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.internal.*;
import com.dataforge.config.SimpleFieldConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * 网络设备类生成器演示
 * 
 * 展示新实现的5个网络设备类生成器的功能
 */
public class NetworkDeviceDemo {
    
    public static void main(String[] args) {
        DataForgeContext context = new DataForgeContext();
        
        System.out.println("=== DataForge 网络设备类生成器演示 ===\n");
        
        // 1. 设备ID生成器演示
        demonstrateDeviceIdGenerator(context);
        
        // 2. User-Agent生成器演示
        demonstrateUserAgentGenerator(context);
        
        // 3. 地理坐标生成器演示
        demonstrateGeolocationGenerator(context);
        
        // 4. 时区生成器演示
        demonstrateTimezoneGenerator(context);
        
        // 5. Cookie生成器演示
        demonstrateCookieGenerator(context);
    }
    
    private static void demonstrateDeviceIdGenerator(DataForgeContext context) {
        System.out.println("1. 设备ID生成器 (DeviceIdGenerator)");
        System.out.println("=====================================");
        
        DeviceIdGenerator generator = new DeviceIdGenerator();
        
        // UUID格式
        SimpleFieldConfig uuidConfig = new SimpleFieldConfig("device_id", "device_id");
        uuidConfig.setParam("type", "UUID");
        System.out.println("UUID格式: " + generator.generate(uuidConfig, context));
        
        // IMEI格式
        SimpleFieldConfig imeiConfig = new SimpleFieldConfig("device_id", "device_id");
        imeiConfig.setParam("type", "IMEI");
        imeiConfig.setParam("valid", true);
        System.out.println("IMEI格式: " + generator.generate(imeiConfig, context));
        
        // IMSI格式
        SimpleFieldConfig imsiConfig = new SimpleFieldConfig("device_id", "device_id");
        imsiConfig.setParam("type", "IMSI");
        imsiConfig.setParam("mcc", "460");
        System.out.println("IMSI格式: " + generator.generate(imsiConfig, context));
        
        // 自定义格式
        SimpleFieldConfig customConfig = new SimpleFieldConfig("device_id", "device_id");
        customConfig.setParam("type", "CUSTOM");
        customConfig.setParam("length", 16);
        customConfig.setParam("format", "HYPHEN");
        System.out.println("自定义格式: " + generator.generate(customConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateUserAgentGenerator(DataForgeContext context) {
        System.out.println("2. User-Agent生成器 (UserAgentGenerator)");
        System.out.println("=========================================");
        
        UserAgentGenerator generator = new UserAgentGenerator();
        
        // Chrome桌面版
        SimpleFieldConfig chromeConfig = new SimpleFieldConfig("user_agent", "user_agent");
        chromeConfig.setParam("browser", "CHROME");
        chromeConfig.setParam("os", "WINDOWS");
        chromeConfig.setParam("device", "DESKTOP");
        System.out.println("Chrome桌面版:");
        System.out.println(generator.generate(chromeConfig, context));
        
        // 移动端
        SimpleFieldConfig mobileConfig = new SimpleFieldConfig("user_agent", "user_agent");
        mobileConfig.setParam("browser", "CHROME");
        mobileConfig.setParam("os", "ANDROID");
        mobileConfig.setParam("device", "MOBILE");
        System.out.println("\n移动端:");
        System.out.println(generator.generate(mobileConfig, context));
        
        // 随机生成
        SimpleFieldConfig randomConfig = new SimpleFieldConfig("user_agent", "user_agent");
        randomConfig.setParam("browser", "RANDOM");
        randomConfig.setParam("os", "RANDOM");
        System.out.println("\n随机生成:");
        System.out.println(generator.generate(randomConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateGeolocationGenerator(DataForgeContext context) {
        System.out.println("3. 地理坐标生成器 (GeolocationGenerator)");
        System.out.println("========================================");
        
        GeolocationGenerator generator = new GeolocationGenerator();
        
        // 小数格式
        SimpleFieldConfig decimalConfig = new SimpleFieldConfig("location", "geolocation");
        decimalConfig.setParam("format", "DECIMAL");
        decimalConfig.setParam("region", "CHINA");
        decimalConfig.setParam("precision", 6);
        System.out.println("中国区域小数格式: " + generator.generate(decimalConfig, context));
        
        // JSON格式
        SimpleFieldConfig jsonConfig = new SimpleFieldConfig("location", "geolocation");
        jsonConfig.setParam("format", "JSON");
        jsonConfig.setParam("include_altitude", true);
        jsonConfig.setParam("precision", 4);
        System.out.println("JSON格式: " + generator.generate(jsonConfig, context));
        
        // 度分秒格式
        SimpleFieldConfig dmsConfig = new SimpleFieldConfig("location", "geolocation");
        dmsConfig.setParam("format", "DMS");
        dmsConfig.setParam("latitude_min", 39.0);
        dmsConfig.setParam("latitude_max", 41.0);
        dmsConfig.setParam("longitude_min", 115.0);
        dmsConfig.setParam("longitude_max", 118.0);
        System.out.println("度分秒格式: " + generator.generate(dmsConfig, context));
        
        // WKT格式
        SimpleFieldConfig wktConfig = new SimpleFieldConfig("location", "geolocation");
        wktConfig.setParam("format", "WKT");
        wktConfig.setParam("region", "USA");
        System.out.println("WKT格式: " + generator.generate(wktConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateTimezoneGenerator(DataForgeContext context) {
        System.out.println("4. 时区生成器 (TimezoneGenerator)");
        System.out.println("=================================");
        
        TimezoneGenerator generator = new TimezoneGenerator();
        
        // IANA格式
        SimpleFieldConfig ianaConfig = new SimpleFieldConfig("timezone", "timezone");
        ianaConfig.setParam("format", "IANA");
        ianaConfig.setParam("region", "ASIA");
        System.out.println("IANA格式: " + generator.generate(ianaConfig, context));
        
        // UTC偏移量
        SimpleFieldConfig offsetConfig = new SimpleFieldConfig("timezone", "timezone");
        offsetConfig.setParam("format", "OFFSET");
        System.out.println("UTC偏移量: " + generator.generate(offsetConfig, context));
        
        // 显示名称
        SimpleFieldConfig displayConfig = new SimpleFieldConfig("timezone", "timezone");
        displayConfig.setParam("format", "DISPLAY_NAME");
        displayConfig.setParam("locale", "en");
        displayConfig.setParam("style", "FULL");
        System.out.println("显示名称: " + generator.generate(displayConfig, context));
        
        // 缩写
        SimpleFieldConfig abbrevConfig = new SimpleFieldConfig("timezone", "timezone");
        abbrevConfig.setParam("format", "ABBREVIATION");
        abbrevConfig.setParam("region", "AMERICA");
        System.out.println("缩写: " + generator.generate(abbrevConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateCookieGenerator(DataForgeContext context) {
        System.out.println("5. Cookie生成器 (CookieGenerator)");
        System.out.println("=================================");
        
        CookieGenerator generator = new CookieGenerator();
        
        // HTTP头格式
        SimpleFieldConfig headerConfig = new SimpleFieldConfig("cookie", "cookie");
        headerConfig.setParam("format", "HEADER");
        headerConfig.setParam("name", "sessionid");
        headerConfig.setParam("value_type", "UUID");
        headerConfig.setParam("domain", "example.com");
        headerConfig.setParam("path", "/");
        headerConfig.setParam("expires_days", "7");
        headerConfig.setParam("secure", true);
        headerConfig.setParam("http_only", true);
        headerConfig.setParam("same_site", "LAX");
        System.out.println("HTTP头格式:");
        System.out.println(generator.generate(headerConfig, context));
        
        // JSON格式
        SimpleFieldConfig jsonConfig = new SimpleFieldConfig("cookie", "cookie");
        jsonConfig.setParam("format", "JSON");
        jsonConfig.setParam("value_type", "TOKEN");
        jsonConfig.setParam("max_age", "3600");
        System.out.println("\nJSON格式:");
        System.out.println(generator.generate(jsonConfig, context));
        
        // 简单格式
        SimpleFieldConfig simpleConfig = new SimpleFieldConfig("cookie", "cookie");
        simpleConfig.setParam("format", "SIMPLE");
        simpleConfig.setParam("name", "user_pref");
        simpleConfig.setParam("value_type", "STRING");
        simpleConfig.setParam("value_length", 32);
        System.out.println("\n简单格式: " + generator.generate(simpleConfig, context));
        
        System.out.println();
    }
}
