<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataLoader</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.util</a> &gt; <span class="el_class">DataLoader</span></div><h1>DataLoader</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">345 of 345</td><td class="ctr2">0%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">31</td><td class="ctr2">31</td><td class="ctr1">86</td><td class="ctr2">86</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a4"><a href="DataLoader.java.html#L92" class="el_method">lambda$loadWeightedDataFromResource$1(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="103" alt="103"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h0">26</td><td class="ctr2" id="i0">26</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="DataLoader.java.html#L47" class="el_method">lambda$loadDataFromResource$0(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="89" height="10" title="77" alt="77"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="DataLoader.java.html#L178" class="el_method">loadDataFromFile(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="62" height="10" title="54" alt="54"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="DataLoader.java.html#L148" class="el_method">selectByWeight(Map, Random)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="50" alt="50"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="DataLoader.java.html#L233" class="el_method">getCacheStats()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="17" alt="17"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="DataLoader.java.html#L27" class="el_method">static {...}</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="12" alt="12"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="DataLoader.java.html#L214" class="el_method">parseItem(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="9" alt="9"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="DataLoader.java.html#L222" class="el_method">clearCache()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="8" alt="8"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h4">4</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="DataLoader.java.html#L46" class="el_method">loadDataFromResource(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a7"><a href="DataLoader.java.html#L91" class="el_method">loadWeightedDataFromResource(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="DataLoader.java.html#L25" class="el_method">DataLoader()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>