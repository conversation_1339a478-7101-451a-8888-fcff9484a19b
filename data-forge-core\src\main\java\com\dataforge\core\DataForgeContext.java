package com.dataforge.core;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据生成上下文。
 * 
 * <p>
 * 在一次生成请求的生命周期内共享数据，用于解决字段间的关联性问题。
 * 该类是线程安全的，支持并发访问。
 * 
 * <p>
 * 典型使用场景：
 * <ul>
 * <li>身份证号生成器将出生日期、性别、地区信息存入上下文</li>
 * <li>年龄生成器从上下文获取出生日期，计算对应年龄</li>
 * <li>地址生成器从上下文获取地区信息，生成对应地区的详细地址</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class DataForgeContext {

    private static final Logger logger = LoggerFactory.getLogger(DataForgeContext.class);

    /**
     * 使用 ConcurrentHashMap 保证多线程生成时的线程安全。
     */
    private final Map<String, Object> contextMap = new ConcurrentHashMap<>();

    /**
     * 上下文创建时间，用于调试和日志记录。
     */
    private final LocalDateTime createdAt;

    /**
     * 当前记录的索引，用于批量生成时的记录标识。
     */
    private volatile int currentRecordIndex = 0;

    /**
     * 构造函数，初始化上下文。
     */
    public DataForgeContext() {
        this.createdAt = LocalDateTime.now();
        logger.debug("DataForgeContext created at {}", createdAt);
    }

    /**
     * 向上下文中存储键值对。
     * 
     * <p>
     * 如果键已存在，将覆盖原有值。存储操作是线程安全的。
     * 
     * @param key   键，不能为null或空字符串
     * @param value 值，可以为null
     * @throws IllegalArgumentException 当key为null或空字符串时
     */
    public void put(String key, Object value) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Key cannot be null or empty");
        }

        Object oldValue = contextMap.put(key, value);
        logger.trace("Context put: key={}, value={}, oldValue={}", key, value, oldValue);
    }

    /**
     * 从上下文中获取指定类型的值。
     * 
     * <p>
     * 该方法是类型安全的，只有当存储的值确实是指定类型的实例时才会返回。
     * 
     * @param <V>  期望的值类型
     * @param key  键，不能为null或空字符串
     * @param type 期望的值类型的Class对象
     * @return 包含值的Optional，如果键不存在或类型不匹配则返回空Optional
     * @throws IllegalArgumentException 当key为null或空字符串，或type为null时
     */
    @SuppressWarnings("unchecked")
    public <V> Optional<V> get(String key, Class<V> type) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Key cannot be null or empty");
        }
        if (type == null) {
            throw new IllegalArgumentException("Type cannot be null");
        }

        Object value = contextMap.get(key);
        if (value != null && type.isInstance(value)) {
            logger.trace("Context get: key={}, type={}, value={}", key, type.getSimpleName(), value);
            return Optional.of((V) value);
        }

        logger.trace("Context get: key={}, type={}, value not found or type mismatch",
                key, type.getSimpleName());
        return Optional.empty();
    }

    /**
     * 从上下文中获取值，不进行类型检查。
     * 
     * @param key 键
     * @return 值的Optional，如果键不存在则返回空Optional
     */
    public Optional<Object> get(String key) {
        if (key == null || key.trim().isEmpty()) {
            return Optional.empty();
        }
        return Optional.ofNullable(contextMap.get(key));
    }

    /**
     * 检查上下文中是否包含指定的键。
     * 
     * @param key 键
     * @return 如果包含该键返回true，否则返回false
     */
    public boolean containsKey(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }
        return contextMap.containsKey(key);
    }

    /**
     * 从上下文中移除指定的键值对。
     * 
     * @param key 要移除的键
     * @return 被移除的值的Optional，如果键不存在则返回空Optional
     */
    public Optional<Object> remove(String key) {
        if (key == null || key.trim().isEmpty()) {
            return Optional.empty();
        }

        Object removedValue = contextMap.remove(key);
        logger.trace("Context remove: key={}, removedValue={}", key, removedValue);
        return Optional.ofNullable(removedValue);
    }

    /**
     * 清空上下文中的所有数据。
     * 
     * <p>
     * 通常在开始生成新记录时调用，以避免数据污染。
     */
    public void clear() {
        int size = contextMap.size();
        contextMap.clear();
        logger.debug("Context cleared, removed {} entries", size);
    }

    /**
     * 获取上下文中存储的键值对数量。
     * 
     * @return 键值对数量
     */
    public int size() {
        return contextMap.size();
    }

    /**
     * 检查上下文是否为空。
     * 
     * @return 如果上下文为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return contextMap.isEmpty();
    }

    /**
     * 获取当前记录的索引。
     * 
     * @return 当前记录索引，从0开始
     */
    public int getCurrentRecordIndex() {
        return currentRecordIndex;
    }

    /**
     * 设置当前记录的索引。
     * 
     * <p>
     * 通常由框架在批量生成时自动调用。
     * 
     * @param index 记录索引，应该大于等于0
     * @throws IllegalArgumentException 当index小于0时
     */
    public void setCurrentRecordIndex(int index) {
        if (index < 0) {
            throw new IllegalArgumentException("Record index cannot be negative");
        }
        this.currentRecordIndex = index;
        logger.trace("Current record index set to {}", index);
    }

    /**
     * 递增当前记录索引。
     * 
     * @return 递增后的索引值
     */
    public int incrementRecordIndex() {
        int newIndex = ++currentRecordIndex;
        logger.trace("Record index incremented to {}", newIndex);
        return newIndex;
    }

    /**
     * 获取上下文创建时间。
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    /**
     * 获取上下文的字符串表示，用于调试。
     * 
     * @return 上下文的字符串表示
     */
    @Override
    public String toString() {
        return String.format("DataForgeContext{size=%d, recordIndex=%d, createdAt=%s}",
                size(), currentRecordIndex, createdAt);
    }
}