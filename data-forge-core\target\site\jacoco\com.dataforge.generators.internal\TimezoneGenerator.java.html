<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimezoneGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">TimezoneGenerator.java</span></div><h1>TimezoneGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时区标识生成器
 * 
 * &lt;p&gt;
 * 支持生成各种时区标识符，用于国际化应用测试、时间处理功能验证、
 * 多时区系统开发等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;format: 输出格式 (IANA|OFFSET|ABBREVIATION|DISPLAY_NAME) 默认: IANA&lt;/li&gt;
 * &lt;li&gt;region: 地区过滤 (AMERICA|EUROPE|ASIA|AFRICA|AUSTRALIA|PACIFIC|ALL) 默认: ALL&lt;/li&gt;
 * &lt;li&gt;offset_min: 最小UTC偏移（小时）默认: -12&lt;/li&gt;
 * &lt;li&gt;offset_max: 最大UTC偏移（小时）默认: 14&lt;/li&gt;
 * &lt;li&gt;include_dst: 是否包含夏令时时区 默认: true&lt;/li&gt;
 * &lt;li&gt;locale: 显示名称的语言环境 默认: en&lt;/li&gt;
 * &lt;li&gt;style: 显示名称样式 (FULL|SHORT) 默认: FULL&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L38">public class TimezoneGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L40">    private static final Logger logger = LoggerFactory.getLogger(TimezoneGenerator.class);</span>
<span class="fc" id="L41">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 输出格式枚举
<span class="fc" id="L44">    public enum OutputFormat {</span>
<span class="fc" id="L45">        IANA(&quot;IANA时区标识符&quot;),</span>
<span class="fc" id="L46">        OFFSET(&quot;UTC偏移量&quot;),</span>
<span class="fc" id="L47">        ABBREVIATION(&quot;时区缩写&quot;),</span>
<span class="fc" id="L48">        DISPLAY_NAME(&quot;显示名称&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L52">        OutputFormat(String description) {</span>
<span class="fc" id="L53">            this.description = description;</span>
<span class="fc" id="L54">        }</span>
        
        public String getDescription() {
<span class="nc" id="L57">            return description;</span>
        }
    }
    
    // 地区枚举
<span class="fc" id="L62">    public enum Region {</span>
<span class="fc" id="L63">        AMERICA(&quot;美洲&quot;),</span>
<span class="fc" id="L64">        EUROPE(&quot;欧洲&quot;),</span>
<span class="fc" id="L65">        ASIA(&quot;亚洲&quot;),</span>
<span class="fc" id="L66">        AFRICA(&quot;非洲&quot;),</span>
<span class="fc" id="L67">        AUSTRALIA(&quot;澳洲&quot;),</span>
<span class="fc" id="L68">        PACIFIC(&quot;太平洋&quot;),</span>
<span class="fc" id="L69">        ALL(&quot;全部&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L73">        Region(String description) {</span>
<span class="fc" id="L74">            this.description = description;</span>
<span class="fc" id="L75">        }</span>
        
        public String getDescription() {
<span class="nc" id="L78">            return description;</span>
        }
    }
    
    // 常用时区映射
<span class="fc" id="L83">    private static final Map&lt;String, List&lt;String&gt;&gt; REGION_TIMEZONES = new HashMap&lt;&gt;();</span>
    
    static {
<span class="fc" id="L86">        REGION_TIMEZONES.put(&quot;AMERICA&quot;, Arrays.asList(</span>
<span class="fc" id="L87">            &quot;America/New_York&quot;, &quot;America/Chicago&quot;, &quot;America/Denver&quot;, &quot;America/Los_Angeles&quot;,</span>
<span class="fc" id="L88">            &quot;America/Toronto&quot;, &quot;America/Vancouver&quot;, &quot;America/Mexico_City&quot;, &quot;America/Sao_Paulo&quot;,</span>
<span class="fc" id="L89">            &quot;America/Buenos_Aires&quot;, &quot;America/Lima&quot;, &quot;America/Bogota&quot;, &quot;America/Caracas&quot;</span>
        ));
        
<span class="fc" id="L92">        REGION_TIMEZONES.put(&quot;EUROPE&quot;, Arrays.asList(</span>
<span class="fc" id="L93">            &quot;Europe/London&quot;, &quot;Europe/Paris&quot;, &quot;Europe/Berlin&quot;, &quot;Europe/Rome&quot;,</span>
<span class="fc" id="L94">            &quot;Europe/Madrid&quot;, &quot;Europe/Amsterdam&quot;, &quot;Europe/Brussels&quot;, &quot;Europe/Vienna&quot;,</span>
<span class="fc" id="L95">            &quot;Europe/Prague&quot;, &quot;Europe/Warsaw&quot;, &quot;Europe/Moscow&quot;, &quot;Europe/Istanbul&quot;</span>
        ));
        
<span class="fc" id="L98">        REGION_TIMEZONES.put(&quot;ASIA&quot;, Arrays.asList(</span>
<span class="fc" id="L99">            &quot;Asia/Shanghai&quot;, &quot;Asia/Tokyo&quot;, &quot;Asia/Seoul&quot;, &quot;Asia/Hong_Kong&quot;,</span>
<span class="fc" id="L100">            &quot;Asia/Singapore&quot;, &quot;Asia/Bangkok&quot;, &quot;Asia/Jakarta&quot;, &quot;Asia/Manila&quot;,</span>
<span class="fc" id="L101">            &quot;Asia/Kolkata&quot;, &quot;Asia/Dubai&quot;, &quot;Asia/Riyadh&quot;, &quot;Asia/Tehran&quot;</span>
        ));
        
<span class="fc" id="L104">        REGION_TIMEZONES.put(&quot;AFRICA&quot;, Arrays.asList(</span>
<span class="fc" id="L105">            &quot;Africa/Cairo&quot;, &quot;Africa/Lagos&quot;, &quot;Africa/Johannesburg&quot;, &quot;Africa/Nairobi&quot;,</span>
<span class="fc" id="L106">            &quot;Africa/Casablanca&quot;, &quot;Africa/Tunis&quot;, &quot;Africa/Algiers&quot;, &quot;Africa/Addis_Ababa&quot;</span>
        ));
        
<span class="fc" id="L109">        REGION_TIMEZONES.put(&quot;AUSTRALIA&quot;, Arrays.asList(</span>
<span class="fc" id="L110">            &quot;Australia/Sydney&quot;, &quot;Australia/Melbourne&quot;, &quot;Australia/Brisbane&quot;, &quot;Australia/Perth&quot;,</span>
<span class="fc" id="L111">            &quot;Australia/Adelaide&quot;, &quot;Australia/Darwin&quot;, &quot;Australia/Hobart&quot;</span>
        ));
        
<span class="fc" id="L114">        REGION_TIMEZONES.put(&quot;PACIFIC&quot;, Arrays.asList(</span>
<span class="fc" id="L115">            &quot;Pacific/Auckland&quot;, &quot;Pacific/Fiji&quot;, &quot;Pacific/Honolulu&quot;, &quot;Pacific/Guam&quot;,</span>
<span class="fc" id="L116">            &quot;Pacific/Tahiti&quot;, &quot;Pacific/Samoa&quot;, &quot;Pacific/Tonga&quot;</span>
        ));
<span class="fc" id="L118">    }</span>

    @Override
    public String getType() {
<span class="fc" id="L122">        return &quot;timezone&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L127">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取输出格式
<span class="fc" id="L134">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;IANA&quot;);</span>
<span class="fc" id="L135">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 获取地区过滤
<span class="fc" id="L138">            String regionStr = getStringParam(config, &quot;region&quot;, &quot;ALL&quot;);</span>
<span class="fc" id="L139">            Region region = parseRegion(regionStr);</span>
            
            // 获取其他参数
<span class="fc" id="L142">            boolean includeDst = getBooleanParam(config, &quot;include_dst&quot;, true);</span>
<span class="fc" id="L143">            String localeStr = getStringParam(config, &quot;locale&quot;, &quot;en&quot;);</span>
<span class="fc" id="L144">            String styleStr = getStringParam(config, &quot;style&quot;, &quot;FULL&quot;);</span>
            
            // 生成时区
<span class="fc" id="L147">            ZoneId zoneId = generateTimezone(region, includeDst, config);</span>
            
            // 格式化输出
<span class="fc" id="L150">            return formatTimezone(zoneId, format, localeStr, styleStr);</span>
            
<span class="nc" id="L152">        } catch (Exception e) {</span>
<span class="nc" id="L153">            logger.error(&quot;Failed to generate timezone&quot;, e);</span>
            // 返回一个默认时区作为fallback
<span class="nc" id="L155">            return &quot;UTC&quot;;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L164">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L165">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L166">            logger.warn(&quot;Invalid output format: {}, using IANA as default&quot;, formatStr);</span>
<span class="nc" id="L167">            return OutputFormat.IANA;</span>
        }
    }

    /**
     * 解析地区
     */
    private Region parseRegion(String regionStr) {
        try {
<span class="fc" id="L176">            return Region.valueOf(regionStr.toUpperCase());</span>
<span class="nc" id="L177">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L178">            logger.warn(&quot;Invalid region: {}, using ALL as default&quot;, regionStr);</span>
<span class="nc" id="L179">            return Region.ALL;</span>
        }
    }

    /**
     * 生成时区
     */
    private ZoneId generateTimezone(Region region, boolean includeDst, FieldConfig config) {
<span class="fc" id="L187">        List&lt;String&gt; candidateTimezones = new ArrayList&lt;&gt;();</span>

<span class="fc bfc" id="L189" title="All 2 branches covered.">        if (region == Region.ALL) {</span>
            // 获取所有时区
<span class="fc bfc" id="L191" title="All 2 branches covered.">            for (List&lt;String&gt; timezones : REGION_TIMEZONES.values()) {</span>
<span class="fc" id="L192">                candidateTimezones.addAll(timezones);</span>
            }
<span class="fc" id="L194">        } else {</span>
            // 获取指定地区的时区
<span class="fc" id="L196">            List&lt;String&gt; regionTimezones = REGION_TIMEZONES.get(region.name());</span>
<span class="pc bpc" id="L197" title="1 of 2 branches missed.">            if (regionTimezones != null) {</span>
<span class="fc" id="L198">                candidateTimezones.addAll(regionTimezones);</span>
            }
        }

        // 如果没有候选时区，使用所有可用时区
<span class="pc bpc" id="L203" title="1 of 2 branches missed.">        if (candidateTimezones.isEmpty()) {</span>
<span class="nc" id="L204">            candidateTimezones = ZoneId.getAvailableZoneIds().stream()</span>
<span class="nc" id="L205">                .collect(Collectors.toList());</span>
        }

        // 过滤出系统中实际可用的时区
<span class="fc" id="L209">        List&lt;String&gt; validTimezones = candidateTimezones.stream()</span>
<span class="fc" id="L210">            .filter(zoneId -&gt; {</span>
                try {
<span class="fc" id="L212">                    ZoneId.of(zoneId);</span>
<span class="fc" id="L213">                    return true;</span>
<span class="fc" id="L214">                } catch (Exception e) {</span>
<span class="fc" id="L215">                    logger.debug(&quot;Invalid timezone ID: {}&quot;, zoneId);</span>
<span class="fc" id="L216">                    return false;</span>
                }
            })
<span class="fc" id="L219">            .collect(Collectors.toList());</span>

<span class="pc bpc" id="L221" title="1 of 2 branches missed.">        if (validTimezones.isEmpty()) {</span>
<span class="nc" id="L222">            return ZoneId.of(&quot;UTC&quot;);</span>
        }

        // 根据UTC偏移量过滤
<span class="fc" id="L226">        int offsetMin = getIntParam(config, &quot;offset_min&quot;, -12);</span>
<span class="fc" id="L227">        int offsetMax = getIntParam(config, &quot;offset_max&quot;, 14);</span>

<span class="fc" id="L229">        List&lt;ZoneId&gt; filteredZones = validTimezones.stream()</span>
<span class="fc" id="L230">            .map(ZoneId::of)</span>
<span class="fc" id="L231">            .filter(zone -&gt; {</span>
<span class="fc" id="L232">                ZoneOffset offset = zone.getRules().getOffset(java.time.Instant.now());</span>
<span class="fc" id="L233">                int offsetHours = offset.getTotalSeconds() / 3600;</span>
<span class="pc bpc" id="L234" title="2 of 4 branches missed.">                return offsetHours &gt;= offsetMin &amp;&amp; offsetHours &lt;= offsetMax;</span>
            })
<span class="fc" id="L236">            .collect(Collectors.toList());</span>

        // 如果不包含夏令时，过滤掉有夏令时的时区
<span class="pc bpc" id="L239" title="1 of 2 branches missed.">        if (!includeDst) {</span>
<span class="nc" id="L240">            filteredZones = filteredZones.stream()</span>
<span class="nc bnc" id="L241" title="All 2 branches missed.">                .filter(zone -&gt; !zone.getRules().isDaylightSavings(java.time.Instant.now()))</span>
<span class="nc" id="L242">                .collect(Collectors.toList());</span>
        }

        // 如果过滤后没有时区，返回UTC
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">        if (filteredZones.isEmpty()) {</span>
<span class="nc" id="L247">            return ZoneId.of(&quot;UTC&quot;);</span>
        }

        // 随机选择一个时区
<span class="fc" id="L251">        return filteredZones.get(random.nextInt(filteredZones.size()));</span>
    }

    /**
     * 格式化时区
     */
    private String formatTimezone(ZoneId zoneId, OutputFormat format, String localeStr, String styleStr) {
<span class="pc bpc" id="L258" title="2 of 5 branches missed.">        switch (format) {</span>
            case IANA:
<span class="fc" id="L260">                return zoneId.getId();</span>
            case OFFSET:
<span class="fc" id="L262">                return formatOffset(zoneId);</span>
            case ABBREVIATION:
<span class="nc" id="L264">                return formatAbbreviation(zoneId, localeStr);</span>
            case DISPLAY_NAME:
<span class="fc" id="L266">                return formatDisplayName(zoneId, localeStr, styleStr);</span>
            default:
<span class="nc" id="L268">                return zoneId.getId();</span>
        }
    }

    /**
     * 格式化为UTC偏移量
     */
    private String formatOffset(ZoneId zoneId) {
<span class="fc" id="L276">        ZoneOffset offset = zoneId.getRules().getOffset(java.time.Instant.now());</span>
<span class="fc" id="L277">        return offset.getId();</span>
    }

    /**
     * 格式化为时区缩写
     */
    private String formatAbbreviation(ZoneId zoneId, String localeStr) {
<span class="nc" id="L284">        Locale locale = parseLocale(localeStr);</span>
<span class="nc" id="L285">        return zoneId.getDisplayName(TextStyle.SHORT, locale);</span>
    }

    /**
     * 格式化为显示名称
     */
    private String formatDisplayName(ZoneId zoneId, String localeStr, String styleStr) {
<span class="fc" id="L292">        Locale locale = parseLocale(localeStr);</span>
<span class="fc" id="L293">        TextStyle style = parseTextStyle(styleStr);</span>
<span class="fc" id="L294">        return zoneId.getDisplayName(style, locale);</span>
    }

    /**
     * 解析语言环境
     */
    private Locale parseLocale(String localeStr) {
        try {
<span class="fc" id="L302">            return Locale.forLanguageTag(localeStr.replace(&quot;_&quot;, &quot;-&quot;));</span>
<span class="nc" id="L303">        } catch (Exception e) {</span>
<span class="nc" id="L304">            logger.warn(&quot;Invalid locale: {}, using English as default&quot;, localeStr);</span>
<span class="nc" id="L305">            return Locale.ENGLISH;</span>
        }
    }

    /**
     * 解析文本样式
     */
    private TextStyle parseTextStyle(String styleStr) {
        try {
<span class="fc" id="L314">            return TextStyle.valueOf(styleStr.toUpperCase());</span>
<span class="nc" id="L315">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L316">            logger.warn(&quot;Invalid text style: {}, using FULL as default&quot;, styleStr);</span>
<span class="nc" id="L317">            return TextStyle.FULL;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>