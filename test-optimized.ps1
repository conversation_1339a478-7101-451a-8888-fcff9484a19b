# DataForge 优化后生成器测试脚本

Write-Host "=== DataForge 优化后生成器测试 ===" -ForegroundColor Green
Write-Host

# 构建项目
Write-Host "1. 构建项目..." -ForegroundColor Yellow
& .\build.sh
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败，退出测试" -ForegroundColor Red
    exit 1
}

Write-Host
Write-Host "2. 测试优化后的生成器..." -ForegroundColor Yellow

# 创建输出目录
New-Item -ItemType Directory -Force -Path "output" | Out-Null

# 测试优化后的生成器
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar --config examples/optimized-generators-test.yml

Write-Host
Write-Host "3. 检查生成结果..." -ForegroundColor Yellow

if (Test-Path "output/optimized-test.csv") {
    Write-Host "✅ 生成成功！查看前5行数据:" -ForegroundColor Green
    Get-Content "output/optimized-test.csv" | Select-Object -First 5
    Write-Host
    $recordCount = (Get-Content "output/optimized-test.csv" | Select-Object -Skip 1).Count
    Write-Host "总共生成了 $recordCount 条记录" -ForegroundColor Green
} else {
    Write-Host "❌ 生成失败，未找到输出文件" -ForegroundColor Red
    exit 1
}

Write-Host
Write-Host "4. 测试数据统计..." -ForegroundColor Yellow

# 测试姓名生成器统计
Write-Host "测试姓名生成器统计功能..." -ForegroundColor Cyan
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar --count 5 --fields "name:name" --format console

Write-Host
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "优化后的生成器支持:" -ForegroundColor Cyan
Write-Host "- 大规模数据文件加载" -ForegroundColor White
Write-Host "- 权重选择算法" -ForegroundColor White
Write-Host "- 数十亿唯一数据生成" -ForegroundColor White
Write-Host "- 智能数据关联" -ForegroundColor White
Write-Host "- 完整的数据校验" -ForegroundColor White