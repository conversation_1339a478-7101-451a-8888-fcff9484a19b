package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.util.*;

/**
 * 度量单位生成器
 * 
 * <p>
 * 支持生成各种物理量的度量数据，包括长度、重量、体积、温度、
 * 时间等，用于科学计算、工程测试、物联网数据模拟等场景。
 * 
 * <p>
 * 支持的参数：
 * <ul>
 * <li>category: 度量类别 (LENGTH|WEIGHT|VOLUME|TEMPERATURE|TIME|AREA|SPEED|PRESSURE|ENERGY|RANDOM) 默认: LENGTH</li>
 * <li>unit: 具体单位（如果不指定则随机选择该类别的单位）</li>
 * <li>min: 最小值 默认: 0.0</li>
 * <li>max: 最大值 默认: 100.0</li>
 * <li>precision: 小数位数 默认: 2</li>
 * <li>format: 输出格式 (VALUE_UNIT|UNIT_VALUE|VALUE_ONLY|UNIT_ONLY|JSON) 默认: VALUE_UNIT</li>
 * <li>system: 单位制 (METRIC|IMPERIAL|BOTH|RANDOM) 默认: METRIC</li>
 * <li>convert_to: 转换目标单位</li>
 * <li>include_symbol: 是否包含单位符号 默认: true</li>
 * <li>include_name: 是否包含单位名称 默认: false</li>
 * <li>scientific: 是否使用科学计数法 默认: false</li>
 * <li>rounding: 舍入模式 (UP|DOWN|HALF_UP|HALF_DOWN) 默认: HALF_UP</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class MeasurementGenerator extends BaseGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(MeasurementGenerator.class);
    private static final SecureRandom random = new SecureRandom();
    
    // 度量类别枚举
    public enum MeasurementCategory {
        LENGTH("长度"),
        WEIGHT("重量"),
        VOLUME("体积"),
        TEMPERATURE("温度"),
        TIME("时间"),
        AREA("面积"),
        SPEED("速度"),
        PRESSURE("压力"),
        ENERGY("能量"),
        RANDOM("随机类别");
        
        private final String description;
        
        MeasurementCategory(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 单位制枚举
    public enum UnitSystem {
        METRIC("公制"),
        IMPERIAL("英制"),
        BOTH("混合"),
        RANDOM("随机");
        
        private final String description;
        
        UnitSystem(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 输出格式枚举
    public enum OutputFormat {
        VALUE_UNIT("数值+单位"),
        UNIT_VALUE("单位+数值"),
        VALUE_ONLY("仅数值"),
        UNIT_ONLY("仅单位"),
        JSON("JSON格式");
        
        private final String description;
        
        OutputFormat(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 单位信息类
    public static class Unit {
        private final String symbol;
        private final String name;
        private final UnitSystem system;
        private final double baseConversion; // 转换为基础单位的系数
        
        public Unit(String symbol, String name, UnitSystem system, double baseConversion) {
            this.symbol = symbol;
            this.name = name;
            this.system = system;
            this.baseConversion = baseConversion;
        }
        
        public String getSymbol() { return symbol; }
        public String getName() { return name; }
        public UnitSystem getSystem() { return system; }
        public double getBaseConversion() { return baseConversion; }
    }
    
    // 各类别的单位映射
    private static final Map<MeasurementCategory, List<Unit>> CATEGORY_UNITS = new HashMap<>();
    
    static {
        // 长度单位
        CATEGORY_UNITS.put(MeasurementCategory.LENGTH, Arrays.asList(
            new Unit("mm", "毫米", UnitSystem.METRIC, 0.001),
            new Unit("cm", "厘米", UnitSystem.METRIC, 0.01),
            new Unit("m", "米", UnitSystem.METRIC, 1.0),
            new Unit("km", "千米", UnitSystem.METRIC, 1000.0),
            new Unit("in", "英寸", UnitSystem.IMPERIAL, 0.0254),
            new Unit("ft", "英尺", UnitSystem.IMPERIAL, 0.3048),
            new Unit("yd", "码", UnitSystem.IMPERIAL, 0.9144),
            new Unit("mi", "英里", UnitSystem.IMPERIAL, 1609.34)
        ));
        
        // 重量单位
        CATEGORY_UNITS.put(MeasurementCategory.WEIGHT, Arrays.asList(
            new Unit("mg", "毫克", UnitSystem.METRIC, 0.000001),
            new Unit("g", "克", UnitSystem.METRIC, 0.001),
            new Unit("kg", "千克", UnitSystem.METRIC, 1.0),
            new Unit("t", "吨", UnitSystem.METRIC, 1000.0),
            new Unit("oz", "盎司", UnitSystem.IMPERIAL, 0.0283495),
            new Unit("lb", "磅", UnitSystem.IMPERIAL, 0.453592),
            new Unit("st", "英石", UnitSystem.IMPERIAL, 6.35029)
        ));
        
        // 体积单位
        CATEGORY_UNITS.put(MeasurementCategory.VOLUME, Arrays.asList(
            new Unit("ml", "毫升", UnitSystem.METRIC, 0.001),
            new Unit("l", "升", UnitSystem.METRIC, 1.0),
            new Unit("m³", "立方米", UnitSystem.METRIC, 1000.0),
            new Unit("fl oz", "液体盎司", UnitSystem.IMPERIAL, 0.0295735),
            new Unit("pt", "品脱", UnitSystem.IMPERIAL, 0.473176),
            new Unit("qt", "夸脱", UnitSystem.IMPERIAL, 0.946353),
            new Unit("gal", "加仑", UnitSystem.IMPERIAL, 3.78541)
        ));
        
        // 温度单位
        CATEGORY_UNITS.put(MeasurementCategory.TEMPERATURE, Arrays.asList(
            new Unit("°C", "摄氏度", UnitSystem.METRIC, 1.0),
            new Unit("°F", "华氏度", UnitSystem.IMPERIAL, 1.0),
            new Unit("K", "开尔文", UnitSystem.METRIC, 1.0)
        ));
        
        // 时间单位
        CATEGORY_UNITS.put(MeasurementCategory.TIME, Arrays.asList(
            new Unit("ms", "毫秒", UnitSystem.METRIC, 0.001),
            new Unit("s", "秒", UnitSystem.METRIC, 1.0),
            new Unit("min", "分钟", UnitSystem.METRIC, 60.0),
            new Unit("h", "小时", UnitSystem.METRIC, 3600.0),
            new Unit("d", "天", UnitSystem.METRIC, 86400.0),
            new Unit("w", "周", UnitSystem.METRIC, 604800.0),
            new Unit("mo", "月", UnitSystem.METRIC, 2629746.0),
            new Unit("y", "年", UnitSystem.METRIC, 31556952.0)
        ));
        
        // 面积单位
        CATEGORY_UNITS.put(MeasurementCategory.AREA, Arrays.asList(
            new Unit("mm²", "平方毫米", UnitSystem.METRIC, 0.000001),
            new Unit("cm²", "平方厘米", UnitSystem.METRIC, 0.0001),
            new Unit("m²", "平方米", UnitSystem.METRIC, 1.0),
            new Unit("km²", "平方千米", UnitSystem.METRIC, 1000000.0),
            new Unit("in²", "平方英寸", UnitSystem.IMPERIAL, 0.00064516),
            new Unit("ft²", "平方英尺", UnitSystem.IMPERIAL, 0.092903),
            new Unit("yd²", "平方码", UnitSystem.IMPERIAL, 0.836127),
            new Unit("ac", "英亩", UnitSystem.IMPERIAL, 4046.86)
        ));
        
        // 速度单位
        CATEGORY_UNITS.put(MeasurementCategory.SPEED, Arrays.asList(
            new Unit("m/s", "米每秒", UnitSystem.METRIC, 1.0),
            new Unit("km/h", "千米每小时", UnitSystem.METRIC, 0.277778),
            new Unit("ft/s", "英尺每秒", UnitSystem.IMPERIAL, 0.3048),
            new Unit("mph", "英里每小时", UnitSystem.IMPERIAL, 0.44704),
            new Unit("kn", "节", UnitSystem.METRIC, 0.514444)
        ));
        
        // 压力单位
        CATEGORY_UNITS.put(MeasurementCategory.PRESSURE, Arrays.asList(
            new Unit("Pa", "帕斯卡", UnitSystem.METRIC, 1.0),
            new Unit("kPa", "千帕", UnitSystem.METRIC, 1000.0),
            new Unit("MPa", "兆帕", UnitSystem.METRIC, 1000000.0),
            new Unit("bar", "巴", UnitSystem.METRIC, 100000.0),
            new Unit("atm", "大气压", UnitSystem.METRIC, 101325.0),
            new Unit("psi", "磅每平方英寸", UnitSystem.IMPERIAL, 6894.76)
        ));
        
        // 能量单位
        CATEGORY_UNITS.put(MeasurementCategory.ENERGY, Arrays.asList(
            new Unit("J", "焦耳", UnitSystem.METRIC, 1.0),
            new Unit("kJ", "千焦", UnitSystem.METRIC, 1000.0),
            new Unit("MJ", "兆焦", UnitSystem.METRIC, 1000000.0),
            new Unit("cal", "卡路里", UnitSystem.METRIC, 4.184),
            new Unit("kcal", "千卡", UnitSystem.METRIC, 4184.0),
            new Unit("Wh", "瓦时", UnitSystem.METRIC, 3600.0),
            new Unit("kWh", "千瓦时", UnitSystem.METRIC, 3600000.0),
            new Unit("BTU", "英热单位", UnitSystem.IMPERIAL, 1055.06)
        ));
    }

    @Override
    public String getType() {
        return "measurement";
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取度量类别
            String categoryStr = getStringParam(config, "category", "LENGTH");
            MeasurementCategory category = parseMeasurementCategory(categoryStr);
            
            // 获取单位制
            String systemStr = getStringParam(config, "system", "METRIC");
            UnitSystem system = parseUnitSystem(systemStr);
            
            // 获取输出格式
            String formatStr = getStringParam(config, "format", "VALUE_UNIT");
            OutputFormat format = parseOutputFormat(formatStr);
            
            // 选择单位
            Unit unit = selectUnit(category, system, config);
            
            // 生成数值
            BigDecimal value = generateValue(unit, config);
            
            // 处理单位转换
            String convertTo = getStringParam(config, "convert_to", null);
            if (convertTo != null) {
                value = convertUnit(value, unit, convertTo, category);
                unit = findUnitBySymbol(convertTo, category);
            }
            
            // 格式化输出
            return formatMeasurement(value, unit, format, config);
            
        } catch (Exception e) {
            logger.error("Failed to generate measurement", e);
            // 返回一个默认的度量值作为fallback
            return "100.00 m";
        }
    }

    /**
     * 解析度量类别
     */
    private MeasurementCategory parseMeasurementCategory(String categoryStr) {
        try {
            return MeasurementCategory.valueOf(categoryStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid measurement category: {}, using LENGTH as default", categoryStr);
            return MeasurementCategory.LENGTH;
        }
    }

    /**
     * 解析单位制
     */
    private UnitSystem parseUnitSystem(String systemStr) {
        try {
            return UnitSystem.valueOf(systemStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid unit system: {}, using METRIC as default", systemStr);
            return UnitSystem.METRIC;
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
            return OutputFormat.valueOf(formatStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid output format: {}, using VALUE_UNIT as default", formatStr);
            return OutputFormat.VALUE_UNIT;
        }
    }

    /**
     * 选择单位
     */
    private Unit selectUnit(MeasurementCategory category, UnitSystem system, FieldConfig config) {
        // 处理随机类别
        if (category == MeasurementCategory.RANDOM) {
            MeasurementCategory[] categories = {MeasurementCategory.LENGTH, MeasurementCategory.WEIGHT, 
                                              MeasurementCategory.VOLUME, MeasurementCategory.TEMPERATURE};
            category = categories[random.nextInt(categories.length)];
        }
        
        // 获取指定单位
        String unitStr = getStringParam(config, "unit", null);
        if (unitStr != null) {
            Unit specifiedUnit = findUnitBySymbol(unitStr, category);
            if (specifiedUnit != null) {
                return specifiedUnit;
            }
        }
        
        // 根据单位制过滤单位
        List<Unit> availableUnits = CATEGORY_UNITS.get(category);
        if (availableUnits == null || availableUnits.isEmpty()) {
            // 返回默认单位
            return new Unit("unit", "单位", UnitSystem.METRIC, 1.0);
        }
        
        List<Unit> filteredUnits = new ArrayList<>();
        for (Unit unit : availableUnits) {
            if (system == UnitSystem.BOTH || system == UnitSystem.RANDOM || 
                unit.getSystem() == system) {
                filteredUnits.add(unit);
            }
        }
        
        if (filteredUnits.isEmpty()) {
            filteredUnits = availableUnits;
        }
        
        return filteredUnits.get(random.nextInt(filteredUnits.size()));
    }

    /**
     * 根据符号查找单位
     */
    private Unit findUnitBySymbol(String symbol, MeasurementCategory category) {
        List<Unit> units = CATEGORY_UNITS.get(category);
        if (units != null) {
            for (Unit unit : units) {
                if (unit.getSymbol().equals(symbol)) {
                    return unit;
                }
            }
        }
        return null;
    }

    /**
     * 生成数值
     */
    private BigDecimal generateValue(Unit unit, FieldConfig config) {
        double min = getDoubleParam(config, "min", 0.0);
        double max = getDoubleParam(config, "max", 100.0);
        
        // 根据单位调整范围
        if (unit.getSymbol().equals("mm") || unit.getSymbol().equals("mg")) {
            max = Math.max(max, 1000.0);
        } else if (unit.getSymbol().equals("km") || unit.getSymbol().equals("t")) {
            max = Math.min(max, 100.0);
        }
        
        double value = min + random.nextDouble() * (max - min);
        
        int precision = getIntParam(config, "precision", 2);
        String roundingStr = getStringParam(config, "rounding", "HALF_UP");
        RoundingMode rounding = parseRoundingMode(roundingStr);
        
        return BigDecimal.valueOf(value).setScale(precision, rounding);
    }

    /**
     * 解析舍入模式
     */
    private RoundingMode parseRoundingMode(String roundingStr) {
        try {
            return RoundingMode.valueOf(roundingStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid rounding mode: {}, using HALF_UP as default", roundingStr);
            return RoundingMode.HALF_UP;
        }
    }

    /**
     * 单位转换
     */
    private BigDecimal convertUnit(BigDecimal value, Unit fromUnit, String toUnitSymbol, MeasurementCategory category) {
        Unit toUnit = findUnitBySymbol(toUnitSymbol, category);
        if (toUnit == null) {
            logger.warn("Target unit not found: {}", toUnitSymbol);
            return value;
        }
        
        // 特殊处理温度转换
        if (category == MeasurementCategory.TEMPERATURE) {
            return convertTemperature(value, fromUnit.getSymbol(), toUnit.getSymbol());
        }
        
        // 通用单位转换：先转换为基础单位，再转换为目标单位
        double baseValue = value.doubleValue() * fromUnit.getBaseConversion();
        double convertedValue = baseValue / toUnit.getBaseConversion();
        
        return BigDecimal.valueOf(convertedValue).setScale(value.scale(), RoundingMode.HALF_UP);
    }

    /**
     * 温度转换
     */
    private BigDecimal convertTemperature(BigDecimal value, String fromUnit, String toUnit) {
        double val = value.doubleValue();
        
        // 先转换为摄氏度
        double celsius = val;
        if ("°F".equals(fromUnit)) {
            celsius = (val - 32) * 5.0 / 9.0;
        } else if ("K".equals(fromUnit)) {
            celsius = val - 273.15;
        }
        
        // 再转换为目标单位
        double result = celsius;
        if ("°F".equals(toUnit)) {
            result = celsius * 9.0 / 5.0 + 32;
        } else if ("K".equals(toUnit)) {
            result = celsius + 273.15;
        }
        
        return BigDecimal.valueOf(result).setScale(value.scale(), RoundingMode.HALF_UP);
    }

    /**
     * 格式化度量值
     */
    private String formatMeasurement(BigDecimal value, Unit unit, OutputFormat format, FieldConfig config) {
        switch (format) {
            case VALUE_UNIT:
                return formatValueUnit(value, unit, config);
            case UNIT_VALUE:
                return formatUnitValue(value, unit, config);
            case VALUE_ONLY:
                return formatValueOnly(value, config);
            case UNIT_ONLY:
                return formatUnitOnly(unit, config);
            case JSON:
                return formatAsJson(value, unit, config);
            default:
                return formatValueUnit(value, unit, config);
        }
    }

    /**
     * 格式化为数值+单位
     */
    private String formatValueUnit(BigDecimal value, Unit unit, FieldConfig config) {
        boolean scientific = getBooleanParam(config, "scientific", false);
        boolean includeSymbol = getBooleanParam(config, "include_symbol", true);
        boolean includeName = getBooleanParam(config, "include_name", false);
        
        String valueStr = scientific ? String.format("%.3E", value.doubleValue()) : value.toPlainString();
        
        if (includeSymbol && includeName) {
            return valueStr + " " + unit.getSymbol() + " (" + unit.getName() + ")";
        } else if (includeSymbol) {
            return valueStr + " " + unit.getSymbol();
        } else if (includeName) {
            return valueStr + " " + unit.getName();
        } else {
            return valueStr + " " + unit.getSymbol();
        }
    }

    /**
     * 格式化为单位+数值
     */
    private String formatUnitValue(BigDecimal value, Unit unit, FieldConfig config) {
        boolean scientific = getBooleanParam(config, "scientific", false);
        boolean includeSymbol = getBooleanParam(config, "include_symbol", true);
        
        String valueStr = scientific ? String.format("%.3E", value.doubleValue()) : value.toPlainString();
        String unitStr = includeSymbol ? unit.getSymbol() : unit.getName();
        
        return unitStr + " " + valueStr;
    }

    /**
     * 格式化为仅数值
     */
    private String formatValueOnly(BigDecimal value, FieldConfig config) {
        boolean scientific = getBooleanParam(config, "scientific", false);
        return scientific ? String.format("%.3E", value.doubleValue()) : value.toPlainString();
    }

    /**
     * 格式化为仅单位
     */
    private String formatUnitOnly(Unit unit, FieldConfig config) {
        boolean includeSymbol = getBooleanParam(config, "include_symbol", true);
        return includeSymbol ? unit.getSymbol() : unit.getName();
    }

    /**
     * 格式化为JSON
     */
    private String formatAsJson(BigDecimal value, Unit unit, FieldConfig config) {
        boolean scientific = getBooleanParam(config, "scientific", false);
        String valueStr = scientific ? String.format("%.3E", value.doubleValue()) : value.toPlainString();
        
        StringBuilder json = new StringBuilder("{");
        json.append("\"value\":").append(valueStr).append(",");
        json.append("\"unit\":\"").append(unit.getSymbol()).append("\",");
        json.append("\"unitName\":\"").append(unit.getName()).append("\",");
        json.append("\"system\":\"").append(unit.getSystem().name().toLowerCase()).append("\"");
        json.append("}");
        
        return json.toString();
    }
}
