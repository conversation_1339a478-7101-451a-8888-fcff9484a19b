<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReligionGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">ReligionGenerator.java</span></div><h1>ReligionGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 宗教信仰生成器
 * 
 * 支持的参数：
 * - type: 宗教类型 (BUDDHISM|CHRISTIANITY|ISLAM|TAOISM|NONE|ANY)
 * - none_ratio: 无信仰者占比 (0.0-1.0)
 * - file: 自定义宗教列表文件路径
 * - weights: 宗教权重配置 (如 &quot;无:70,佛教:15,基督教:10&quot;)
 * - format: 输出格式 (CHINESE|ENGLISH|CODE)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class ReligionGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(ReligionGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 宗教信仰枚举
<span class="nc" id="L30">    private enum Religion {</span>
<span class="nc" id="L31">        NONE, // 无信仰</span>
<span class="nc" id="L32">        BUDDHISM, // 佛教</span>
<span class="nc" id="L33">        CHRISTIANITY, // 基督教</span>
<span class="nc" id="L34">        ISLAM, // 伊斯兰教</span>
<span class="nc" id="L35">        TAOISM, // 道教</span>
<span class="nc" id="L36">        CONFUCIANISM, // 儒教</span>
<span class="nc" id="L37">        HINDUISM, // 印度教</span>
<span class="nc" id="L38">        JUDAISM, // 犹太教</span>
<span class="nc" id="L39">        FOLK_BELIEF, // 民间信仰</span>
<span class="nc" id="L40">        OTHER // 其他</span>
    }

    // 宗教信息类
    private static class ReligionInfo {
        final Religion religion;
        final String chineseName;
        final String englishName;
        final String code;

<span class="nc" id="L50">        ReligionInfo(Religion religion, String chineseName, String englishName, String code) {</span>
<span class="nc" id="L51">            this.religion = religion;</span>
<span class="nc" id="L52">            this.chineseName = chineseName;</span>
<span class="nc" id="L53">            this.englishName = englishName;</span>
<span class="nc" id="L54">            this.code = code;</span>
<span class="nc" id="L55">        }</span>
    }

    // 宗教信息映射
<span class="nc" id="L59">    private static final Map&lt;Religion, ReligionInfo&gt; RELIGION_INFO = new HashMap&lt;&gt;();</span>

    // 现实分布权重（基于中国宗教信仰调查数据）
<span class="nc" id="L62">    private static final Map&lt;Religion, Double&gt; REALISTIC_WEIGHTS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L65">        initializeReligionInfo();</span>
<span class="nc" id="L66">        initializeRealisticWeights();</span>
<span class="nc" id="L67">    }</span>

    private static void initializeReligionInfo() {
<span class="nc" id="L70">        RELIGION_INFO.put(Religion.NONE, new ReligionInfo(</span>
                Religion.NONE, &quot;无&quot;, &quot;None&quot;, &quot;00&quot;));
<span class="nc" id="L72">        RELIGION_INFO.put(Religion.BUDDHISM, new ReligionInfo(</span>
                Religion.BUDDHISM, &quot;佛教&quot;, &quot;Buddhism&quot;, &quot;01&quot;));
<span class="nc" id="L74">        RELIGION_INFO.put(Religion.CHRISTIANITY, new ReligionInfo(</span>
                Religion.CHRISTIANITY, &quot;基督教&quot;, &quot;Christianity&quot;, &quot;02&quot;));
<span class="nc" id="L76">        RELIGION_INFO.put(Religion.ISLAM, new ReligionInfo(</span>
                Religion.ISLAM, &quot;伊斯兰教&quot;, &quot;Islam&quot;, &quot;03&quot;));
<span class="nc" id="L78">        RELIGION_INFO.put(Religion.TAOISM, new ReligionInfo(</span>
                Religion.TAOISM, &quot;道教&quot;, &quot;Taoism&quot;, &quot;04&quot;));
<span class="nc" id="L80">        RELIGION_INFO.put(Religion.CONFUCIANISM, new ReligionInfo(</span>
                Religion.CONFUCIANISM, &quot;儒教&quot;, &quot;Confucianism&quot;, &quot;05&quot;));
<span class="nc" id="L82">        RELIGION_INFO.put(Religion.HINDUISM, new ReligionInfo(</span>
                Religion.HINDUISM, &quot;印度教&quot;, &quot;Hinduism&quot;, &quot;06&quot;));
<span class="nc" id="L84">        RELIGION_INFO.put(Religion.JUDAISM, new ReligionInfo(</span>
                Religion.JUDAISM, &quot;犹太教&quot;, &quot;Judaism&quot;, &quot;07&quot;));
<span class="nc" id="L86">        RELIGION_INFO.put(Religion.FOLK_BELIEF, new ReligionInfo(</span>
                Religion.FOLK_BELIEF, &quot;民间信仰&quot;, &quot;Folk Belief&quot;, &quot;08&quot;));
<span class="nc" id="L88">        RELIGION_INFO.put(Religion.OTHER, new ReligionInfo(</span>
                Religion.OTHER, &quot;其他&quot;, &quot;Other&quot;, &quot;99&quot;));
<span class="nc" id="L90">    }</span>

    private static void initializeRealisticWeights() {
        // 基于中国宗教信仰状况的近似数据
<span class="nc" id="L94">        REALISTIC_WEIGHTS.put(Religion.NONE, 70.0); // 70% 无信仰</span>
<span class="nc" id="L95">        REALISTIC_WEIGHTS.put(Religion.BUDDHISM, 15.0); // 15% 佛教</span>
<span class="nc" id="L96">        REALISTIC_WEIGHTS.put(Religion.FOLK_BELIEF, 8.0); // 8% 民间信仰</span>
<span class="nc" id="L97">        REALISTIC_WEIGHTS.put(Religion.CHRISTIANITY, 3.0); // 3% 基督教</span>
<span class="nc" id="L98">        REALISTIC_WEIGHTS.put(Religion.TAOISM, 2.0); // 2% 道教</span>
<span class="nc" id="L99">        REALISTIC_WEIGHTS.put(Religion.ISLAM, 1.5); // 1.5% 伊斯兰教</span>
<span class="nc" id="L100">        REALISTIC_WEIGHTS.put(Religion.CONFUCIANISM, 0.3); // 0.3% 儒教</span>
<span class="nc" id="L101">        REALISTIC_WEIGHTS.put(Religion.HINDUISM, 0.1); // 0.1% 印度教</span>
<span class="nc" id="L102">        REALISTIC_WEIGHTS.put(Religion.JUDAISM, 0.05); // 0.05% 犹太教</span>
<span class="nc" id="L103">        REALISTIC_WEIGHTS.put(Religion.OTHER, 0.05); // 0.05% 其他</span>
<span class="nc" id="L104">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L108">        return &quot;religion&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L113">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L120">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L121">            double noneRatio = Double.parseDouble(config.getParam(&quot;none_ratio&quot;, String.class, &quot;0.7&quot;));</span>
<span class="nc" id="L122">            String weightsParam = config.getParam(&quot;weights&quot;, String.class, null);</span>
<span class="nc" id="L123">            String format = config.getParam(&quot;format&quot;, String.class, &quot;CHINESE&quot;);</span>

            // 加载宗教数据
<span class="nc" id="L126">            List&lt;Religion&gt; religions = loadReligions(config, type);</span>

            // 根据权重选择宗教
<span class="nc" id="L129">            Religion religion = selectReligion(religions, noneRatio, weightsParam);</span>

            // 将宗教信息存入上下文
<span class="nc" id="L132">            context.put(&quot;religion&quot;, religion.name());</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">            context.put(&quot;has_religion&quot;, religion != Religion.NONE ? &quot;true&quot; : &quot;false&quot;);</span>

            // 格式化输出
<span class="nc" id="L136">            String result = formatReligion(religion, format);</span>

<span class="nc" id="L138">            logger.debug(&quot;Generated religion: {}&quot;, result);</span>
<span class="nc" id="L139">            return result;</span>

<span class="nc" id="L141">        } catch (Exception e) {</span>
<span class="nc" id="L142">            logger.error(&quot;Error generating religion&quot;, e);</span>
<span class="nc" id="L143">            return &quot;无&quot;;</span>
        }
    }

    private List&lt;Religion&gt; loadReligions(FieldConfig config, String type) {
<span class="nc" id="L148">        String customFile = config.getParam(&quot;file&quot;, String.class, null);</span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">        if (customFile != null) {</span>
            try {
<span class="nc" id="L151">                List&lt;String&gt; customReligions = DataLoader.loadDataFromFile(customFile);</span>
<span class="nc" id="L152">                List&lt;Religion&gt; religions = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L154" title="All 2 branches missed.">                for (String religionName : customReligions) {</span>
                    // 尝试匹配已知宗教
<span class="nc" id="L156">                    Religion religion = findReligionByName(religionName);</span>
<span class="nc bnc" id="L157" title="All 2 branches missed.">                    if (religion != null) {</span>
<span class="nc" id="L158">                        religions.add(religion);</span>
                    }
<span class="nc" id="L160">                }</span>

<span class="nc bnc" id="L162" title="All 2 branches missed.">                if (!religions.isEmpty()) {</span>
<span class="nc" id="L163">                    return religions;</span>
                }
<span class="nc" id="L165">            } catch (Exception e) {</span>
<span class="nc" id="L166">                logger.warn(&quot;Failed to load custom religion file: {}&quot;, customFile, e);</span>
<span class="nc" id="L167">            }</span>
        }

        // 使用内置宗教数据
<span class="nc" id="L171">        List&lt;Religion&gt; religions = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L173" title="All 6 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;BUDDHISM&quot;:
<span class="nc" id="L175">                religions.add(Religion.BUDDHISM);</span>
<span class="nc" id="L176">                break;</span>

            case &quot;CHRISTIANITY&quot;:
<span class="nc" id="L179">                religions.add(Religion.CHRISTIANITY);</span>
<span class="nc" id="L180">                break;</span>

            case &quot;ISLAM&quot;:
<span class="nc" id="L183">                religions.add(Religion.ISLAM);</span>
<span class="nc" id="L184">                break;</span>

            case &quot;TAOISM&quot;:
<span class="nc" id="L187">                religions.add(Religion.TAOISM);</span>
<span class="nc" id="L188">                break;</span>

            case &quot;NONE&quot;:
<span class="nc" id="L191">                religions.add(Religion.NONE);</span>
<span class="nc" id="L192">                break;</span>

            case &quot;ANY&quot;:
            default:
<span class="nc" id="L196">                religions.addAll(Arrays.asList(Religion.values()));</span>
                break;
        }

<span class="nc" id="L200">        return religions;</span>
    }

    private Religion findReligionByName(String name) {
<span class="nc bnc" id="L204" title="All 2 branches missed.">        for (ReligionInfo info : RELIGION_INFO.values()) {</span>
<span class="nc bnc" id="L205" title="All 4 branches missed.">            if (info.chineseName.equals(name) || info.englishName.equalsIgnoreCase(name)) {</span>
<span class="nc" id="L206">                return info.religion;</span>
            }
<span class="nc" id="L208">        }</span>
<span class="nc" id="L209">        return null;</span>
    }

    private Religion selectReligion(List&lt;Religion&gt; religions, double noneRatio, String weightsParam) {
        // 如果只有一个选项，直接返回
<span class="nc bnc" id="L214" title="All 2 branches missed.">        if (religions.size() == 1) {</span>
<span class="nc" id="L215">            return religions.get(0);</span>
        }

        // 如果有自定义权重，使用自定义权重
<span class="nc bnc" id="L219" title="All 4 branches missed.">        if (weightsParam != null &amp;&amp; !weightsParam.isEmpty()) {</span>
<span class="nc" id="L220">            return selectWithCustomWeights(religions, weightsParam);</span>
        }

        // 如果指定了无信仰比例且包含无信仰选项
<span class="nc bnc" id="L224" title="All 2 branches missed.">        if (religions.contains(Religion.NONE)) {</span>
<span class="nc" id="L225">            double randomValue = random.nextDouble();</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">            if (randomValue &lt; noneRatio) {</span>
<span class="nc" id="L227">                return Religion.NONE;</span>
            } else {
                // 从有信仰的宗教中选择
<span class="nc" id="L230">                List&lt;Religion&gt; religiousOptions = new ArrayList&lt;&gt;(religions);</span>
<span class="nc" id="L231">                religiousOptions.remove(Religion.NONE);</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">                if (!religiousOptions.isEmpty()) {</span>
<span class="nc" id="L233">                    return selectWithRealisticWeights(religiousOptions);</span>
                }
            }
        }

        // 使用现实分布权重
<span class="nc" id="L239">        return selectWithRealisticWeights(religions);</span>
    }

    private Religion selectWithRealisticWeights(List&lt;Religion&gt; religions) {
        // 计算总权重
<span class="nc" id="L244">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">        for (Religion religion : religions) {</span>
<span class="nc" id="L246">            totalWeight += REALISTIC_WEIGHTS.getOrDefault(religion, 0.1);</span>
<span class="nc" id="L247">        }</span>

<span class="nc bnc" id="L249" title="All 2 branches missed.">        if (totalWeight &lt;= 0) {</span>
            // 如果没有权重，使用均匀分布
<span class="nc" id="L251">            return religions.get(random.nextInt(religions.size()));</span>
        }

        // 随机选择
<span class="nc" id="L255">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L256">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L258" title="All 2 branches missed.">        for (Religion religion : religions) {</span>
<span class="nc" id="L259">            currentWeight += REALISTIC_WEIGHTS.getOrDefault(religion, 0.1);</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L261">                return religion;</span>
            }
<span class="nc" id="L263">        }</span>

        // 默认返回第一个
<span class="nc" id="L266">        return religions.get(0);</span>
    }

    private Religion selectWithCustomWeights(List&lt;Religion&gt; religions, String weightsParam) {
<span class="nc" id="L270">        Map&lt;String, Integer&gt; customWeights = parseWeights(weightsParam);</span>

        // 计算总权重
<span class="nc" id="L273">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">        for (Religion religion : religions) {</span>
<span class="nc" id="L275">            ReligionInfo info = RELIGION_INFO.get(religion);</span>
<span class="nc" id="L276">            int weight = customWeights.getOrDefault(info.chineseName,</span>
<span class="nc" id="L277">                    customWeights.getOrDefault(info.englishName, 1));</span>
<span class="nc" id="L278">            totalWeight += weight;</span>
<span class="nc" id="L279">        }</span>

        // 随机选择
<span class="nc" id="L282">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L283">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L285" title="All 2 branches missed.">        for (Religion religion : religions) {</span>
<span class="nc" id="L286">            ReligionInfo info = RELIGION_INFO.get(religion);</span>
<span class="nc" id="L287">            int weight = customWeights.getOrDefault(info.chineseName,</span>
<span class="nc" id="L288">                    customWeights.getOrDefault(info.englishName, 1));</span>
<span class="nc" id="L289">            currentWeight += weight;</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L291">                return religion;</span>
            }
<span class="nc" id="L293">        }</span>

        // 默认返回第一个
<span class="nc" id="L296">        return religions.get(0);</span>
    }

    private Map&lt;String, Integer&gt; parseWeights(String weightsParam) {
<span class="nc" id="L300">        Map&lt;String, Integer&gt; weights = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L303">            String[] pairs = weightsParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L304" title="All 2 branches missed.">            for (String pair : pairs) {</span>
<span class="nc" id="L305">                String[] parts = pair.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L307">                    String religion = parts[0].trim();</span>
<span class="nc" id="L308">                    int weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L309">                    weights.put(religion, weight);</span>
                }
            }
<span class="nc" id="L312">        } catch (Exception e) {</span>
<span class="nc" id="L313">            logger.warn(&quot;Failed to parse weights: {}&quot;, weightsParam, e);</span>
<span class="nc" id="L314">        }</span>

<span class="nc" id="L316">        return weights;</span>
    }

    private String formatReligion(Religion religion, String format) {
<span class="nc" id="L320">        ReligionInfo info = RELIGION_INFO.get(religion);</span>

<span class="nc bnc" id="L322" title="All 4 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;CHINESE&quot;:
            case &quot;CN&quot;:
<span class="nc" id="L325">                return info.chineseName;</span>

            case &quot;ENGLISH&quot;:
            case &quot;EN&quot;:
<span class="nc" id="L329">                return info.englishName;</span>

            case &quot;CODE&quot;:
<span class="nc" id="L332">                return info.code;</span>

            default:
<span class="nc" id="L335">                logger.warn(&quot;Unknown religion format: {}. Using CHINESE format.&quot;, format);</span>
<span class="nc" id="L336">                return info.chineseName;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>