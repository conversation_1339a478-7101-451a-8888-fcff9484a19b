<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GeolocationGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">GeolocationGenerator</span></div><h1>GeolocationGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">331 of 594</td><td class="ctr2">44%</td><td class="bar">22 of 29</td><td class="ctr2">24%</td><td class="ctr1">18</td><td class="ctr2">31</td><td class="ctr1">59</td><td class="ctr2">103</td><td class="ctr1">4</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a0"><a href="GeolocationGenerator.java.html#L330" class="el_method">convertToDMS(double, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="70" alt="70"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="GeolocationGenerator.java.html#L219" class="el_method">generateWithinRadius(double, double, double)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="61" alt="61"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="GeolocationGenerator.java.html#L312" class="el_method">formatWKT(GeolocationGenerator.GeoLocation, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="56" alt="56"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h2">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="GeolocationGenerator.java.html#L199" class="el_method">getBounds(FieldConfig)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="44" alt="44"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="33" alt="33"/></td><td class="ctr2" id="c9">42%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="GeolocationGenerator.java.html#L276" class="el_method">formatDMS(GeolocationGenerator.GeoLocation, int)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="36" alt="36"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="GeolocationGenerator.java.html#L161" class="el_method">generateGeoLocation(FieldConfig, boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="87" alt="87"/></td><td class="ctr2" id="c7">75%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">37%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="GeolocationGenerator.java.html#L243" class="el_method">formatGeoLocation(GeolocationGenerator.GeoLocation, GeolocationGenerator.OutputFormat, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">46%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">40%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="GeolocationGenerator.java.html#L122" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="33" alt="33"/></td><td class="ctr2" id="c5">82%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="GeolocationGenerator.java.html#L261" class="el_method">formatDecimal(GeolocationGenerator.GeoLocation, int)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="23" alt="23"/></td><td class="ctr2" id="c6">76%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="GeolocationGenerator.java.html#L149" class="el_method">parseOutputFormat(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">36%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="GeolocationGenerator.java.html#L293" class="el_method">formatJSON(GeolocationGenerator.GeoLocation, int)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="57" height="10" title="55" alt="55"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i6">9</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a14"><a href="GeolocationGenerator.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="GeolocationGenerator.java.html#L40" class="el_method">GeolocationGenerator()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="GeolocationGenerator.java.html#L110" class="el_method">getType()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a11"><a href="GeolocationGenerator.java.html#L115" class="el_method">getConfigClass()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>