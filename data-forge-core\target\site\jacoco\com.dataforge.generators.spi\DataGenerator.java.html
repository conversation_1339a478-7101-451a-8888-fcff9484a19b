<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.spi</a> &gt; <span class="el_source">DataGenerator.java</span></div><h1>DataGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.spi;

import com.dataforge.core.DataForgeContext;
import com.dataforge.model.FieldConfig;

/**
 * 所有数据生成器的统一接口。
 * 
 * &lt;p&gt;
 * 该接口定义了数据生成器的核心契约，支持泛型以确保类型安全。
 * 所有具体的数据生成器实现都必须实现此接口。
 * 
 * @param &lt;T&gt; 生成的数据类型 (例如: String, Integer, LocalDate)
 * @param &lt;C&gt; 该生成器特定的配置类，必须继承自 FieldConfig
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface DataGenerator&lt;T, C extends FieldConfig&gt; {

    /**
     * 返回该生成器能处理的类型名称，用于CLI或配置文件中的匹配。
     * 
     * &lt;p&gt;
     * 类型名称应该是唯一的，且易于理解的字符串标识符。
     * 例如: &quot;idcard&quot;, &quot;name&quot;, &quot;phone&quot;, &quot;email&quot; 等。
     * 
     * @return 数据类型标识符，不能为null或空字符串
     */
    String getType();

    /**
     * 根据配置和上下文生成数据。
     * 
     * &lt;p&gt;
     * 这是数据生成的核心方法。实现时应该：
     * &lt;ul&gt;
     * &lt;li&gt;根据配置参数生成符合要求的数据&lt;/li&gt;
     * &lt;li&gt;利用上下文实现字段间的关联性&lt;/li&gt;
     * &lt;li&gt;确保生成的数据符合业务规则和校验要求&lt;/li&gt;
     * &lt;li&gt;处理异常情况并提供有意义的错误信息&lt;/li&gt;
     * &lt;/ul&gt;
     * 
     * @param config  特定于此生成器的配置对象，包含生成参数
     * @param context 生成上下文，用于处理字段间的关联和共享数据
     * @return 生成的数据，类型为T
     * @throws IllegalArgumentException 当配置参数无效时
     * @throws RuntimeException         当生成过程中发生不可恢复的错误时
     */
    T generate(C config, DataForgeContext context);

    /**
     * 返回此生成器对应的配置类类型。
     * 
     * &lt;p&gt;
     * 用于配置反序列化和类型检查。框架会使用此信息
     * 将通用的配置映射转换为具体的配置对象。
     * 
     * @return 配置类的Class对象，不能为null
     */
    Class&lt;C&gt; getConfigClass();

    /**
     * 验证配置参数的有效性。
     * 
     * &lt;p&gt;
     * 默认实现返回true，子类可以重写此方法提供具体的验证逻辑。
     * 建议在generate方法执行前调用此方法进行预检查。
     * 
     * @param config 要验证的配置对象
     * @return 如果配置有效返回true，否则返回false
     */
    default boolean isValidConfig(C config) {
<span class="nc bnc" id="L74" title="All 2 branches missed.">        return config != null;</span>
    }

    /**
     * 获取生成器的描述信息。
     * 
     * &lt;p&gt;
     * 用于CLI帮助信息和文档生成。默认返回类型名称，
     * 子类可以重写提供更详细的描述。
     * 
     * @return 生成器的描述信息
     */
    default String getDescription() {
<span class="nc" id="L87">        return &quot;Generator for &quot; + getType() + &quot; data type&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>