<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LandlineGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">LandlineGenerator.java</span></div><h1>LandlineGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 固话号码生成器
 * 
 * 支持的参数：
 * - region: 地区 (CN|US|UK|JP|ANY)
 * - area_code: 区号 (指定区号或随机)
 * - format: 输出格式 (STANDARD|INTERNATIONAL|COMPACT|PARENTHESES)
 * - include_extension: 是否包含分机号 (true|false)
 * - extension_length: 分机号长度 (2-6)
 * - number_length: 号码长度 (7-8)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class LandlineGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(LandlineGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 输出格式枚举
<span class="nc" id="L30">    private enum LandlineFormat {</span>
<span class="nc" id="L31">        STANDARD, // 标准格式：区号-号码</span>
<span class="nc" id="L32">        INTERNATIONAL, // 国际格式：+国家码-区号-号码</span>
<span class="nc" id="L33">        COMPACT, // 紧凑格式：区号号码（无分隔符）</span>
<span class="nc" id="L34">        PARENTHESES // 括号格式：(区号) 号码</span>
    }

    // 国家信息
<span class="nc" id="L38">    private static final Map&lt;String, CountryInfo&gt; COUNTRY_INFO = new HashMap&lt;&gt;();</span>

    // 中国区号和对应城市
<span class="nc" id="L41">    private static final Map&lt;String, String&gt; CHINA_AREA_CODES = new HashMap&lt;&gt;();</span>

    // 国家信息类
    private static class CountryInfo {
        final String countryCode;
        final Map&lt;String, String&gt; areaCodes; // 区号 -&gt; 城市名
        final int[] numberLengths; // 支持的号码长度
        final String separator;

<span class="nc" id="L50">        CountryInfo(String countryCode, Map&lt;String, String&gt; areaCodes, int[] numberLengths, String separator) {</span>
<span class="nc" id="L51">            this.countryCode = countryCode;</span>
<span class="nc" id="L52">            this.areaCodes = areaCodes;</span>
<span class="nc" id="L53">            this.numberLengths = numberLengths;</span>
<span class="nc" id="L54">            this.separator = separator;</span>
<span class="nc" id="L55">        }</span>
    }

    static {
<span class="nc" id="L59">        initializeChinaAreaCodes();</span>
<span class="nc" id="L60">        initializeCountryInfo();</span>
<span class="nc" id="L61">    }</span>

    private static void initializeChinaAreaCodes() {
        // 主要城市区号
<span class="nc" id="L65">        CHINA_AREA_CODES.put(&quot;010&quot;, &quot;北京&quot;);</span>
<span class="nc" id="L66">        CHINA_AREA_CODES.put(&quot;021&quot;, &quot;上海&quot;);</span>
<span class="nc" id="L67">        CHINA_AREA_CODES.put(&quot;022&quot;, &quot;天津&quot;);</span>
<span class="nc" id="L68">        CHINA_AREA_CODES.put(&quot;023&quot;, &quot;重庆&quot;);</span>
<span class="nc" id="L69">        CHINA_AREA_CODES.put(&quot;024&quot;, &quot;沈阳&quot;);</span>
<span class="nc" id="L70">        CHINA_AREA_CODES.put(&quot;025&quot;, &quot;南京&quot;);</span>
<span class="nc" id="L71">        CHINA_AREA_CODES.put(&quot;027&quot;, &quot;武汉&quot;);</span>
<span class="nc" id="L72">        CHINA_AREA_CODES.put(&quot;028&quot;, &quot;成都&quot;);</span>
<span class="nc" id="L73">        CHINA_AREA_CODES.put(&quot;029&quot;, &quot;西安&quot;);</span>

        // 省会城市区号
<span class="nc" id="L76">        CHINA_AREA_CODES.put(&quot;0311&quot;, &quot;石家庄&quot;);</span>
<span class="nc" id="L77">        CHINA_AREA_CODES.put(&quot;0351&quot;, &quot;太原&quot;);</span>
<span class="nc" id="L78">        CHINA_AREA_CODES.put(&quot;0371&quot;, &quot;郑州&quot;);</span>
<span class="nc" id="L79">        CHINA_AREA_CODES.put(&quot;0431&quot;, &quot;长春&quot;);</span>
<span class="nc" id="L80">        CHINA_AREA_CODES.put(&quot;0451&quot;, &quot;哈尔滨&quot;);</span>
<span class="nc" id="L81">        CHINA_AREA_CODES.put(&quot;0471&quot;, &quot;呼和浩特&quot;);</span>
<span class="nc" id="L82">        CHINA_AREA_CODES.put(&quot;0531&quot;, &quot;济南&quot;);</span>
<span class="nc" id="L83">        CHINA_AREA_CODES.put(&quot;0551&quot;, &quot;合肥&quot;);</span>
<span class="nc" id="L84">        CHINA_AREA_CODES.put(&quot;0571&quot;, &quot;杭州&quot;);</span>
<span class="nc" id="L85">        CHINA_AREA_CODES.put(&quot;0591&quot;, &quot;福州&quot;);</span>
<span class="nc" id="L86">        CHINA_AREA_CODES.put(&quot;0731&quot;, &quot;长沙&quot;);</span>
<span class="nc" id="L87">        CHINA_AREA_CODES.put(&quot;0771&quot;, &quot;南宁&quot;);</span>
<span class="nc" id="L88">        CHINA_AREA_CODES.put(&quot;0791&quot;, &quot;南昌&quot;);</span>
<span class="nc" id="L89">        CHINA_AREA_CODES.put(&quot;0851&quot;, &quot;贵阳&quot;);</span>
<span class="nc" id="L90">        CHINA_AREA_CODES.put(&quot;0871&quot;, &quot;昆明&quot;);</span>
<span class="nc" id="L91">        CHINA_AREA_CODES.put(&quot;0891&quot;, &quot;拉萨&quot;);</span>
<span class="nc" id="L92">        CHINA_AREA_CODES.put(&quot;0931&quot;, &quot;兰州&quot;);</span>
<span class="nc" id="L93">        CHINA_AREA_CODES.put(&quot;0951&quot;, &quot;银川&quot;);</span>
<span class="nc" id="L94">        CHINA_AREA_CODES.put(&quot;0971&quot;, &quot;西宁&quot;);</span>
<span class="nc" id="L95">        CHINA_AREA_CODES.put(&quot;0991&quot;, &quot;乌鲁木齐&quot;);</span>

        // 重要地级市区号
<span class="nc" id="L98">        CHINA_AREA_CODES.put(&quot;0411&quot;, &quot;大连&quot;);</span>
<span class="nc" id="L99">        CHINA_AREA_CODES.put(&quot;0512&quot;, &quot;苏州&quot;);</span>
<span class="nc" id="L100">        CHINA_AREA_CODES.put(&quot;0532&quot;, &quot;青岛&quot;);</span>
<span class="nc" id="L101">        CHINA_AREA_CODES.put(&quot;0574&quot;, &quot;宁波&quot;);</span>
<span class="nc" id="L102">        CHINA_AREA_CODES.put(&quot;0592&quot;, &quot;厦门&quot;);</span>
<span class="nc" id="L103">        CHINA_AREA_CODES.put(&quot;0755&quot;, &quot;深圳&quot;);</span>
<span class="nc" id="L104">        CHINA_AREA_CODES.put(&quot;0756&quot;, &quot;珠海&quot;);</span>
<span class="nc" id="L105">        CHINA_AREA_CODES.put(&quot;0760&quot;, &quot;中山&quot;);</span>
<span class="nc" id="L106">        CHINA_AREA_CODES.put(&quot;0769&quot;, &quot;东莞&quot;);</span>
<span class="nc" id="L107">        CHINA_AREA_CODES.put(&quot;020&quot;, &quot;广州&quot;);</span>
<span class="nc" id="L108">    }</span>

    private static void initializeCountryInfo() {
        // 中国
<span class="nc" id="L112">        Map&lt;String, String&gt; usAreaCodes = new HashMap&lt;&gt;();</span>
<span class="nc" id="L113">        usAreaCodes.put(&quot;212&quot;, &quot;New York&quot;);</span>
<span class="nc" id="L114">        usAreaCodes.put(&quot;213&quot;, &quot;Los Angeles&quot;);</span>
<span class="nc" id="L115">        usAreaCodes.put(&quot;312&quot;, &quot;Chicago&quot;);</span>
<span class="nc" id="L116">        usAreaCodes.put(&quot;415&quot;, &quot;San Francisco&quot;);</span>
<span class="nc" id="L117">        usAreaCodes.put(&quot;617&quot;, &quot;Boston&quot;);</span>
<span class="nc" id="L118">        usAreaCodes.put(&quot;202&quot;, &quot;Washington DC&quot;);</span>
<span class="nc" id="L119">        usAreaCodes.put(&quot;305&quot;, &quot;Miami&quot;);</span>
<span class="nc" id="L120">        usAreaCodes.put(&quot;713&quot;, &quot;Houston&quot;);</span>
<span class="nc" id="L121">        usAreaCodes.put(&quot;214&quot;, &quot;Dallas&quot;);</span>
<span class="nc" id="L122">        usAreaCodes.put(&quot;404&quot;, &quot;Atlanta&quot;);</span>

<span class="nc" id="L124">        COUNTRY_INFO.put(&quot;CN&quot;, new CountryInfo(&quot;+86&quot;, CHINA_AREA_CODES, new int[] { 7, 8 }, &quot;-&quot;));</span>
<span class="nc" id="L125">        COUNTRY_INFO.put(&quot;US&quot;, new CountryInfo(&quot;+1&quot;, usAreaCodes, new int[] { 7 }, &quot;-&quot;));</span>

        // 英国
<span class="nc" id="L128">        Map&lt;String, String&gt; ukAreaCodes = new HashMap&lt;&gt;();</span>
<span class="nc" id="L129">        ukAreaCodes.put(&quot;20&quot;, &quot;London&quot;);</span>
<span class="nc" id="L130">        ukAreaCodes.put(&quot;121&quot;, &quot;Birmingham&quot;);</span>
<span class="nc" id="L131">        ukAreaCodes.put(&quot;131&quot;, &quot;Edinburgh&quot;);</span>
<span class="nc" id="L132">        ukAreaCodes.put(&quot;141&quot;, &quot;Glasgow&quot;);</span>
<span class="nc" id="L133">        ukAreaCodes.put(&quot;151&quot;, &quot;Liverpool&quot;);</span>
<span class="nc" id="L134">        ukAreaCodes.put(&quot;161&quot;, &quot;Manchester&quot;);</span>
<span class="nc" id="L135">        ukAreaCodes.put(&quot;113&quot;, &quot;Leeds&quot;);</span>
<span class="nc" id="L136">        ukAreaCodes.put(&quot;114&quot;, &quot;Sheffield&quot;);</span>
<span class="nc" id="L137">        ukAreaCodes.put(&quot;115&quot;, &quot;Nottingham&quot;);</span>
<span class="nc" id="L138">        ukAreaCodes.put(&quot;117&quot;, &quot;Bristol&quot;);</span>

<span class="nc" id="L140">        COUNTRY_INFO.put(&quot;UK&quot;, new CountryInfo(&quot;+44&quot;, ukAreaCodes, new int[] { 6, 7 }, &quot; &quot;));</span>

        // 日本
<span class="nc" id="L143">        Map&lt;String, String&gt; jpAreaCodes = new HashMap&lt;&gt;();</span>
<span class="nc" id="L144">        jpAreaCodes.put(&quot;3&quot;, &quot;Tokyo&quot;);</span>
<span class="nc" id="L145">        jpAreaCodes.put(&quot;6&quot;, &quot;Osaka&quot;);</span>
<span class="nc" id="L146">        jpAreaCodes.put(&quot;52&quot;, &quot;Nagoya&quot;);</span>
<span class="nc" id="L147">        jpAreaCodes.put(&quot;92&quot;, &quot;Fukuoka&quot;);</span>
<span class="nc" id="L148">        jpAreaCodes.put(&quot;11&quot;, &quot;Sapporo&quot;);</span>
<span class="nc" id="L149">        jpAreaCodes.put(&quot;22&quot;, &quot;Sendai&quot;);</span>
<span class="nc" id="L150">        jpAreaCodes.put(&quot;75&quot;, &quot;Kyoto&quot;);</span>
<span class="nc" id="L151">        jpAreaCodes.put(&quot;82&quot;, &quot;Hiroshima&quot;);</span>
<span class="nc" id="L152">        jpAreaCodes.put(&quot;95&quot;, &quot;Niigata&quot;);</span>
<span class="nc" id="L153">        jpAreaCodes.put(&quot;96&quot;, &quot;Kumamoto&quot;);</span>

<span class="nc" id="L155">        COUNTRY_INFO.put(&quot;JP&quot;, new CountryInfo(&quot;+81&quot;, jpAreaCodes, new int[] { 8 }, &quot;-&quot;));</span>
<span class="nc" id="L156">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L160">        return &quot;landline&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L165">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L172">            String region = config.getParam(&quot;region&quot;, String.class, &quot;CN&quot;);</span>
<span class="nc" id="L173">            String areaCode = config.getParam(&quot;area_code&quot;, String.class, null);</span>
<span class="nc" id="L174">            String format = config.getParam(&quot;format&quot;, String.class, &quot;STANDARD&quot;);</span>
<span class="nc" id="L175">            boolean includeExtension = Boolean</span>
<span class="nc" id="L176">                    .parseBoolean(config.getParam(&quot;include_extension&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L177">            int extensionLength = Integer.parseInt(config.getParam(&quot;extension_length&quot;, String.class, &quot;3&quot;));</span>
<span class="nc" id="L178">            int numberLength = Integer.parseInt(config.getParam(&quot;number_length&quot;, String.class, &quot;0&quot;));</span>

            // 生成固话号码
<span class="nc" id="L181">            String landlineNumber = generateLandlineNumber(region, areaCode, format, includeExtension, extensionLength,</span>
                    numberLength);

            // 将固话号码信息存入上下文
<span class="nc" id="L185">            context.put(&quot;landline_number&quot;, landlineNumber);</span>
<span class="nc" id="L186">            context.put(&quot;landline_region&quot;, region);</span>
<span class="nc" id="L187">            context.put(&quot;landline_area_code&quot;, extractAreaCode(landlineNumber, region));</span>
<span class="nc" id="L188">            context.put(&quot;landline_city&quot;, getCityName(extractAreaCode(landlineNumber, region), region));</span>

<span class="nc" id="L190">            logger.debug(&quot;Generated landline number: {}&quot;, landlineNumber);</span>
<span class="nc" id="L191">            return landlineNumber;</span>

<span class="nc" id="L193">        } catch (Exception e) {</span>
<span class="nc" id="L194">            logger.error(&quot;Error generating landline number&quot;, e);</span>
<span class="nc" id="L195">            return &quot;010-12345678&quot;;</span>
        }
    }

    private String generateLandlineNumber(String region, String areaCode, String format,
            boolean includeExtension, int extensionLength, int numberLength) {

        // 获取国家信息
<span class="nc" id="L203">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L205">            countryInfo = COUNTRY_INFO.get(&quot;CN&quot;); // 默认使用中国</span>
        }

        // 确定区号
<span class="nc bnc" id="L209" title="All 2 branches missed.">        String finalAreaCode = areaCode != null ? areaCode : selectRandomAreaCode(countryInfo);</span>

        // 确定号码长度
<span class="nc bnc" id="L212" title="All 2 branches missed.">        int finalNumberLength = numberLength &gt; 0 ? numberLength : selectRandomNumberLength(countryInfo);</span>

        // 生成号码主体
<span class="nc" id="L215">        String mainNumber = generateMainNumber(finalNumberLength);</span>

        // 生成分机号
<span class="nc bnc" id="L218" title="All 2 branches missed.">        String extension = includeExtension ? generateExtension(extensionLength) : null;</span>

        // 应用格式
<span class="nc" id="L221">        return formatLandlineNumber(countryInfo, finalAreaCode, mainNumber, extension, format);</span>
    }

    private String selectRandomAreaCode(CountryInfo countryInfo) {
<span class="nc" id="L225">        List&lt;String&gt; areaCodes = new ArrayList&lt;&gt;(countryInfo.areaCodes.keySet());</span>
<span class="nc" id="L226">        return areaCodes.get(random.nextInt(areaCodes.size()));</span>
    }

    private int selectRandomNumberLength(CountryInfo countryInfo) {
<span class="nc" id="L230">        int[] lengths = countryInfo.numberLengths;</span>
<span class="nc" id="L231">        return lengths[random.nextInt(lengths.length)];</span>
    }

    private String generateMainNumber(int length) {
<span class="nc" id="L235">        StringBuilder number = new StringBuilder();</span>

        // 第一位不能是0
<span class="nc" id="L238">        number.append(1 + random.nextInt(9));</span>

        // 生成剩余位数
<span class="nc bnc" id="L241" title="All 2 branches missed.">        for (int i = 1; i &lt; length; i++) {</span>
<span class="nc" id="L242">            number.append(random.nextInt(10));</span>
        }

<span class="nc" id="L245">        return number.toString();</span>
    }

    private String generateExtension(int length) {
<span class="nc" id="L249">        StringBuilder extension = new StringBuilder();</span>

<span class="nc bnc" id="L251" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L252">            extension.append(random.nextInt(10));</span>
        }

<span class="nc" id="L255">        return extension.toString();</span>
    }

    private String formatLandlineNumber(CountryInfo countryInfo, String areaCode, String mainNumber,
            String extension, String format) {

<span class="nc" id="L261">        StringBuilder landlineNumber = new StringBuilder();</span>
<span class="nc" id="L262">        LandlineFormat landlineFormat = parseLandlineFormat(format);</span>

<span class="nc bnc" id="L264" title="All 4 branches missed.">        switch (landlineFormat) {</span>
            case INTERNATIONAL:
<span class="nc" id="L266">                landlineNumber.append(countryInfo.countryCode);</span>
<span class="nc" id="L267">                landlineNumber.append(countryInfo.separator);</span>
<span class="nc" id="L268">                landlineNumber.append(areaCode);</span>
<span class="nc" id="L269">                landlineNumber.append(countryInfo.separator);</span>
<span class="nc" id="L270">                landlineNumber.append(mainNumber);</span>
<span class="nc" id="L271">                break;</span>

            case COMPACT:
<span class="nc" id="L274">                landlineNumber.append(areaCode).append(mainNumber);</span>
<span class="nc" id="L275">                break;</span>

            case PARENTHESES:
<span class="nc" id="L278">                landlineNumber.append(&quot;(&quot;).append(areaCode).append(&quot;) &quot;);</span>
<span class="nc" id="L279">                landlineNumber.append(mainNumber);</span>
<span class="nc" id="L280">                break;</span>

            case STANDARD:
            default:
<span class="nc" id="L284">                landlineNumber.append(areaCode);</span>
<span class="nc" id="L285">                landlineNumber.append(countryInfo.separator);</span>
<span class="nc" id="L286">                landlineNumber.append(mainNumber);</span>
                break;
        }

        // 添加分机号
<span class="nc bnc" id="L291" title="All 2 branches missed.">        if (extension != null) {</span>
<span class="nc" id="L292">            landlineNumber.append(&quot; ext.&quot;).append(extension);</span>
        }

<span class="nc" id="L295">        return landlineNumber.toString();</span>
    }

    private LandlineFormat parseLandlineFormat(String format) {
        try {
<span class="nc" id="L300">            return LandlineFormat.valueOf(format.toUpperCase());</span>
<span class="nc" id="L301">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L302">            logger.warn(&quot;Unknown landline format: {}. Using STANDARD.&quot;, format);</span>
<span class="nc" id="L303">            return LandlineFormat.STANDARD;</span>
        }
    }

    private String extractAreaCode(String landlineNumber, String region) {
        try {
<span class="nc" id="L309">            CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L310" title="All 2 branches missed.">            if (countryInfo == null) {</span>
<span class="nc" id="L311">                return &quot;unknown&quot;;</span>
            }

            // 移除国家代码和格式字符
<span class="nc" id="L315">            String cleanNumber = landlineNumber;</span>
<span class="nc bnc" id="L316" title="All 2 branches missed.">            if (cleanNumber.startsWith(countryInfo.countryCode)) {</span>
<span class="nc" id="L317">                cleanNumber = cleanNumber.substring(countryInfo.countryCode.length());</span>
            }

            // 移除格式字符
<span class="nc" id="L321">            cleanNumber = cleanNumber.replaceAll(&quot;[^0-9]&quot;, &quot;&quot;);</span>

            // 根据区号长度提取
<span class="nc bnc" id="L324" title="All 2 branches missed.">            for (String code : countryInfo.areaCodes.keySet()) {</span>
<span class="nc bnc" id="L325" title="All 2 branches missed.">                if (cleanNumber.startsWith(code)) {</span>
<span class="nc" id="L326">                    return code;</span>
                }
<span class="nc" id="L328">            }</span>

<span class="nc" id="L330">            return &quot;unknown&quot;;</span>
<span class="nc" id="L331">        } catch (Exception e) {</span>
<span class="nc" id="L332">            return &quot;unknown&quot;;</span>
        }
    }

    private String getCityName(String areaCode, String region) {
<span class="nc" id="L337">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L338" title="All 4 branches missed.">        if (countryInfo != null &amp;&amp; countryInfo.areaCodes.containsKey(areaCode)) {</span>
<span class="nc" id="L339">            return countryInfo.areaCodes.get(areaCode);</span>
        }
<span class="nc" id="L341">        return &quot;unknown&quot;;</span>
    }

    /**
     * 验证固话号码格式
     */
    public boolean validateLandlineNumber(String landlineNumber, String region) {
<span class="nc bnc" id="L348" title="All 4 branches missed.">        if (landlineNumber == null || landlineNumber.isEmpty()) {</span>
<span class="nc" id="L349">            return false;</span>
        }

<span class="nc" id="L352">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L353" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L354">            return false;</span>
        }

        // 移除所有非数字字符（除了+号）
<span class="nc" id="L358">        String cleanNumber = landlineNumber.replaceAll(&quot;[^0-9+]&quot;, &quot;&quot;);</span>

        // 检查是否包含有效的区号
<span class="nc bnc" id="L361" title="All 2 branches missed.">        for (String areaCode : countryInfo.areaCodes.keySet()) {</span>
<span class="nc bnc" id="L362" title="All 2 branches missed.">            if (cleanNumber.contains(areaCode)) {</span>
<span class="nc" id="L363">                return true;</span>
            }
<span class="nc" id="L365">        }</span>

<span class="nc" id="L367">        return false;</span>
    }

    /**
     * 生成特定城市的固话号码
     */
    public String generateCityLandline(String city, String region) {
<span class="nc" id="L374">        CountryInfo countryInfo = COUNTRY_INFO.get(region.toUpperCase());</span>
<span class="nc bnc" id="L375" title="All 2 branches missed.">        if (countryInfo == null) {</span>
<span class="nc" id="L376">            return generateLandlineNumber(region, null, &quot;STANDARD&quot;, false, 0, 0);</span>
        }

        // 查找城市对应的区号
<span class="nc" id="L380">        String areaCode = null;</span>
<span class="nc bnc" id="L381" title="All 2 branches missed.">        for (Map.Entry&lt;String, String&gt; entry : countryInfo.areaCodes.entrySet()) {</span>
<span class="nc bnc" id="L382" title="All 2 branches missed.">            if (entry.getValue().equalsIgnoreCase(city) ||</span>
<span class="nc bnc" id="L383" title="All 2 branches missed.">                    entry.getValue().contains(city) ||</span>
<span class="nc bnc" id="L384" title="All 2 branches missed.">                    city.contains(entry.getValue())) {</span>
<span class="nc" id="L385">                areaCode = entry.getKey();</span>
<span class="nc" id="L386">                break;</span>
            }
<span class="nc" id="L388">        }</span>

<span class="nc bnc" id="L390" title="All 2 branches missed.">        if (areaCode == null) {</span>
<span class="nc" id="L391">            areaCode = selectRandomAreaCode(countryInfo);</span>
        }

<span class="nc" id="L394">        int numberLength = selectRandomNumberLength(countryInfo);</span>
<span class="nc" id="L395">        String mainNumber = generateMainNumber(numberLength);</span>

<span class="nc" id="L397">        return formatLandlineNumber(countryInfo, areaCode, mainNumber, null, &quot;STANDARD&quot;);</span>
    }

    /**
     * 生成企业固话号码（可能包含多个分机号）
     */
    public String generateCorporateLandline(String region, int extensionCount) {
<span class="nc" id="L404">        String baseLandline = generateLandlineNumber(region, null, &quot;STANDARD&quot;, false, 0, 0);</span>

<span class="nc bnc" id="L406" title="All 2 branches missed.">        if (extensionCount &lt;= 1) {</span>
<span class="nc" id="L407">            return baseLandline + &quot; ext.&quot; + generateExtension(3);</span>
        }

<span class="nc" id="L410">        StringBuilder result = new StringBuilder(baseLandline);</span>
<span class="nc" id="L411">        result.append(&quot; (ext.&quot;);</span>

<span class="nc bnc" id="L413" title="All 2 branches missed.">        for (int i = 0; i &lt; extensionCount; i++) {</span>
<span class="nc bnc" id="L414" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L415">                result.append(&quot;,&quot;);</span>
            }
<span class="nc" id="L417">            result.append(generateExtension(3));</span>
        }

<span class="nc" id="L420">        result.append(&quot;)&quot;);</span>
<span class="nc" id="L421">        return result.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>