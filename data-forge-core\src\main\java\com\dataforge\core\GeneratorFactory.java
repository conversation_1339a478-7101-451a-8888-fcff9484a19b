package com.dataforge.core;

import com.dataforge.generators.spi.DataGenerator;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 数据生成器工厂类。
 * 
 * <p>
 * 使用Java ServiceLoader (SPI) 机制动态加载所有DataGenerator实现。
 * 该工厂负责管理和提供数据生成器实例，支持运行时发现和注册新的生成器。
 * 
 * <p>
 * 工厂采用单例模式，确保生成器的统一管理和高效访问。
 * 所有注册的生成器都会被缓存，避免重复创建实例。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class GeneratorFactory {

    private static final Logger logger = LoggerFactory.getLogger(GeneratorFactory.class);

    /**
     * 生成器缓存，键为类型名称，值为生成器实例。
     * 使用ConcurrentHashMap确保线程安全。
     */
    private final Map<String, DataGenerator<?, ?>> generators = new ConcurrentHashMap<>();

    /**
     * 是否已初始化标志。
     */
    private volatile boolean initialized = false;

    /**
     * 构造函数，自动初始化生成器。
     */
    public GeneratorFactory() {
        initialize();
    }

    /**
     * 初始化生成器工厂，加载所有可用的数据生成器。
     * 
     * <p>
     * 该方法使用ServiceLoader机制扫描classpath中所有实现了
     * DataGenerator接口的类，并将其注册到工厂中。
     */
    private synchronized void initialize() {
        if (initialized) {
            return;
        }

        logger.info("Initializing GeneratorFactory...");

        try {
            // 使用ServiceLoader加载所有DataGenerator实现
            @SuppressWarnings("rawtypes")
            ServiceLoader<DataGenerator> serviceLoader = ServiceLoader.load(DataGenerator.class);

            int loadedCount = 0;
            for (DataGenerator<?, ?> generator : serviceLoader) {
                try {
                    String type = generator.getType();
                    if (type == null || type.trim().isEmpty()) {
                        logger.warn("Generator {} returned null or empty type, skipping",
                                generator.getClass().getName());
                        continue;
                    }

                    // 检查是否已存在相同类型的生成器
                    if (generators.containsKey(type)) {
                        logger.warn("Duplicate generator type '{}' found. Existing: {}, New: {}. Keeping existing.",
                                type, generators.get(type).getClass().getName(), generator.getClass().getName());
                        continue;
                    }

                    generators.put(type, generator);
                    loadedCount++;
                    logger.debug("Registered generator: type='{}', class='{}'", type, generator.getClass().getName());

                } catch (Exception e) {
                    logger.error("Failed to register generator: {}", generator.getClass().getName(), e);
                }
            }

            initialized = true;
            logger.info("GeneratorFactory initialized successfully. Loaded {} generators: {}",
                    loadedCount, generators.keySet());

        } catch (Exception e) {
            logger.error("Failed to initialize GeneratorFactory", e);
            throw new RuntimeException("Failed to initialize GeneratorFactory", e);
        }
    }

    /**
     * 根据类型获取数据生成器。
     * 
     * @param type 数据类型标识符
     * @return 对应的数据生成器，如果不存在返回null
     * @throws IllegalArgumentException 当type为null或空字符串时
     */
    public DataGenerator<?, ?> getGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("Generator type cannot be null or empty");
        }

        DataGenerator<?, ?> generator = generators.get(type.trim());
        if (generator == null) {
            logger.debug("No generator found for type: {}", type);
        }

        return generator;
    }

    /**
     * 检查是否存在指定类型的生成器。
     * 
     * @param type 数据类型标识符
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return false;
        }
        return generators.containsKey(type.trim());
    }

    /**
     * 获取所有已注册的生成器类型。
     * 
     * @return 生成器类型集合的副本
     */
    public java.util.Set<String> getAvailableTypes() {
        return new java.util.HashSet<>(generators.keySet());
    }

    /**
     * 获取已注册的生成器数量。
     * 
     * @return 生成器数量
     */
    public int getGeneratorCount() {
        return generators.size();
    }

    /**
     * 手动注册数据生成器。
     * 
     * <p>
     * 该方法主要用于测试或动态注册生成器的场景。
     * 在正常情况下，生成器应该通过SPI机制自动发现和注册。
     * 
     * @param generator 要注册的数据生成器
     * @throws IllegalArgumentException 当generator为null或其类型为null/空字符串时
     * @throws IllegalStateException    当指定类型的生成器已存在时
     */
    public void registerGenerator(DataGenerator<?, ?> generator) {
        if (generator == null) {
            throw new IllegalArgumentException("Generator cannot be null");
        }

        String type = generator.getType();
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("Generator type cannot be null or empty");
        }

        if (generators.containsKey(type)) {
            throw new IllegalStateException("Generator for type '" + type + "' already exists");
        }

        generators.put(type, generator);
        logger.info("Manually registered generator: type='{}', class='{}'", type, generator.getClass().getName());
    }

    /**
     * 注销指定类型的数据生成器。
     * 
     * <p>
     * 该方法主要用于测试或动态管理生成器的场景。
     * 
     * @param type 要注销的生成器类型
     * @return 被注销的生成器，如果不存在返回null
     */
    public DataGenerator<?, ?> unregisterGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        DataGenerator<?, ?> removed = generators.remove(type.trim());
        if (removed != null) {
            logger.info("Unregistered generator: type='{}', class='{}'", type, removed.getClass().getName());
        }

        return removed;
    }

    /**
     * 重新初始化生成器工厂。
     * 
     * <p>
     * 清除所有已注册的生成器，重新扫描和加载。
     * 该方法主要用于开发和测试场景。
     */
    public synchronized void reinitialize() {
        logger.info("Reinitializing GeneratorFactory...");
        generators.clear();
        initialized = false;
        initialize();
    }

    /**
     * 获取生成器的详细信息。
     * 
     * @return 包含所有生成器信息的映射
     */
    public Map<String, String> getGeneratorInfo() {
        Map<String, String> info = new HashMap<>();
        for (Map.Entry<String, DataGenerator<?, ?>> entry : generators.entrySet()) {
            DataGenerator<?, ?> generator = entry.getValue();
            info.put(entry.getKey(), String.format("%s - %s",
                    generator.getClass().getSimpleName(),
                    generator.getDescription()));
        }
        return info;
    }

    @Override
    public String toString() {
        return String.format("GeneratorFactory{initialized=%s, generatorCount=%d, types=%s}",
                initialized, generators.size(), generators.keySet());
    }
}