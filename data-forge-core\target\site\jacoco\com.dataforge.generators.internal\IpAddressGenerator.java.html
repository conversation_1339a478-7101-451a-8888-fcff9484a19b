<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IpAddressGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">IpAddressGenerator.java</span></div><h1>IpAddressGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * IP地址生成器
 * 
 * 支持的参数：
 * - version: IP版本 (IPV4|IPV6|ANY)
 * - type: IP类型 (PUBLIC|PRIVATE|LOOPBACK|MULTICAST|ANY)
 * - subnet: 子网范围 (如 &quot;***********/24&quot;)
 * - format: 输出格式 (STANDARD|COMPRESSED|FULL)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L22">public class IpAddressGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(IpAddressGenerator.class);</span>
<span class="nc" id="L25">    private static final Random random = new Random();</span>

    // IPv4私网地址范围
<span class="nc" id="L28">    private static final List&lt;String&gt; IPV4_PRIVATE_RANGES = Arrays.asList(</span>
            &quot;10.0.0.0/8&quot;, // 10.0.0.0 - **************
            &quot;**********/12&quot;, // ********** - **************
            &quot;***********/16&quot; // *********** - ***************
    );

    @Override
    public String getType() {
<span class="nc" id="L36">        return &quot;ip&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L41">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L48">            String version = config.getParam(&quot;version&quot;, String.class, &quot;IPV4&quot;);</span>
<span class="nc" id="L49">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L50">            String subnet = config.getParam(&quot;subnet&quot;, String.class, null);</span>
<span class="nc" id="L51">            String format = config.getParam(&quot;format&quot;, String.class, &quot;STANDARD&quot;);</span>

            // 生成IP地址
            String ipAddress;
<span class="nc bnc" id="L55" title="All 4 branches missed.">            if (subnet != null &amp;&amp; !subnet.isEmpty()) {</span>
<span class="nc" id="L56">                ipAddress = generateFromSubnet(subnet);</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">            } else if (&quot;IPV6&quot;.equalsIgnoreCase(version)) {</span>
<span class="nc" id="L58">                ipAddress = generateIpv6(type, format);</span>
            } else {
<span class="nc" id="L60">                ipAddress = generateIpv4(type);</span>
            }

            // 将IP信息存入上下文
<span class="nc" id="L64">            context.put(&quot;ip_address&quot;, ipAddress);</span>
<span class="nc" id="L65">            context.put(&quot;ip_version&quot;, version);</span>

<span class="nc" id="L67">            logger.debug(&quot;Generated IP address: {}&quot;, ipAddress);</span>
<span class="nc" id="L68">            return ipAddress;</span>

<span class="nc" id="L70">        } catch (Exception e) {</span>
<span class="nc" id="L71">            logger.error(&quot;Error generating IP address&quot;, e);</span>
<span class="nc" id="L72">            return &quot;***********&quot;;</span>
        }
    }

    private String generateFromSubnet(String subnet) {
        try {
<span class="nc" id="L78">            String[] parts = subnet.split(&quot;/&quot;);</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">            if (parts.length != 2) {</span>
<span class="nc" id="L80">                return generateIpv4(&quot;PRIVATE&quot;);</span>
            }

<span class="nc" id="L83">            String networkAddress = parts[0];</span>
<span class="nc" id="L84">            int prefixLength = Integer.parseInt(parts[1]);</span>

<span class="nc bnc" id="L86" title="All 2 branches missed.">            if (networkAddress.contains(&quot;:&quot;)) {</span>
<span class="nc" id="L87">                return generateIpv6FromSubnet(networkAddress, prefixLength);</span>
            } else {
<span class="nc" id="L89">                return generateIpv4FromSubnet(networkAddress, prefixLength);</span>
            }

<span class="nc" id="L92">        } catch (Exception e) {</span>
<span class="nc" id="L93">            logger.warn(&quot;Failed to generate IP from subnet: {}&quot;, subnet, e);</span>
<span class="nc" id="L94">            return generateIpv4(&quot;PRIVATE&quot;);</span>
        }
    }

    private String generateIpv4FromSubnet(String networkAddress, int prefixLength) {
<span class="nc" id="L99">        String[] octets = networkAddress.split(&quot;\\.&quot;);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        if (octets.length != 4) {</span>
<span class="nc" id="L101">            return generateIpv4(&quot;PRIVATE&quot;);</span>
        }

        // 计算主机位数
<span class="nc" id="L105">        int hostBits = 32 - prefixLength;</span>
<span class="nc" id="L106">        int maxHosts = Math.max(1, (1 &lt;&lt; hostBits) - 2);</span>

        // 生成随机主机号
<span class="nc" id="L109">        int hostId = random.nextInt(maxHosts) + 1;</span>

        // 将网络地址转换为整数
<span class="nc" id="L112">        long networkInt = 0;</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">        for (int i = 0; i &lt; 4; i++) {</span>
<span class="nc" id="L114">            networkInt = (networkInt &lt;&lt; 8) + Integer.parseInt(octets[i]);</span>
        }

        // 添加主机号
<span class="nc" id="L118">        long ipInt = networkInt + hostId;</span>

        // 转换回点分十进制
<span class="nc" id="L121">        return String.format(&quot;%d.%d.%d.%d&quot;,</span>
<span class="nc" id="L122">                (ipInt &gt;&gt; 24) &amp; 0xFF,</span>
<span class="nc" id="L123">                (ipInt &gt;&gt; 16) &amp; 0xFF,</span>
<span class="nc" id="L124">                (ipInt &gt;&gt; 8) &amp; 0xFF,</span>
<span class="nc" id="L125">                ipInt &amp; 0xFF);</span>
    }

    private String generateIpv4(String type) {
<span class="nc bnc" id="L129" title="All 5 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;PRIVATE&quot;:
<span class="nc" id="L131">                return generateIpv4Private();</span>

            case &quot;LOOPBACK&quot;:
<span class="nc" id="L134">                return &quot;127.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256);</span>

            case &quot;MULTICAST&quot;:
<span class="nc" id="L137">                return (224 + random.nextInt(16)) + &quot;.&quot; + random.nextInt(256) + &quot;.&quot; +</span>
<span class="nc" id="L138">                        random.nextInt(256) + &quot;.&quot; + random.nextInt(256);</span>

            case &quot;PUBLIC&quot;:
<span class="nc" id="L141">                return generateIpv4Public();</span>

            case &quot;ANY&quot;:
            default:
<span class="nc bnc" id="L145" title="All 2 branches missed.">                return random.nextBoolean() ? generateIpv4Private() : generateIpv4Public();</span>
        }
    }

    private String generateIpv4Private() {
<span class="nc" id="L150">        String range = IPV4_PRIVATE_RANGES.get(random.nextInt(IPV4_PRIVATE_RANGES.size()));</span>

<span class="nc bnc" id="L152" title="All 2 branches missed.">        if (range.equals(&quot;10.0.0.0/8&quot;)) {</span>
<span class="nc" id="L153">            return &quot;10.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256);</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">        } else if (range.equals(&quot;**********/12&quot;)) {</span>
<span class="nc" id="L155">            return &quot;172.&quot; + (16 + random.nextInt(16)) + &quot;.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256);</span>
        } else { // ***********/16
<span class="nc" id="L157">            return &quot;192.168.&quot; + random.nextInt(256) + &quot;.&quot; + random.nextInt(256);</span>
        }
    }

    private String generateIpv4Public() {
        int a, b, c, d;

        do {
<span class="nc" id="L165">            a = 1 + random.nextInt(223); // 避免0和224-255</span>
<span class="nc" id="L166">            b = random.nextInt(256);</span>
<span class="nc" id="L167">            c = random.nextInt(256);</span>
<span class="nc" id="L168">            d = random.nextInt(256);</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">        } while (isReservedIpv4(a, b, c, d));</span>

<span class="nc" id="L171">        return a + &quot;.&quot; + b + &quot;.&quot; + c + &quot;.&quot; + d;</span>
    }

    private boolean isReservedIpv4(int a, int b, int c, int d) {
        // 检查是否为保留地址
<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (a == 127)</span>
<span class="nc" id="L177">            return true; // 127.x.x.x</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">        if (a == 10)</span>
<span class="nc" id="L179">            return true; // 10.x.x.x</span>
<span class="nc bnc" id="L180" title="All 6 branches missed.">        if (a == 172 &amp;&amp; b &gt;= 16 &amp;&amp; b &lt;= 31)</span>
<span class="nc" id="L181">            return true; // 172.16-31.x.x</span>
<span class="nc bnc" id="L182" title="All 4 branches missed.">        if (a == 192 &amp;&amp; b == 168)</span>
<span class="nc" id="L183">            return true; // 192.168.x.x</span>
<span class="nc bnc" id="L184" title="All 4 branches missed.">        if (a == 169 &amp;&amp; b == 254)</span>
<span class="nc" id="L185">            return true; // 169.254.x.x</span>

<span class="nc" id="L187">        return false;</span>
    }

    private String generateIpv6(String type, String format) {
<span class="nc" id="L191">        StringBuilder ipv6 = new StringBuilder();</span>

<span class="nc bnc" id="L193" title="All 5 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;PRIVATE&quot;:
<span class="nc" id="L195">                ipv6.append(&quot;fd&quot;).append(String.format(&quot;%02x&quot;, random.nextInt(256)));</span>
<span class="nc" id="L196">                break;</span>

            case &quot;LOOPBACK&quot;:
<span class="nc" id="L199">                return formatIpv6(&quot;::1&quot;, format);</span>

            case &quot;MULTICAST&quot;:
<span class="nc" id="L202">                ipv6.append(&quot;ff&quot;).append(String.format(&quot;%02x&quot;, random.nextInt(256)));</span>
<span class="nc" id="L203">                break;</span>

            case &quot;LINK_LOCAL&quot;:
<span class="nc" id="L206">                ipv6.append(&quot;fe80&quot;);</span>
<span class="nc" id="L207">                break;</span>

            case &quot;PUBLIC&quot;:
            case &quot;ANY&quot;:
            default:
                // 生成全球单播地址
<span class="nc" id="L213">                ipv6.append(String.format(&quot;%04x&quot;, 0x2000 + random.nextInt(0x2000)));</span>
                break;
        }

        // 补充剩余部分
<span class="nc bnc" id="L218" title="All 2 branches missed.">        for (int i = 1; i &lt; 8; i++) {</span>
<span class="nc" id="L219">            ipv6.append(&quot;:&quot;).append(String.format(&quot;%04x&quot;, random.nextInt(0x10000)));</span>
        }

<span class="nc" id="L222">        return formatIpv6(ipv6.toString(), format);</span>
    }

    private String generateIpv6FromSubnet(String networkAddress, int prefixLength) {
        // 简化的IPv6子网生成
<span class="nc" id="L227">        String[] parts = networkAddress.split(&quot;:&quot;);</span>
<span class="nc" id="L228">        StringBuilder ipv6 = new StringBuilder();</span>

        // 保留网络部分，随机生成主机部分
<span class="nc" id="L231">        int networkParts = prefixLength / 16;</span>

<span class="nc bnc" id="L233" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc bnc" id="L234" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L235">                ipv6.append(&quot;:&quot;);</span>
            }

<span class="nc bnc" id="L238" title="All 4 branches missed.">            if (i &lt; networkParts &amp;&amp; i &lt; parts.length) {</span>
<span class="nc" id="L239">                ipv6.append(parts[i]);</span>
            } else {
<span class="nc" id="L241">                ipv6.append(String.format(&quot;%04x&quot;, random.nextInt(0x10000)));</span>
            }
        }

<span class="nc" id="L245">        return ipv6.toString();</span>
    }

    private String formatIpv6(String ipv6, String format) {
<span class="nc bnc" id="L249" title="All 3 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;COMPRESSED&quot;:
<span class="nc" id="L251">                return compressIpv6(ipv6);</span>

            case &quot;FULL&quot;:
<span class="nc" id="L254">                return expandIpv6(ipv6);</span>

            case &quot;STANDARD&quot;:
            default:
<span class="nc" id="L258">                return ipv6;</span>
        }
    }

    private String compressIpv6(String ipv6) {
        // 简化的IPv6压缩算法
<span class="nc" id="L264">        return ipv6.replaceAll(&quot;(^|:)0+([0-9a-fA-F])&quot;, &quot;$1$2&quot;)</span>
<span class="nc" id="L265">                .replaceAll(&quot;::+&quot;, &quot;::&quot;);</span>
    }

    private String expandIpv6(String ipv6) {
        // 简化的IPv6展开算法
<span class="nc" id="L270">        String[] parts = ipv6.split(&quot;:&quot;);</span>
<span class="nc" id="L271">        StringBuilder expanded = new StringBuilder();</span>

<span class="nc bnc" id="L273" title="All 2 branches missed.">        for (int i = 0; i &lt; parts.length; i++) {</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L275">                expanded.append(&quot;:&quot;);</span>
            }

<span class="nc" id="L278">            String part = parts[i];</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">            if (part.length() &lt; 4) {</span>
<span class="nc" id="L280">                part = String.format(&quot;%04s&quot;, part).replace(' ', '0');</span>
            }
<span class="nc" id="L282">            expanded.append(part);</span>
        }

<span class="nc" id="L285">        return expanded.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>