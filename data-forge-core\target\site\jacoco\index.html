<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="jacoco-resources/report.gif" type="image/gif"/><title>DataForge Core</title><script type="text/javascript" src="jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="jacoco-sessions.html" class="el_session">Sessions</a></span><span class="el_report">DataForge Core</span></div><h1>DataForge Core</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">38,503 of 41,769</td><td class="ctr2">7%</td><td class="bar">2,721 of 2,857</td><td class="ctr2">4%</td><td class="ctr1">2,334</td><td class="ctr2">2,494</td><td class="ctr1">5,953</td><td class="ctr2">6,488</td><td class="ctr1">876</td><td class="ctr2">996</td><td class="ctr1">80</td><td class="ctr2">103</td></tr></tfoot><tbody><tr><td id="a2"><a href="com.dataforge.generators.internal/index.html" class="el_package">com.dataforge.generators.internal</a></td><td class="bar" id="b0"><img src="jacoco-resources/redbar.gif" width="109" height="10" title="32,936" alt="32,936"/><img src="jacoco-resources/greenbar.gif" width="10" height="10" title="3,089" alt="3,089"/></td><td class="ctr2" id="c1">8%</td><td class="bar" id="d0"><img src="jacoco-resources/redbar.gif" width="113" height="10" title="2,162" alt="2,162"/><img src="jacoco-resources/greenbar.gif" width="6" height="10" title="121" alt="121"/></td><td class="ctr2" id="e2">5%</td><td class="ctr1" id="f0">1,793</td><td class="ctr2" id="g0">1,940</td><td class="ctr1" id="h0">4,684</td><td class="ctr2" id="i0">5,175</td><td class="ctr1" id="j0">618</td><td class="ctr2" id="k0">729</td><td class="ctr1" id="l0">60</td><td class="ctr2" id="m0">80</td></tr><tr><td id="a8"><a href="com.dataforge.validation/index.html" class="el_package">com.dataforge.validation</a></td><td class="bar" id="b1"><img src="jacoco-resources/redbar.gif" width="7" height="10" title="2,204" alt="2,204"/></td><td class="ctr2" id="c2">5%</td><td class="bar" id="d1"><img src="jacoco-resources/redbar.gif" width="11" height="10" title="216" alt="216"/></td><td class="ctr2" id="e1">6%</td><td class="ctr1" id="f1">196</td><td class="ctr2" id="g1">204</td><td class="ctr1" id="h1">455</td><td class="ctr2" id="i1">483</td><td class="ctr1" id="j1">85</td><td class="ctr2" id="k1">89</td><td class="ctr1" id="l1">5</td><td class="ctr2" id="m1">6</td></tr><tr><td id="a4"><a href="com.dataforge.io/index.html" class="el_package">com.dataforge.io</a></td><td class="bar" id="b2"><img src="jacoco-resources/redbar.gif" width="4" height="10" title="1,309" alt="1,309"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="jacoco-resources/redbar.gif" width="7" height="10" title="142" alt="142"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">119</td><td class="ctr2" id="g2">119</td><td class="ctr1" id="h2">321</td><td class="ctr2" id="i2">321</td><td class="ctr1" id="j3">48</td><td class="ctr2" id="k3">48</td><td class="ctr1" id="l2">5</td><td class="ctr2" id="m2">5</td></tr><tr><td id="a1"><a href="com.dataforge.core/index.html" class="el_package">com.dataforge.core</a></td><td class="bar" id="b3"><img src="jacoco-resources/redbar.gif" width="1" height="10" title="558" alt="558"/></td><td class="ctr2" id="c3">3%</td><td class="bar" id="d3"><img src="jacoco-resources/redbar.gif" width="3" height="10" title="64" alt="64"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">57</td><td class="ctr2" id="g4">59</td><td class="ctr1" id="h5">123</td><td class="ctr2" id="i3">130</td><td class="ctr1" id="j4">25</td><td class="ctr2" id="k4">27</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m4">2</td></tr><tr><td id="a6"><a href="com.dataforge.service/index.html" class="el_package">com.dataforge.service</a></td><td class="bar" id="b4"><img src="jacoco-resources/redbar.gif" width="1" height="10" title="554" alt="554"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="jacoco-resources/redbar.gif" width="2" height="10" title="46" alt="46"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">40</td><td class="ctr2" id="g5">40</td><td class="ctr1" id="h3">129</td><td class="ctr2" id="i4">129</td><td class="ctr1" id="j5">17</td><td class="ctr2" id="k6">17</td><td class="ctr1" id="l4">2</td><td class="ctr2" id="m5">2</td></tr><tr><td id="a0"><a href="com.dataforge.config/index.html" class="el_package">com.dataforge.config</a></td><td class="bar" id="b5"><img src="jacoco-resources/redbar.gif" width="1" height="10" title="476" alt="476"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="jacoco-resources/redbar.gif" width="2" height="10" title="44" alt="44"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f3">77</td><td class="ctr2" id="g3">77</td><td class="ctr1" id="h4">128</td><td class="ctr2" id="i5">128</td><td class="ctr1" id="j2">55</td><td class="ctr2" id="k2">55</td><td class="ctr1" id="l3">5</td><td class="ctr2" id="m3">5</td></tr><tr><td id="a7"><a href="com.dataforge.util/index.html" class="el_package">com.dataforge.util</a></td><td class="bar" id="b6"><img src="jacoco-resources/redbar.gif" width="1" height="10" title="345" alt="345"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="jacoco-resources/redbar.gif" width="2" height="10" title="40" alt="40"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">31</td><td class="ctr2" id="g6">31</td><td class="ctr1" id="h6">86</td><td class="ctr2" id="i6">86</td><td class="ctr1" id="j7">11</td><td class="ctr2" id="k7">11</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a5"><a href="com.dataforge.model/index.html" class="el_package">com.dataforge.model</a></td><td class="bar" id="b7"/><td class="ctr2" id="c0">19%</td><td class="bar" id="d7"/><td class="ctr2" id="e0">16%</td><td class="ctr1" id="f7">18</td><td class="ctr2" id="g7">21</td><td class="ctr1" id="h7">25</td><td class="ctr2" id="i7">34</td><td class="ctr1" id="j6">15</td><td class="ctr2" id="k5">18</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a3"><a href="com.dataforge.generators.spi/index.html" class="el_package">com.dataforge.generators.spi</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">2</td><td class="ctr2" id="k8">2</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>