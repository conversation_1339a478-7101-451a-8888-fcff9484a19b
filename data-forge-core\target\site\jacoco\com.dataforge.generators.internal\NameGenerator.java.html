<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NameGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">NameGenerator.java</span></div><h1>NameGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 优化版姓名生成器。
 * 
 * &lt;p&gt;
 * 生成中文或英文姓名，支持大规模唯一姓名生成。
 * 支持性别关联、权重选择和自定义姓名库。
 * 通过配置文件管理姓名数据，支持生成1亿+唯一姓名。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L26">public class NameGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L28">    private static final Logger logger = LoggerFactory.getLogger(NameGenerator.class);</span>

    /**
     * 数据文件路径常量。
     */
    private static final String CHINESE_SURNAMES_PATH = &quot;data/chinese-surnames.txt&quot;;
    private static final String CHINESE_MALE_NAMES_PATH = &quot;data/chinese-male-names.txt&quot;;
    private static final String CHINESE_FEMALE_NAMES_PATH = &quot;data/chinese-female-names.txt&quot;;
    private static final String ENGLISH_FIRST_NAMES_PATH = &quot;data/english-first-names.txt&quot;;
    private static final String ENGLISH_LAST_NAMES_PATH = &quot;data/english-last-names.txt&quot;;

    /**
     * 缓存的数据列表。
     */
    private volatile List&lt;String&gt; chineseSurnames;
    private volatile List&lt;String&gt; chineseMaleNames;
    private volatile List&lt;String&gt; chineseFemaleNames;
    private volatile List&lt;String&gt; englishFirstNames;
    private volatile List&lt;String&gt; englishLastNames;

    /**
     * 缓存的权重数据。
     */
    private volatile Map&lt;String, Integer&gt; chineseSurnameWeights;

    @Override
    public String getType() {
<span class="nc" id="L55">        return &quot;name&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 延迟加载数据
<span class="nc" id="L62">            ensureDataLoaded(config);</span>

            // 从参数中获取名字类型，默认为中文
<span class="nc" id="L65">            String nameType = getStringParam(config, &quot;type&quot;, &quot;CN&quot;);</span>

            // 从参数中获取性别，默认为随机
<span class="nc" id="L68">            String gender = getStringParam(config, &quot;gender&quot;, &quot;ANY&quot;);</span>

            // 如果上下文中有性别信息，优先使用
<span class="nc" id="L71">            String contextGender = context.get(&quot;gender&quot;, String.class).orElse(null);</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">            if (contextGender != null) {</span>
<span class="nc" id="L73">                gender = contextGender;</span>
            }

            // 从参数中获取是否使用权重选择
<span class="nc" id="L77">            boolean useWeight = getBooleanParam(config, &quot;use_weight&quot;, true);</span>

<span class="nc bnc" id="L79" title="All 4 branches missed.">            String generatedName = switch (nameType.toUpperCase()) {</span>
<span class="nc" id="L80">                case &quot;CN&quot; -&gt; generateChineseName(gender, useWeight, config);</span>
<span class="nc" id="L81">                case &quot;EN&quot; -&gt; generateEnglishName(gender, config);</span>
                case &quot;BOTH&quot; -&gt;
<span class="nc bnc" id="L83" title="All 2 branches missed.">                    ThreadLocalRandom.current().nextBoolean() ? generateChineseName(gender, useWeight, config)</span>
<span class="nc" id="L84">                            : generateEnglishName(gender, config);</span>
                default -&gt; {
<span class="nc" id="L86">                    logger.warn(&quot;Unknown name type: {}, using CN&quot;, nameType);</span>
<span class="nc" id="L87">                    yield generateChineseName(gender, useWeight, config);</span>
                }
            };

            // 将生成的姓名信息放入上下文
<span class="nc" id="L92">            context.put(&quot;name&quot;, generatedName);</span>
<span class="nc" id="L93">            context.put(&quot;name_type&quot;, nameType);</span>

<span class="nc" id="L95">            return generatedName;</span>

<span class="nc" id="L97">        } catch (Exception e) {</span>
<span class="nc" id="L98">            logger.error(&quot;Failed to generate name&quot;, e);</span>
            // 返回一个默认名字作为fallback
<span class="nc" id="L100">            return &quot;张三&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L106">        return FieldConfig.class;</span>
    }

    /**
     * 确保数据已加载。
     * 
     * @param config 配置
     */
    private void ensureDataLoaded(FieldConfig config) {
<span class="nc bnc" id="L115" title="All 2 branches missed.">        if (chineseSurnames == null) {</span>
<span class="nc" id="L116">            synchronized (this) {</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">                if (chineseSurnames == null) {</span>
<span class="nc" id="L118">                    loadData(config);</span>
                }
<span class="nc" id="L120">            }</span>
        }
<span class="nc" id="L122">    }</span>

    /**
     * 加载姓名数据。
     * 
     * @param config 配置
     */
    private void loadData(FieldConfig config) {
        try {
            // 检查是否有自定义数据文件路径
<span class="nc" id="L132">            String customSurnamesPath = getStringParam(config, &quot;cn_surname_file&quot;, null);</span>
<span class="nc" id="L133">            String customMaleNamesPath = getStringParam(config, &quot;cn_male_names_file&quot;, null);</span>
<span class="nc" id="L134">            String customFemaleNamesPath = getStringParam(config, &quot;cn_female_names_file&quot;, null);</span>
<span class="nc" id="L135">            String customEnglishFirstPath = getStringParam(config, &quot;en_first_names_file&quot;, null);</span>
<span class="nc" id="L136">            String customEnglishLastPath = getStringParam(config, &quot;en_last_names_file&quot;, null);</span>

            // 加载中文姓氏（支持权重）
<span class="nc bnc" id="L139" title="All 2 branches missed.">            if (customSurnamesPath != null) {</span>
<span class="nc" id="L140">                chineseSurnames = DataLoader.loadDataFromFile(customSurnamesPath);</span>
<span class="nc" id="L141">                chineseSurnameWeights = loadWeightedDataFromFile(customSurnamesPath);</span>
            } else {
<span class="nc" id="L143">                chineseSurnames = DataLoader.loadDataFromResource(CHINESE_SURNAMES_PATH);</span>
<span class="nc" id="L144">                chineseSurnameWeights = DataLoader.loadWeightedDataFromResource(CHINESE_SURNAMES_PATH);</span>
            }

            // 加载中文男性名字
<span class="nc bnc" id="L148" title="All 2 branches missed.">            if (customMaleNamesPath != null) {</span>
<span class="nc" id="L149">                chineseMaleNames = DataLoader.loadDataFromFile(customMaleNamesPath);</span>
            } else {
<span class="nc" id="L151">                chineseMaleNames = DataLoader.loadDataFromResource(CHINESE_MALE_NAMES_PATH);</span>
            }

            // 加载中文女性名字
<span class="nc bnc" id="L155" title="All 2 branches missed.">            if (customFemaleNamesPath != null) {</span>
<span class="nc" id="L156">                chineseFemaleNames = DataLoader.loadDataFromFile(customFemaleNamesPath);</span>
            } else {
<span class="nc" id="L158">                chineseFemaleNames = DataLoader.loadDataFromResource(CHINESE_FEMALE_NAMES_PATH);</span>
            }

            // 加载英文名字
<span class="nc bnc" id="L162" title="All 2 branches missed.">            if (customEnglishFirstPath != null) {</span>
<span class="nc" id="L163">                englishFirstNames = DataLoader.loadDataFromFile(customEnglishFirstPath);</span>
            } else {
<span class="nc" id="L165">                englishFirstNames = DataLoader.loadDataFromResource(ENGLISH_FIRST_NAMES_PATH);</span>
            }

            // 加载英文姓氏
<span class="nc bnc" id="L169" title="All 2 branches missed.">            if (customEnglishLastPath != null) {</span>
<span class="nc" id="L170">                englishLastNames = DataLoader.loadDataFromFile(customEnglishLastPath);</span>
            } else {
<span class="nc" id="L172">                englishLastNames = DataLoader.loadDataFromResource(ENGLISH_LAST_NAMES_PATH);</span>
            }

<span class="nc" id="L175">            logger.info(</span>
                    &quot;Name data loaded - Chinese surnames: {}, Male names: {}, Female names: {}, English first: {}, English last: {}&quot;,
<span class="nc" id="L177">                    chineseSurnames.size(), chineseMaleNames.size(), chineseFemaleNames.size(),</span>
<span class="nc" id="L178">                    englishFirstNames.size(), englishLastNames.size());</span>

<span class="nc" id="L180">        } catch (Exception e) {</span>
<span class="nc" id="L181">            logger.error(&quot;Failed to load name data, using fallback&quot;, e);</span>
<span class="nc" id="L182">            initializeFallbackData();</span>
<span class="nc" id="L183">        }</span>
<span class="nc" id="L184">    }</span>

    /**
     * 从文件加载权重数据。
     * 
     * @param filePath 文件路径
     * @return 权重映射
     */
    private Map&lt;String, Integer&gt; loadWeightedDataFromFile(String filePath) {
        try {
<span class="nc" id="L194">            return DataLoader.loadWeightedDataFromResource(filePath);</span>
<span class="nc" id="L195">        } catch (Exception e) {</span>
<span class="nc" id="L196">            logger.warn(&quot;Failed to load weighted data from: {}&quot;, filePath, e);</span>
<span class="nc" id="L197">            return new java.util.HashMap&lt;&gt;();</span>
        }
    }

    /**
     * 初始化fallback数据。
     */
    private void initializeFallbackData() {
<span class="nc" id="L205">        chineseSurnames = java.util.Arrays.asList(&quot;王&quot;, &quot;李&quot;, &quot;张&quot;, &quot;刘&quot;, &quot;陈&quot;);</span>
<span class="nc" id="L206">        chineseMaleNames = java.util.Arrays.asList(&quot;伟&quot;, &quot;强&quot;, &quot;磊&quot;, &quot;军&quot;, &quot;勇&quot;);</span>
<span class="nc" id="L207">        chineseFemaleNames = java.util.Arrays.asList(&quot;丽&quot;, &quot;娟&quot;, &quot;敏&quot;, &quot;静&quot;, &quot;洁&quot;);</span>
<span class="nc" id="L208">        englishFirstNames = java.util.Arrays.asList(&quot;James&quot;, &quot;Mary&quot;, &quot;John&quot;, &quot;Patricia&quot;);</span>
<span class="nc" id="L209">        englishLastNames = java.util.Arrays.asList(&quot;Smith&quot;, &quot;Johnson&quot;, &quot;Williams&quot;, &quot;Brown&quot;);</span>
<span class="nc" id="L210">        chineseSurnameWeights = new java.util.HashMap&lt;&gt;();</span>
<span class="nc" id="L211">    }</span>

    /**
     * 
     * 生成中文姓名。
     * 
     * @param gender    性别
     * @param useWeight 是否使用权重选择
     * @param config    配置
     * @return 中文姓名
     */
    private String generateChineseName(String gender, boolean useWeight, FieldConfig config) {
        // 选择姓氏
<span class="nc" id="L224">        String surname = selectChineseSurname(useWeight);</span>

        // 选择名字
<span class="nc" id="L227">        String givenName = selectChineseGivenName(gender, config);</span>

<span class="nc" id="L229">        return surname + givenName;</span>
    }

    /**
     * 选择中文姓氏。
     * 
     * @param useWeight 是否使用权重选择
     * @return 姓氏
     */
    private String selectChineseSurname(boolean useWeight) {
<span class="nc bnc" id="L239" title="All 6 branches missed.">        if (useWeight &amp;&amp; chineseSurnameWeights != null &amp;&amp; !chineseSurnameWeights.isEmpty()) {</span>
<span class="nc" id="L240">            return DataLoader.selectByWeight(chineseSurnameWeights, ThreadLocalRandom.current());</span>
        }

<span class="nc bnc" id="L243" title="All 4 branches missed.">        if (chineseSurnames == null || chineseSurnames.isEmpty()) {</span>
<span class="nc" id="L244">            return &quot;王&quot;;</span>
        }

<span class="nc" id="L247">        return chineseSurnames.get(ThreadLocalRandom.current().nextInt(chineseSurnames.size()));</span>
    }

    /**
     * 选择中文名字。
     * 
     * @param gender 性别
     * @param config 配置
     * @return 名字
     */
    private String selectChineseGivenName(String gender, FieldConfig config) {
        List&lt;String&gt; namePool;

<span class="nc bnc" id="L260" title="All 4 branches missed.">        if (&quot;MALE&quot;.equalsIgnoreCase(gender) || &quot;M&quot;.equalsIgnoreCase(gender)) {</span>
<span class="nc" id="L261">            namePool = chineseMaleNames;</span>
<span class="nc bnc" id="L262" title="All 4 branches missed.">        } else if (&quot;FEMALE&quot;.equalsIgnoreCase(gender) || &quot;F&quot;.equalsIgnoreCase(gender)) {</span>
<span class="nc" id="L263">            namePool = chineseFemaleNames;</span>
        } else {
            // 随机选择性别
<span class="nc bnc" id="L266" title="All 2 branches missed.">            namePool = ThreadLocalRandom.current().nextBoolean() ? chineseMaleNames : chineseFemaleNames;</span>
        }

<span class="nc bnc" id="L269" title="All 4 branches missed.">        if (namePool == null || namePool.isEmpty()) {</span>
<span class="nc" id="L270">            return &quot;三&quot;;</span>
        }

        // 支持组合名字以增加唯一性
<span class="nc" id="L274">        boolean allowCombination = getBooleanParam(config, &quot;allow_combination&quot;, true);</span>
<span class="nc bnc" id="L275" title="All 4 branches missed.">        if (allowCombination &amp;&amp; ThreadLocalRandom.current().nextDouble() &lt; 0.3) { // 30%概率组合</span>
<span class="nc" id="L276">            return generateCombinedChineseName(namePool);</span>
        }

<span class="nc" id="L279">        return namePool.get(ThreadLocalRandom.current().nextInt(namePool.size()));</span>
    }

    /**
     * 生成组合中文名字。
     * 
     * @param namePool 名字池
     * @return 组合名字
     */
    private String generateCombinedChineseName(List&lt;String&gt; namePool) {
        // 从单字名中选择两个字组合
<span class="nc" id="L290">        List&lt;String&gt; singleCharNames = namePool.stream()</span>
<span class="nc bnc" id="L291" title="All 2 branches missed.">                .filter(name -&gt; name.length() == 1)</span>
<span class="nc" id="L292">                .collect(java.util.stream.Collectors.toList());</span>

<span class="nc bnc" id="L294" title="All 2 branches missed.">        if (singleCharNames.size() &gt;= 2) {</span>
<span class="nc" id="L295">            String first = singleCharNames.get(ThreadLocalRandom.current().nextInt(singleCharNames.size()));</span>
            String second;
            do {
<span class="nc" id="L298">                second = singleCharNames.get(ThreadLocalRandom.current().nextInt(singleCharNames.size()));</span>
<span class="nc bnc" id="L299" title="All 4 branches missed.">            } while (first.equals(second) &amp;&amp; singleCharNames.size() &gt; 1);</span>

<span class="nc" id="L301">            return first + second;</span>
        }

        // 如果单字名不够，返回随机名字
<span class="nc" id="L305">        return namePool.get(ThreadLocalRandom.current().nextInt(namePool.size()));</span>
    }

    /**
     * 生成英文姓名。
     * 
     * @param gender 性别
     * @param config 配置
     * @return 英文姓名
     */
    private String generateEnglishName(String gender, FieldConfig config) {
<span class="nc bnc" id="L316" title="All 6 branches missed.">        if (englishFirstNames == null || englishFirstNames.isEmpty() ||</span>
<span class="nc bnc" id="L317" title="All 2 branches missed.">                englishLastNames == null || englishLastNames.isEmpty()) {</span>
<span class="nc" id="L318">            return &quot;John Smith&quot;;</span>
        }

<span class="nc" id="L321">        String firstName = englishFirstNames.get(ThreadLocalRandom.current().nextInt(englishFirstNames.size()));</span>
<span class="nc" id="L322">        String lastName = englishLastNames.get(ThreadLocalRandom.current().nextInt(englishLastNames.size()));</span>

        // 支持中间名以增加唯一性
<span class="nc" id="L325">        boolean includeMiddleName = getBooleanParam(config, &quot;include_middle_name&quot;, false);</span>
<span class="nc bnc" id="L326" title="All 4 branches missed.">        if (includeMiddleName &amp;&amp; ThreadLocalRandom.current().nextDouble() &lt; 0.2) { // 20%概率添加中间名</span>
<span class="nc" id="L327">            String middleName = englishFirstNames.get(ThreadLocalRandom.current().nextInt(englishFirstNames.size()));</span>
<span class="nc" id="L328">            return firstName + &quot; &quot; + middleName + &quot; &quot; + lastName;</span>
        }

<span class="nc" id="L331">        return firstName + &quot; &quot; + lastName;</span>
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L338" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L339">            return defaultValue;</span>
        }

<span class="nc" id="L342">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L343" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L350" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L351">            return defaultValue;</span>
        }

<span class="nc" id="L354">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L355" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L356">            return defaultValue;</span>
        }

<span class="nc bnc" id="L359" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L360">            return (Boolean) value;</span>
        }

<span class="nc" id="L363">        return Boolean.parseBoolean(value.toString());</span>
    }

    /**
     * 获取姓名库统计信息。
     * 
     * @return 统计信息
     */
    public String getNameLibraryStats() {
<span class="nc" id="L372">        ensureDataLoaded(null);</span>

<span class="nc" id="L374">        long chineseCombinations = calculateChineseCombinations();</span>
<span class="nc" id="L375">        long englishCombinations = calculateEnglishCombinations();</span>

<span class="nc" id="L377">        return String.format(&quot;Chinese combinations: %,d, English combinations: %,d, Total: %,d&quot;,</span>
<span class="nc" id="L378">                chineseCombinations, englishCombinations, chineseCombinations + englishCombinations);</span>
    }

    /**
     * 计算中文姓名组合数。
     * 
     * @return 组合数
     */
    private long calculateChineseCombinations() {
<span class="nc bnc" id="L387" title="All 6 branches missed.">        if (chineseSurnames == null || chineseMaleNames == null || chineseFemaleNames == null) {</span>
<span class="nc" id="L388">            return 0;</span>
        }

        // 基础组合：姓氏 × (男性名字 + 女性名字)
<span class="nc" id="L392">        long basicCombinations = (long) chineseSurnames.size() * (chineseMaleNames.size() + chineseFemaleNames.size());</span>

        // 单字组合：姓氏 × 单字名 × 单字名
<span class="nc bnc" id="L395" title="All 2 branches missed.">        long singleCharMale = chineseMaleNames.stream().mapToLong(name -&gt; name.length() == 1 ? 1 : 0).sum();</span>
<span class="nc bnc" id="L396" title="All 2 branches missed.">        long singleCharFemale = chineseFemaleNames.stream().mapToLong(name -&gt; name.length() == 1 ? 1 : 0).sum();</span>
<span class="nc" id="L397">        long combinationNames = singleCharMale * singleCharMale + singleCharFemale * singleCharFemale;</span>
<span class="nc" id="L398">        long combinedCombinations = (long) chineseSurnames.size() * combinationNames;</span>

<span class="nc" id="L400">        return basicCombinations + combinedCombinations;</span>
    }

    /**
     * 计算英文姓名组合数。
     * 
     * @return 组合数
     */
    private long calculateEnglishCombinations() {
<span class="nc bnc" id="L409" title="All 4 branches missed.">        if (englishFirstNames == null || englishLastNames == null) {</span>
<span class="nc" id="L410">            return 0;</span>
        }

        // 基础组合：名 × 姓
<span class="nc" id="L414">        long basicCombinations = (long) englishFirstNames.size() * englishLastNames.size();</span>

        // 中间名组合：名 × 中间名 × 姓
<span class="nc" id="L417">        long middleNameCombinations = (long) englishFirstNames.size() * englishFirstNames.size()</span>
<span class="nc" id="L418">                * englishLastNames.size();</span>

<span class="nc" id="L420">        return basicCombinations + middleNameCombinations;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L425">        return &quot;name generator - generates Chinese/English names with massive scale support (100M+ unique names)&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>