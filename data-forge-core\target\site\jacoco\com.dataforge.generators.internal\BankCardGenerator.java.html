<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BankCardGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">BankCardGenerator.java</span></div><h1>BankCardGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import com.dataforge.validation.LuhnValidator;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 银行卡号生成器。
 * 
 * &lt;p&gt;
 * 生成符合Luhn算法的银行卡号，支持大规模卡号生成。
 * 支持不同银行、卡组织的BIN码和权重选择。
 * 通过配置文件管理BIN码数据，支持生成数十亿唯一卡号。
 * 基于BIN码（Bank Identification Number）生成真实有效的卡号。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L32">public class BankCardGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L34">    private static final Logger logger = LoggerFactory.getLogger(BankCardGenerator.class);</span>

    @Autowired
    private LuhnValidator luhnValidator;

    /**
     * BIN码数据文件路径。
     */
    private static final String BANK_BINS_PATH = &quot;data/bank-bins.txt&quot;;

    /**
     * 缓存的BIN码数据。
     */
    private volatile Map&lt;String, BinInfo&gt; bankBins;
    private volatile Map&lt;String, List&lt;String&gt;&gt; binsByBank;
    private volatile Map&lt;String, List&lt;String&gt;&gt; binsByOrganization;
    private volatile Map&lt;String, List&lt;String&gt;&gt; binsByType;

    /**
     * Fallback BIN码数据（当文件加载失败时使用）。
     */
<span class="nc" id="L55">    private static final Map&lt;String, BinInfo&gt; FALLBACK_BANK_BINS = new HashMap&lt;&gt;();</span>

    static {
        // 初始化fallback数据
<span class="nc" id="L59">        FALLBACK_BANK_BINS.put(&quot;ICBC_DEBIT&quot;, new BinInfo(&quot;622202&quot;, 19, &quot;工商银行&quot;, &quot;UNIONPAY&quot;, &quot;DEBIT&quot;, 15));</span>
<span class="nc" id="L60">        FALLBACK_BANK_BINS.put(&quot;ICBC_CREDIT&quot;, new BinInfo(&quot;625330&quot;, 16, &quot;工商银行&quot;, &quot;UNIONPAY&quot;, &quot;CREDIT&quot;, 12));</span>
<span class="nc" id="L61">        FALLBACK_BANK_BINS.put(&quot;CCB_DEBIT&quot;, new BinInfo(&quot;621700&quot;, 19, &quot;建设银行&quot;, &quot;UNIONPAY&quot;, &quot;DEBIT&quot;, 12));</span>
<span class="nc" id="L62">        FALLBACK_BANK_BINS.put(&quot;CCB_CREDIT&quot;, new BinInfo(&quot;625362&quot;, 16, &quot;建设银行&quot;, &quot;UNIONPAY&quot;, &quot;CREDIT&quot;, 10));</span>
<span class="nc" id="L63">        FALLBACK_BANK_BINS.put(&quot;ABC_DEBIT&quot;, new BinInfo(&quot;622848&quot;, 19, &quot;农业银行&quot;, &quot;UNIONPAY&quot;, &quot;DEBIT&quot;, 12));</span>
<span class="nc" id="L64">        FALLBACK_BANK_BINS.put(&quot;ABC_CREDIT&quot;, new BinInfo(&quot;625996&quot;, 16, &quot;农业银行&quot;, &quot;UNIONPAY&quot;, &quot;CREDIT&quot;, 10));</span>
<span class="nc" id="L65">        FALLBACK_BANK_BINS.put(&quot;BOC_DEBIT&quot;, new BinInfo(&quot;621661&quot;, 19, &quot;中国银行&quot;, &quot;UNIONPAY&quot;, &quot;DEBIT&quot;, 12));</span>
<span class="nc" id="L66">        FALLBACK_BANK_BINS.put(&quot;BOC_CREDIT&quot;, new BinInfo(&quot;625906&quot;, 16, &quot;中国银行&quot;, &quot;UNIONPAY&quot;, &quot;CREDIT&quot;, 10));</span>
<span class="nc" id="L67">        FALLBACK_BANK_BINS.put(&quot;CMB_DEBIT&quot;, new BinInfo(&quot;621483&quot;, 16, &quot;招商银行&quot;, &quot;UNIONPAY&quot;, &quot;DEBIT&quot;, 10));</span>
<span class="nc" id="L68">        FALLBACK_BANK_BINS.put(&quot;CMB_CREDIT&quot;, new BinInfo(&quot;545947&quot;, 16, &quot;招商银行&quot;, &quot;UNIONPAY&quot;, &quot;CREDIT&quot;, 10));</span>
    }

    /**
     * 卡组织前缀。
     */
<span class="nc" id="L74">    private static final Map&lt;String, List&lt;String&gt;&gt; CARD_ORGANIZATION_PREFIXES = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L77">        CARD_ORGANIZATION_PREFIXES.put(&quot;VISA&quot;, Arrays.asList(&quot;4&quot;));</span>
<span class="nc" id="L78">        CARD_ORGANIZATION_PREFIXES.put(&quot;MASTERCARD&quot;, Arrays.asList(&quot;5&quot;));</span>
<span class="nc" id="L79">        CARD_ORGANIZATION_PREFIXES.put(&quot;UNIONPAY&quot;, Arrays.asList(&quot;62&quot;));</span>
<span class="nc" id="L80">        CARD_ORGANIZATION_PREFIXES.put(&quot;AMEX&quot;, Arrays.asList(&quot;34&quot;, &quot;37&quot;));</span>
<span class="nc" id="L81">        CARD_ORGANIZATION_PREFIXES.put(&quot;JCB&quot;, Arrays.asList(&quot;35&quot;));</span>
<span class="nc" id="L82">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L86">        return &quot;bankcard&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 延迟加载数据
<span class="nc" id="L93">            ensureDataLoaded(config);</span>

            // 从参数中获取卡类型
<span class="nc" id="L96">            String cardType = getStringParam(config, &quot;type&quot;, &quot;BOTH&quot;);</span>

            // 从参数中获取银行代码
<span class="nc" id="L99">            String bankCode = getStringParam(config, &quot;bank&quot;, null);</span>

            // 从参数中获取卡组织
<span class="nc" id="L102">            String issuer = getStringParam(config, &quot;issuer&quot;, &quot;ANY&quot;);</span>

            // 从参数中获取是否生成有效卡号
<span class="nc" id="L105">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>

            // 从参数中获取卡号长度
<span class="nc" id="L108">            Integer length = getIntegerParam(config, &quot;length&quot;, null);</span>

            // 从参数中获取是否使用权重选择
<span class="nc" id="L111">            boolean useWeight = getBooleanParam(config, &quot;use_weight&quot;, true);</span>

<span class="nc bnc" id="L113" title="All 2 branches missed.">            if (!valid) {</span>
<span class="nc" id="L114">                return generateInvalidBankCard();</span>
            }

<span class="nc" id="L117">            String cardNumber = generateValidBankCard(cardType, bankCode, issuer, length, useWeight);</span>

            // 将生成的银行卡信息放入上下文
<span class="nc" id="L120">            context.put(&quot;bankcard&quot;, cardNumber);</span>
<span class="nc" id="L121">            context.put(&quot;bankcard_masked&quot;, maskCardNumber(cardNumber));</span>

<span class="nc" id="L123">            return cardNumber;</span>

<span class="nc" id="L125">        } catch (Exception e) {</span>
<span class="nc" id="L126">            logger.error(&quot;Failed to generate bank card number&quot;, e);</span>
            // 返回一个默认银行卡号作为fallback
<span class="nc" id="L128">            return &quot;****************&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L134">        return FieldConfig.class;</span>
    }

    /**
     * 确保数据已加载。
     * 
     * @param config 配置
     */
    private void ensureDataLoaded(FieldConfig config) {
<span class="nc bnc" id="L143" title="All 2 branches missed.">        if (bankBins == null) {</span>
<span class="nc" id="L144">            synchronized (this) {</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">                if (bankBins == null) {</span>
<span class="nc" id="L146">                    loadData(config);</span>
                }
<span class="nc" id="L148">            }</span>
        }
<span class="nc" id="L150">    }</span>

    /**
     * 加载BIN码数据。
     * 
     * @param config 配置
     */
    private void loadData(FieldConfig config) {
        try {
            // 检查是否有自定义数据文件路径
<span class="nc" id="L160">            String customBinsPath = getStringParam(config, &quot;bank_bins_file&quot;, null);</span>

            List&lt;String&gt; lines;
<span class="nc bnc" id="L163" title="All 2 branches missed.">            if (customBinsPath != null) {</span>
<span class="nc" id="L164">                lines = DataLoader.loadDataFromFile(customBinsPath);</span>
            } else {
<span class="nc" id="L166">                lines = DataLoader.loadDataFromResource(BANK_BINS_PATH);</span>
            }

<span class="nc" id="L169">            bankBins = new HashMap&lt;&gt;();</span>
<span class="nc" id="L170">            binsByBank = new HashMap&lt;&gt;();</span>
<span class="nc" id="L171">            binsByOrganization = new HashMap&lt;&gt;();</span>
<span class="nc" id="L172">            binsByType = new HashMap&lt;&gt;();</span>

<span class="nc bnc" id="L174" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc" id="L175">                String[] parts = line.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">                if (parts.length &gt;= 4) {</span>
<span class="nc" id="L177">                    String prefix = parts[0].trim();</span>
<span class="nc" id="L178">                    String bankName = parts[1].trim();</span>
<span class="nc" id="L179">                    String organization = parts[2].trim();</span>
<span class="nc" id="L180">                    String cardType = parts[3].trim();</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">                    int weight = parts.length &gt; 4 ? parseWeight(parts[4].trim()) : 1;</span>

                    // 根据前缀确定卡号长度
<span class="nc" id="L184">                    int length = determineCardLength(prefix, organization);</span>

<span class="nc" id="L186">                    BinInfo info = new BinInfo(prefix, length, bankName, organization, cardType, weight);</span>
<span class="nc" id="L187">                    bankBins.put(prefix, info);</span>

<span class="nc" id="L189">                    binsByBank.computeIfAbsent(bankName, k -&gt; new java.util.ArrayList&lt;&gt;()).add(prefix);</span>
<span class="nc" id="L190">                    binsByOrganization.computeIfAbsent(organization, k -&gt; new java.util.ArrayList&lt;&gt;()).add(prefix);</span>
<span class="nc" id="L191">                    binsByType.computeIfAbsent(cardType, k -&gt; new java.util.ArrayList&lt;&gt;()).add(prefix);</span>
                }
<span class="nc" id="L193">            }</span>

            // 如果加载失败，使用fallback数据
<span class="nc bnc" id="L196" title="All 2 branches missed.">            if (bankBins.isEmpty()) {</span>
<span class="nc" id="L197">                initializeFallbackData();</span>
            }

<span class="nc" id="L200">            logger.info(&quot;Bank BIN data loaded - Total BINs: {}, Banks: {}, Organizations: {}&quot;,</span>
<span class="nc" id="L201">                    bankBins.size(), binsByBank.keySet().size(), binsByOrganization.keySet().size());</span>

<span class="nc" id="L203">        } catch (Exception e) {</span>
<span class="nc" id="L204">            logger.error(&quot;Failed to load bank BIN data, using fallback&quot;, e);</span>
<span class="nc" id="L205">            initializeFallbackData();</span>
<span class="nc" id="L206">        }</span>
<span class="nc" id="L207">    }</span>

    /**
     * 解析权重值。
     * 
     * @param weightStr 权重字符串
     * @return 权重值
     */
    private int parseWeight(String weightStr) {
        try {
<span class="nc" id="L217">            return Integer.parseInt(weightStr);</span>
<span class="nc" id="L218">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L219">            return 1;</span>
        }
    }

    /**
     * 根据前缀和卡组织确定卡号长度。
     * 
     * @param prefix       前缀
     * @param organization 卡组织
     * @return 卡号长度
     */
    private int determineCardLength(String prefix, String organization) {
        // 根据卡组织和前缀确定标准长度
<span class="nc bnc" id="L232" title="All 6 branches missed.">        return switch (organization.toUpperCase()) {</span>
<span class="nc" id="L233">            case &quot;VISA&quot; -&gt; 16;</span>
<span class="nc" id="L234">            case &quot;MASTERCARD&quot; -&gt; 16;</span>
<span class="nc bnc" id="L235" title="All 2 branches missed.">            case &quot;UNIONPAY&quot; -&gt; prefix.startsWith(&quot;62&quot;) ? 19 : 16;</span>
<span class="nc" id="L236">            case &quot;AMEX&quot; -&gt; 15;</span>
<span class="nc" id="L237">            case &quot;JCB&quot; -&gt; 16;</span>
<span class="nc" id="L238">            default -&gt; 16;</span>
        };
    }

    /**
     * 初始化fallback数据。
     */
    private void initializeFallbackData() {
<span class="nc" id="L246">        bankBins = new HashMap&lt;&gt;();</span>
<span class="nc" id="L247">        binsByBank = new HashMap&lt;&gt;();</span>
<span class="nc" id="L248">        binsByOrganization = new HashMap&lt;&gt;();</span>
<span class="nc" id="L249">        binsByType = new HashMap&lt;&gt;();</span>

        // 添加fallback数据
<span class="nc bnc" id="L252" title="All 2 branches missed.">        for (Map.Entry&lt;String, BinInfo&gt; entry : FALLBACK_BANK_BINS.entrySet()) {</span>
<span class="nc" id="L253">            BinInfo info = entry.getValue();</span>
<span class="nc" id="L254">            bankBins.put(info.prefix, info);</span>

<span class="nc" id="L256">            binsByBank.computeIfAbsent(info.bankName, k -&gt; new java.util.ArrayList&lt;&gt;()).add(info.prefix);</span>
<span class="nc" id="L257">            binsByOrganization.computeIfAbsent(info.organization, k -&gt; new java.util.ArrayList&lt;&gt;()).add(info.prefix);</span>
<span class="nc" id="L258">            binsByType.computeIfAbsent(info.cardType, k -&gt; new java.util.ArrayList&lt;&gt;()).add(info.prefix);</span>
<span class="nc" id="L259">        }</span>

<span class="nc" id="L261">    }</span>

    /**
     * 生成有效的银行卡号。
     * 
     * @param cardType  卡类型
     * @param bankCode  银行代码
     * @param issuer    卡组织
     * @param length    卡号长度
     * @param useWeight 是否使用权重选择
     * @return 有效的银行卡号
     */
    private String generateValidBankCard(String cardType, String bankCode, String issuer, Integer length,
            boolean useWeight) {
<span class="nc" id="L275">        BinInfo binInfo = selectBinInfo(cardType, bankCode, issuer, useWeight);</span>

<span class="nc bnc" id="L277" title="All 2 branches missed.">        if (binInfo == null) {</span>
<span class="nc" id="L278">            logger.warn(&quot;No matching BIN found, using default&quot;);</span>
<span class="nc" id="L279">            binInfo = bankBins.values().iterator().next(); // 使用第一个可用的BIN</span>
        }

        // 使用指定长度或BIN默认长度
<span class="nc bnc" id="L283" title="All 2 branches missed.">        int cardLength = length != null ? length : binInfo.length;</span>

        // 确保长度在合理范围内
<span class="nc bnc" id="L286" title="All 4 branches missed.">        if (cardLength &lt; 13 || cardLength &gt; 19) {</span>
<span class="nc" id="L287">            logger.warn(&quot;Invalid card length: {}, using default: {}&quot;, cardLength, binInfo.length);</span>
<span class="nc" id="L288">            cardLength = binInfo.length;</span>
        }

        // 生成卡号前缀
<span class="nc" id="L292">        String prefix = binInfo.prefix;</span>

        // 生成中间随机数字
<span class="nc" id="L295">        StringBuilder cardNumber = new StringBuilder(prefix);</span>
<span class="nc" id="L296">        int remainingDigits = cardLength - prefix.length() - 1; // 减去1位校验位</span>

<span class="nc bnc" id="L298" title="All 2 branches missed.">        for (int i = 0; i &lt; remainingDigits; i++) {</span>
<span class="nc" id="L299">            cardNumber.append(ThreadLocalRandom.current().nextInt(10));</span>
        }

        // 使用Luhn算法生成校验位
<span class="nc" id="L303">        String partialNumber = cardNumber.toString();</span>
<span class="nc" id="L304">        int checkDigit = luhnValidator.generateCheckDigit(partialNumber);</span>
<span class="nc" id="L305">        cardNumber.append(checkDigit);</span>

<span class="nc" id="L307">        String result = cardNumber.toString();</span>

        // 验证生成的卡号
<span class="nc bnc" id="L310" title="All 2 branches missed.">        if (!luhnValidator.isValid(result)) {</span>
<span class="nc" id="L311">            logger.error(&quot;Generated invalid card number: {}&quot;, maskCardNumber(result));</span>
            // 重新生成
<span class="nc" id="L313">            return generateValidBankCard(cardType, bankCode, issuer, length, useWeight);</span>
        }

<span class="nc" id="L316">        logger.debug(&quot;Generated valid bank card: {} ({} {})&quot;, maskCardNumber(result), binInfo.bankName,</span>
                binInfo.cardType);
<span class="nc" id="L318">        return result;</span>
    }

    /**
     * 生成无效的银行卡号。
     * 
     * @return 无效的银行卡号
     */
    private String generateInvalidBankCard() {
<span class="nc" id="L327">        int type = ThreadLocalRandom.current().nextInt(4);</span>

<span class="nc bnc" id="L329" title="All 4 branches missed.">        return switch (type) {</span>
<span class="nc" id="L330">            case 0 -&gt; generateWrongLengthCard();</span>
<span class="nc" id="L331">            case 1 -&gt; generateWrongChecksumCard();</span>
<span class="nc" id="L332">            case 2 -&gt; generateNonNumericCard();</span>
<span class="nc" id="L333">            default -&gt; generateOtherInvalidCard();</span>
        };
    }

    /**
     * 生成长度错误的银行卡号。
     * 
     * @return 长度错误的银行卡号
     */
    private String generateWrongLengthCard() {
<span class="nc" id="L343">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc bnc" id="L344" title="All 2 branches missed.">        int length = random.nextBoolean() ? random.nextInt(5) + 8 : // 8-12位</span>
<span class="nc" id="L345">                random.nextInt(5) + 20; // 20-24位</span>

<span class="nc" id="L347">        StringBuilder card = new StringBuilder();</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L349">            card.append(random.nextInt(10));</span>
        }

<span class="nc" id="L352">        return card.toString();</span>
    }

    /**
     * 生成校验位错误的银行卡号。
     * 
     * @return 校验位错误的银行卡号
     */
    private String generateWrongChecksumCard() {
        // 先生成一个有效的卡号
<span class="nc" id="L362">        String validCard = generateValidBankCard(&quot;DEBIT&quot;, null, &quot;ANY&quot;, 16, false);</span>

        // 修改最后一位校验位
<span class="nc" id="L365">        StringBuilder invalidCard = new StringBuilder(validCard.substring(0, validCard.length() - 1));</span>
<span class="nc" id="L366">        int lastDigit = Character.getNumericValue(validCard.charAt(validCard.length() - 1));</span>
<span class="nc" id="L367">        int wrongDigit = (lastDigit + 1) % 10;</span>
<span class="nc" id="L368">        invalidCard.append(wrongDigit);</span>

<span class="nc" id="L370">        return invalidCard.toString();</span>
    }

    /**
     * 生成包含非数字字符的银行卡号。
     * 
     * @return 包含非数字字符的银行卡号
     */
    private String generateNonNumericCard() {
<span class="nc" id="L379">        StringBuilder card = new StringBuilder();</span>
<span class="nc" id="L380">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

        // 生成16位，其中随机位置包含字母
<span class="nc bnc" id="L383" title="All 2 branches missed.">        for (int i = 0; i &lt; 16; i++) {</span>
<span class="nc bnc" id="L384" title="All 2 branches missed.">            if (random.nextInt(5) == 0) { // 20%概率插入字母</span>
<span class="nc" id="L385">                card.append((char) ('A' + random.nextInt(26)));</span>
            } else {
<span class="nc" id="L387">                card.append(random.nextInt(10));</span>
            }
        }

<span class="nc" id="L391">        return card.toString();</span>
    }

    /**
     * 生成其他类型的无效银行卡号。
     * 
     * @return 其他无效银行卡号
     */
    private String generateOtherInvalidCard() {
<span class="nc" id="L400">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
        // 生成全0或全相同数字的卡号
<span class="nc bnc" id="L402" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L403">            return &quot;****************&quot;;</span>
        } else {
<span class="nc" id="L405">            int digit = random.nextInt(10);</span>
<span class="nc" id="L406">            return String.valueOf(digit).repeat(16);</span>
        }
    }

    /**
     * 选择BIN信息。
     * 
     * @param cardType  卡类型
     * @param bankCode  银行代码
     * @param issuer    卡组织
     * @param useWeight 是否使用权重选择
     * @return BIN信息
     */
    private BinInfo selectBinInfo(String cardType, String bankCode, String issuer, boolean useWeight) {
<span class="nc" id="L420">        List&lt;BinInfo&gt; candidates = bankBins.values().stream()</span>
<span class="nc" id="L421">                .filter(bin -&gt; matchesCardType(bin, cardType))</span>
<span class="nc" id="L422">                .filter(bin -&gt; matchesBankCode(bin, bankCode))</span>
<span class="nc" id="L423">                .filter(bin -&gt; matchesIssuer(bin, issuer))</span>
<span class="nc" id="L424">                .collect(Collectors.toList());</span>

<span class="nc bnc" id="L426" title="All 2 branches missed.">        if (candidates.isEmpty()) {</span>
<span class="nc" id="L427">            return null;</span>
        }

<span class="nc bnc" id="L430" title="All 4 branches missed.">        if (!useWeight || candidates.size() == 1) {</span>
<span class="nc" id="L431">            return candidates.get(ThreadLocalRandom.current().nextInt(candidates.size()));</span>
        }

        // 使用权重选择
<span class="nc" id="L435">        Map&lt;String, Integer&gt; weightMap = candidates.stream()</span>
<span class="nc" id="L436">                .collect(Collectors.toMap(</span>
<span class="nc" id="L437">                        bin -&gt; bin.prefix,</span>
<span class="nc" id="L438">                        bin -&gt; bin.weight));</span>

<span class="nc" id="L440">        String selectedPrefix = DataLoader.selectByWeight(weightMap, ThreadLocalRandom.current());</span>
<span class="nc" id="L441">        return bankBins.get(selectedPrefix);</span>
    }

    /**
     * 检查是否匹配卡类型。
     * 
     * @param binInfo  BIN信息
     * @param cardType 卡类型
     * @return 是否匹配
     */
    private boolean matchesCardType(BinInfo binInfo, String cardType) {
<span class="nc bnc" id="L452" title="All 6 branches missed.">        if (cardType == null || &quot;BOTH&quot;.equalsIgnoreCase(cardType) || &quot;ANY&quot;.equalsIgnoreCase(cardType)) {</span>
<span class="nc" id="L453">            return true;</span>
        }

<span class="nc" id="L456">        return cardType.equalsIgnoreCase(binInfo.cardType);</span>
    }

    /**
     * 检查是否匹配银行代码。
     * 
     * @param binInfo  BIN信息
     * @param bankCode 银行代码
     * @return 是否匹配
     */
    private boolean matchesBankCode(BinInfo binInfo, String bankCode) {
<span class="nc bnc" id="L467" title="All 4 branches missed.">        if (bankCode == null || &quot;ANY&quot;.equalsIgnoreCase(bankCode)) {</span>
<span class="nc" id="L468">            return true;</span>
        }

<span class="nc" id="L471">        return bankCode.equalsIgnoreCase(binInfo.bankName);</span>
    }

    /**
     * 检查是否匹配卡组织。
     * 
     * @param binInfo BIN信息
     * @param issuer  卡组织
     * @return 是否匹配
     */
    private boolean matchesIssuer(BinInfo binInfo, String issuer) {
<span class="nc bnc" id="L482" title="All 4 branches missed.">        if (issuer == null || &quot;ANY&quot;.equalsIgnoreCase(issuer)) {</span>
<span class="nc" id="L483">            return true;</span>
        }

<span class="nc" id="L486">        return issuer.equalsIgnoreCase(binInfo.organization);</span>
    }

    /**
     * 掩码银行卡号用于日志记录。
     * 
     * @param cardNumber 原始卡号
     * @return 掩码后的卡号
     */
    private String maskCardNumber(String cardNumber) {
<span class="nc bnc" id="L496" title="All 4 branches missed.">        if (cardNumber == null || cardNumber.length() &lt; 8) {</span>
<span class="nc" id="L497">            return &quot;****&quot;;</span>
        }

        // 显示前4位和后4位，中间用*代替
<span class="nc" id="L501">        String prefix = cardNumber.substring(0, 4);</span>
<span class="nc" id="L502">        String suffix = cardNumber.substring(cardNumber.length() - 4);</span>
<span class="nc" id="L503">        int maskLength = cardNumber.length() - 8;</span>
<span class="nc" id="L504">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="nc" id="L506">        return prefix + mask + suffix;</span>
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L513" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L514">            return defaultValue;</span>
        }

<span class="nc" id="L517">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L518" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L525" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L526">            return defaultValue;</span>
        }

<span class="nc" id="L529">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L530" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L531">            return defaultValue;</span>
        }

<span class="nc bnc" id="L534" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L535">            return (Boolean) value;</span>
        }

<span class="nc" id="L538">        return Boolean.parseBoolean(value.toString());</span>
    }

    /**
     * 从配置中获取整数参数。
     */
    private Integer getIntegerParam(FieldConfig config, String key, Integer defaultValue) {
<span class="nc bnc" id="L545" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L546">            return defaultValue;</span>
        }

<span class="nc" id="L549">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L550" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L551">            return defaultValue;</span>
        }

<span class="nc bnc" id="L554" title="All 2 branches missed.">        if (value instanceof Integer) {</span>
<span class="nc" id="L555">            return (Integer) value;</span>
        }

        try {
<span class="nc" id="L559">            return Integer.parseInt(value.toString());</span>
<span class="nc" id="L560">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L561">            logger.warn(&quot;Invalid integer parameter {}: {}&quot;, key, value);</span>
<span class="nc" id="L562">            return defaultValue;</span>
        }
    }

    /**
     * 获取BIN码库统计信息。
     * 
     * @return 统计信息
     */
    public String getBinStats() {
<span class="nc" id="L572">        ensureDataLoaded(null);</span>

<span class="nc" id="L574">        StringBuilder stats = new StringBuilder();</span>
<span class="nc" id="L575">        stats.append(&quot;Total BINs: &quot;).append(bankBins.size()).append(&quot;\n&quot;);</span>

<span class="nc bnc" id="L577" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;String&gt;&gt; entry : binsByBank.entrySet()) {</span>
<span class="nc" id="L578">            stats.append(entry.getKey()).append(&quot;: &quot;).append(entry.getValue().size()).append(&quot; BINs\n&quot;);</span>
<span class="nc" id="L579">        }</span>

<span class="nc" id="L581">        stats.append(&quot;\nOrganizations:\n&quot;);</span>
<span class="nc bnc" id="L582" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;String&gt;&gt; entry : binsByOrganization.entrySet()) {</span>
<span class="nc" id="L583">            stats.append(entry.getKey()).append(&quot;: &quot;).append(entry.getValue().size()).append(&quot; BINs\n&quot;);</span>
<span class="nc" id="L584">        }</span>

        // 计算理论组合数（每个BIN可生成数十亿个卡号）
<span class="nc" id="L587">        long totalCombinations = 0;</span>
<span class="nc bnc" id="L588" title="All 2 branches missed.">        for (BinInfo bin : bankBins.values()) {</span>
            // 每个BIN根据其长度可生成的卡号数量
<span class="nc" id="L590">            int remainingDigits = bin.length - bin.prefix.length() - 1; // 减去前缀和校验位</span>
<span class="nc" id="L591">            long combinations = (long) Math.pow(10, remainingDigits);</span>
<span class="nc" id="L592">            totalCombinations += combinations;</span>
<span class="nc" id="L593">        }</span>

<span class="nc" id="L595">        stats.append(&quot;\nTotal possible combinations: &quot;).append(String.format(&quot;%,d&quot;, totalCombinations));</span>

<span class="nc" id="L597">        return stats.toString();</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L602">        return &quot;Bank card number generator - generates valid bank card numbers with Luhn algorithm and comprehensive BIN support&quot;;</span>
    }

    /**
     * BIN信息类。
     */
    private static class BinInfo {
        final String prefix;
        final int length;
        final String bankName;
        final String organization;
        final String cardType;
        final int weight;

<span class="nc" id="L616">        BinInfo(String prefix, int length, String bankName, String organization, String cardType, int weight) {</span>
<span class="nc" id="L617">            this.prefix = prefix;</span>
<span class="nc" id="L618">            this.length = length;</span>
<span class="nc" id="L619">            this.bankName = bankName;</span>
<span class="nc" id="L620">            this.organization = organization;</span>
<span class="nc" id="L621">            this.cardType = cardType;</span>
<span class="nc" id="L622">            this.weight = weight;</span>
<span class="nc" id="L623">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>