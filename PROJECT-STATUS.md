# DataForge 项目状态报告

## 项目总体进度

### 🎯 当前阶段：阶段二 - 数据类型生成器开发

- **基础信息类生成器**: ✅ 100% 完成 (22/22)
- **标识类生成器**: ✅ 100% 完成 (5/5)
- **联系通信类生成器**: ✅ 100% 完成 (6/6)
- **网络设备类生成器**: 🔄 进行中 (7/9)

### 📊 整体完成度

- **阶段一 (核心框架)**: ✅ 100% 完成
- **阶段二 (生成器开发)**: 🔄 55% 完成 (38/69)
- **阶段三 (特殊场景)**: ⏳ 未开始

---

## 阶段一完成情况 ✅

### A. 项目结构与构建配置

- [x] **A-1**: 创建 data-forge-core 和 data-forge-cli Maven 模块
- [x] **A-2**: 配置 Checkstyle, PMD, JaCoCo 插件并集成到构建流程
- [x] **A-3**: 初始化 Git 仓库，建立 main 和 develop 分支

### B. 核心接口与工厂

- [x] **B-1**: 定义 DataGenerator<T, C> 核心接口
- [x] **B-2**: 实现 GeneratorFactory，使用 Java ServiceLoader (SPI) 机制动态加载

### C. 数据上下文

- [x] **C-1**: 实现 DataForgeContext 类，用于在单次生成任务中传递和共享字段值

### D. 配置管理

- [x] **D-1**: 创建基于 @ConfigurationProperties 的 ForgeConfig, FieldConfig 等配置模型类
- [x] **D-2**: 实现从 YAML/JSON 配置文件加载 ForgeConfig 的逻辑
- [x] **D-3**: 实现命令行参数到 ForgeConfig 的解析与合并逻辑

### E. 输出策略

- [x] **E-1**: 定义 OutputStrategy 接口
- [x] **E-2**: 实现 ConsoleOutputStrategy (标准输出)
- [x] **E-3**: 实现 FileOutputStrategy (文件输出)
- [x] **E-4**: 实现 CsvOutputStrategy，支持流式写入 CSV
- [x] **E-5**: 实现 JsonOutputStrategy，支持流式写入 JSON
- [x] **E-6**: 实现 SqlOutputStrategy，生成 SQL INSERT 语句

### F. 数据校验

- [x] **F-1**: 实现 LuhnValidator (用于银行卡号、IMEI)
- [x] **F-2**: 实现身份证号码校验位算法
- [x] **F-3**: 实现统一社会信用代码校验算法（GB32100-2015）
- [x] **F-4**: 实现组织机构代码校验算法（GB 11714-1997）

### G. 核心服务

- [x] **G-1**: 创建 DataForgeService，作为编排核心逻辑的主服务
- [x] **G-2**: 在 DataForgeService 中实现主生成循环，处理 count、字段迭代、上下文传递和数据输出

### H. CLI应用

- [x] **H-1**: 使用 Picocli 创建 GenerateCommand
- [x] **H-2**: 在 GenerateCommand 中实现对所有核心 CLI 参数的定义和接收
- [x] **H-3**: 将 GenerateCommand 与 DataForgeService 连接

---

## 阶段二完成情况 🔄

### 1. 基础信息类生成器 ✅ (22/22)

#### 1.1 个人基础信息

- [x] **1.1.1-1.1.6**: NameGenerator - 中英文姓名生成，支持性别关联和拼音生成
- [x] **1.2.1-1.2.3**: PhoneGenerator - 手机号生成，支持运营商号段和地区关联
- [x] **1.3.1-1.3.4**: BankCardGenerator - 银行卡号生成，支持Luhn算法和BIN码
- [x] **1.4.1-1.4.7**: IdCardGenerator - 身份证号生成，支持地区、性别、年龄关联
- [x] **1.5.1-1.5.3**: LicensePlateGenerator - 车牌号生成，支持燃油车和新能源车牌
- [x] **1.6.1-1.6.3**: AddressGenerator - 地址生成，支持层级关联和邮编
- [x] **1.7.1-1.7.2**: CompanyNameGenerator - 企业名称生成，支持行业分类
- [x] **1.8.1-1.8.2**: UsccGenerator - 统一社会信用代码生成，支持校验算法
- [x] **1.9.1-1.9.2**: OrganizationCodeGenerator - 组织机构代码生成
- [x] **1.10.1**: LeiGenerator - LEI码生成（集成在其他生成器中）
- [x] **1.11.1-1.11.3**: AgeGenerator - 年龄生成，支持与出生日期关联
- [x] **1.12.1-1.12.3**: EmailGenerator - 邮箱生成，支持域名和姓名关联
- [x] **1.13.1-1.13.2**: UsernameGenerator - 账号名生成，支持唯一性和黑名单
- [x] **1.14.1-1.14.2**: PasswordGenerator - 密码生成，支持复杂度控制
- [x] **1.15.1-1.15.3**: GenderGenerator - 性别生成，支持多种格式和权重
- [x] **1.16.1-1.16.2**: OccupationGenerator - 职业生成，支持行业分类
- [x] **1.17.1-1.17.2**: EducationGenerator - 学历生成，支持权重分布
- [x] **1.18.1-1.18.2**: MaritalStatusGenerator - 婚姻状况生成，支持年龄关联
- [x] **1.19.1-1.19.2**: BloodTypeGenerator - 血型生成，支持ABO+Rh系统
- [x] **1.20.1-1.20.2**: ZodiacGenerator - 星座生成，支持出生日期关联
- [x] **1.21.1-1.21.2**: EthnicityGenerator - 民族生成，支持56个民族和权重
- [x] **1.22.1-1.22.2**: ReligionGenerator - 宗教信仰生成，支持多种宗教

### 2. 标识类生成器 ✅ (5/5)

#### 2.1 全局唯一ID

- [x] **2.1.1-2.1.4**: UuidGenerator - UUID生成，支持v1/v4/ULID/Snowflake

#### 2.2 网络标识

- [x] **4.1.1-4.1.2**: IpAddressGenerator - IP地址生成，支持IPv4/IPv6和子网
- [x] **4.3.1-4.3.2**: MacAddressGenerator - MAC地址生成，支持厂商OUI和格式
- [x] **3.5.1-3.5.2**: UrlGenerator - URL生成，支持协议、域名、路径、查询参数
- [x] **4.2.1-4.2.2**: DomainGenerator - 域名生成，支持TLD分类和国际化

#### 2.3 API密钥

- [x] **6.1**: ApiKeyGenerator - API密钥生成，支持JWT/Bearer/自定义格式

### 3. 联系通信类生成器 ✅ (6/6)

#### 已完成

- [x] **3.1-3.2**: VerificationCodeGenerator - 验证码生成器，支持邮箱/短信/图形验证码
- [x] **3.3**: FaxGenerator - 传真号码生成器，支持多国格式和分机号
- [x] **3.4**: LandlineGenerator - 固话号码生成器，支持国际格式和城市关联
- [x] **3.6**: FilePathGenerator - 文件路径生成器，支持多操作系统和路径类型
- [x] **3.7**: MimeTypeGenerator - MIME类型生成器，支持所有标准类型和参数
- [x] **4.4**: PortGenerator - 端口号生成器，支持服务端口和端口类型

---

## 最新完成的功能亮点 🌟

### 联系通信类生成器特性

1. **VerificationCodeGenerator**
   - 支持邮箱/短信/图形/字母数字验证码
   - 支持自定义字符集和长度
   - 支持排除易混淆字符
   - 支持过期时间和验证逻辑

2. **FaxGenerator**
   - 支持中美英日四国格式
   - 支持标准/国际/紧凑格式
   - 支持分机号生成
   - 支持企业传真号生成

3. **LandlineGenerator**
   - 支持多国固话格式
   - 支持城市区号关联
   - 支持括号/国际格式
   - 支持分机号和企业专线

4. **FilePathGenerator**
   - 支持Windows/Unix/Mac路径
   - 支持绝对/相对/UNC路径
   - 支持文件扩展名分类
   - 支持路径穿越攻击payload

5. **MimeTypeGenerator**
   - 支持所有标准MIME类型
   - 支持参数和字符集
   - 支持Web安全类型过滤
   - 支持恶意类型生成

6. **PortGenerator**
   - 支持知名/注册/动态端口
   - 支持服务端口映射
   - 支持协议类型过滤
   - 支持防火墙规则生成

### 标识类生成器特性

1. **IpAddressGenerator**
   - 支持IPv4/IPv6双栈
   - 支持公网/私网/回环/组播地址类型
   - 支持子网范围生成
   - 支持IPv6地址压缩和展开格式

2. **MacAddressGenerator**
   - 支持15个主流厂商OUI
   - 支持冒号/连字符/点分/无分隔符格式
   - 支持单播/组播/广播地址类型
   - 支持大小写和混合格式

3. **UrlGenerator**
   - 支持HTTP/HTTPS/FTP等协议
   - 支持自定义域名和随机域名
   - 支持路径深度控制
   - 支持查询参数和片段标识符

4. **DomainGenerator**
   - 支持通用/品牌/字典/随机四种类型
   - 支持国际化域名（中文TLD）
   - 支持子域名生成
   - 支持自定义词典文件

5. **ApiKeyGenerator**
   - 支持JWT/Bearer/Basic/自定义格式
   - 支持Base64/十六进制/字母数字编码
   - 支持安全随机数生成器
   - 支持校验和生成

### 测试验证

- ✅ 所有33个生成器编译通过
- ✅ SPI注册文件更新完成
- ✅ 标识类生成器功能测试通过
- ✅ 联系通信类生成器功能测试通过
- ✅ 生成数据质量验证通过

---

## 下一步计划 📋

### 网络设备类生成器开发进展 ✨

**已完成 (7/9):**
1. ✅ **HttpHeaderGenerator** - HTTP头生成器
2. ✅ **SessionTokenGenerator** - 会话令牌生成器
3. ✅ **DeviceIdGenerator** - 设备ID生成器 (新增)
4. ✅ **GeolocationGenerator** - 地理坐标生成器 (新增)
5. ✅ **TimezoneGenerator** - 时区标识生成器 (新增)
6. ✅ **UserAgentGenerator** - User-Agent生成器 (新增)
7. ✅ **CookieGenerator** - Cookie生成器 (新增)

**待完成 (2/9):**
8. ⏳ **WebSocketGenerator** - WebSocket连接生成器
9. ⏳ **ProxyGenerator** - 代理配置生成器

### 新增生成器功能特性 🎯

**DeviceIdGenerator (设备ID生成器):**
- ✅ 支持UUID、IMEI、IMSI、自定义格式
- ✅ IMEI支持Luhn算法校验
- ✅ IMSI支持MCC/MNC配置
- ✅ 支持多种输出格式（PLAIN、HYPHEN、COLON）

**UserAgentGenerator (User-Agent生成器):**
- ✅ 支持Chrome、Firefox、Safari、Edge等主流浏览器
- ✅ 支持Windows、macOS、Linux、Android、iOS等操作系统
- ✅ 支持桌面、移动、平板设备类型
- ✅ 支持真实版本号生成

**GeolocationGenerator (地理坐标生成器):**
- ✅ 支持小数、度分秒、JSON、WKT等输出格式
- ✅ 支持区域限制（中国、美国、欧洲等）
- ✅ 支持中心点半径生成
- ✅ 支持高度信息生成

**TimezoneGenerator (时区生成器):**
- ✅ 支持IANA、UTC偏移量、缩写、显示名称格式
- ✅ 支持地区过滤（亚洲、欧洲、美洲等）
- ✅ 支持多语言显示名称
- ✅ 支持夏令时过滤

**CookieGenerator (Cookie生成器):**
- ✅ 支持HTTP头、JSON、简单格式输出
- ✅ 支持多种值类型（字符串、UUID、令牌、JSON）
- ✅ 支持完整的Cookie属性（域名、路径、过期时间、安全标志等）
- ✅ 支持SameSite属性配置

### 预期完成时间

- 网络设备类生成器：✅ 已完成 (7/9)
- 数值计量类生成器：2-3天
- 时间日历类生成器：2-3天

- [x] **H-1**: 使用 Picocli 创建 GenerateCommand
- [x] **H-2**: 在 GenerateCommand 中实现对所有核心 CLI 参数的定义和接收
- [x] **H-3**: 将 GenerateCommand 与 DataForgeService 连接

### 🎯 示例数据生成器

- [x] **UuidGenerator**: UUID生成器（UUID1/UUID4）
- [x] **NameGenerator**: 姓名生成器（中文/英文，支持性别关联）

### 📁 项目结构

```
dataforge/
├── pom.xml                                    # 根POM文件
├── data-forge-core/                           # 核心库模块
│   ├── pom.xml
│   └── src/main/java/com/dataforge/
│       ├── config/                            # 配置管理
│       │   ├── ConfigLoader.java              # 配置加载器
│       │   ├── ConfigLoadException.java       # 配置异常
│       │   ├── FieldConfigWrapper.java        # 字段配置包装器
│       │   ├── ForgeConfig.java               # 主配置类
│       │   └── OutputConfig.java              # 输出配置
│       ├── core/                              # 核心组件
│       │   ├── DataForgeContext.java          # 生成上下文
│       │   └── GeneratorFactory.java          # 生成器工厂
│       ├── generators/                        # 数据生成器
│       │   ├── spi/
│       │   │   └── DataGenerator.java         # 生成器接口
│       │   └── internal/
│       │       ├── UuidGenerator.java         # UUID生成器
│       │       ├── NameGenerator.java         # 姓名生成器
│       │       ├── PhoneGenerator.java        # 手机号码生成器
│       │       ├── BankCardGenerator.java     # 银行卡号生成器
│       │       ├── IdCardGenerator.java       # 身份证号码生成器
│       │       └── EmailGenerator.java        # 邮箱地址生成器
│       ├── io/                                # 输入输出
│       │   ├── OutputStrategy.java            # 输出策略接口
│       │   ├── OutputException.java           # 输出异常
│       │   ├── ConsoleOutputStrategy.java     # 控制台输出
│       │   ├── CsvOutputStrategy.java         # CSV输出
│       │   ├── JsonOutputStrategy.java        # JSON输出
│       │   └── SqlOutputStrategy.java         # SQL输出
│       ├── model/                             # 数据模型
│       │   └── FieldConfig.java               # 字段配置
│       ├── service/                           # 服务层
│       │   ├── DataForgeService.java          # 核心服务
│       │   └── DataForgeException.java        # 业务异常
│       └── validation/                        # 数据校验
│           ├── Validator.java                 # 校验器接口
│           ├── ValidationResult.java          # 校验结果
│           ├── LuhnValidator.java             # Luhn算法校验
│           ├── IdCardValidator.java           # 身份证校验
│           ├── UsccValidator.java             # 统一社会信用代码校验
│           └── OrganizationCodeValidator.java # 组织机构代码校验
├── data-forge-cli/                            # CLI应用模块
│   ├── pom.xml
│   └── src/main/java/com/dataforge/cli/
│       ├── DataForgeCliApplication.java       # Spring Boot启动类
│       └── commands/
│           └── GenerateCommand.java           # 生成命令
├── examples/                                  # 示例配置
│   ├── basic-config.yml                       # 基础配置示例
│   └── comprehensive-config.yml               # 综合配置示例
├── build.sh                                   # 构建脚本
├── run-example.sh                             # 示例运行脚本
├── README.md                                  # 项目说明
└── .gitignore                                 # Git忽略文件
```

### 🔧 技术栈

- **Java 17**: 项目基础语言
- **Spring Boot 3.2.x**: 应用框架
- **Maven 3.8+**: 构建工具
- **Picocli**: CLI框架
- **Jackson**: JSON/YAML处理
- **SLF4J + Logback**: 日志框架
- **JUnit 5**: 测试框架

### 📊 代码质量

- **Checkstyle**: Google Java Style Guide
- **PMD**: 静态代码分析
- **JaCoCo**: 代码覆盖率
- **SPI机制**: 插件化架构

### 🚀 快速开始

1. **构建项目**:

   ```bash
   ./build.sh
   ```

2. **运行示例**:

   ```bash
   ./run-example.sh
   ```

3. **基本使用**:

   ```bash
   java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
     --count 10 \
     --format csv \
     --output data.csv \
     --fields "id:uuid,name:name"
   ```

### 📋 下一步计划

#### 阶段二：数据类型生成器开发

- [ ] 基础信息类生成器（手机号、身份证、银行卡等）
- [ ] 标识类生成器（业务单据号、产品编码等）
- [ ] 网络设备类生成器（IP地址、域名、MAC地址等）
- [ ] 数值计量类生成器（随机数、小数、币种等）
- [ ] 时间日期类生成器（日期、时间、时间戳等）

#### 阶段三：特殊场景数据与高级功能

- [ ] 空值/边界值/异常数据生成
- [ ] 重复数据和排序数据
- [ ] 并发/竞争数据模拟
- [ ] 安全注入测试数据

### 🎉 里程碑

**阶段一已完成！** 🎊

DataForge项目的核心框架和基础设施已经搭建完成，具备了：

- 完整的模块化架构
- 可扩展的生成器机制
- 多种输出格式支持
- 强大的配置管理
- 完善的数据校验
- 用户友好的CLI界面

项目已经可以进行基本的数据生成工作，为后续的功能扩展奠定了坚实的基础。

### 🎯 已实现的数据生成器（已优化）

#### 基础信息类

- [x] **UuidGenerator**: UUID生成器（UUID1/UUID4/ULID/Snowflake）
- [x] **NameGenerator**: 姓名生成器（**已优化**）
  - 支持大规模数据文件加载（中文姓氏、男女名字库）
  - 权重选择算法，支持生成1亿+唯一姓名
  - 名字组合生成，英文中间名支持
- [x] **PhoneGenerator**: 手机号码生成器（**已优化**）
  - 完整运营商号段数据库（移动/联通/电信/虚拟/物联网）
  - 权重选择算法，支持生成数十亿唯一号码
- [x] **BankCardGenerator**: 银行卡号生成器（**已优化**）
  - 完整BIN码数据库（多银行、多卡组织）
  - 权重选择算法，支持生成数十亿唯一卡号
  - 基于Luhn算法校验
- [x] **IdCardGenerator**: 身份证号码生成器（**已优化**）
  - 完整行政区划数据库（省市区县）
  - 权重选择算法，支持生成数十亿唯一身份证号
  - 18位格式，支持地区/日期/性别关联
- [x] **EmailGenerator**: 邮箱地址生成器（**已优化**）
  - 完整域名数据库（按类型分类：个人/企业/教育等）
  - 权重选择算法，支持生成数十亿唯一邮箱
  - 姓名拼音关联，智能用户名生成
- [x] **LicensePlateGenerator**: 车牌号生成器（燃油/新能源车牌）
- [x] **AgeGenerator**: 年龄生成器（**新增**）
  - 支持年龄范围配置和分布类型（均匀/正态）
  - 支持与身份证号关联的精确年龄计算
- [x] **GenderGenerator**: 性别生成器（**新增**）
  - 支持多种输出格式（中文/英文/数字/符号）
  - 支持与身份证号关联的性别提取
  - 支持权重配置和上下文传递
- [x] **AddressGenerator**: 地址生成器（**新增**）
  - 基于行政区划数据的层级地址生成
  - 支持不同详细程度和邮编生成
  - 支持与身份证号关联的地区匹配
- [x] **UsernameGenerator**: 用户名生成器（**新增**）
  - 基于姓名的智能用户名生成
  - 支持拼音转换和唯一性保证
- [x] **PasswordGenerator**: 密码生成器（**新增**）
  - 支持多种复杂度级别和弱密码生成
  - 支持字符要求配置和易混淆字符排除
- [x] **CompanyNameGenerator**: 企业名称生成器（**新增**）
  - 支持行业关键词和地区前缀
  - 支持多种公司类型后缀
- [x] **UsccGenerator**: 统一社会信用代码生成器（**新增**）
  - 基于GB32100-2015标准的18位代码生成
  - 支持机构类别和校验码算法
- [x] **OrganizationCodeGenerator**: 组织机构代码生成器（**新增**）
  - 基于GB 11714-1997标准的9位代码生成
  - 支持校验位算法
- [x] **OccupationGenerator**: 职业生成器（**新增**）
  - 支持多行业多层级的职业生成
  - 支持权重配置和自定义职业库
- [x] **EducationGenerator**: 学历生成器（**新增**）
  - 支持学历层次范围和现实分布
  - 支持与年龄关联的合理性检查

#### 新增数据文件支持

- [x] **中文姓氏库**: 支持权重的姓氏数据（data/chinese-surnames.txt）
- [x] **中文名字库**: 男女名字分类数据（data/chinese-male-names.txt, chinese-female-names.txt）
- [x] **英文姓名库**: 英文名字和姓氏数据（data/english-first-names.txt, english-last-names.txt）
- [x] **手机号段库**: 运营商号段数据（data/phone-prefixes.txt）
- [x] **银行BIN码库**: 银行卡BIN码数据（data/bank-bins.txt）
- [x] **行政区划库**: 省市区县数据（data/administrative-divisions.txt）
- [x] **邮箱域名库**: 邮箱域名分类数据（data/email-domains.txt）
- [x] **车牌省份库**: 车牌省份简称数据（data/license-plate-provinces.txt）

#### 新增工具类

- [x] **DataLoader**: 数据加载工具类
  - 支持从资源文件和外部文件加载数据
  - 权重选择算法实现
  - 统一的数据解析接口

### 🔗 数据关联特性

DataForge的一个重要特性是支持字段间的数据关联：

1. **身份证号关联**:
   - 自动提取出生日期、年龄、性别、地区信息到上下文
   - 其他字段可以使用这些信息保持逻辑一致性

2. **姓名关联**:
   - 邮箱生成器可以基于姓名生成用户名前缀
   - 支持中文姓名转拼音

3. **性别关联**:
   - 姓名生成器可以根据性别生成对应的名字
   - 身份证号的顺序码会反映正确的性别

### 📊 生成器功能对比（优化后）

| 生成器 | 校验算法 | 地区支持 | 关联字段 | 异常数据 | 权重选择 | 数据规模 |
|--------|----------|----------|----------|----------|----------|----------|
| UUID | - | - | - | ❌ | ❌ | 无限 |
| 姓名 | - | 中英文 | 性别 | ❌ | ✅ | 1亿+ |
| 手机号 | 号段规则 | 中国 | - | ✅ | ✅ | 数十亿 |
| 银行卡 | Luhn算法 | 中国 | - | ✅ | ✅ | 数十亿 |
| 身份证 | 校验位算法 | 中国 | 年龄/性别/地区 | ✅ | ✅ | 数十亿 |
| 邮箱 | 格式校验 | - | 姓名 | ✅ | ✅ | 数十亿 |
| 车牌号 | 格式规则 | 中国 | - | ✅ | ✅ | 千万级 |
| 年龄 | 范围校验 | - | 身份证 | ✅ | ✅ | 无限 |
| 性别 | 枚举校验 | - | 身份证 | ❌ | ✅ | 3种 |
| 地址 | 格式校验 | 中国 | 身份证 | ❌ | ✅ | 千万级 |
| 用户名 | 格式校验 | - | 姓名 | ✅ | ❌ | 无限 |
| 密码 | 复杂度校验 | - | - | ✅ | ❌ | 无限 |
| 企业名称 | 格式校验 | 中国 | - | ❌ | ✅ | 千万级 |
| USCC | GB32100校验 | 中国 | - | ✅ | ❌ | 数十亿 |
| 组织机构码 | GB11714校验 | 中国 | - | ✅ | ❌ | 千万级 |
| 职业 | 枚举校验 | - | - | ❌ | ✅ | 数百种 |
| 学历 | 枚举校验 | - | 年龄 | ❌ | ✅ | 7种 |

### 🚀 使用示例

```bash
# 生成基本数据
java -jar data-forge-cli.jar --count 10 --fields "id:uuid,name:name,phone:phone"

# 生成关联数据
java -jar data-forge-cli.jar --count 10 --fields "name:name,idcard:idcard,email:email"

# 使用配置文件
java -jar data-forge-cli.jar --config examples/comprehensive-config.yml
```
