<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SqlOutputStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.io</a> &gt; <span class="el_source">SqlOutputStrategy.java</span></div><h1>SqlOutputStrategy.java</h1><pre class="source lang-java linenums">package com.dataforge.io;

import com.dataforge.config.OutputConfig;
import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * SQL INSERT输出策略实现。
 * 
 * &lt;p&gt;
 * 将生成的数据转换为SQL INSERT语句输出到文件或标准输出。
 * 支持自定义表名、字符编码等配置。
 * 采用流式写入，支持大数据量输出而不会导致内存溢出。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L33">public class SqlOutputStrategy implements OutputStrategy {</span>

<span class="nc" id="L35">    private static final Logger logger = LoggerFactory.getLogger(SqlOutputStrategy.class);</span>

    /**
     * 输出配置。
     */
    private OutputConfig config;

    /**
     * 字段名称列表。
     */
    private List&lt;String&gt; fieldNames;

    /**
     * 输出写入器。
     */
    private PrintWriter writer;

    /**
     * 已输出的记录数量。
     */
<span class="nc" id="L55">    private long recordCount = 0;</span>

    /**
     * SQL表名。
     */
    private String tableName;

    /**
     * 日期时间格式化器。
     */
<span class="nc" id="L65">    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd&quot;);</span>
<span class="nc" id="L66">    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="nc" id="L67">    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(&quot;HH:mm:ss&quot;);</span>

    @Override
    public OutputConfig.Format getSupportedFormat() {
<span class="nc" id="L71">        return OutputConfig.Format.SQL;</span>
    }

    @Override
    public void initialize(OutputConfig config, List&lt;String&gt; fieldNames) throws OutputException {
<span class="nc bnc" id="L76" title="All 2 branches missed.">        if (config == null) {</span>
<span class="nc" id="L77">            throw new OutputException(&quot;Output configuration cannot be null&quot;);</span>
        }

<span class="nc bnc" id="L80" title="All 4 branches missed.">        if (fieldNames == null || fieldNames.isEmpty()) {</span>
<span class="nc" id="L81">            throw new OutputException(&quot;Field names cannot be null or empty for SQL output&quot;);</span>
        }

<span class="nc" id="L84">        this.config = config;</span>
<span class="nc" id="L85">        this.fieldNames = fieldNames;</span>
<span class="nc" id="L86">        this.recordCount = 0;</span>
<span class="nc" id="L87">        this.tableName = config.getSqlTableName();</span>

<span class="nc bnc" id="L89" title="All 4 branches missed.">        if (tableName == null || tableName.trim().isEmpty()) {</span>
<span class="nc" id="L90">            tableName = &quot;test_data&quot;;</span>
        }

        try {
            // 初始化输出写入器
<span class="nc" id="L95">            initializeWriter();</span>

            // 写入SQL注释和表结构信息
<span class="nc" id="L98">            writeSqlHeader();</span>

<span class="nc" id="L100">            logger.info(&quot;SQL output strategy initialized. Output: {}, Table: {}, Fields: {}&quot;,</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">                    config.isFileOutput() ? config.getFile() : &quot;STDOUT&quot;,</span>
                    tableName,
<span class="nc" id="L103">                    fieldNames.size());</span>

<span class="nc" id="L105">        } catch (Exception e) {</span>
<span class="nc" id="L106">            throw new OutputException(&quot;Failed to initialize SQL output strategy&quot;, e);</span>
<span class="nc" id="L107">        }</span>
<span class="nc" id="L108">    }</span>

    @Override
    public void writeRecord(Map&lt;String, Object&gt; record) throws OutputException {
<span class="nc bnc" id="L112" title="All 2 branches missed.">        if (record == null) {</span>
<span class="nc" id="L113">            throw new OutputException(&quot;Record cannot be null&quot;);</span>
        }

<span class="nc bnc" id="L116" title="All 2 branches missed.">        if (writer == null) {</span>
<span class="nc" id="L117">            throw new OutputException(&quot;SQL output strategy not initialized&quot;);</span>
        }

        try {
            // 构建INSERT语句
<span class="nc" id="L122">            StringBuilder sql = new StringBuilder();</span>
<span class="nc" id="L123">            sql.append(&quot;INSERT INTO &quot;).append(escapeIdentifier(tableName)).append(&quot; (&quot;);</span>

            // 添加列名
<span class="nc" id="L126">            StringJoiner columnJoiner = new StringJoiner(&quot;, &quot;);</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">            for (String fieldName : fieldNames) {</span>
<span class="nc" id="L128">                columnJoiner.add(escapeIdentifier(fieldName));</span>
<span class="nc" id="L129">            }</span>
<span class="nc" id="L130">            sql.append(columnJoiner.toString());</span>

<span class="nc" id="L132">            sql.append(&quot;) VALUES (&quot;);</span>

            // 添加值
<span class="nc" id="L135">            StringJoiner valueJoiner = new StringJoiner(&quot;, &quot;);</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">            for (String fieldName : fieldNames) {</span>
<span class="nc" id="L137">                Object value = record.get(fieldName);</span>
<span class="nc" id="L138">                String sqlValue = formatSqlValue(value);</span>
<span class="nc" id="L139">                valueJoiner.add(sqlValue);</span>
<span class="nc" id="L140">            }</span>
<span class="nc" id="L141">            sql.append(valueJoiner.toString());</span>

<span class="nc" id="L143">            sql.append(&quot;);&quot;);</span>

            // 写入SQL语句
<span class="nc" id="L146">            writer.println(sql.toString());</span>
<span class="nc" id="L147">            recordCount++;</span>

            // 定期刷新缓冲区（每1000条记录）
<span class="nc bnc" id="L150" title="All 2 branches missed.">            if (recordCount % 1000 == 0) {</span>
<span class="nc" id="L151">                writer.flush();</span>
<span class="nc" id="L152">                logger.debug(&quot;Written {} SQL INSERT statements&quot;, recordCount);</span>
            }

<span class="nc" id="L155">        } catch (Exception e) {</span>
<span class="nc" id="L156">            throw new OutputException(&quot;Failed to write SQL INSERT statement&quot;, e);</span>
<span class="nc" id="L157">        }</span>
<span class="nc" id="L158">    }</span>

    @Override
    public void writeRecords(List&lt;Map&lt;String, Object&gt;&gt; records) throws OutputException {
<span class="nc bnc" id="L162" title="All 4 branches missed.">        if (records == null || records.isEmpty()) {</span>
<span class="nc" id="L163">            return;</span>
        }

<span class="nc" id="L166">        logger.debug(&quot;Writing {} SQL INSERT statements in batch&quot;, records.size());</span>

        // 可以优化为批量INSERT语句
<span class="nc bnc" id="L169" title="All 2 branches missed.">        if (records.size() &gt; 1) {</span>
<span class="nc" id="L170">            writeBatchInsert(records);</span>
        } else {
<span class="nc" id="L172">            writeRecord(records.get(0));</span>
        }

        // 批量写入后刷新
<span class="nc" id="L176">        flush();</span>
<span class="nc" id="L177">    }</span>

    @Override
    public void finish() throws OutputException {
        try {
<span class="nc bnc" id="L182" title="All 2 branches missed.">            if (writer != null) {</span>
                // 写入SQL尾部注释
<span class="nc" id="L184">                writeSqlFooter();</span>

<span class="nc" id="L186">                writer.flush();</span>

                // 如果是文件输出，关闭写入器
<span class="nc bnc" id="L189" title="All 2 branches missed.">                if (config.isFileOutput()) {</span>
<span class="nc" id="L190">                    writer.close();</span>
                }

<span class="nc" id="L193">                logger.info(&quot;SQL output completed. Total INSERT statements: {}&quot;, recordCount);</span>
            }

<span class="nc" id="L196">        } catch (Exception e) {</span>
<span class="nc" id="L197">            throw new OutputException(&quot;Failed to finish SQL output&quot;, e);</span>
        } finally {
<span class="nc" id="L199">            writer = null;</span>
        }
<span class="nc" id="L201">    }</span>

    @Override
    public void flush() throws OutputException {
<span class="nc bnc" id="L205" title="All 2 branches missed.">        if (writer != null) {</span>
<span class="nc" id="L206">            writer.flush();</span>
        }
<span class="nc" id="L208">    }</span>

    @Override
    public long getWrittenRecordCount() {
<span class="nc" id="L212">        return recordCount;</span>
    }

    /**
     * 初始化输出写入器。
     * 
     * @throws IOException 当初始化失败时
     */
    private void initializeWriter() throws IOException {
<span class="nc bnc" id="L221" title="All 2 branches missed.">        if (config.isFileOutput()) {</span>
            // 文件输出
<span class="nc" id="L223">            String filePath = config.getFile();</span>
<span class="nc" id="L224">            Charset charset = Charset.forName(config.getEncoding());</span>

            // 创建父目录（如果不存在）
<span class="nc" id="L227">            java.io.File file = new java.io.File(filePath);</span>
<span class="nc" id="L228">            java.io.File parentDir = file.getParentFile();</span>
<span class="nc bnc" id="L229" title="All 4 branches missed.">            if (parentDir != null &amp;&amp; !parentDir.exists()) {</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">                if (!parentDir.mkdirs()) {</span>
<span class="nc" id="L231">                    throw new IOException(&quot;Failed to create parent directories: &quot; + parentDir.getAbsolutePath());</span>
                }
            }

            // 创建文件输出流
<span class="nc" id="L236">            FileOutputStream fos = new FileOutputStream(file, config.isAppend());</span>
<span class="nc" id="L237">            OutputStreamWriter osw = new OutputStreamWriter(fos, charset);</span>
<span class="nc" id="L238">            BufferedWriter bw = new BufferedWriter(osw, 8192); // 8KB缓冲区</span>
<span class="nc" id="L239">            writer = new PrintWriter(bw);</span>

<span class="nc" id="L241">            logger.debug(&quot;Initialized SQL file writer: {}, encoding: {}, append: {}&quot;,</span>
<span class="nc" id="L242">                    filePath, config.getEncoding(), config.isAppend());</span>
<span class="nc" id="L243">        } else {</span>
            // 标准输出
<span class="nc" id="L245">            writer = new PrintWriter(System.out);</span>
<span class="nc" id="L246">            logger.debug(&quot;Initialized SQL console writer&quot;);</span>
        }
<span class="nc" id="L248">    }</span>

    /**
     * 写入SQL文件头部注释。
     */
    private void writeSqlHeader() {
<span class="nc" id="L254">        writer.println(&quot;-- ========================================&quot;);</span>
<span class="nc" id="L255">        writer.println(&quot;-- DataForge Generated SQL INSERT Statements&quot;);</span>
<span class="nc" id="L256">        writer.println(&quot;-- Generated at: &quot; + java.time.LocalDateTime.now().format(DATETIME_FORMATTER));</span>
<span class="nc" id="L257">        writer.println(&quot;-- Table: &quot; + tableName);</span>
<span class="nc" id="L258">        writer.println(&quot;-- Fields: &quot; + String.join(&quot;, &quot;, fieldNames));</span>
<span class="nc" id="L259">        writer.println(&quot;-- ========================================&quot;);</span>
<span class="nc" id="L260">        writer.println();</span>
<span class="nc" id="L261">    }</span>

    /**
     * 写入SQL文件尾部注释。
     */
    private void writeSqlFooter() {
<span class="nc" id="L267">        writer.println();</span>
<span class="nc" id="L268">        writer.println(&quot;-- ========================================&quot;);</span>
<span class="nc" id="L269">        writer.println(&quot;-- Total records inserted: &quot; + recordCount);</span>
<span class="nc" id="L270">        writer.println(&quot;-- Generation completed at: &quot; + java.time.LocalDateTime.now().format(DATETIME_FORMATTER));</span>
<span class="nc" id="L271">        writer.println(&quot;-- ========================================&quot;);</span>
<span class="nc" id="L272">    }</span>

    /**
     * 写入批量INSERT语句。
     * 
     * @param records 记录列表
     */
    private void writeBatchInsert(List&lt;Map&lt;String, Object&gt;&gt; records) {
<span class="nc bnc" id="L280" title="All 2 branches missed.">        if (records.isEmpty()) {</span>
<span class="nc" id="L281">            return;</span>
        }

        // 构建批量INSERT语句
<span class="nc" id="L285">        StringBuilder sql = new StringBuilder();</span>
<span class="nc" id="L286">        sql.append(&quot;INSERT INTO &quot;).append(escapeIdentifier(tableName)).append(&quot; (&quot;);</span>

        // 添加列名
<span class="nc" id="L289">        StringJoiner columnJoiner = new StringJoiner(&quot;, &quot;);</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">        for (String fieldName : fieldNames) {</span>
<span class="nc" id="L291">            columnJoiner.add(escapeIdentifier(fieldName));</span>
<span class="nc" id="L292">        }</span>
<span class="nc" id="L293">        sql.append(columnJoiner.toString());</span>
<span class="nc" id="L294">        sql.append(&quot;) VALUES&quot;);</span>

        // 添加多行值
<span class="nc bnc" id="L297" title="All 2 branches missed.">        for (int i = 0; i &lt; records.size(); i++) {</span>
<span class="nc bnc" id="L298" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L299">                sql.append(&quot;,&quot;);</span>
            }
<span class="nc" id="L301">            sql.append(&quot;\n    (&quot;);</span>

<span class="nc" id="L303">            Map&lt;String, Object&gt; record = records.get(i);</span>
<span class="nc" id="L304">            StringJoiner valueJoiner = new StringJoiner(&quot;, &quot;);</span>
<span class="nc bnc" id="L305" title="All 2 branches missed.">            for (String fieldName : fieldNames) {</span>
<span class="nc" id="L306">                Object value = record.get(fieldName);</span>
<span class="nc" id="L307">                String sqlValue = formatSqlValue(value);</span>
<span class="nc" id="L308">                valueJoiner.add(sqlValue);</span>
<span class="nc" id="L309">            }</span>
<span class="nc" id="L310">            sql.append(valueJoiner.toString());</span>
<span class="nc" id="L311">            sql.append(&quot;)&quot;);</span>
        }

<span class="nc" id="L314">        sql.append(&quot;;&quot;);</span>

        // 写入批量INSERT语句
<span class="nc" id="L317">        writer.println(sql.toString());</span>
<span class="nc" id="L318">        recordCount += records.size();</span>
<span class="nc" id="L319">    }</span>

    /**
     * 转义SQL标识符（表名、列名）。
     * 
     * @param identifier 标识符
     * @return 转义后的标识符
     */
    private String escapeIdentifier(String identifier) {
<span class="nc bnc" id="L328" title="All 4 branches missed.">        if (identifier == null || identifier.isEmpty()) {</span>
<span class="nc" id="L329">            return identifier;</span>
        }

        // 简单的标识符转义，使用反引号
<span class="nc" id="L333">        return &quot;`&quot; + identifier.replace(&quot;`&quot;, &quot;``&quot;) + &quot;`&quot;;</span>
    }

    /**
     * 格式化SQL值。
     * 
     * &lt;p&gt;
     * 处理null值、字符串转义、数值、日期时间等类型。
     * 
     * @param value 原始值
     * @return 格式化后的SQL值
     */
    private String formatSqlValue(Object value) {
<span class="nc bnc" id="L346" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L347">            return &quot;NULL&quot;;</span>
        }

<span class="nc bnc" id="L350" title="All 2 branches missed.">        if (value instanceof String) {</span>
            // 字符串需要转义单引号
<span class="nc" id="L352">            String str = (String) value;</span>
<span class="nc" id="L353">            str = str.replace(&quot;'&quot;, &quot;''&quot;); // 转义单引号</span>
<span class="nc" id="L354">            str = str.replace(&quot;\\&quot;, &quot;\\\\&quot;); // 转义反斜杠</span>
<span class="nc" id="L355">            return &quot;'&quot; + str + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L358" title="All 2 branches missed.">        if (value instanceof Number) {</span>
            // 数值类型直接返回
<span class="nc" id="L360">            return value.toString();</span>
        }

<span class="nc bnc" id="L363" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
            // 布尔值转换为数值
<span class="nc bnc" id="L365" title="All 2 branches missed.">            return ((Boolean) value) ? &quot;1&quot; : &quot;0&quot;;</span>
        }

<span class="nc bnc" id="L368" title="All 2 branches missed.">        if (value instanceof LocalDate) {</span>
            // 日期格式化
<span class="nc" id="L370">            return &quot;'&quot; + ((LocalDate) value).format(DATE_FORMATTER) + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L373" title="All 2 branches missed.">        if (value instanceof LocalDateTime) {</span>
            // 日期时间格式化
<span class="nc" id="L375">            return &quot;'&quot; + ((LocalDateTime) value).format(DATETIME_FORMATTER) + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L378" title="All 2 branches missed.">        if (value instanceof LocalTime) {</span>
            // 时间格式化
<span class="nc" id="L380">            return &quot;'&quot; + ((LocalTime) value).format(TIME_FORMATTER) + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L383" title="All 2 branches missed.">        if (value instanceof java.util.Date) {</span>
            // java.util.Date转换
<span class="nc" id="L385">            java.util.Date date = (java.util.Date) value;</span>
<span class="nc" id="L386">            LocalDateTime ldt = LocalDateTime.ofInstant(date.toInstant(), java.time.ZoneId.systemDefault());</span>
<span class="nc" id="L387">            return &quot;'&quot; + ldt.format(DATETIME_FORMATTER) + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L390" title="All 2 branches missed.">        if (value instanceof java.sql.Date) {</span>
            // SQL日期
<span class="nc" id="L392">            return &quot;'&quot; + value.toString() + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L395" title="All 2 branches missed.">        if (value instanceof java.sql.Timestamp) {</span>
            // SQL时间戳
<span class="nc" id="L397">            return &quot;'&quot; + value.toString() + &quot;'&quot;;</span>
        }

<span class="nc bnc" id="L400" title="All 2 branches missed.">        if (value instanceof java.sql.Time) {</span>
            // SQL时间
<span class="nc" id="L402">            return &quot;'&quot; + value.toString() + &quot;'&quot;;</span>
        }

        // 其他类型转换为字符串并转义
<span class="nc" id="L406">        String str = value.toString();</span>
<span class="nc" id="L407">        str = str.replace(&quot;'&quot;, &quot;''&quot;);</span>
<span class="nc" id="L408">        str = str.replace(&quot;\\&quot;, &quot;\\\\&quot;);</span>
<span class="nc" id="L409">        return &quot;'&quot; + str + &quot;'&quot;;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L414">        return &quot;SQL output strategy - exports data as SQL INSERT statements&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>