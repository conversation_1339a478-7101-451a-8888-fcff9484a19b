<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserAgentGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">UserAgentGenerator</span></div><h1>UserAgentGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">88 of 708</td><td class="ctr2">87%</td><td class="bar">11 of 50</td><td class="ctr2">78%</td><td class="ctr1">11</td><td class="ctr2">43</td><td class="ctr1">27</td><td class="ctr2">114</td><td class="ctr1">0</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a4"><a href="UserAgentGenerator.java.html#L306" class="el_method">generateVersion(UserAgentGenerator.BrowserType, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="34" alt="34"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="79" alt="79"/></td><td class="ctr2" id="c11">69%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">57%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h0">6</td><td class="ctr2" id="i1">17</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="UserAgentGenerator.java.html#L235" class="el_method">generateUserAgent(UserAgentGenerator.BrowserType, UserAgentGenerator.OSType, UserAgentGenerator.DeviceType, boolean, boolean)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="68" alt="68"/></td><td class="ctr2" id="c10">81%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="UserAgentGenerator.java.html#L133" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="141" alt="141"/></td><td class="ctr2" id="c5">95%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i0">22</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="UserAgentGenerator.java.html#L181" class="el_method">parseBrowserType(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">36%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a12"><a href="UserAgentGenerator.java.html#L193" class="el_method">parseOSType(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">36%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a11"><a href="UserAgentGenerator.java.html#L205" class="el_method">parseDeviceType(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">36%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="UserAgentGenerator.java.html#L276" class="el_method">getRandomTemplate(UserAgentGenerator.BrowserType, UserAgentGenerator.DeviceType)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="29" alt="29"/></td><td class="ctr2" id="c9">85%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">66%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i3">13</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="UserAgentGenerator.java.html#L347" class="el_method">generateChromeVersion(boolean)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="34" alt="34"/></td><td class="ctr2" id="c6">94%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="UserAgentGenerator.java.html#L263" class="el_method">getRandomOSString(UserAgentGenerator.OSType)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="18" alt="18"/></td><td class="ctr2" id="c7">90%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="UserAgentGenerator.java.html#L337" class="el_method">generateWebKitVersion(boolean)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c8">88%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="1" alt="1"/></td><td class="ctr2" id="e7">50%</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a13"><a href="UserAgentGenerator.java.html#L34" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="187" alt="187"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i4">13</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="UserAgentGenerator.java.html#L217" class="el_method">adjustOSAndDevice(UserAgentGenerator.OSType, UserAgentGenerator.DeviceType)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="29" alt="29"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a14"><a href="UserAgentGenerator.java.html#L32" class="el_method">UserAgentGenerator()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="UserAgentGenerator.java.html#L121" class="el_method">getType()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a6"><a href="UserAgentGenerator.java.html#L126" class="el_method">getConfigClass()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>