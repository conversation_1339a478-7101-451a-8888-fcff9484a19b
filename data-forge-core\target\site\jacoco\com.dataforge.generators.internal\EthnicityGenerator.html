<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EthnicityGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">EthnicityGenerator</span></div><h1>EthnicityGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">883 of 883</td><td class="ctr2">0%</td><td class="bar">54 of 54</td><td class="ctr2">0%</td><td class="ctr1">41</td><td class="ctr2">41</td><td class="ctr1">138</td><td class="ctr2">138</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a12"><a href="EthnicityGenerator.java.html#L26" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="246" alt="246"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="EthnicityGenerator.java.html#L51" class="el_method">initializeEthnicityCodes()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="133" alt="133"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h0">25</td><td class="ctr2" id="i0">25</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="EthnicityGenerator.java.html#L82" class="el_method">initializeRealisticWeights()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="82" alt="82"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="EthnicityGenerator.java.html#L210" class="el_method">selectWithRealisticWeights(List)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="74" alt="74"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h3">14</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="EthnicityGenerator.java.html#L115" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="73" alt="73"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h4">14</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="EthnicityGenerator.java.html#L236" class="el_method">selectWithCustomWeights(List, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="68" alt="68"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i5">13</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="EthnicityGenerator.java.html#L143" class="el_method">loadEthnicities(FieldConfig, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="62" alt="62"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="9" alt="9"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="EthnicityGenerator.java.html#L260" class="el_method">parseWeights(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="58" alt="58"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="EthnicityGenerator.java.html#L180" class="el_method">selectEthnicity(List, String, double, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="57" alt="57"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h6">13</td><td class="ctr2" id="i6">13</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="EthnicityGenerator.java.html#L280" class="el_method">formatEthnicity(String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="23" alt="23"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a0"><a href="EthnicityGenerator.java.html#L24" class="el_method">EthnicityGenerator()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a4"><a href="EthnicityGenerator.java.html#L103" class="el_method">getType()</a></td><td class="bar" id="b11"/><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="EthnicityGenerator.java.html#L108" class="el_method">getConfigClass()</a></td><td class="bar" id="b12"/><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>