package com.dataforge.cli;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * DataForge CLI应用程序启动类。
 * 
 * <p>
 * 使用Spring Boot框架启动CLI应用程序。
 * 该类是整个CLI应用的入口点。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = { "com.dataforge" })
public class DataForgeCliApplication {

    /**
     * 应用程序主入口方法。
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置Spring Boot应用为非Web应用
        System.setProperty("spring.main.web-application-type", "none");

        // 启动Spring Boot应用
        SpringApplication.run(DataForgeCliApplication.class, args);
    }
}