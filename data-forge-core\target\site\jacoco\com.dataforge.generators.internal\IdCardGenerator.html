<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IdCardGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">IdCardGenerator</span></div><h1>IdCardGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,226 of 1,226</td><td class="ctr2">0%</td><td class="bar">114 of 114</td><td class="ctr2">0%</td><td class="ctr1">86</td><td class="ctr2">86</td><td class="ctr1">245</td><td class="ctr2">245</td><td class="ctr1">28</td><td class="ctr2">28</td></tr></tfoot><tbody><tr><td id="a22"><a href="IdCardGenerator.java.html#L170" class="el_method">loadData(FieldConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="150" alt="150"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h0">30</td><td class="ctr2" id="i0">30</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a17"><a href="IdCardGenerator.java.html#L234" class="el_method">initializeFallbackData()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="121" alt="121"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h4">18</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a25"><a href="IdCardGenerator.java.html#L522" class="el_method">putIdCardInfoToContext(DataForgeContext, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="103" alt="103"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h1">24</td><td class="ctr2" id="i1">24</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a13"><a href="IdCardGenerator.java.html#L615" class="el_method">getRegionStats()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="94" alt="94"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a27"><a href="IdCardGenerator.java.html#L35" class="el_method">static {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="88" alt="88"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="IdCardGenerator.java.html#L448" class="el_method">generateBirthDate(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="86" alt="86"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="12" alt="12"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h2">21</td><td class="ctr2" id="i2">21</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="IdCardGenerator.java.html#L271" class="el_method">generateValidIdCard(String, String, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="73" alt="73"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h6">13</td><td class="ctr2" id="i6">13</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="IdCardGenerator.java.html#L491" class="el_method">generateSequenceCode(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="66" alt="66"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h10">9</td><td class="ctr2" id="i10">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="IdCardGenerator.java.html#L104" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="63" alt="63"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h5">16</td><td class="ctr2" id="i5">16</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a26"><a href="IdCardGenerator.java.html#L417" class="el_method">selectRegionCode(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="60" alt="60"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h8">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="IdCardGenerator.java.html#L371" class="el_method">generateInvalidDateIdCard()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="58" alt="58"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h12">8</td><td class="ctr2" id="i12">8</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="IdCardGenerator.java.html#L323" class="el_method">generateWrongLengthIdCard()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="50" alt="50"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h11">9</td><td class="ctr2" id="i11">9</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a8"><a href="IdCardGenerator.java.html#L346" class="el_method">generateWrongChecksumIdCard()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="50" alt="50"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h9">11</td><td class="ctr2" id="i9">11</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a23"><a href="IdCardGenerator.java.html#L564" class="el_method">maskIdCard(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="36" alt="36"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f12">3</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i14">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a10"><a href="IdCardGenerator.java.html#L593" class="el_method">getBooleanParam(FieldConfig, String, boolean)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="27" alt="27"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h13">8</td><td class="ctr2" id="i13">8</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a14"><a href="IdCardGenerator.java.html#L581" class="el_method">getStringParam(FieldConfig, String, String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">4</td><td class="ctr1" id="h18">4</td><td class="ctr2" id="i18">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a4"><a href="IdCardGenerator.java.html#L307" class="el_method">generateInvalidIdCard()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h15">6</td><td class="ctr2" id="i15">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a0"><a href="IdCardGenerator.java.html#L153" class="el_method">ensureDataLoaded(FieldConfig)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="17" alt="17"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f13">3</td><td class="ctr2" id="g13">3</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a5"><a href="IdCardGenerator.java.html#L400" class="el_method">generateOtherInvalidIdCard()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="16" alt="16"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h17">5</td><td class="ctr2" id="i17">5</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a24"><a href="IdCardGenerator.java.html#L224" class="el_method">parseWeight(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">3</td><td class="ctr2" id="i19">3</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a19"><a href="IdCardGenerator.java.html#L254" class="el_method">lambda$initializeFallbackData$3(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a18"><a href="IdCardGenerator.java.html#L252" class="el_method">lambda$initializeFallbackData$2(String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a21"><a href="IdCardGenerator.java.html#L196" class="el_method">lambda$loadData$1(String)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a20"><a href="IdCardGenerator.java.html#L195" class="el_method">lambda$loadData$0(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a16"><a href="IdCardGenerator.java.html#L33" class="el_method">IdCardGenerator()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a15"><a href="IdCardGenerator.java.html#L97" class="el_method">getType()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a11"><a href="IdCardGenerator.java.html#L144" class="el_method">getConfigClass()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a12"><a href="IdCardGenerator.java.html#L638" class="el_method">getDescription()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>