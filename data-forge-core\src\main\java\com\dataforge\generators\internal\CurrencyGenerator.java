package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.text.NumberFormat;
import java.util.*;

/**
 * 币种金额生成器
 * 
 * <p>
 * 支持生成各种货币的金额数据，包括主要国际货币、数字货币等，
 * 用于财务系统测试、电商平台测试、支付系统验证等场景。
 * 
 * <p>
 * 支持的参数：
 * <ul>
 * <li>currency: 货币代码 (USD|EUR|CNY|JPY|GBP|CRYPTO|RANDOM) 默认: USD</li>
 * <li>min: 最小金额 默认: 0.01</li>
 * <li>max: 最大金额 默认: 10000.00</li>
 * <li>format: 输出格式 (SYMBOL|CODE|FULL|PLAIN|JSON) 默认: SYMBOL</li>
 * <li>precision: 小数位数 默认: 2</li>
 * <li>locale: 本地化设置 默认: en_US</li>
 * <li>include_symbol: 是否包含货币符号 默认: true</li>
 * <li>include_code: 是否包含货币代码 默认: false</li>
 * <li>grouping: 是否使用千分位分隔符 默认: true</li>
 * <li>crypto_type: 数字货币类型 (BTC|ETH|USDT|RANDOM) 默认: BTC</li>
 * <li>exchange_rate: 汇率（相对于USD）</li>
 * <li>rounding: 舍入模式 (UP|DOWN|HALF_UP|HALF_DOWN) 默认: HALF_UP</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class CurrencyGenerator extends BaseGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(CurrencyGenerator.class);
    private static final SecureRandom random = new SecureRandom();
    
    // 货币类型枚举
    public enum CurrencyType {
        USD("美元", "$", 2),
        EUR("欧元", "€", 2),
        CNY("人民币", "¥", 2),
        JPY("日元", "¥", 0),
        GBP("英镑", "£", 2),
        KRW("韩元", "₩", 0),
        INR("印度卢比", "₹", 2),
        CAD("加拿大元", "C$", 2),
        AUD("澳大利亚元", "A$", 2),
        CHF("瑞士法郎", "CHF", 2),
        CRYPTO("数字货币", "", 8),
        RANDOM("随机货币", "", 2);
        
        private final String description;
        private final String symbol;
        private final int defaultPrecision;
        
        CurrencyType(String description, String symbol, int defaultPrecision) {
            this.description = description;
            this.symbol = symbol;
            this.defaultPrecision = defaultPrecision;
        }
        
        public String getDescription() { return description; }
        public String getSymbol() { return symbol; }
        public int getDefaultPrecision() { return defaultPrecision; }
    }
    
    // 输出格式枚举
    public enum OutputFormat {
        SYMBOL("符号格式"),
        CODE("代码格式"),
        FULL("完整格式"),
        PLAIN("纯数字格式"),
        JSON("JSON格式");
        
        private final String description;
        
        OutputFormat(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 数字货币类型
    public enum CryptoType {
        BTC("比特币", "₿", 8),
        ETH("以太坊", "Ξ", 18),
        USDT("泰达币", "₮", 6),
        BNB("币安币", "BNB", 8),
        ADA("艾达币", "₳", 6),
        DOT("波卡币", "DOT", 10),
        RANDOM("随机数字货币", "", 8);
        
        private final String description;
        private final String symbol;
        private final int precision;
        
        CryptoType(String description, String symbol, int precision) {
            this.description = description;
            this.symbol = symbol;
            this.precision = precision;
        }
        
        public String getDescription() { return description; }
        public String getSymbol() { return symbol; }
        public int getPrecision() { return precision; }
    }
    
    // 汇率映射（相对于USD）
    private static final Map<CurrencyType, Double> EXCHANGE_RATES = new HashMap<>();
    
    static {
        EXCHANGE_RATES.put(CurrencyType.USD, 1.0);
        EXCHANGE_RATES.put(CurrencyType.EUR, 0.85);
        EXCHANGE_RATES.put(CurrencyType.CNY, 7.2);
        EXCHANGE_RATES.put(CurrencyType.JPY, 150.0);
        EXCHANGE_RATES.put(CurrencyType.GBP, 0.79);
        EXCHANGE_RATES.put(CurrencyType.KRW, 1320.0);
        EXCHANGE_RATES.put(CurrencyType.INR, 83.0);
        EXCHANGE_RATES.put(CurrencyType.CAD, 1.35);
        EXCHANGE_RATES.put(CurrencyType.AUD, 1.52);
        EXCHANGE_RATES.put(CurrencyType.CHF, 0.88);
    }
    
    // 数字货币价格映射（相对于USD）
    private static final Map<CryptoType, Double> CRYPTO_PRICES = new HashMap<>();
    
    static {
        CRYPTO_PRICES.put(CryptoType.BTC, 45000.0);
        CRYPTO_PRICES.put(CryptoType.ETH, 2800.0);
        CRYPTO_PRICES.put(CryptoType.USDT, 1.0);
        CRYPTO_PRICES.put(CryptoType.BNB, 320.0);
        CRYPTO_PRICES.put(CryptoType.ADA, 0.45);
        CRYPTO_PRICES.put(CryptoType.DOT, 6.8);
    }

    @Override
    public String getType() {
        return "currency";
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取货币类型
            String currencyStr = getStringParam(config, "currency", "USD");
            CurrencyType currency = parseCurrencyType(currencyStr);
            
            // 获取输出格式
            String formatStr = getStringParam(config, "format", "SYMBOL");
            OutputFormat format = parseOutputFormat(formatStr);
            
            // 生成金额
            BigDecimal amount = generateAmount(currency, config);
            
            // 格式化输出
            return formatCurrency(amount, currency, format, config);
            
        } catch (Exception e) {
            logger.error("Failed to generate currency amount", e);
            // 返回一个默认的货币金额作为fallback
            return "$100.00";
        }
    }

    /**
     * 解析货币类型
     */
    private CurrencyType parseCurrencyType(String currencyStr) {
        try {
            return CurrencyType.valueOf(currencyStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid currency type: {}, using USD as default", currencyStr);
            return CurrencyType.USD;
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
            return OutputFormat.valueOf(formatStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid output format: {}, using SYMBOL as default", formatStr);
            return OutputFormat.SYMBOL;
        }
    }

    /**
     * 生成金额
     */
    private BigDecimal generateAmount(CurrencyType currency, FieldConfig config) {
        // 获取金额范围
        double min = getDoubleParam(config, "min", 0.01);
        double max = getDoubleParam(config, "max", 10000.0);
        
        // 处理特殊货币类型
        if (currency == CurrencyType.RANDOM) {
            CurrencyType[] currencies = {CurrencyType.USD, CurrencyType.EUR, CurrencyType.CNY, 
                                       CurrencyType.JPY, CurrencyType.GBP};
            currency = currencies[random.nextInt(currencies.length)];
        }
        
        if (currency == CurrencyType.CRYPTO) {
            return generateCryptoAmount(config, min, max);
        }
        
        // 生成基础金额
        double amount = min + random.nextDouble() * (max - min);
        
        // 应用汇率
        String exchangeRateStr = getStringParam(config, "exchange_rate", null);
        if (exchangeRateStr != null) {
            try {
                double customRate = Double.parseDouble(exchangeRateStr);
                amount *= customRate;
            } catch (NumberFormatException e) {
                logger.warn("Invalid exchange rate: {}, using default", exchangeRateStr);
                Double defaultRate = EXCHANGE_RATES.get(currency);
                if (defaultRate != null) {
                    amount *= defaultRate;
                }
            }
        } else {
            Double defaultRate = EXCHANGE_RATES.get(currency);
            if (defaultRate != null && defaultRate != 1.0) {
                amount *= defaultRate;
            }
        }
        
        // 应用精度和舍入
        int precision = getIntParam(config, "precision", currency.getDefaultPrecision());
        String roundingStr = getStringParam(config, "rounding", "HALF_UP");
        RoundingMode rounding = parseRoundingMode(roundingStr);
        
        return BigDecimal.valueOf(amount).setScale(precision, rounding);
    }

    /**
     * 生成数字货币金额
     */
    private BigDecimal generateCryptoAmount(FieldConfig config, double min, double max) {
        String cryptoTypeStr = getStringParam(config, "crypto_type", "BTC");
        CryptoType cryptoType = parseCryptoType(cryptoTypeStr);
        
        if (cryptoType == CryptoType.RANDOM) {
            CryptoType[] cryptos = {CryptoType.BTC, CryptoType.ETH, CryptoType.USDT, CryptoType.BNB};
            cryptoType = cryptos[random.nextInt(cryptos.length)];
        }
        
        // 生成数字货币数量（通常较小）
        double cryptoMin = Math.max(min, 0.00000001); // 最小单位
        double cryptoMax = Math.min(max, 100.0); // 通常不会持有太多数字货币
        
        double amount = cryptoMin + random.nextDouble() * (cryptoMax - cryptoMin);
        
        int precision = getIntParam(config, "precision", cryptoType.getPrecision());
        String roundingStr = getStringParam(config, "rounding", "HALF_UP");
        RoundingMode rounding = parseRoundingMode(roundingStr);
        
        return BigDecimal.valueOf(amount).setScale(precision, rounding);
    }

    /**
     * 解析数字货币类型
     */
    private CryptoType parseCryptoType(String cryptoTypeStr) {
        try {
            return CryptoType.valueOf(cryptoTypeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid crypto type: {}, using BTC as default", cryptoTypeStr);
            return CryptoType.BTC;
        }
    }

    /**
     * 解析舍入模式
     */
    private RoundingMode parseRoundingMode(String roundingStr) {
        try {
            return RoundingMode.valueOf(roundingStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid rounding mode: {}, using HALF_UP as default", roundingStr);
            return RoundingMode.HALF_UP;
        }
    }

    /**
     * 格式化货币
     */
    private String formatCurrency(BigDecimal amount, CurrencyType currency, OutputFormat format, FieldConfig config) {
        switch (format) {
            case SYMBOL:
                return formatWithSymbol(amount, currency, config);
            case CODE:
                return formatWithCode(amount, currency, config);
            case FULL:
                return formatFull(amount, currency, config);
            case PLAIN:
                return formatPlain(amount, config);
            case JSON:
                return formatAsJson(amount, currency, config);
            default:
                return formatWithSymbol(amount, currency, config);
        }
    }

    /**
     * 格式化为符号格式
     */
    private String formatWithSymbol(BigDecimal amount, CurrencyType currency, FieldConfig config) {
        boolean includeSymbol = getBooleanParam(config, "include_symbol", true);
        boolean grouping = getBooleanParam(config, "grouping", true);
        
        if (currency == CurrencyType.CRYPTO) {
            String cryptoTypeStr = getStringParam(config, "crypto_type", "BTC");
            CryptoType cryptoType = parseCryptoType(cryptoTypeStr);
            
            if (includeSymbol && !cryptoType.getSymbol().isEmpty()) {
                return cryptoType.getSymbol() + formatNumber(amount, grouping);
            } else {
                return formatNumber(amount, grouping) + " " + cryptoType.name();
            }
        }
        
        if (includeSymbol) {
            return currency.getSymbol() + formatNumber(amount, grouping);
        } else {
            return formatNumber(amount, grouping);
        }
    }

    /**
     * 格式化为代码格式
     */
    private String formatWithCode(BigDecimal amount, CurrencyType currency, FieldConfig config) {
        boolean grouping = getBooleanParam(config, "grouping", true);
        
        if (currency == CurrencyType.CRYPTO) {
            String cryptoTypeStr = getStringParam(config, "crypto_type", "BTC");
            CryptoType cryptoType = parseCryptoType(cryptoTypeStr);
            return formatNumber(amount, grouping) + " " + cryptoType.name();
        }
        
        return formatNumber(amount, grouping) + " " + currency.name();
    }

    /**
     * 格式化为完整格式
     */
    private String formatFull(BigDecimal amount, CurrencyType currency, FieldConfig config) {
        boolean grouping = getBooleanParam(config, "grouping", true);
        
        if (currency == CurrencyType.CRYPTO) {
            String cryptoTypeStr = getStringParam(config, "crypto_type", "BTC");
            CryptoType cryptoType = parseCryptoType(cryptoTypeStr);
            return cryptoType.getSymbol() + formatNumber(amount, grouping) + " " + 
                   cryptoType.name() + " (" + cryptoType.getDescription() + ")";
        }
        
        return currency.getSymbol() + formatNumber(amount, grouping) + " " + 
               currency.name() + " (" + currency.getDescription() + ")";
    }

    /**
     * 格式化为纯数字格式
     */
    private String formatPlain(BigDecimal amount, FieldConfig config) {
        boolean grouping = getBooleanParam(config, "grouping", false);
        return formatNumber(amount, grouping);
    }

    /**
     * 格式化为JSON格式
     */
    private String formatAsJson(BigDecimal amount, CurrencyType currency, FieldConfig config) {
        StringBuilder json = new StringBuilder("{");
        json.append("\"amount\":").append(amount.toPlainString()).append(",");
        
        if (currency == CurrencyType.CRYPTO) {
            String cryptoTypeStr = getStringParam(config, "crypto_type", "BTC");
            CryptoType cryptoType = parseCryptoType(cryptoTypeStr);
            json.append("\"currency\":\"").append(cryptoType.name()).append("\",");
            json.append("\"symbol\":\"").append(cryptoType.getSymbol()).append("\",");
            json.append("\"type\":\"crypto\",");
            json.append("\"description\":\"").append(cryptoType.getDescription()).append("\"");
        } else {
            json.append("\"currency\":\"").append(currency.name()).append("\",");
            json.append("\"symbol\":\"").append(currency.getSymbol()).append("\",");
            json.append("\"type\":\"fiat\",");
            json.append("\"description\":\"").append(currency.getDescription()).append("\"");
        }
        
        json.append("}");
        return json.toString();
    }

    /**
     * 格式化数字
     */
    private String formatNumber(BigDecimal amount, boolean grouping) {
        if (grouping) {
            NumberFormat formatter = NumberFormat.getNumberInstance();
            formatter.setMaximumFractionDigits(amount.scale());
            formatter.setMinimumFractionDigits(amount.scale());
            return formatter.format(amount);
        } else {
            return amount.toPlainString();
        }
    }
}
