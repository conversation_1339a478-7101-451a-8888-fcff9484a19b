<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MimeTypeGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">MimeTypeGenerator.java</span></div><h1>MimeTypeGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * MIME类型生成器
 * 
 * 支持的参数：
 * - category: MIME类型类别 (TEXT|IMAGE|APPLICATION|AUDIO|VIDEO|ANY)
 * - subtype: 子类型 (指定具体子类型或随机)
 * - charset: 字符集 (仅对text类型有效)
 * - include_parameters: 是否包含参数 (true|false)
 * - custom_types: 自定义MIME类型列表
 * 
 * <AUTHOR>
 */
<span class="nc" id="L23">public class MimeTypeGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(MimeTypeGenerator.class);</span>
<span class="nc" id="L26">    private static final Random random = new Random();</span>

    // MIME类型类别枚举
<span class="nc" id="L29">    private enum MimeCategory {</span>
<span class="nc" id="L30">        TEXT, IMAGE, APPLICATION, AUDIO, VIDEO, MULTIPART, MESSAGE, MODEL</span>
    }

    // MIME类型映射
<span class="nc" id="L34">    private static final Map&lt;MimeCategory, List&lt;String&gt;&gt; MIME_TYPES = new HashMap&lt;&gt;();</span>

    // 常见字符集
<span class="nc" id="L37">    private static final List&lt;String&gt; CHARSETS = Arrays.asList(</span>
            &quot;UTF-8&quot;, &quot;UTF-16&quot;, &quot;UTF-32&quot;, &quot;ISO-8859-1&quot;, &quot;ISO-8859-15&quot;, &quot;Windows-1252&quot;,
            &quot;ASCII&quot;, &quot;US-ASCII&quot;, &quot;GB2312&quot;, &quot;GBK&quot;, &quot;GB18030&quot;, &quot;Big5&quot;, &quot;Shift_JIS&quot;, &quot;EUC-JP&quot;);

    // 常见参数
<span class="nc" id="L42">    private static final Map&lt;String, List&lt;String&gt;&gt; MIME_PARAMETERS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L45">        initializeMimeTypes();</span>
<span class="nc" id="L46">        initializeMimeParameters();</span>
<span class="nc" id="L47">    }</span>

    private static void initializeMimeTypes() {
        // Text类型
<span class="nc" id="L51">        MIME_TYPES.put(MimeCategory.TEXT, Arrays.asList(</span>
                &quot;plain&quot;, &quot;html&quot;, &quot;css&quot;, &quot;javascript&quot;, &quot;xml&quot;, &quot;json&quot;, &quot;csv&quot;, &quot;markdown&quot;,
                &quot;rtf&quot;, &quot;calendar&quot;, &quot;vcard&quot;, &quot;yaml&quot;, &quot;x-python&quot;, &quot;x-java-source&quot;, &quot;x-c&quot;,
                &quot;x-shellscript&quot;, &quot;x-sql&quot;, &quot;x-log&quot;, &quot;x-diff&quot;, &quot;x-patch&quot;));

        // Image类型
<span class="nc" id="L57">        MIME_TYPES.put(MimeCategory.IMAGE, Arrays.asList(</span>
                &quot;jpeg&quot;, &quot;png&quot;, &quot;gif&quot;, &quot;webp&quot;, &quot;svg+xml&quot;, &quot;bmp&quot;, &quot;tiff&quot;, &quot;x-icon&quot;,
                &quot;x-ms-bmp&quot;, &quot;vnd.adobe.photoshop&quot;, &quot;x-portable-pixmap&quot;, &quot;x-portable-graymap&quot;,
                &quot;x-portable-bitmap&quot;, &quot;x-xbitmap&quot;, &quot;x-xpixmap&quot;, &quot;avif&quot;, &quot;heic&quot;, &quot;heif&quot;));

        // Application类型
<span class="nc" id="L63">        MIME_TYPES.put(MimeCategory.APPLICATION, Arrays.asList(</span>
                &quot;json&quot;, &quot;xml&quot;, &quot;pdf&quot;, &quot;zip&quot;, &quot;gzip&quot;, &quot;x-tar&quot;, &quot;x-rar-compressed&quot;,
                &quot;x-7z-compressed&quot;, &quot;octet-stream&quot;, &quot;x-executable&quot;, &quot;x-sharedlib&quot;,
                &quot;x-msdownload&quot;, &quot;vnd.ms-excel&quot;, &quot;vnd.openxmlformats-officedocument.spreadsheetml.sheet&quot;,
                &quot;vnd.ms-powerpoint&quot;, &quot;vnd.openxmlformats-officedocument.presentationml.presentation&quot;,
                &quot;msword&quot;, &quot;vnd.openxmlformats-officedocument.wordprocessingml.document&quot;,
                &quot;x-www-form-urlencoded&quot;, &quot;x-javascript&quot;, &quot;x-httpd-php&quot;, &quot;x-python-code&quot;,
                &quot;java-archive&quot;, &quot;x-java-serialized-object&quot;, &quot;x-java-vm&quot;));

        // Audio类型
<span class="nc" id="L73">        MIME_TYPES.put(MimeCategory.AUDIO, Arrays.asList(</span>
                &quot;mpeg&quot;, &quot;wav&quot;, &quot;x-wav&quot;, &quot;ogg&quot;, &quot;flac&quot;, &quot;aac&quot;, &quot;x-ms-wma&quot;, &quot;x-m4a&quot;,
                &quot;webm&quot;, &quot;x-aiff&quot;, &quot;x-au&quot;, &quot;x-pn-realaudio&quot;, &quot;x-pn-realaudio-plugin&quot;,
                &quot;vnd.rn-realaudio&quot;, &quot;x-realaudio&quot;, &quot;basic&quot;, &quot;midi&quot;, &quot;x-midi&quot;));

        // Video类型
<span class="nc" id="L79">        MIME_TYPES.put(MimeCategory.VIDEO, Arrays.asList(</span>
                &quot;mp4&quot;, &quot;mpeg&quot;, &quot;quicktime&quot;, &quot;x-msvideo&quot;, &quot;x-ms-wmv&quot;, &quot;webm&quot;, &quot;ogg&quot;,
                &quot;x-flv&quot;, &quot;3gpp&quot;, &quot;x-matroska&quot;, &quot;x-ms-asf&quot;, &quot;x-dv&quot;, &quot;x-sgi-movie&quot;,
                &quot;vnd.rn-realvideo&quot;, &quot;x-pn-realvideo&quot;, &quot;x-pn-realvideo-plugin&quot;));

        // Multipart类型
<span class="nc" id="L85">        MIME_TYPES.put(MimeCategory.MULTIPART, Arrays.asList(</span>
                &quot;form-data&quot;, &quot;byteranges&quot;, &quot;alternative&quot;, &quot;digest&quot;, &quot;parallel&quot;,
                &quot;related&quot;, &quot;report&quot;, &quot;signed&quot;, &quot;encrypted&quot;, &quot;mixed&quot;));

        // Message类型
<span class="nc" id="L90">        MIME_TYPES.put(MimeCategory.MESSAGE, Arrays.asList(</span>
                &quot;rfc822&quot;, &quot;partial&quot;, &quot;external-body&quot;, &quot;news&quot;, &quot;http&quot;, &quot;s-http&quot;,
                &quot;imdn+xml&quot;, &quot;tracking-status&quot;, &quot;global&quot;, &quot;global-headers&quot;,
                &quot;global-disposition-notification&quot;, &quot;global-delivery-status&quot;));

        // Model类型
<span class="nc" id="L96">        MIME_TYPES.put(MimeCategory.MODEL, Arrays.asList(</span>
                &quot;iges&quot;, &quot;mesh&quot;, &quot;vrml&quot;, &quot;x3d+binary&quot;, &quot;x3d+fastinfoset&quot;, &quot;x3d+vrml&quot;,
                &quot;x3d+xml&quot;, &quot;x3d-vrml&quot;, &quot;gltf+json&quot;, &quot;gltf-binary&quot;, &quot;stl&quot;, &quot;obj&quot;));
<span class="nc" id="L99">    }</span>

    private static void initializeMimeParameters() {
        // Text类型参数
<span class="nc" id="L103">        MIME_PARAMETERS.put(&quot;text&quot;, Arrays.asList(&quot;charset&quot;, &quot;boundary&quot;, &quot;format&quot;));</span>

        // Application类型参数
<span class="nc" id="L106">        MIME_PARAMETERS.put(&quot;application&quot;, Arrays.asList(&quot;charset&quot;, &quot;boundary&quot;, &quot;name&quot;, &quot;filename&quot;));</span>

        // Multipart类型参数
<span class="nc" id="L109">        MIME_PARAMETERS.put(&quot;multipart&quot;, Arrays.asList(&quot;boundary&quot;, &quot;start&quot;, &quot;start-info&quot;, &quot;type&quot;));</span>

        // Message类型参数
<span class="nc" id="L112">        MIME_PARAMETERS.put(&quot;message&quot;, Arrays.asList(&quot;boundary&quot;, &quot;charset&quot;));</span>
<span class="nc" id="L113">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L117">        return &quot;mimetype&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L122">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L129">            String category = config.getParam(&quot;category&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L130">            String subtype = config.getParam(&quot;subtype&quot;, String.class, null);</span>
<span class="nc" id="L131">            String charset = config.getParam(&quot;charset&quot;, String.class, null);</span>
<span class="nc" id="L132">            boolean includeParameters = Boolean</span>
<span class="nc" id="L133">                    .parseBoolean(config.getParam(&quot;include_parameters&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L134">            String customTypes = config.getParam(&quot;custom_types&quot;, String.class, null);</span>

            // 生成MIME类型
<span class="nc" id="L137">            String mimeType = generateMimeType(category, subtype, charset, includeParameters, customTypes);</span>

            // 将MIME类型信息存入上下文
<span class="nc" id="L140">            context.put(&quot;mime_type&quot;, mimeType);</span>
<span class="nc" id="L141">            context.put(&quot;mime_category&quot;, extractCategory(mimeType));</span>
<span class="nc" id="L142">            context.put(&quot;mime_subtype&quot;, extractSubtype(mimeType));</span>

<span class="nc" id="L144">            logger.debug(&quot;Generated MIME type: {}&quot;, mimeType);</span>
<span class="nc" id="L145">            return mimeType;</span>

<span class="nc" id="L147">        } catch (Exception e) {</span>
<span class="nc" id="L148">            logger.error(&quot;Error generating MIME type&quot;, e);</span>
<span class="nc" id="L149">            return &quot;text/plain&quot;;</span>
        }
    }

    private String generateMimeType(String category, String subtype, String charset,
            boolean includeParameters, String customTypes) {

        // 处理自定义类型
<span class="nc bnc" id="L157" title="All 4 branches missed.">        if (customTypes != null &amp;&amp; !customTypes.isEmpty()) {</span>
<span class="nc" id="L158">            String[] types = customTypes.split(&quot;,&quot;);</span>
<span class="nc" id="L159">            return types[random.nextInt(types.length)].trim();</span>
        }

        // 确定类别
<span class="nc" id="L163">        MimeCategory mimeCategory = determineMimeCategory(category);</span>

        // 确定子类型
<span class="nc bnc" id="L166" title="All 2 branches missed.">        String finalSubtype = subtype != null ? subtype : selectRandomSubtype(mimeCategory);</span>

        // 构建基本MIME类型
<span class="nc" id="L169">        String baseMimeType = mimeCategory.name().toLowerCase() + &quot;/&quot; + finalSubtype;</span>

        // 添加参数
<span class="nc bnc" id="L172" title="All 2 branches missed.">        if (includeParameters) {</span>
<span class="nc" id="L173">            String parameters = generateParameters(mimeCategory, charset);</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">            if (!parameters.isEmpty()) {</span>
<span class="nc" id="L175">                baseMimeType += &quot;; &quot; + parameters;</span>
            }
        }

<span class="nc" id="L179">        return baseMimeType;</span>
    }

    private MimeCategory determineMimeCategory(String category) {
<span class="nc bnc" id="L183" title="All 2 branches missed.">        if (&quot;ANY&quot;.equalsIgnoreCase(category)) {</span>
<span class="nc" id="L184">            MimeCategory[] categories = MimeCategory.values();</span>
<span class="nc" id="L185">            return categories[random.nextInt(categories.length)];</span>
        }

        try {
<span class="nc" id="L189">            return MimeCategory.valueOf(category.toUpperCase());</span>
<span class="nc" id="L190">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L191">            logger.warn(&quot;Unknown MIME category: {}. Using TEXT.&quot;, category);</span>
<span class="nc" id="L192">            return MimeCategory.TEXT;</span>
        }
    }

    private String selectRandomSubtype(MimeCategory category) {
<span class="nc" id="L197">        List&lt;String&gt; subtypes = MIME_TYPES.get(category);</span>
<span class="nc bnc" id="L198" title="All 4 branches missed.">        if (subtypes == null || subtypes.isEmpty()) {</span>
<span class="nc" id="L199">            return &quot;plain&quot;;</span>
        }
<span class="nc" id="L201">        return subtypes.get(random.nextInt(subtypes.size()));</span>
    }

    private String generateParameters(MimeCategory category, String charset) {
<span class="nc" id="L205">        List&lt;String&gt; parameters = new ArrayList&lt;&gt;();</span>

        // 添加字符集参数（主要用于text类型）
<span class="nc bnc" id="L208" title="All 4 branches missed.">        if (category == MimeCategory.TEXT || category == MimeCategory.APPLICATION) {</span>
<span class="nc bnc" id="L209" title="All 2 branches missed.">            String finalCharset = charset != null ? charset : selectRandomCharset();</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">            if (random.nextDouble() &lt; 0.7) { // 70%概率添加charset</span>
<span class="nc" id="L211">                parameters.add(&quot;charset=&quot; + finalCharset);</span>
            }
        }

        // 添加其他参数
<span class="nc" id="L216">        List&lt;String&gt; availableParams = MIME_PARAMETERS.get(category.name().toLowerCase());</span>
<span class="nc bnc" id="L217" title="All 4 branches missed.">        if (availableParams != null &amp;&amp; random.nextDouble() &lt; 0.3) { // 30%概率添加其他参数</span>
<span class="nc" id="L218">            String param = availableParams.get(random.nextInt(availableParams.size()));</span>

<span class="nc bnc" id="L220" title="All 2 branches missed.">            if (!&quot;charset&quot;.equals(param)) { // 避免重复添加charset</span>
<span class="nc" id="L221">                String value = generateParameterValue(param);</span>
<span class="nc" id="L222">                parameters.add(param + &quot;=&quot; + value);</span>
            }
        }

<span class="nc" id="L226">        return String.join(&quot;; &quot;, parameters);</span>
    }

    private String selectRandomCharset() {
<span class="nc" id="L230">        return CHARSETS.get(random.nextInt(CHARSETS.size()));</span>
    }

    private String generateParameterValue(String parameter) {
<span class="nc bnc" id="L234" title="All 7 branches missed.">        switch (parameter) {</span>
            case &quot;boundary&quot;:
<span class="nc" id="L236">                return &quot;----WebKitFormBoundary&quot; + generateRandomString(16);</span>

            case &quot;name&quot;:
            case &quot;filename&quot;:
<span class="nc" id="L240">                return &quot;\&quot;file&quot; + random.nextInt(1000) + &quot;.dat\&quot;&quot;;</span>

            case &quot;format&quot;:
<span class="nc bnc" id="L243" title="All 2 branches missed.">                return random.nextBoolean() ? &quot;fixed&quot; : &quot;flowed&quot;;</span>

            case &quot;start&quot;:
<span class="nc" id="L246">                return &quot;&lt;<EMAIL>&gt;&quot;;</span>

            case &quot;start-info&quot;:
<span class="nc" id="L249">                return &quot;text/html&quot;;</span>

            case &quot;type&quot;:
<span class="nc" id="L252">                return &quot;text/html&quot;;</span>

            default:
<span class="nc" id="L255">                return &quot;value&quot; + random.nextInt(100);</span>
        }
    }

    private String generateRandomString(int length) {
<span class="nc" id="L260">        String chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;;</span>
<span class="nc" id="L261">        StringBuilder result = new StringBuilder();</span>

<span class="nc bnc" id="L263" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L264">            result.append(chars.charAt(random.nextInt(chars.length())));</span>
        }

<span class="nc" id="L267">        return result.toString();</span>
    }

    private String extractCategory(String mimeType) {
<span class="nc" id="L271">        int slashIndex = mimeType.indexOf('/');</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">        if (slashIndex &gt; 0) {</span>
<span class="nc" id="L273">            return mimeType.substring(0, slashIndex);</span>
        }
<span class="nc" id="L275">        return &quot;unknown&quot;;</span>
    }

    private String extractSubtype(String mimeType) {
<span class="nc" id="L279">        int slashIndex = mimeType.indexOf('/');</span>
<span class="nc" id="L280">        int semicolonIndex = mimeType.indexOf(';');</span>

<span class="nc bnc" id="L282" title="All 2 branches missed.">        if (slashIndex &gt; 0) {</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">            int endIndex = semicolonIndex &gt; slashIndex ? semicolonIndex : mimeType.length();</span>
<span class="nc" id="L284">            return mimeType.substring(slashIndex + 1, endIndex);</span>
        }

<span class="nc" id="L287">        return &quot;unknown&quot;;</span>
    }

    /**
     * 根据文件扩展名生成对应的MIME类型
     */
    public String generateMimeTypeByExtension(String extension) {
<span class="nc bnc" id="L294" title="All 4 branches missed.">        if (extension == null || extension.isEmpty()) {</span>
<span class="nc" id="L295">            return &quot;application/octet-stream&quot;;</span>
        }

<span class="nc" id="L298">        extension = extension.toLowerCase();</span>

        // 常见扩展名到MIME类型的映射
<span class="nc" id="L301">        Map&lt;String, String&gt; extensionMimeMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L302">        extensionMimeMap.put(&quot;txt&quot;, &quot;text/plain&quot;);</span>
<span class="nc" id="L303">        extensionMimeMap.put(&quot;html&quot;, &quot;text/html&quot;);</span>
<span class="nc" id="L304">        extensionMimeMap.put(&quot;css&quot;, &quot;text/css&quot;);</span>
<span class="nc" id="L305">        extensionMimeMap.put(&quot;js&quot;, &quot;text/javascript&quot;);</span>
<span class="nc" id="L306">        extensionMimeMap.put(&quot;json&quot;, &quot;application/json&quot;);</span>
<span class="nc" id="L307">        extensionMimeMap.put(&quot;xml&quot;, &quot;application/xml&quot;);</span>
<span class="nc" id="L308">        extensionMimeMap.put(&quot;pdf&quot;, &quot;application/pdf&quot;);</span>
<span class="nc" id="L309">        extensionMimeMap.put(&quot;zip&quot;, &quot;application/zip&quot;);</span>
<span class="nc" id="L310">        extensionMimeMap.put(&quot;jpg&quot;, &quot;image/jpeg&quot;);</span>
<span class="nc" id="L311">        extensionMimeMap.put(&quot;jpeg&quot;, &quot;image/jpeg&quot;);</span>
<span class="nc" id="L312">        extensionMimeMap.put(&quot;png&quot;, &quot;image/png&quot;);</span>
<span class="nc" id="L313">        extensionMimeMap.put(&quot;gif&quot;, &quot;image/gif&quot;);</span>
<span class="nc" id="L314">        extensionMimeMap.put(&quot;svg&quot;, &quot;image/svg+xml&quot;);</span>
<span class="nc" id="L315">        extensionMimeMap.put(&quot;mp3&quot;, &quot;audio/mpeg&quot;);</span>
<span class="nc" id="L316">        extensionMimeMap.put(&quot;wav&quot;, &quot;audio/wav&quot;);</span>
<span class="nc" id="L317">        extensionMimeMap.put(&quot;mp4&quot;, &quot;video/mp4&quot;);</span>
<span class="nc" id="L318">        extensionMimeMap.put(&quot;avi&quot;, &quot;video/x-msvideo&quot;);</span>

<span class="nc" id="L320">        return extensionMimeMap.getOrDefault(extension, &quot;application/octet-stream&quot;);</span>
    }

    /**
     * 验证MIME类型格式
     */
    public boolean validateMimeType(String mimeType) {
<span class="nc bnc" id="L327" title="All 4 branches missed.">        if (mimeType == null || mimeType.isEmpty()) {</span>
<span class="nc" id="L328">            return false;</span>
        }

        // 基本格式检查：type/subtype
<span class="nc" id="L332">        String[] parts = mimeType.split(&quot;;&quot;)[0].split(&quot;/&quot;);</span>
<span class="nc bnc" id="L333" title="All 2 branches missed.">        if (parts.length != 2) {</span>
<span class="nc" id="L334">            return false;</span>
        }

<span class="nc" id="L337">        String type = parts[0].trim();</span>
<span class="nc" id="L338">        String subtype = parts[1].trim();</span>

        // 检查是否为空
<span class="nc bnc" id="L341" title="All 4 branches missed.">        if (type.isEmpty() || subtype.isEmpty()) {</span>
<span class="nc" id="L342">            return false;</span>
        }

        // 检查字符是否合法（简化版本）
<span class="nc bnc" id="L346" title="All 2 branches missed.">        return type.matches(&quot;[a-zA-Z][a-zA-Z0-9]*[a-zA-Z0-9\\-]*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L347" title="All 2 branches missed.">                subtype.matches(&quot;[a-zA-Z0-9][a-zA-Z0-9\\-+.]*&quot;);</span>
    }

    /**
     * 生成Web安全的MIME类型（排除可执行类型）
     */
    public String generateWebSafeMimeType() {
<span class="nc" id="L354">        MimeCategory[] safeCategories = { MimeCategory.TEXT, MimeCategory.IMAGE, MimeCategory.AUDIO,</span>
                MimeCategory.VIDEO };
<span class="nc" id="L356">        MimeCategory category = safeCategories[random.nextInt(safeCategories.length)];</span>

<span class="nc" id="L358">        String subtype = selectRandomSubtype(category);</span>
<span class="nc" id="L359">        return category.name().toLowerCase() + &quot;/&quot; + subtype;</span>
    }

    /**
     * 生成恶意MIME类型（用于安全测试）
     */
    public String generateMaliciousMimeType() {
<span class="nc" id="L366">        String[] maliciousTypes = {</span>
                &quot;application/x-executable&quot;,
                &quot;application/x-msdownload&quot;,
                &quot;application/x-msdos-program&quot;,
                &quot;application/x-winexe&quot;,
                &quot;application/x-javascript&quot;,
                &quot;text/javascript&quot;,
                &quot;application/javascript&quot;,
                &quot;text/html&quot;,
                &quot;application/x-httpd-php&quot;
        };

<span class="nc" id="L378">        return maliciousTypes[random.nextInt(maliciousTypes.length)];</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>