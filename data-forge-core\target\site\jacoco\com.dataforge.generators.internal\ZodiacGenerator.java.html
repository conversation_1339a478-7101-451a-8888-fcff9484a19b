<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ZodiacGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">ZodiacGenerator.java</span></div><h1>ZodiacGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * 星座生成器
 * 
 * 支持的参数：
 * - sign: 指定星座
 * (ARIES|TAURUS|GEMINI|CANCER|LEO|VIRGO|LIBRA|SCORPIO|SAGITTARIUS|CAPRICORN|AQUARIUS|PISCES|ANY)
 * - birth_date_related: 是否与出生日期关联 (true|false)
 * - format: 输出格式 (CHINESE|ENGLISH|SYMBOL|CODE)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L25">public class ZodiacGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(ZodiacGenerator.class);</span>
<span class="nc" id="L28">    private static final Random random = new Random();</span>

    // 星座枚举
<span class="nc" id="L31">    private enum ZodiacSign {</span>
<span class="nc" id="L32">        ARIES, // 白羊座</span>
<span class="nc" id="L33">        TAURUS, // 金牛座</span>
<span class="nc" id="L34">        GEMINI, // 双子座</span>
<span class="nc" id="L35">        CANCER, // 巨蟹座</span>
<span class="nc" id="L36">        LEO, // 狮子座</span>
<span class="nc" id="L37">        VIRGO, // 处女座</span>
<span class="nc" id="L38">        LIBRA, // 天秤座</span>
<span class="nc" id="L39">        SCORPIO, // 天蝎座</span>
<span class="nc" id="L40">        SAGITTARIUS, // 射手座</span>
<span class="nc" id="L41">        CAPRICORN, // 摩羯座</span>
<span class="nc" id="L42">        AQUARIUS, // 水瓶座</span>
<span class="nc" id="L43">        PISCES // 双鱼座</span>
    }

    // 星座信息类
    private static class ZodiacInfo {
        final ZodiacSign sign;
        final String chineseName;
        final String englishName;
        final String symbol;
        final String code;
        final int startMonth;
        final int startDay;
        final int endMonth;
        final int endDay;

        ZodiacInfo(ZodiacSign sign, String chineseName, String englishName, String symbol, String code,
<span class="nc" id="L59">                int startMonth, int startDay, int endMonth, int endDay) {</span>
<span class="nc" id="L60">            this.sign = sign;</span>
<span class="nc" id="L61">            this.chineseName = chineseName;</span>
<span class="nc" id="L62">            this.englishName = englishName;</span>
<span class="nc" id="L63">            this.symbol = symbol;</span>
<span class="nc" id="L64">            this.code = code;</span>
<span class="nc" id="L65">            this.startMonth = startMonth;</span>
<span class="nc" id="L66">            this.startDay = startDay;</span>
<span class="nc" id="L67">            this.endMonth = endMonth;</span>
<span class="nc" id="L68">            this.endDay = endDay;</span>
<span class="nc" id="L69">        }</span>
    }

    // 星座信息映射
<span class="nc" id="L73">    private static final Map&lt;ZodiacSign, ZodiacInfo&gt; ZODIAC_INFO = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L76">        initializeZodiacInfo();</span>
<span class="nc" id="L77">    }</span>

    private static void initializeZodiacInfo() {
<span class="nc" id="L80">        ZODIAC_INFO.put(ZodiacSign.ARIES, new ZodiacInfo(</span>
                ZodiacSign.ARIES, &quot;白羊座&quot;, &quot;Aries&quot;, &quot;♈&quot;, &quot;ARI&quot;, 3, 21, 4, 19));
<span class="nc" id="L82">        ZODIAC_INFO.put(ZodiacSign.TAURUS, new ZodiacInfo(</span>
                ZodiacSign.TAURUS, &quot;金牛座&quot;, &quot;Taurus&quot;, &quot;♉&quot;, &quot;TAU&quot;, 4, 20, 5, 20));
<span class="nc" id="L84">        ZODIAC_INFO.put(ZodiacSign.GEMINI, new ZodiacInfo(</span>
                ZodiacSign.GEMINI, &quot;双子座&quot;, &quot;Gemini&quot;, &quot;♊&quot;, &quot;GEM&quot;, 5, 21, 6, 21));
<span class="nc" id="L86">        ZODIAC_INFO.put(ZodiacSign.CANCER, new ZodiacInfo(</span>
                ZodiacSign.CANCER, &quot;巨蟹座&quot;, &quot;Cancer&quot;, &quot;♋&quot;, &quot;CAN&quot;, 6, 22, 7, 22));
<span class="nc" id="L88">        ZODIAC_INFO.put(ZodiacSign.LEO, new ZodiacInfo(</span>
                ZodiacSign.LEO, &quot;狮子座&quot;, &quot;Leo&quot;, &quot;♌&quot;, &quot;LEO&quot;, 7, 23, 8, 22));
<span class="nc" id="L90">        ZODIAC_INFO.put(ZodiacSign.VIRGO, new ZodiacInfo(</span>
                ZodiacSign.VIRGO, &quot;处女座&quot;, &quot;Virgo&quot;, &quot;♍&quot;, &quot;VIR&quot;, 8, 23, 9, 22));
<span class="nc" id="L92">        ZODIAC_INFO.put(ZodiacSign.LIBRA, new ZodiacInfo(</span>
                ZodiacSign.LIBRA, &quot;天秤座&quot;, &quot;Libra&quot;, &quot;♎&quot;, &quot;LIB&quot;, 9, 23, 10, 23));
<span class="nc" id="L94">        ZODIAC_INFO.put(ZodiacSign.SCORPIO, new ZodiacInfo(</span>
                ZodiacSign.SCORPIO, &quot;天蝎座&quot;, &quot;Scorpio&quot;, &quot;♏&quot;, &quot;SCO&quot;, 10, 24, 11, 22));
<span class="nc" id="L96">        ZODIAC_INFO.put(ZodiacSign.SAGITTARIUS, new ZodiacInfo(</span>
                ZodiacSign.SAGITTARIUS, &quot;射手座&quot;, &quot;Sagittarius&quot;, &quot;♐&quot;, &quot;SAG&quot;, 11, 23, 12, 21));
<span class="nc" id="L98">        ZODIAC_INFO.put(ZodiacSign.CAPRICORN, new ZodiacInfo(</span>
                ZodiacSign.CAPRICORN, &quot;摩羯座&quot;, &quot;Capricorn&quot;, &quot;♑&quot;, &quot;CAP&quot;, 12, 22, 1, 19));
<span class="nc" id="L100">        ZODIAC_INFO.put(ZodiacSign.AQUARIUS, new ZodiacInfo(</span>
                ZodiacSign.AQUARIUS, &quot;水瓶座&quot;, &quot;Aquarius&quot;, &quot;♒&quot;, &quot;AQU&quot;, 1, 20, 2, 18));
<span class="nc" id="L102">        ZODIAC_INFO.put(ZodiacSign.PISCES, new ZodiacInfo(</span>
                ZodiacSign.PISCES, &quot;双鱼座&quot;, &quot;Pisces&quot;, &quot;♓&quot;, &quot;PIS&quot;, 2, 19, 3, 20));
<span class="nc" id="L104">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L108">        return &quot;zodiac&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L113">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L120">            String sign = config.getParam(&quot;sign&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L121">            boolean birthDateRelated = Boolean</span>
<span class="nc" id="L122">                    .parseBoolean(config.getParam(&quot;birth_date_related&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L123">            String format = config.getParam(&quot;format&quot;, String.class, &quot;CHINESE&quot;);</span>

            ZodiacSign zodiacSign;

            // 如果启用出生日期关联，尝试从上下文获取出生日期
<span class="nc bnc" id="L128" title="All 2 branches missed.">            if (birthDateRelated) {</span>
<span class="nc" id="L129">                zodiacSign = getZodiacFromBirthDate(context, sign);</span>
            } else {
<span class="nc" id="L131">                zodiacSign = getSpecifiedZodiac(sign);</span>
            }

            // 将星座信息存入上下文
<span class="nc" id="L135">            context.put(&quot;zodiac_sign&quot;, zodiacSign.name());</span>

            // 格式化输出
<span class="nc" id="L138">            String result = formatZodiac(zodiacSign, format);</span>

<span class="nc" id="L140">            logger.debug(&quot;Generated zodiac sign: {}&quot;, result);</span>
<span class="nc" id="L141">            return result;</span>

<span class="nc" id="L143">        } catch (Exception e) {</span>
<span class="nc" id="L144">            logger.error(&quot;Error generating zodiac sign&quot;, e);</span>
<span class="nc" id="L145">            return &quot;白羊座&quot;;</span>
        }
    }

    private ZodiacSign getZodiacFromBirthDate(DataForgeContext context, String sign) {
        // 尝试从上下文获取出生日期
<span class="nc" id="L151">        String birthDateStr = context.get(&quot;birth_date&quot;, String.class).orElse(null);</span>

<span class="nc bnc" id="L153" title="All 2 branches missed.">        if (birthDateStr != null) {</span>
            try {
<span class="nc" id="L155">                LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd&quot;));</span>
<span class="nc" id="L156">                ZodiacSign calculatedSign = calculateZodiacSign(birthDate);</span>

                // 如果指定了特定星座，检查是否匹配
<span class="nc bnc" id="L159" title="All 2 branches missed.">                if (!&quot;ANY&quot;.equals(sign)) {</span>
                    try {
<span class="nc" id="L161">                        ZodiacSign specifiedSign = ZodiacSign.valueOf(sign);</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">                        if (calculatedSign == specifiedSign) {</span>
<span class="nc" id="L163">                            return calculatedSign;</span>
                        } else {
<span class="nc" id="L165">                            logger.debug(</span>
                                    &quot;Birth date {} corresponds to {}, but {} was specified. Using calculated sign.&quot;,
                                    birthDateStr, calculatedSign, specifiedSign);
<span class="nc" id="L168">                            return calculatedSign;</span>
                        }
<span class="nc" id="L170">                    } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L171">                        logger.warn(&quot;Unknown zodiac sign: {}. Using calculated sign from birth date.&quot;, sign);</span>
<span class="nc" id="L172">                        return calculatedSign;</span>
                    }
                }

<span class="nc" id="L176">                return calculatedSign;</span>

<span class="nc" id="L178">            } catch (DateTimeParseException e) {</span>
<span class="nc" id="L179">                logger.warn(&quot;Failed to parse birth date: {}. Using random zodiac sign.&quot;, birthDateStr);</span>
            }
        }

        // 如果没有出生日期或解析失败，使用指定的星座或随机选择
<span class="nc" id="L184">        return getSpecifiedZodiac(sign);</span>
    }

    private ZodiacSign getSpecifiedZodiac(String sign) {
<span class="nc bnc" id="L188" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(sign)) {</span>
<span class="nc" id="L189">            ZodiacSign[] signs = ZodiacSign.values();</span>
<span class="nc" id="L190">            return signs[random.nextInt(signs.length)];</span>
        } else {
            try {
<span class="nc" id="L193">                return ZodiacSign.valueOf(sign);</span>
<span class="nc" id="L194">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L195">                logger.warn(&quot;Unknown zodiac sign: {}. Using random sign.&quot;, sign);</span>
<span class="nc" id="L196">                ZodiacSign[] signs = ZodiacSign.values();</span>
<span class="nc" id="L197">                return signs[random.nextInt(signs.length)];</span>
            }
        }
    }

    private ZodiacSign calculateZodiacSign(LocalDate birthDate) {
<span class="nc" id="L203">        int month = birthDate.getMonthValue();</span>
<span class="nc" id="L204">        int day = birthDate.getDayOfMonth();</span>

<span class="nc bnc" id="L206" title="All 2 branches missed.">        for (ZodiacInfo info : ZODIAC_INFO.values()) {</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">            if (isDateInZodiacRange(month, day, info)) {</span>
<span class="nc" id="L208">                return info.sign;</span>
            }
<span class="nc" id="L210">        }</span>

        // 默认返回白羊座（理论上不应该到达这里）
<span class="nc" id="L213">        return ZodiacSign.ARIES;</span>
    }

    private boolean isDateInZodiacRange(int month, int day, ZodiacInfo info) {
        // 处理跨年的星座（摩羯座、水瓶座、双鱼座）
<span class="nc bnc" id="L218" title="All 2 branches missed.">        if (info.startMonth &gt; info.endMonth) {</span>
            // 跨年星座
<span class="nc bnc" id="L220" title="All 12 branches missed.">            return (month == info.startMonth &amp;&amp; day &gt;= info.startDay) ||</span>
                    (month == info.endMonth &amp;&amp; day &lt;= info.endDay) ||
                    (month &gt; info.startMonth || month &lt; info.endMonth);
        } else {
            // 同年星座
<span class="nc bnc" id="L225" title="All 12 branches missed.">            return (month == info.startMonth &amp;&amp; day &gt;= info.startDay) ||</span>
                    (month == info.endMonth &amp;&amp; day &lt;= info.endDay) ||
                    (month &gt; info.startMonth &amp;&amp; month &lt; info.endMonth);
        }
    }

    private String formatZodiac(ZodiacSign sign, String format) {
<span class="nc" id="L232">        ZodiacInfo info = ZODIAC_INFO.get(sign);</span>

<span class="nc bnc" id="L234" title="All 5 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;CHINESE&quot;:
            case &quot;CN&quot;:
<span class="nc" id="L237">                return info.chineseName;</span>

            case &quot;ENGLISH&quot;:
            case &quot;EN&quot;:
<span class="nc" id="L241">                return info.englishName;</span>

            case &quot;SYMBOL&quot;:
<span class="nc" id="L244">                return info.symbol;</span>

            case &quot;CODE&quot;:
<span class="nc" id="L247">                return info.code;</span>

            default:
<span class="nc" id="L250">                logger.warn(&quot;Unknown zodiac format: {}. Using CHINESE format.&quot;, format);</span>
<span class="nc" id="L251">                return info.chineseName;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>