<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.dataforge.validation</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <span class="el_package">com.dataforge.validation</span></div><h1>com.dataforge.validation</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,204 of 2,331</td><td class="ctr2">5%</td><td class="bar">216 of 230</td><td class="ctr2">6%</td><td class="ctr1">196</td><td class="ctr2">204</td><td class="ctr1">455</td><td class="ctr2">483</td><td class="ctr1">85</td><td class="ctr2">89</td><td class="ctr1">5</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a0"><a href="IdCardValidator.java.html" class="el_source">IdCardValidator.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="725" alt="725"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="60" alt="60"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">45</td><td class="ctr2" id="g1">45</td><td class="ctr1" id="h0">142</td><td class="ctr2" id="i0">142</td><td class="ctr1" id="j3">15</td><td class="ctr2" id="k3">15</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="UsccValidator.java.html" class="el_source">UsccValidator.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="641" alt="641"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="56" alt="56"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">57</td><td class="ctr2" id="g0">57</td><td class="ctr1" id="h1">132</td><td class="ctr2" id="i1">132</td><td class="ctr1" id="j0">29</td><td class="ctr2" id="k0">29</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a2"><a href="OrganizationCodeValidator.java.html" class="el_source">OrganizationCodeValidator.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="432" alt="432"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="50" alt="50"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">42</td><td class="ctr2" id="g2">42</td><td class="ctr1" id="h2">89</td><td class="ctr2" id="i2">89</td><td class="ctr1" id="j1">17</td><td class="ctr2" id="k1">17</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="ValidationResult.java.html" class="el_source">ValidationResult.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="214" alt="214"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="16" alt="16"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">24</td><td class="ctr2" id="g4">24</td><td class="ctr1" id="h4">43</td><td class="ctr2" id="i4">43</td><td class="ctr1" id="j2">16</td><td class="ctr2" id="k2">16</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="LuhnValidator.java.html" class="el_source">LuhnValidator.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="192" alt="192"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="127" alt="127"/></td><td class="ctr2" id="c0">39%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="34" alt="34"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">29%</td><td class="ctr1" id="f3">28</td><td class="ctr2" id="g3">36</td><td class="ctr1" id="h3">49</td><td class="ctr2" id="i3">77</td><td class="ctr1" id="j4">8</td><td class="ctr2" id="k4">12</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>