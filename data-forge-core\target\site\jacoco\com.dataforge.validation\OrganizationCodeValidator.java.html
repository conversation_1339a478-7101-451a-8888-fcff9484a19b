<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrganizationCodeValidator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_source">OrganizationCodeValidator.java</span></div><h1>OrganizationCodeValidator.java</h1><pre class="source lang-java linenums">package com.dataforge.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * 组织机构代码校验器。
 * 
 * &lt;p&gt;
 * 实现中国组织机构代码的校验算法，遵循GB 11714-1997标准。
 * 
 * &lt;p&gt;
 * 组织机构代码结构（9位）：
 * - 前8位：本体代码，由数字和大写英文字母组成（不使用I、O、S、V、Z）
 * - 第9位：校验码，由数字或大写英文字母X表示
 * 
 * &lt;p&gt;
 * 校验算法：
 * 1. 将前8位字符转换为对应的数值
 * 2. 每位数值乘以对应的权重（3,7,9,10,5,8,4,2）
 * 3. 求和后对11取模
 * 4. 用11减去模值得到校验码
 * 5. 如果校验码为10，则用X表示；如果为11，则用0表示
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L32">public class OrganizationCodeValidator implements Validator&lt;String&gt; {</span>

<span class="nc" id="L34">    private static final Logger logger = LoggerFactory.getLogger(OrganizationCodeValidator.class);</span>

    /**
     * 代码字符集（不包含I、O、S、V、Z）。
     */
    private static final String CODE_SET = &quot;0123456789ABCDEFGHJKLMNPQRTUWXY&quot;;

    /**
     * 字符到数值的映射。
     */
<span class="nc" id="L44">    private static final Map&lt;Character, Integer&gt; CHAR_TO_VALUE = new HashMap&lt;&gt;();</span>

    /**
     * 权重数组。
     */
<span class="nc" id="L49">    private static final int[] WEIGHTS = { 3, 7, 9, 10, 5, 8, 4, 2 };</span>

    static {
        // 初始化字符到数值的映射
<span class="nc bnc" id="L53" title="All 2 branches missed.">        for (int i = 0; i &lt; CODE_SET.length(); i++) {</span>
<span class="nc" id="L54">            CHAR_TO_VALUE.put(CODE_SET.charAt(i), i);</span>
        }
<span class="nc" id="L56">    }</span>

    @Override
    public boolean isValid(String data) {
<span class="nc" id="L60">        return validate(data).isValid();</span>
    }

    @Override
    public ValidationResult validate(String data) {
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (data == null) {</span>
<span class="nc" id="L66">            return ValidationResult.failure(&quot;Organization code cannot be null&quot;);</span>
        }

        // 移除所有非字母数字字符并转换为大写
<span class="nc" id="L70">        String cleanData = data.replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;).toUpperCase();</span>

<span class="nc bnc" id="L72" title="All 2 branches missed.">        if (cleanData.isEmpty()) {</span>
<span class="nc" id="L73">            return ValidationResult.failure(&quot;Organization code cannot be empty&quot;);</span>
        }

        // 长度校验
<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (cleanData.length() != 9) {</span>
<span class="nc" id="L78">            return ValidationResult.failure(&quot;Organization code must be exactly 9 characters long&quot;);</span>
        }

        try {
            // 字符集校验
<span class="nc" id="L83">            ValidationResult charSetResult = validateCharacterSet(cleanData);</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">            if (!charSetResult.isValid()) {</span>
<span class="nc" id="L85">                return charSetResult;</span>
            }

            // 校验码校验
<span class="nc" id="L89">            ValidationResult checkCodeResult = validateCheckCode(cleanData);</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">            if (!checkCodeResult.isValid()) {</span>
<span class="nc" id="L91">                return checkCodeResult;</span>
            }

<span class="nc" id="L94">            logger.debug(&quot;Organization code validation passed for: {}&quot;, maskOrgCode(data));</span>
<span class="nc" id="L95">            return ValidationResult.success();</span>

<span class="nc" id="L97">        } catch (Exception e) {</span>
<span class="nc" id="L98">            logger.error(&quot;Error during organization code validation for: {}&quot;, maskOrgCode(data), e);</span>
<span class="nc" id="L99">            return ValidationResult.failure(&quot;Error during organization code validation: &quot; + e.getMessage());</span>
        }
    }

    /**
     * 校验字符集。
     * 
     * @param orgCode 组织机构代码
     * @return 校验结果
     */
    private ValidationResult validateCharacterSet(String orgCode) {
        // 前8位字符校验
<span class="nc" id="L111">        String first8 = orgCode.substring(0, 8);</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">        for (char c : first8.toCharArray()) {</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">            if (!CHAR_TO_VALUE.containsKey(c)) {</span>
<span class="nc" id="L114">                return ValidationResult.failure(&quot;Invalid character in first 8 positions: &quot; + c +</span>
                        &quot;. Valid characters: &quot; + CODE_SET);
            }
        }

        // 第9位校验码字符校验（可以是0-9或X）
<span class="nc" id="L120">        char checkChar = orgCode.charAt(8);</span>
<span class="nc bnc" id="L121" title="All 4 branches missed.">        if (!Character.isDigit(checkChar) &amp;&amp; checkChar != 'X') {</span>
<span class="nc" id="L122">            return ValidationResult.failure(&quot;Invalid check code character: &quot; + checkChar +</span>
                    &quot;. Must be digit (0-9) or X&quot;);
        }

<span class="nc" id="L126">        return ValidationResult.success();</span>
    }

    /**
     * 校验校验码。
     * 
     * @param orgCode 完整的9位组织机构代码
     * @return 校验结果
     */
    private ValidationResult validateCheckCode(String orgCode) {
<span class="nc" id="L136">        String first8 = orgCode.substring(0, 8);</span>
<span class="nc" id="L137">        char actualCheckCode = orgCode.charAt(8);</span>
<span class="nc" id="L138">        char expectedCheckCode = calculateCheckCode(first8);</span>

<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (actualCheckCode == expectedCheckCode) {</span>
<span class="nc" id="L141">            return ValidationResult.success();</span>
        } else {
<span class="nc" id="L143">            return ValidationResult.failure(</span>
<span class="nc" id="L144">                    String.format(&quot;Check code mismatch. Expected: %c, Actual: %c&quot;,</span>
<span class="nc" id="L145">                            expectedCheckCode, actualCheckCode));</span>
        }
    }

    /**
     * 计算组织机构代码的校验码。
     * 
     * &lt;p&gt;
     * 算法步骤：
     * 1. 将前8位字符转换为对应的数值
     * 2. 每位数值乘以对应的权重
     * 3. 求和后对11取模
     * 4. 用11减去模值得到校验码
     * 5. 如果校验码为10，则用X表示；如果为11，则用0表示
     * 
     * @param first8 前8位字符
     * @return 校验码字符
     */
    public char calculateCheckCode(String first8) {
<span class="nc bnc" id="L164" title="All 4 branches missed.">        if (first8 == null || first8.length() != 8) {</span>
<span class="nc" id="L165">            throw new IllegalArgumentException(&quot;First 8 characters must be exactly 8 characters&quot;);</span>
        }

        // 校验字符集
<span class="nc bnc" id="L169" title="All 2 branches missed.">        for (char c : first8.toCharArray()) {</span>
<span class="nc bnc" id="L170" title="All 2 branches missed.">            if (!CHAR_TO_VALUE.containsKey(c)) {</span>
<span class="nc" id="L171">                throw new IllegalArgumentException(&quot;Invalid character: &quot; + c);</span>
            }
        }

<span class="nc" id="L175">        int sum = 0;</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc" id="L177">            char c = first8.charAt(i);</span>
<span class="nc" id="L178">            int value = CHAR_TO_VALUE.get(c);</span>
<span class="nc" id="L179">            sum += value * WEIGHTS[i];</span>
        }

<span class="nc" id="L182">        int remainder = sum % 11;</span>
<span class="nc" id="L183">        int checkValue = 11 - remainder;</span>

        // 特殊处理
<span class="nc bnc" id="L186" title="All 2 branches missed.">        if (checkValue == 10) {</span>
<span class="nc" id="L187">            return 'X';</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">        } else if (checkValue == 11) {</span>
<span class="nc" id="L189">            return '0';</span>
        } else {
<span class="nc" id="L191">            return (char) ('0' + checkValue);</span>
        }
    }

    /**
     * 生成完整的有效组织机构代码。
     * 
     * @param first8 前8位字符
     * @return 完整的9位组织机构代码
     */
    public String generateValidOrganizationCode(String first8) {
<span class="nc" id="L202">        char checkCode = calculateCheckCode(first8);</span>
<span class="nc" id="L203">        return first8 + checkCode;</span>
    }

    /**
     * 生成随机的组织机构代码本体（前8位）。
     * 
     * @return 8位随机本体代码
     */
    public String generateRandomBodyCode() {
<span class="nc" id="L212">        StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L213">        java.util.Random random = new java.util.Random();</span>

<span class="nc bnc" id="L215" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc" id="L216">            int index = random.nextInt(CODE_SET.length());</span>
<span class="nc" id="L217">            sb.append(CODE_SET.charAt(index));</span>
        }

<span class="nc" id="L220">        return sb.toString();</span>
    }

    /**
     * 生成完整的随机组织机构代码。
     * 
     * @return 完整的9位组织机构代码
     */
    public String generateRandomOrganizationCode() {
<span class="nc" id="L229">        String bodyCode = generateRandomBodyCode();</span>
<span class="nc" id="L230">        return generateValidOrganizationCode(bodyCode);</span>
    }

    /**
     * 检查字符是否为有效的组织机构代码字符。
     * 
     * @param c 字符
     * @return 如果是有效字符返回true，否则返回false
     */
    public boolean isValidCodeCharacter(char c) {
<span class="nc" id="L240">        return CHAR_TO_VALUE.containsKey(c);</span>
    }

    /**
     * 获取有效的代码字符集。
     * 
     * @return 代码字符集字符串
     */
    public String getValidCharacterSet() {
<span class="nc" id="L249">        return CODE_SET;</span>
    }

    /**
     * 掩码组织机构代码用于日志记录。
     * 
     * @param orgCode 原始组织机构代码
     * @return 掩码后的组织机构代码
     */
    private String maskOrgCode(String orgCode) {
<span class="nc bnc" id="L259" title="All 4 branches missed.">        if (orgCode == null || orgCode.length() &lt; 6) {</span>
<span class="nc" id="L260">            return &quot;****&quot;;</span>
        }

        // 显示前3位和后3位，中间用*代替
<span class="nc" id="L264">        String prefix = orgCode.substring(0, 3);</span>
<span class="nc" id="L265">        String suffix = orgCode.substring(orgCode.length() - 3);</span>
<span class="nc" id="L266">        int maskLength = orgCode.length() - 6;</span>
<span class="nc" id="L267">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="nc" id="L269">        return prefix + mask + suffix;</span>
    }

    /**
     * 格式化组织机构代码为标准格式（XXXXXXXX-X）。
     * 
     * @param orgCode 组织机构代码
     * @return 格式化后的代码
     */
    public String formatOrganizationCode(String orgCode) {
<span class="nc bnc" id="L279" title="All 4 branches missed.">        if (orgCode == null || orgCode.length() != 9) {</span>
<span class="nc" id="L280">            return orgCode;</span>
        }

<span class="nc" id="L283">        String cleanCode = orgCode.replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;).toUpperCase();</span>
<span class="nc bnc" id="L284" title="All 2 branches missed.">        if (cleanCode.length() != 9) {</span>
<span class="nc" id="L285">            return orgCode;</span>
        }

<span class="nc" id="L288">        return cleanCode.substring(0, 8) + &quot;-&quot; + cleanCode.charAt(8);</span>
    }

    /**
     * 解析格式化的组织机构代码。
     * 
     * @param formattedCode 格式化的代码（如：XXXXXXXX-X）
     * @return 纯代码字符串
     */
    public String parseFormattedCode(String formattedCode) {
<span class="nc bnc" id="L298" title="All 2 branches missed.">        if (formattedCode == null) {</span>
<span class="nc" id="L299">            return null;</span>
        }

<span class="nc" id="L302">        return formattedCode.replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;).toUpperCase();</span>
    }

    @Override
    public String getName() {
<span class="nc" id="L307">        return &quot;OrganizationCode&quot;;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L312">        return &quot;Chinese organization code validator (GB 11714-1997)&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>