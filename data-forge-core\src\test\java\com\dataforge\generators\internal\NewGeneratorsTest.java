package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.model.FieldConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新增生成器测试类
 * 测试WebSocketGenerator、ProxyGenerator、RandomNumberGenerator、DecimalGenerator
 */
class NewGeneratorsTest {

    private DataForgeContext context;

    @BeforeEach
    void setUp() {
        context = new DataForgeContext();
    }

    @Test
    void testWebSocketGenerator() {
        WebSocketGenerator generator = new WebSocketGenerator();
        
        // 测试URL格式
        FieldConfig urlConfig = createConfig("format", "URL", "protocol", "WS");
        String url = generator.generate(urlConfig, context);
        assertNotNull(url);
        assertTrue(url.startsWith("ws://"));
        
        // 测试JSON格式
        FieldConfig jsonConfig = createConfig("format", "JSON", "protocol", "WSS", "include_query", "true");
        String json = generator.generate(jsonConfig, context);
        assertNotNull(json);
        assertTrue(json.startsWith("{"));
        assertTrue(json.contains("\"protocol\":\"wss\""));
        
        // 测试配置格式
        FieldConfig configFormat = createConfig("format", "CONFIG", "host", "localhost", "port", "8080");
        String config = generator.generate(configFormat, context);
        assertNotNull(config);
        assertTrue(config.contains("WebSocket Configuration"));
        assertTrue(config.contains("localhost"));
        assertTrue(config.contains("8080"));
    }

    @Test
    void testProxyGenerator() {
        ProxyGenerator generator = new ProxyGenerator();
        
        // 测试URL格式
        FieldConfig urlConfig = createConfig("format", "URL", "type", "HTTP");
        String url = generator.generate(urlConfig, context);
        assertNotNull(url);
        assertTrue(url.startsWith("http://"));
        
        // 测试SOCKS代理
        FieldConfig socksConfig = createConfig("format", "URL", "type", "SOCKS5", "auth_required", "true");
        String socksUrl = generator.generate(socksConfig, context);
        assertNotNull(socksUrl);
        assertTrue(socksUrl.startsWith("socks5://"));
        
        // 测试JSON格式
        FieldConfig jsonConfig = createConfig("format", "JSON", "type", "HTTPS", "country", "US");
        String json = generator.generate(jsonConfig, context);
        assertNotNull(json);
        assertTrue(json.startsWith("{"));
        assertTrue(json.contains("\"type\":\"https\""));
        assertTrue(json.contains("\"country\":\"US\""));
        
        // 测试PAC格式
        FieldConfig pacConfig = createConfig("format", "PAC", "host", "proxy.example.com", "port", "8080");
        String pac = generator.generate(pacConfig, context);
        assertNotNull(pac);
        assertTrue(pac.contains("function FindProxyForURL"));
        assertTrue(pac.contains("proxy.example.com:8080"));
    }

    @Test
    void testRandomNumberGenerator() {
        RandomNumberGenerator generator = new RandomNumberGenerator();
        
        // 测试整数生成
        FieldConfig intConfig = createConfig("type", "INT", "min", "1", "max", "100");
        String intValue = generator.generate(intConfig, context);
        assertNotNull(intValue);
        int value = Integer.parseInt(intValue);
        assertTrue(value >= 1 && value <= 100);
        
        // 测试长整数生成
        FieldConfig longConfig = createConfig("type", "LONG", "min", "1000", "max", "9999");
        String longValue = generator.generate(longConfig, context);
        assertNotNull(longValue);
        long longVal = Long.parseLong(longValue);
        assertTrue(longVal >= 1000 && longVal <= 9999);
        
        // 测试十六进制格式
        FieldConfig hexConfig = createConfig("type", "INT", "format", "HEX", "min", "10", "max", "255");
        String hexValue = generator.generate(hexConfig, context);
        assertNotNull(hexValue);
        assertTrue(hexValue.startsWith("0x"));
        
        // 测试二进制格式
        FieldConfig binConfig = createConfig("type", "INT", "format", "BINARY", "min", "1", "max", "15");
        String binValue = generator.generate(binConfig, context);
        assertNotNull(binValue);
        assertTrue(binValue.startsWith("0b"));
        
        // 测试正态分布
        FieldConfig normalConfig = createConfig("type", "INT", "distribution", "NORMAL", "mean", "50", "stddev", "10");
        String normalValue = generator.generate(normalConfig, context);
        assertNotNull(normalValue);
        assertDoesNotThrow(() -> Integer.parseInt(normalValue));
        
        // 测试大整数
        FieldConfig bigintConfig = createConfig("type", "BIGINT", "precision", "20");
        String bigintValue = generator.generate(bigintConfig, context);
        assertNotNull(bigintValue);
        assertTrue(bigintValue.length() >= 15); // 至少15位数字
    }

    @Test
    void testDecimalGenerator() {
        DecimalGenerator generator = new DecimalGenerator();
        
        // 测试双精度数生成
        FieldConfig doubleConfig = createConfig("type", "DOUBLE", "min", "0.0", "max", "100.0", "scale", "2");
        String doubleValue = generator.generate(doubleConfig, context);
        assertNotNull(doubleValue);
        double value = Double.parseDouble(doubleValue);
        assertTrue(value >= 0.0 && value <= 100.0);
        
        // 测试高精度小数
        FieldConfig bigDecimalConfig = createConfig("type", "BIGDECIMAL", "min", "0.0", "max", "1000.0", "scale", "4");
        String bigDecimalValue = generator.generate(bigDecimalConfig, context);
        assertNotNull(bigDecimalValue);
        assertDoesNotThrow(() -> Double.parseDouble(bigDecimalValue));
        
        // 测试科学计数法格式
        FieldConfig scientificConfig = createConfig("type", "DOUBLE", "format", "SCIENTIFIC", "min", "1000.0", "max", "10000.0");
        String scientificValue = generator.generate(scientificConfig, context);
        assertNotNull(scientificValue);
        assertTrue(scientificValue.contains("E"));
        
        // 测试货币格式
        FieldConfig currencyConfig = createConfig("type", "DOUBLE", "format", "CURRENCY", "min", "10.0", "max", "1000.0", "currency_code", "USD");
        String currencyValue = generator.generate(currencyConfig, context);
        assertNotNull(currencyValue);
        assertTrue(currencyValue.contains("$") || currencyValue.contains("USD"));
        
        // 测试百分比格式
        FieldConfig percentageConfig = createConfig("type", "DOUBLE", "format", "PERCENTAGE", "min", "0.0", "max", "100.0");
        String percentageValue = generator.generate(percentageConfig, context);
        assertNotNull(percentageValue);
        assertTrue(percentageValue.contains("%"));
        
        // 测试自定义格式
        FieldConfig customConfig = createConfig("type", "DOUBLE", "format", "CUSTOM", "pattern", "#,##0.000");
        String customValue = generator.generate(customConfig, context);
        assertNotNull(customValue);
        assertTrue(customValue.matches("\\d{1,3}(,\\d{3})*\\.\\d{3}"));
        
        // 测试正态分布
        FieldConfig normalConfig = createConfig("type", "DOUBLE", "distribution", "NORMAL", "mean", "50.0", "stddev", "10.0");
        String normalValue = generator.generate(normalConfig, context);
        assertNotNull(normalValue);
        assertDoesNotThrow(() -> Double.parseDouble(normalValue));
        
        // 测试只生成正数
        FieldConfig positiveConfig = createConfig("type", "DOUBLE", "positive_only", "true", "min", "-10.0", "max", "10.0");
        String positiveValue = generator.generate(positiveConfig, context);
        assertNotNull(positiveValue);
        double positiveVal = Double.parseDouble(positiveValue);
        assertTrue(positiveVal > 0);
    }

    @Test
    void testGeneratorTypes() {
        assertEquals("websocket", new WebSocketGenerator().getType());
        assertEquals("proxy", new ProxyGenerator().getType());
        assertEquals("random_number", new RandomNumberGenerator().getType());
        assertEquals("decimal", new DecimalGenerator().getType());
    }

    @Test
    void testGeneratorConfigClasses() {
        assertEquals(FieldConfig.class, new WebSocketGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new ProxyGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new RandomNumberGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new DecimalGenerator().getConfigClass());
    }

    @Test
    void testErrorHandling() {
        // 测试无效参数的错误处理
        WebSocketGenerator wsGenerator = new WebSocketGenerator();
        FieldConfig invalidConfig = createConfig("format", "INVALID_FORMAT");
        String result = wsGenerator.generate(invalidConfig, context);
        assertNotNull(result);
        assertTrue(result.startsWith("ws://"));
        
        ProxyGenerator proxyGenerator = new ProxyGenerator();
        FieldConfig invalidProxyConfig = createConfig("type", "INVALID_TYPE");
        String proxyResult = proxyGenerator.generate(invalidProxyConfig, context);
        assertNotNull(proxyResult);
        assertTrue(proxyResult.startsWith("http://"));
        
        RandomNumberGenerator numberGenerator = new RandomNumberGenerator();
        FieldConfig invalidNumberConfig = createConfig("type", "INVALID_TYPE", "distribution", "INVALID_DIST");
        String numberResult = numberGenerator.generate(invalidNumberConfig, context);
        assertNotNull(numberResult);
        assertDoesNotThrow(() -> Integer.parseInt(numberResult));
        
        DecimalGenerator decimalGenerator = new DecimalGenerator();
        FieldConfig invalidDecimalConfig = createConfig("type", "INVALID_TYPE", "format", "INVALID_FORMAT");
        String decimalResult = decimalGenerator.generate(invalidDecimalConfig, context);
        assertNotNull(decimalResult);
        assertDoesNotThrow(() -> Double.parseDouble(decimalResult));
    }

    private FieldConfig createConfig(String... keyValues) {
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            params.put(keyValues[i], keyValues[i + 1]);
        }
        
        return new TestFieldConfig("test_field", "test", params);
    }
    
    /**
     * 测试用的FieldConfig实现类。
     */
    private static class TestFieldConfig extends FieldConfig {
        public TestFieldConfig(String name, String type, Map<String, Object> params) {
            super(name, type);
            setParams(params);
        }
    }
}
