<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DeviceIdGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">DeviceIdGenerator.java</span></div><h1>DeviceIdGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.validation.LuhnValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * 设备ID生成器
 * 
 * &lt;p&gt;
 * 支持生成多种类型的设备标识符：
 * &lt;ul&gt;
 * &lt;li&gt;UUID - 通用唯一标识符&lt;/li&gt;
 * &lt;li&gt;IMEI - 国际移动设备识别码（15位）&lt;/li&gt;
 * &lt;li&gt;IMSI - 国际移动用户识别码（15位）&lt;/li&gt;
 * &lt;li&gt;CUSTOM - 自定义格式设备ID&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;type: 设备ID类型 (UUID|IMEI|IMSI|CUSTOM) 默认: UUID&lt;/li&gt;
 * &lt;li&gt;length: 自定义长度（仅对CUSTOM类型有效）默认: 16&lt;/li&gt;
 * &lt;li&gt;tac: IMEI的TAC码前缀（8位）&lt;/li&gt;
 * &lt;li&gt;mcc: IMSI的移动国家码（3位）&lt;/li&gt;
 * &lt;li&gt;mnc: IMSI的移动网络码（2-3位）&lt;/li&gt;
 * &lt;li&gt;valid: 是否生成有效的设备ID 默认: true&lt;/li&gt;
 * &lt;li&gt;format: 输出格式 (PLAIN|HYPHEN|COLON) 默认: PLAIN&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L40">public class DeviceIdGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L42">    private static final Logger logger = LoggerFactory.getLogger(DeviceIdGenerator.class);</span>
<span class="fc" id="L43">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 设备ID类型枚举
<span class="fc" id="L46">    public enum DeviceIdType {</span>
<span class="fc" id="L47">        UUID(&quot;UUID格式设备ID&quot;),</span>
<span class="fc" id="L48">        IMEI(&quot;国际移动设备识别码&quot;),</span>
<span class="fc" id="L49">        IMSI(&quot;国际移动用户识别码&quot;),</span>
<span class="fc" id="L50">        CUSTOM(&quot;自定义格式设备ID&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L54">        DeviceIdType(String description) {</span>
<span class="fc" id="L55">            this.description = description;</span>
<span class="fc" id="L56">        }</span>
        
        public String getDescription() {
<span class="nc" id="L59">            return description;</span>
        }
    }
    
    // 输出格式枚举
<span class="fc" id="L64">    public enum OutputFormat {</span>
<span class="fc" id="L65">        PLAIN(&quot;无分隔符&quot;),</span>
<span class="fc" id="L66">        HYPHEN(&quot;连字符分隔&quot;),</span>
<span class="fc" id="L67">        COLON(&quot;冒号分隔&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L71">        OutputFormat(String description) {</span>
<span class="fc" id="L72">            this.description = description;</span>
<span class="fc" id="L73">        }</span>
        
        public String getDescription() {
<span class="nc" id="L76">            return description;</span>
        }
    }
    
    // 常见TAC码（设备类型分配码）
<span class="fc" id="L81">    private static final List&lt;String&gt; COMMON_TAC_CODES = Arrays.asList(</span>
        &quot;35328506&quot;, // Apple iPhone
        &quot;35209006&quot;, // Samsung Galaxy
        &quot;35875505&quot;, // Huawei
        &quot;35404906&quot;, // Xiaomi
        &quot;35891706&quot;, // OPPO
        &quot;35917406&quot;, // Vivo
        &quot;35824005&quot;, // OnePlus
        &quot;35679804&quot;, // Google Pixel
        &quot;35434104&quot;, // Sony Xperia
        &quot;35251203&quot;  // LG
    );
    
    // 移动国家码和网络码映射
<span class="fc" id="L95">    private static final Map&lt;String, List&lt;String&gt;&gt; MCC_MNC_MAP = new HashMap&lt;&gt;();</span>
    
    static {
        // 中国
<span class="fc" id="L99">        MCC_MNC_MAP.put(&quot;460&quot;, Arrays.asList(&quot;00&quot;, &quot;01&quot;, &quot;02&quot;, &quot;03&quot;, &quot;07&quot;, &quot;08&quot;, &quot;11&quot;, &quot;20&quot;));</span>
        // 美国
<span class="fc" id="L101">        MCC_MNC_MAP.put(&quot;310&quot;, Arrays.asList(&quot;030&quot;, &quot;070&quot;, &quot;150&quot;, &quot;260&quot;, &quot;410&quot;, &quot;560&quot;, &quot;680&quot;, &quot;890&quot;));</span>
        // 英国
<span class="fc" id="L103">        MCC_MNC_MAP.put(&quot;234&quot;, Arrays.asList(&quot;10&quot;, &quot;15&quot;, &quot;20&quot;, &quot;30&quot;, &quot;33&quot;, &quot;50&quot;, &quot;55&quot;, &quot;58&quot;));</span>
        // 日本
<span class="fc" id="L105">        MCC_MNC_MAP.put(&quot;440&quot;, Arrays.asList(&quot;00&quot;, &quot;01&quot;, &quot;02&quot;, &quot;03&quot;, &quot;04&quot;, &quot;05&quot;, &quot;06&quot;, &quot;07&quot;));</span>
        // 韩国
<span class="fc" id="L107">        MCC_MNC_MAP.put(&quot;450&quot;, Arrays.asList(&quot;02&quot;, &quot;03&quot;, &quot;04&quot;, &quot;05&quot;, &quot;06&quot;, &quot;07&quot;, &quot;08&quot;, &quot;11&quot;));</span>
<span class="fc" id="L108">    }</span>

    @Override
    public String getType() {
<span class="fc" id="L112">        return &quot;device_id&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L117">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取设备ID类型
<span class="fc" id="L124">            String typeStr = getStringParam(config, &quot;type&quot;, &quot;UUID&quot;);</span>
<span class="fc" id="L125">            DeviceIdType type = parseDeviceIdType(typeStr);</span>
            
            // 获取是否生成有效设备ID
<span class="fc" id="L128">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>
            
            // 获取输出格式
<span class="fc" id="L131">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;PLAIN&quot;);</span>
<span class="fc" id="L132">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            String deviceId;
            
<span class="pc bpc" id="L136" title="1 of 5 branches missed.">            switch (type) {</span>
                case UUID:
<span class="fc" id="L138">                    deviceId = generateUuidDeviceId();</span>
<span class="fc" id="L139">                    break;</span>
                case IMEI:
<span class="fc" id="L141">                    deviceId = generateImei(config, valid);</span>
<span class="fc" id="L142">                    break;</span>
                case IMSI:
<span class="fc" id="L144">                    deviceId = generateImsi(config, valid);</span>
<span class="fc" id="L145">                    break;</span>
                case CUSTOM:
<span class="fc" id="L147">                    deviceId = generateCustomDeviceId(config);</span>
<span class="fc" id="L148">                    break;</span>
                default:
<span class="nc" id="L150">                    deviceId = generateUuidDeviceId();</span>
                    break;
            }
            
            // 应用输出格式
<span class="fc" id="L155">            return applyOutputFormat(deviceId, format, type);</span>
            
<span class="nc" id="L157">        } catch (Exception e) {</span>
<span class="nc" id="L158">            logger.error(&quot;Failed to generate device ID&quot;, e);</span>
            // 返回一个默认的UUID作为fallback
<span class="nc" id="L160">            return UUID.randomUUID().toString().replace(&quot;-&quot;, &quot;&quot;);</span>
        }
    }

    /**
     * 解析设备ID类型
     */
    private DeviceIdType parseDeviceIdType(String typeStr) {
        try {
<span class="fc" id="L169">            return DeviceIdType.valueOf(typeStr.toUpperCase());</span>
<span class="nc" id="L170">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L171">            logger.warn(&quot;Invalid device ID type: {}, using UUID as default&quot;, typeStr);</span>
<span class="nc" id="L172">            return DeviceIdType.UUID;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L181">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L182">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L183">            logger.warn(&quot;Invalid output format: {}, using PLAIN as default&quot;, formatStr);</span>
<span class="nc" id="L184">            return OutputFormat.PLAIN;</span>
        }
    }

    /**
     * 生成UUID格式的设备ID
     */
    private String generateUuidDeviceId() {
<span class="fc" id="L192">        return UUID.randomUUID().toString().replace(&quot;-&quot;, &quot;&quot;);</span>
    }

    /**
     * 生成IMEI（国际移动设备识别码）
     */
    private String generateImei(FieldConfig config, boolean valid) {
        // 获取TAC码
<span class="fc" id="L200">        String tac = getStringParam(config, &quot;tac&quot;, null);</span>
<span class="pc bpc" id="L201" title="3 of 4 branches missed.">        if (tac == null || tac.length() != 8) {</span>
            // 随机选择一个常见的TAC码
<span class="fc" id="L203">            tac = COMMON_TAC_CODES.get(random.nextInt(COMMON_TAC_CODES.size()));</span>
        }
        
        // 生成序列号（6位）
<span class="fc" id="L207">        String serialNumber = String.format(&quot;%06d&quot;, random.nextInt(1000000));</span>
        
        // 组合前14位
<span class="fc" id="L210">        String imeiWithoutChecksum = tac + serialNumber;</span>
        
<span class="pc bpc" id="L212" title="1 of 2 branches missed.">        if (!valid) {</span>
            // 生成无效的IMEI（错误的校验位）
<span class="nc" id="L214">            return imeiWithoutChecksum + String.valueOf(random.nextInt(10));</span>
        }
        
        // 计算校验位（Luhn算法）
<span class="fc" id="L218">        LuhnValidator luhnValidator = new LuhnValidator();</span>
<span class="fc" id="L219">        int checksum = luhnValidator.generateCheckDigit(imeiWithoutChecksum);</span>
        
<span class="fc" id="L221">        return imeiWithoutChecksum + checksum;</span>
    }

    /**
     * 生成IMSI（国际移动用户识别码）
     */
    private String generateImsi(FieldConfig config, boolean valid) {
        // 获取MCC（移动国家码）
<span class="fc" id="L229">        String mcc = getStringParam(config, &quot;mcc&quot;, &quot;460&quot;); // 默认中国</span>
<span class="pc bpc" id="L230" title="1 of 2 branches missed.">        if (mcc.length() != 3) {</span>
<span class="nc" id="L231">            mcc = &quot;460&quot;;</span>
        }
        
        // 获取MNC（移动网络码）
<span class="fc" id="L235">        String mnc = getStringParam(config, &quot;mnc&quot;, null);</span>
<span class="pc bpc" id="L236" title="1 of 2 branches missed.">        if (mnc == null) {</span>
<span class="fc" id="L237">            List&lt;String&gt; mncList = MCC_MNC_MAP.get(mcc);</span>
<span class="pc bpc" id="L238" title="2 of 4 branches missed.">            if (mncList != null &amp;&amp; !mncList.isEmpty()) {</span>
<span class="fc" id="L239">                mnc = mncList.get(random.nextInt(mncList.size()));</span>
            } else {
<span class="nc" id="L241">                mnc = String.format(&quot;%02d&quot;, random.nextInt(100));</span>
            }
        }
        
        // 确保MNC长度为2-3位
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">        if (mnc.length() &lt; 2) {</span>
<span class="nc" id="L247">            mnc = String.format(&quot;%02d&quot;, Integer.parseInt(mnc));</span>
<span class="pc bpc" id="L248" title="1 of 2 branches missed.">        } else if (mnc.length() &gt; 3) {</span>
<span class="nc" id="L249">            mnc = mnc.substring(0, 3);</span>
        }
        
        // 计算MSIN长度（总长度15位 - MCC 3位 - MNC长度）
<span class="fc" id="L253">        int msinLength = 15 - 3 - mnc.length();</span>
        
        // 生成MSIN（移动用户识别号）
<span class="fc" id="L256">        StringBuilder msin = new StringBuilder();</span>
<span class="fc bfc" id="L257" title="All 2 branches covered.">        for (int i = 0; i &lt; msinLength; i++) {</span>
<span class="fc" id="L258">            msin.append(random.nextInt(10));</span>
        }
        
<span class="fc" id="L261">        String imsi = mcc + mnc + msin.toString();</span>
        
<span class="pc bpc" id="L263" title="1 of 2 branches missed.">        if (!valid) {</span>
            // 生成无效的IMSI（长度不正确）
<span class="nc" id="L265">            return imsi + String.valueOf(random.nextInt(10));</span>
        }
        
<span class="fc" id="L268">        return imsi;</span>
    }

    /**
     * 生成自定义格式设备ID
     */
    private String generateCustomDeviceId(FieldConfig config) {
<span class="fc" id="L275">        int length = getIntParam(config, &quot;length&quot;, 16);</span>
        
<span class="fc" id="L277">        StringBuilder deviceId = new StringBuilder();</span>
<span class="fc" id="L278">        String chars = &quot;0123456789ABCDEF&quot;;</span>
        
<span class="fc bfc" id="L280" title="All 2 branches covered.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="fc" id="L281">            deviceId.append(chars.charAt(random.nextInt(chars.length())));</span>
        }
        
<span class="fc" id="L284">        return deviceId.toString();</span>
    }

    /**
     * 应用输出格式
     */
    private String applyOutputFormat(String deviceId, OutputFormat format, DeviceIdType type) {
<span class="pc bpc" id="L291" title="5 of 6 branches missed.">        if (format == OutputFormat.PLAIN || type == DeviceIdType.IMEI || type == DeviceIdType.IMSI) {</span>
<span class="fc" id="L292">            return deviceId;</span>
        }
        
<span class="nc" id="L295">        StringBuilder formatted = new StringBuilder();</span>
<span class="nc bnc" id="L296" title="All 2 branches missed.">        String separator = (format == OutputFormat.HYPHEN) ? &quot;-&quot; : &quot;:&quot;;</span>
        
        // 根据设备ID类型决定分组方式
<span class="nc bnc" id="L299" title="All 2 branches missed.">        int groupSize = (type == DeviceIdType.UUID) ? 8 : 4;</span>
        
<span class="nc bnc" id="L301" title="All 2 branches missed.">        for (int i = 0; i &lt; deviceId.length(); i++) {</span>
<span class="nc bnc" id="L302" title="All 4 branches missed.">            if (i &gt; 0 &amp;&amp; i % groupSize == 0) {</span>
<span class="nc" id="L303">                formatted.append(separator);</span>
            }
<span class="nc" id="L305">            formatted.append(deviceId.charAt(i));</span>
        }
        
<span class="nc" id="L308">        return formatted.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>