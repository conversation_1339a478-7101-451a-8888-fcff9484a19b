package com.dataforge.service;

import com.dataforge.config.FieldConfigWrapper;
import com.dataforge.config.ForgeConfig;
import com.dataforge.config.OutputConfig;
import com.dataforge.core.GeneratorFactory;
import com.dataforge.io.ConsoleOutputStrategy;
import com.dataforge.io.OutputStrategy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;

/**
 * DataForgeService测试类。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DataForgeServiceTest {

    @Mock
    private GeneratorFactory generatorFactory;

    private DataForgeService dataForgeService;
    private List<OutputStrategy> outputStrategies;

    @BeforeEach
    void setUp() {
        outputStrategies = new ArrayList<>();
        outputStrategies.add(new ConsoleOutputStrategy());

        dataForgeService = new DataForgeService(generatorFactory, outputStrategies);
    }

    @Test
    void testConstructor() {
        assertNotNull(dataForgeService);
    }

    @Test
    void testGenerateDataWithNullConfig() {
        assertThrows(DataForgeException.class, () -> {
            dataForgeService.generateData(null);
        });
    }

    @Test
    void testCreateValidConfig() {
        ForgeConfig config = createValidConfig();

        assertNotNull(config);
        assertEquals(5, config.getCount());
        assertEquals(1, config.getThreads());
        assertNotNull(config.getOutput());
        assertNotNull(config.getFields());
        assertEquals(2, config.getFields().size());
    }

    /**
     * 创建有效的配置对象用于测试。
     * 
     * @return 有效的配置对象
     */
    private ForgeConfig createValidConfig() {
        ForgeConfig config = new ForgeConfig();
        config.setCount(5);
        config.setThreads(1);
        config.setValidate(true);

        // 输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat(OutputConfig.Format.CONSOLE);
        config.setOutput(outputConfig);

        // 字段配置
        List<FieldConfigWrapper> fields = new ArrayList<>();

        // UUID字段
        FieldConfigWrapper uuidField = new FieldConfigWrapper();
        uuidField.setName("id");
        uuidField.setType("uuid");
        Map<String, Object> uuidParams = new HashMap<>();
        uuidParams.put("type", "UUID4");
        uuidField.setParams(uuidParams);
        fields.add(uuidField);

        // 姓名字段
        FieldConfigWrapper nameField = new FieldConfigWrapper();
        nameField.setName("name");
        nameField.setType("name");
        Map<String, Object> nameParams = new HashMap<>();
        nameParams.put("type", "CN");
        nameParams.put("gender", "ANY");
        nameField.setParams(nameParams);
        fields.add(nameField);

        config.setFields(fields);

        return config;
    }
}