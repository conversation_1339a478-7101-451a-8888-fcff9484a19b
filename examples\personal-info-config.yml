dataforge:
  count: 8
  output:
    format: csv
    file: "output/personal-info-test.csv"
    encoding: "UTF-8"
  fields:
    - name: "id"
      type: "uuid"
      params:
        type: "UUID4"
    
    - name: "name"
      type: "name"
      params:
        type: "CN"
        gender_related: "true"
    
    - name: "gender"
      type: "gender"
      params:
        format: "CN"
        male_ratio: "0.55"
    
    - name: "age"
      type: "age"
      params:
        min: "18"
        max: "70"
        distribution: "NORMAL"
    
    - name: "marital_status"
      type: "marital"
      params:
        status: "ANY"
        age_related: "true"
        format: "CHINESE"
    
    - name: "blood_type"
      type: "bloodtype"
      params:
        group: "ANY"
        rh: "ANY"
        distribution: "REALISTIC"
        format: "STANDARD"
    
    - name: "zodiac"
      type: "zodiac"
      params:
        sign: "ANY"
        birth_date_related: "false"
        format: "CHINESE"
    
    - name: "ethnicity"
      type: "ethnicity"
      params:
        type: "ANY"
        han_ratio: "0.88"
        format: "CHINESE"
    
    - name: "religion"
      type: "religion"
      params:
        type: "ANY"
        none_ratio: "0.75"
        format: "CHINESE"
    
    - name: "education"
      type: "education"
      params:
        levels: "HIGH_SCHOOL|COLLEGE|BACHELOR|MASTER|PHD"
        distribution: "REALISTIC"
        age_related: "true"
    
    - name: "occupation"
      type: "occupation"
      params:
        industry: "ANY"
        level: "ANY"
    
    - name: "phone"
      type: "phone"
      params:
        region: "CN"
        valid: "true"
    
    - name: "email"
      type: "email"
      params:
        domains: "qq.com,163.com,gmail.com,sina.com,126.com"
        prefix_name: "true"