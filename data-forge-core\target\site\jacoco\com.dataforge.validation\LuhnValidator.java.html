<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LuhnValidator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_source">LuhnValidator.java</span></div><h1>LuhnValidator.java</h1><pre class="source lang-java linenums">package com.dataforge.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Luhn算法校验器。
 * 
 * &lt;p&gt;
 * 实现Luhn算法（也称为模10算法）用于校验银行卡号、IMEI号等数字序列的有效性。
 * Luhn算法是一种简单的校验和算法，广泛用于各种识别号码的校验。
 * 
 * &lt;p&gt;
 * 算法步骤：
 * 1. 从右到左，对偶数位数字乘以2
 * 2. 如果乘积大于9，则将乘积的各位数字相加
 * 3. 将所有数字相加
 * 4. 如果总和能被10整除，则校验通过
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="fc" id="L25">public class LuhnValidator implements Validator&lt;String&gt; {</span>

<span class="fc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(LuhnValidator.class);</span>

    @Override
    public boolean isValid(String data) {
<span class="nc" id="L31">        return validate(data).isValid();</span>
    }

    @Override
    public ValidationResult validate(String data) {
<span class="nc bnc" id="L36" title="All 2 branches missed.">        if (data == null) {</span>
<span class="nc" id="L37">            return ValidationResult.failure(&quot;Input cannot be null&quot;);</span>
        }

        // 移除所有非数字字符
<span class="nc" id="L41">        String cleanData = data.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>

<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (cleanData.isEmpty()) {</span>
<span class="nc" id="L44">            return ValidationResult.failure(&quot;Input must contain at least one digit&quot;);</span>
        }

<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (cleanData.length() &lt; 2) {</span>
<span class="nc" id="L48">            return ValidationResult.failure(&quot;Input must contain at least 2 digits for Luhn validation&quot;);</span>
        }

        try {
<span class="nc" id="L52">            boolean isValid = performLuhnCheck(cleanData);</span>

<span class="nc bnc" id="L54" title="All 2 branches missed.">            if (isValid) {</span>
<span class="nc" id="L55">                logger.debug(&quot;Luhn validation passed for input: {}&quot;, maskSensitiveData(data));</span>
<span class="nc" id="L56">                return ValidationResult.success();</span>
            } else {
<span class="nc" id="L58">                logger.debug(&quot;Luhn validation failed for input: {}&quot;, maskSensitiveData(data));</span>
<span class="nc" id="L59">                return ValidationResult.failure(&quot;Luhn checksum validation failed&quot;);</span>
            }

<span class="nc" id="L62">        } catch (Exception e) {</span>
<span class="nc" id="L63">            logger.error(&quot;Error during Luhn validation for input: {}&quot;, maskSensitiveData(data), e);</span>
<span class="nc" id="L64">            return ValidationResult.failure(&quot;Error during Luhn validation: &quot; + e.getMessage());</span>
        }
    }

    /**
     * 执行Luhn算法校验。
     * 
     * @param digits 纯数字字符串
     * @return 如果校验通过返回true，否则返回false
     */
    private boolean performLuhnCheck(String digits) {
<span class="nc" id="L75">        int sum = 0;</span>
<span class="nc" id="L76">        boolean alternate = false;</span>

        // 从右到左遍历数字
<span class="nc bnc" id="L79" title="All 2 branches missed.">        for (int i = digits.length() - 1; i &gt;= 0; i--) {</span>
<span class="nc" id="L80">            int digit = Character.getNumericValue(digits.charAt(i));</span>

<span class="nc bnc" id="L82" title="All 2 branches missed.">            if (alternate) {</span>
<span class="nc" id="L83">                digit *= 2;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">                if (digit &gt; 9) {</span>
<span class="nc" id="L85">                    digit = (digit % 10) + 1; // 等价于将两位数的各位相加</span>
                }
            }

<span class="nc" id="L89">            sum += digit;</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">            alternate = !alternate;</span>
        }

<span class="nc bnc" id="L93" title="All 2 branches missed.">        return (sum % 10) == 0;</span>
    }

    /**
     * 生成符合Luhn算法的校验位。
     * 
     * @param partialNumber 不包含校验位的部分号码
     * @return 校验位（0-9）
     */
    public int generateCheckDigit(String partialNumber) {
<span class="pc bpc" id="L103" title="2 of 4 branches missed.">        if (partialNumber == null || partialNumber.isEmpty()) {</span>
<span class="nc" id="L104">            throw new IllegalArgumentException(&quot;Partial number cannot be null or empty&quot;);</span>
        }

        // 移除所有非数字字符
<span class="fc" id="L108">        String cleanNumber = partialNumber.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>

<span class="pc bpc" id="L110" title="1 of 2 branches missed.">        if (cleanNumber.isEmpty()) {</span>
<span class="nc" id="L111">            throw new IllegalArgumentException(&quot;Partial number must contain at least one digit&quot;);</span>
        }

        // 在末尾添加一个0作为临时校验位
<span class="fc" id="L115">        String tempNumber = cleanNumber + &quot;0&quot;;</span>

        // 计算当前的校验和
<span class="fc" id="L118">        int sum = 0;</span>
<span class="fc" id="L119">        boolean alternate = false;</span>

<span class="fc bfc" id="L121" title="All 2 branches covered.">        for (int i = tempNumber.length() - 1; i &gt;= 0; i--) {</span>
<span class="fc" id="L122">            int digit = Character.getNumericValue(tempNumber.charAt(i));</span>

<span class="fc bfc" id="L124" title="All 2 branches covered.">            if (alternate) {</span>
<span class="fc" id="L125">                digit *= 2;</span>
<span class="fc bfc" id="L126" title="All 2 branches covered.">                if (digit &gt; 9) {</span>
<span class="fc" id="L127">                    digit = (digit % 10) + 1;</span>
                }
            }

<span class="fc" id="L131">            sum += digit;</span>
<span class="fc bfc" id="L132" title="All 2 branches covered.">            alternate = !alternate;</span>
        }

        // 计算需要的校验位
<span class="fc" id="L136">        int checkDigit = (10 - (sum % 10)) % 10;</span>

<span class="fc" id="L138">        logger.debug(&quot;Generated Luhn check digit {} for partial number: {}&quot;,</span>
<span class="fc" id="L139">                checkDigit, maskSensitiveData(partialNumber));</span>

<span class="fc" id="L141">        return checkDigit;</span>
    }

    /**
     * 生成完整的符合Luhn算法的号码。
     * 
     * @param partialNumber 不包含校验位的部分号码
     * @return 包含校验位的完整号码
     */
    public String generateValidNumber(String partialNumber) {
<span class="nc" id="L151">        int checkDigit = generateCheckDigit(partialNumber);</span>
<span class="nc" id="L152">        String cleanNumber = partialNumber.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>
<span class="nc" id="L153">        return cleanNumber + checkDigit;</span>
    }

    /**
     * 校验银行卡号。
     * 
     * &lt;p&gt;
     * 银行卡号通常为13-19位数字，最后一位为校验位。
     * 
     * @param cardNumber 银行卡号
     * @return 校验结果
     */
    public ValidationResult validateBankCard(String cardNumber) {
<span class="nc bnc" id="L166" title="All 2 branches missed.">        if (cardNumber == null) {</span>
<span class="nc" id="L167">            return ValidationResult.failure(&quot;Bank card number cannot be null&quot;);</span>
        }

<span class="nc" id="L170">        String cleanNumber = cardNumber.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>

<span class="nc bnc" id="L172" title="All 4 branches missed.">        if (cleanNumber.length() &lt; 13 || cleanNumber.length() &gt; 19) {</span>
<span class="nc" id="L173">            return ValidationResult.failure(&quot;Bank card number must be 13-19 digits long&quot;);</span>
        }

<span class="nc" id="L176">        return validate(cleanNumber);</span>
    }

    /**
     * 校验IMEI号。
     * 
     * &lt;p&gt;
     * IMEI号为15位数字，最后一位为校验位。
     * 
     * @param imei IMEI号
     * @return 校验结果
     */
    public ValidationResult validateIMEI(String imei) {
<span class="nc bnc" id="L189" title="All 2 branches missed.">        if (imei == null) {</span>
<span class="nc" id="L190">            return ValidationResult.failure(&quot;IMEI cannot be null&quot;);</span>
        }

<span class="nc" id="L193">        String cleanIMEI = imei.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>

<span class="nc bnc" id="L195" title="All 2 branches missed.">        if (cleanIMEI.length() != 15) {</span>
<span class="nc" id="L196">            return ValidationResult.failure(&quot;IMEI must be exactly 15 digits long&quot;);</span>
        }

<span class="nc" id="L199">        return validate(cleanIMEI);</span>
    }

    /**
     * 掩码敏感数据用于日志记录。
     * 
     * @param data 原始数据
     * @return 掩码后的数据
     */
    private String maskSensitiveData(String data) {
<span class="pc bpc" id="L209" title="2 of 4 branches missed.">        if (data == null || data.length() &lt;= 4) {</span>
<span class="nc" id="L210">            return &quot;****&quot;;</span>
        }

<span class="fc" id="L213">        String cleanData = data.replaceAll(&quot;\\D&quot;, &quot;&quot;);</span>
<span class="pc bpc" id="L214" title="1 of 2 branches missed.">        if (cleanData.length() &lt;= 4) {</span>
<span class="nc" id="L215">            return &quot;****&quot;;</span>
        }

        // 显示前4位和后4位，中间用*代替
<span class="fc" id="L219">        String prefix = cleanData.substring(0, 4);</span>
<span class="fc" id="L220">        String suffix = cleanData.substring(cleanData.length() - 4);</span>
<span class="fc" id="L221">        int maskLength = cleanData.length() - 8;</span>
<span class="fc" id="L222">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="fc" id="L224">        return prefix + mask + suffix;</span>
    }

    @Override
    public String getName() {
<span class="nc" id="L229">        return &quot;Luhn&quot;;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L234">        return &quot;Luhn algorithm validator for bank cards, IMEI numbers and other identification numbers&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>