<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OutputStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.io</a> &gt; <span class="el_source">OutputStrategy.java</span></div><h1>OutputStrategy.java</h1><pre class="source lang-java linenums">package com.dataforge.io;

import com.dataforge.config.OutputConfig;
import java.util.List;
import java.util.Map;

/**
 * 数据输出策略接口。
 * 
 * &lt;p&gt;
 * 定义了数据输出的统一契约，支持多种输出格式和目标。
 * 所有具体的输出实现都必须实现此接口。
 * 
 * &lt;p&gt;
 * 该接口采用策略模式设计，使得输出格式的切换变得简单灵活。
 * 支持流式输出，适合处理大量数据的场景。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface OutputStrategy {

    /**
     * 获取输出策略支持的格式类型。
     * 
     * @return 支持的输出格式
     */
    OutputConfig.Format getSupportedFormat();

    /**
     * 初始化输出策略。
     * 
     * &lt;p&gt;
     * 在开始输出数据前调用，用于准备输出环境，
     * 如创建文件、写入头部信息等。
     * 
     * @param config     输出配置
     * @param fieldNames 字段名称列表，用于生成表头或结构定义
     * @throws OutputException 当初始化失败时
     */
    void initialize(OutputConfig config, List&lt;String&gt; fieldNames) throws OutputException;

    /**
     * 输出单条记录。
     * 
     * &lt;p&gt;
     * 将一条记录的数据输出到目标位置。
     * 该方法会被多次调用，每次处理一条记录。
     * 
     * @param record 记录数据，键为字段名，值为字段值
     * @throws OutputException 当输出失败时
     */
    void writeRecord(Map&lt;String, Object&gt; record) throws OutputException;

    /**
     * 批量输出多条记录。
     * 
     * &lt;p&gt;
     * 默认实现是循环调用writeRecord方法。
     * 子类可以重写此方法以提供更高效的批量输出实现。
     * 
     * @param records 记录列表
     * @throws OutputException 当输出失败时
     */
    default void writeRecords(List&lt;Map&lt;String, Object&gt;&gt; records) throws OutputException {
<span class="nc bnc" id="L66" title="All 2 branches missed.">        for (Map&lt;String, Object&gt; record : records) {</span>
<span class="nc" id="L67">            writeRecord(record);</span>
<span class="nc" id="L68">        }</span>
<span class="nc" id="L69">    }</span>

    /**
     * 完成输出并清理资源。
     * 
     * &lt;p&gt;
     * 在所有数据输出完成后调用，用于写入尾部信息、
     * 关闭文件流、清理临时资源等。
     * 
     * @throws OutputException 当完成操作失败时
     */
    void finish() throws OutputException;

    /**
     * 检查输出策略是否支持指定的配置。
     * 
     * &lt;p&gt;
     * 默认实现检查格式是否匹配。
     * 子类可以重写此方法以提供更详细的配置验证。
     * 
     * @param config 输出配置
     * @return 如果支持返回true，否则返回false
     */
    default boolean supports(OutputConfig config) {
<span class="nc bnc" id="L93" title="All 4 branches missed.">        return config != null &amp;&amp; getSupportedFormat() == config.getFormat();</span>
    }

    /**
     * 获取输出策略的描述信息。
     * 
     * &lt;p&gt;
     * 用于日志记录和调试信息。
     * 
     * @return 策略描述
     */
    default String getDescription() {
<span class="nc" id="L105">        return &quot;Output strategy for &quot; + getSupportedFormat() + &quot; format&quot;;</span>
    }

    /**
     * 刷新输出缓冲区。
     * 
     * &lt;p&gt;
     * 强制将缓冲区中的数据写入到目标位置。
     * 默认实现为空，子类可以根据需要重写。
     * 
     * @throws OutputException 当刷新失败时
     */
    default void flush() throws OutputException {
        // 默认实现为空
<span class="nc" id="L119">    }</span>

    /**
     * 获取当前已输出的记录数量。
     * 
     * &lt;p&gt;
     * 用于进度跟踪和统计信息。
     * 默认返回-1表示不支持计数。
     * 
     * @return 已输出的记录数量，-1表示不支持计数
     */
    default long getWrittenRecordCount() {
<span class="nc" id="L131">        return -1;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>