<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EmailGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">EmailGenerator</span></div><h1>EmailGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,100 of 1,100</td><td class="ctr2">0%</td><td class="bar">89 of 89</td><td class="ctr2">0%</td><td class="ctr1">76</td><td class="ctr2">76</td><td class="ctr1">209</td><td class="ctr2">209</td><td class="ctr1">30</td><td class="ctr2">30</td></tr></tfoot><tbody><tr><td id="a29"><a href="EmailGenerator.java.html#L29" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="263" alt="263"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h0">43</td><td class="ctr2" id="i0">43</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a24"><a href="EmailGenerator.java.html#L182" class="el_method">loadData(FieldConfig)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="124" alt="124"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">26</td><td class="ctr2" id="i1">26</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="EmailGenerator.java.html#L285" class="el_method">generateUsername(int[], boolean, DataForgeContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="93" alt="93"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h2">18</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a17"><a href="EmailGenerator.java.html#L560" class="el_method">getDomainStats()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="90" alt="90"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="EmailGenerator.java.html#L122" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="61" alt="61"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h3">14</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a20"><a href="EmailGenerator.java.html#L242" class="el_method">initializeFallbackData()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="52" alt="52"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a26"><a href="EmailGenerator.java.html#L509" class="el_method">parseLengthRange(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="52" alt="52"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h6">9</td><td class="ctr2" id="i6">9</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="EmailGenerator.java.html#L470" class="el_method">generateEmailWithInvalidChars()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="49" alt="49"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h16">3</td><td class="ctr2" id="i16">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="EmailGenerator.java.html#L333" class="el_method">extractNamePrefix(DataForgeContext)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="44" alt="44"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h7">9</td><td class="ctr2" id="i7">9</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="EmailGenerator.java.html#L384" class="el_method">extractChineseNamePrefix(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="44" alt="44"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h10">7</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a28"><a href="EmailGenerator.java.html#L407" class="el_method">selectDomain(List, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="36" alt="36"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h12">6</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a3"><a href="EmailGenerator.java.html#L361" class="el_method">extractEnglishNamePrefix(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="35" alt="35"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a14"><a href="EmailGenerator.java.html#L538" class="el_method">getBooleanParam(FieldConfig, String, boolean)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="27" alt="27"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a11"><a href="EmailGenerator.java.html#L426" class="el_method">generateInvalidEmail()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="21" alt="21"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="5" alt="5"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a25"><a href="EmailGenerator.java.html#L491" class="el_method">parseDomains(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="20" alt="20"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h13">6</td><td class="ctr2" id="i13">6</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a18"><a href="EmailGenerator.java.html#L526" class="el_method">getStringParam(FieldConfig, String, String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="19" alt="19"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h15">4</td><td class="ctr2" id="i15">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a1"><a href="EmailGenerator.java.html#L165" class="el_method">ensureDataLoaded(FieldConfig)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="17" alt="17"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h14">6</td><td class="ctr2" id="i14">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a13"><a href="EmailGenerator.java.html#L268" class="el_method">generateValidEmail(List, int[], boolean, String, DataForgeContext)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="15" alt="15"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h17">3</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a23"><a href="EmailGenerator.java.html#L497" class="el_method">lambda$parseDomains$2(String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a27"><a href="EmailGenerator.java.html#L232" class="el_method">parseWeight(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h18">3</td><td class="ctr2" id="i18">3</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a21"><a href="EmailGenerator.java.html#L249" class="el_method">lambda$initializeFallbackData$1(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a22"><a href="EmailGenerator.java.html#L204" class="el_method">lambda$loadData$0(String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a0"><a href="EmailGenerator.java.html#L27" class="el_method">EmailGenerator()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a19"><a href="EmailGenerator.java.html#L115" class="el_method">getType()</a></td><td class="bar" id="b23"/><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a15"><a href="EmailGenerator.java.html#L156" class="el_method">getConfigClass()</a></td><td class="bar" id="b24"/><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a8"><a href="EmailGenerator.java.html#L443" class="el_method">generateEmailWithoutAt()</a></td><td class="bar" id="b25"/><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a9"><a href="EmailGenerator.java.html#L452" class="el_method">generateEmailWithoutDomain()</a></td><td class="bar" id="b26"/><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a10"><a href="EmailGenerator.java.html#L461" class="el_method">generateEmailWithoutUsername()</a></td><td class="bar" id="b27"/><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a7"><a href="EmailGenerator.java.html#L481" class="el_method">generateEmailWithMultipleAt()</a></td><td class="bar" id="b28"/><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a16"><a href="EmailGenerator.java.html#L585" class="el_method">getDescription()</a></td><td class="bar" id="b29"/><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>