package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.model.FieldConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网络设备类生成器测试
 */
class NetworkDeviceGeneratorsTest {

    private DataForgeContext context;

    @BeforeEach
    void setUp() {
        context = new DataForgeContext();
    }

    @Test
    void testDeviceIdGenerator() {
        DeviceIdGenerator generator = new DeviceIdGenerator();
        
        // 测试UUID格式
        FieldConfig uuidConfig = createConfig("type", "UUID");
        String uuid = generator.generate(uuidConfig, context);
        assertNotNull(uuid);
        assertEquals(32, uuid.length()); // UUID without hyphens
        
        // 测试IMEI格式
        FieldConfig imeiConfig = createConfig("type", "IMEI");
        String imei = generator.generate(imeiConfig, context);
        assertNotNull(imei);
        assertEquals(15, imei.length());
        assertTrue(imei.matches("\\d{15}"));
        
        // 测试IMSI格式
        FieldConfig imsiConfig = createConfig("type", "IMSI");
        String imsi = generator.generate(imsiConfig, context);
        assertNotNull(imsi);
        assertEquals(15, imsi.length());
        assertTrue(imsi.matches("\\d{15}"));
        
        // 测试自定义格式
        FieldConfig customConfig = createConfig("type", "CUSTOM", "length", "20");
        String custom = generator.generate(customConfig, context);
        assertNotNull(custom);
        assertEquals(20, custom.length());
        assertTrue(custom.matches("[0-9A-F]{20}"));
    }

    @Test
    void testUserAgentGenerator() {
        UserAgentGenerator generator = new UserAgentGenerator();
        
        // 测试Chrome浏览器
        FieldConfig chromeConfig = createConfig("browser", "CHROME", "os", "WINDOWS");
        String chromeUA = generator.generate(chromeConfig, context);
        assertNotNull(chromeUA);
        assertTrue(chromeUA.contains("Chrome"));
        assertTrue(chromeUA.contains("Windows"));
        
        // 测试移动端
        FieldConfig mobileConfig = createConfig("browser", "CHROME", "os", "ANDROID", "device", "MOBILE");
        String mobileUA = generator.generate(mobileConfig, context);
        assertNotNull(mobileUA);
        assertTrue(mobileUA.contains("Mobile") || mobileUA.contains("Android"));
        
        // 测试随机生成
        FieldConfig randomConfig = createConfig("browser", "RANDOM");
        String randomUA = generator.generate(randomConfig, context);
        assertNotNull(randomUA);
        assertTrue(randomUA.contains("Mozilla"));
    }

    @Test
    void testGeolocationGenerator() {
        GeolocationGenerator generator = new GeolocationGenerator();
        
        // 测试小数格式
        FieldConfig decimalConfig = createConfig("format", "DECIMAL", "precision", "6");
        String decimal = generator.generate(decimalConfig, context);
        assertNotNull(decimal);
        assertTrue(decimal.matches("-?\\d+\\.\\d+,-?\\d+\\.\\d+") || decimal.matches("\\d+\\.\\d+,\\d+\\.\\d+"));
        
        // 测试JSON格式
        FieldConfig jsonConfig = createConfig("format", "JSON", "include_altitude", "true");
        String json = generator.generate(jsonConfig, context);
        assertNotNull(json);
        assertTrue(json.startsWith("{"));
        assertTrue(json.contains("latitude"));
        assertTrue(json.contains("longitude"));
        assertTrue(json.contains("altitude"));
        
        // 测试区域生成
        FieldConfig regionConfig = createConfig("region", "CHINA", "format", "DECIMAL");
        String china = generator.generate(regionConfig, context);
        assertNotNull(china);
        String[] coords = china.split(",");
        double lat = Double.parseDouble(coords[0]);
        double lon = Double.parseDouble(coords[1]);
        assertTrue(lat >= 18.0 && lat <= 54.0); // 中国纬度范围
        assertTrue(lon >= 73.0 && lon <= 135.0); // 中国经度范围
    }

    @Test
    void testTimezoneGenerator() {
        TimezoneGenerator generator = new TimezoneGenerator();
        
        // 测试IANA格式
        FieldConfig ianaConfig = createConfig("format", "IANA", "region", "ASIA");
        String iana = generator.generate(ianaConfig, context);
        assertNotNull(iana);
        assertTrue(iana.contains("/") || iana.equals("UTC")); // UTC是有效的fallback
        
        // 测试偏移量格式
        FieldConfig offsetConfig = createConfig("format", "OFFSET");
        String offset = generator.generate(offsetConfig, context);
        assertNotNull(offset);
        assertTrue(offset.matches("[+-]\\d{2}:\\d{2}") || offset.equals("Z"));
        
        // 测试显示名称
        FieldConfig displayConfig = createConfig("format", "DISPLAY_NAME", "locale", "en");
        String display = generator.generate(displayConfig, context);
        assertNotNull(display);
        assertFalse(display.isEmpty());
    }

    @Test
    void testCookieGenerator() {
        CookieGenerator generator = new CookieGenerator();
        
        // 测试HTTP头格式
        FieldConfig headerConfig = createConfig("format", "HEADER", "name", "test_cookie");
        String header = generator.generate(headerConfig, context);
        assertNotNull(header);
        assertTrue(header.startsWith("test_cookie="));
        
        // 测试JSON格式
        FieldConfig jsonConfig = createConfig("format", "JSON", "value_type", "UUID");
        String json = generator.generate(jsonConfig, context);
        assertNotNull(json);
        assertTrue(json.startsWith("{"));
        assertTrue(json.contains("name"));
        assertTrue(json.contains("value"));
        
        // 测试简单格式
        FieldConfig simpleConfig = createConfig("format", "SIMPLE");
        String simple = generator.generate(simpleConfig, context);
        assertNotNull(simple);
        assertTrue(simple.contains("="));
        assertFalse(simple.contains(";"));
    }

    @Test
    void testGeneratorTypes() {
        assertEquals("device_id", new DeviceIdGenerator().getType());
        assertEquals("user_agent", new UserAgentGenerator().getType());
        assertEquals("geolocation", new GeolocationGenerator().getType());
        assertEquals("timezone", new TimezoneGenerator().getType());
        assertEquals("cookie", new CookieGenerator().getType());
    }

    @Test
    void testGeneratorConfigClasses() {
        assertEquals(FieldConfig.class, new DeviceIdGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new UserAgentGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new GeolocationGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new TimezoneGenerator().getConfigClass());
        assertEquals(FieldConfig.class, new CookieGenerator().getConfigClass());
    }

    private FieldConfig createConfig(String... keyValues) {
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            params.put(keyValues[i], keyValues[i + 1]);
        }

        return new TestFieldConfig("test_field", "test", params);
    }

    /**
     * 测试用的FieldConfig实现类。
     */
    private static class TestFieldConfig extends FieldConfig {
        public TestFieldConfig(String name, String type, Map<String, Object> params) {
            super(name, type);
            setParams(params);
        }
    }
}
