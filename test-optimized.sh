#!/bin/bash

# DataForge 优化后生成器测试脚本

echo "=== DataForge 优化后生成器测试 ==="
echo

# 构建项目
echo "1. 构建项目..."
./build.sh
if [ $? -ne 0 ]; then
    echo "构建失败，退出测试"
    exit 1
fi

echo
echo "2. 测试优化后的生成器..."

# 创建输出目录
mkdir -p output

# 测试优化后的生成器
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --config examples/optimized-generators-test.yml

echo
echo "3. 检查生成结果..."

if [ -f "output/optimized-test.csv" ]; then
    echo "✅ 生成成功！查看前5行数据："
    head -5 output/optimized-test.csv
    echo
    echo "总共生成了 $(tail -n +2 output/optimized-test.csv | wc -l) 条记录"
else
    echo "❌ 生成失败，未找到输出文件"
    exit 1
fi

echo
echo "4. 测试数据统计..."

# 测试姓名生成器统计
echo "测试姓名生成器统计功能..."
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 5 \
    --fields "name:name" \
    --format console

echo
echo "=== 测试完成 ==="
echo "优化后的生成器支持："
echo "- 大规模数据文件加载"
echo "- 权重选择算法"
echo "- 数十亿唯一数据生成"
echo "- 智能数据关联"
echo "- 完整的数据校验"