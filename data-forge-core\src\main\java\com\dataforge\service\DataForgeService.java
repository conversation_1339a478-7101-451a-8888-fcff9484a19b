package com.dataforge.service;

import com.dataforge.config.FieldConfigWrapper;
import com.dataforge.config.ForgeConfig;
import com.dataforge.config.OutputConfig;
import com.dataforge.core.DataForgeContext;
import com.dataforge.core.GeneratorFactory;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.io.OutputStrategy;
import com.dataforge.model.FieldConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DataForge核心服务类。
 * 
 * <p>
 * 作为编排核心逻辑的主服务，负责协调数据生成器、输出策略和配置管理。
 * 该服务是整个数据生成流程的控制中心。
 * 
 * <p>
 * 主要职责：
 * <ul>
 * <li>解析和验证配置</li>
 * <li>协调数据生成器执行数据生成</li>
 * <li>管理数据生成上下文和字段关联</li>
 * <li>控制数据输出流程</li>
 * <li>支持并发数据生成</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
public class DataForgeService {

    private static final Logger logger = LoggerFactory.getLogger(DataForgeService.class);

    private final GeneratorFactory generatorFactory;
    private final List<OutputStrategy> outputStrategies;

    /**
     * 构造函数，注入依赖。
     * 
     * @param generatorFactory 生成器工厂
     * @param outputStrategies 输出策略列表
     */
    @Autowired
    public DataForgeService(GeneratorFactory generatorFactory, List<OutputStrategy> outputStrategies) {
        this.generatorFactory = generatorFactory;
        this.outputStrategies = outputStrategies != null ? outputStrategies : new ArrayList<>();
        logger.info("DataForgeService initialized with {} generators and {} output strategies",
                generatorFactory.getGeneratorCount(), this.outputStrategies.size());
    }

    /**
     * 根据配置生成数据。
     * 
     * <p>
     * 这是数据生成的主入口方法，负责整个生成流程的协调和控制。
     * 
     * @param config 生成配置
     * @throws DataForgeException 当生成过程中发生错误时
     */
    public void generateData(ForgeConfig config) throws DataForgeException {
        if (config == null) {
            throw new DataForgeException("Configuration cannot be null");
        }

        logger.info("Starting data generation with config: {}", config);

        try {
            // 1. 验证配置
            validateConfig(config);

            // 2. 准备输出策略
            OutputStrategy outputStrategy = prepareOutputStrategy(config.getOutput());

            // 3. 提取字段名称
            List<String> fieldNames = extractFieldNames(config.getFields());

            // 4. 初始化输出策略
            outputStrategy.initialize(config.getOutput(), fieldNames);

            // 5. 执行数据生成
            if (config.getThreads() > 1) {
                generateDataConcurrently(config, outputStrategy, fieldNames);
            } else {
                generateDataSequentially(config, outputStrategy, fieldNames);
            }

            // 6. 完成输出
            outputStrategy.finish();

            logger.info("Data generation completed successfully. Generated {} records.", config.getCount());

        } catch (Exception e) {
            logger.error("Data generation failed", e);
            if (e instanceof DataForgeException) {
                throw e;
            }
            throw new DataForgeException("Data generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * 验证配置的有效性。
     * 
     * @param config 配置对象
     * @throws DataForgeException 当配置无效时
     */
    private void validateConfig(ForgeConfig config) throws DataForgeException {
        if (!config.isValid()) {
            throw new DataForgeException("Invalid configuration: " + config);
        }

        if (config.getFields().isEmpty()) {
            throw new DataForgeException("No fields configured for data generation");
        }

        // 验证所有字段都有对应的生成器
        for (FieldConfigWrapper field : config.getFields()) {
            if (!generatorFactory.hasGenerator(field.getType())) {
                throw new DataForgeException("No generator found for field type: " + field.getType());
            }
        }

        logger.debug("Configuration validation passed");
    }

    /**
     * 准备输出策略。
     * 
     * @param outputConfig 输出配置
     * @return 匹配的输出策略
     * @throws DataForgeException 当找不到匹配的输出策略时
     */
    private OutputStrategy prepareOutputStrategy(OutputConfig outputConfig) throws DataForgeException {
        for (OutputStrategy strategy : outputStrategies) {
            if (strategy.supports(outputConfig)) {
                logger.debug("Selected output strategy: {} for format: {}",
                        strategy.getClass().getSimpleName(), outputConfig.getFormat());
                return strategy;
            }
        }

        throw new DataForgeException("No output strategy found for format: " + outputConfig.getFormat());
    }

    /**
     * 提取字段名称列表。
     * 
     * @param fields 字段配置列表
     * @return 字段名称列表
     */
    private List<String> extractFieldNames(List<FieldConfigWrapper> fields) {
        List<String> fieldNames = new ArrayList<>();
        for (FieldConfigWrapper field : fields) {
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    /**
     * 顺序生成数据。
     * 
     * @param config         生成配置
     * @param outputStrategy 输出策略
     * @param fieldNames     字段名称列表
     * @throws DataForgeException 当生成失败时
     */
    private void generateDataSequentially(ForgeConfig config, OutputStrategy outputStrategy,
            List<String> fieldNames) throws DataForgeException {

        logger.debug("Starting sequential data generation for {} records", config.getCount());

        // 初始化随机数生成器
        Random random = config.getSeed() != null ? new Random(config.getSeed()) : new Random();

        for (int i = 0; i < config.getCount(); i++) {
            try {
                // 创建新的上下文
                DataForgeContext context = new DataForgeContext();
                context.setCurrentRecordIndex(i);

                // 生成单条记录
                Map<String, Object> record = generateSingleRecord(config.getFields(), context, random);

                // 输出记录
                outputStrategy.writeRecord(record);

                // 定期刷新输出（每100条记录）
                if ((i + 1) % 100 == 0) {
                    outputStrategy.flush();
                    logger.debug("Generated {} records", i + 1);
                }

            } catch (Exception e) {
                throw new DataForgeException("Failed to generate record at index " + i, e);
            }
        }
    }

    /**
     * 并发生成数据。
     * 
     * @param config         生成配置
     * @param outputStrategy 输出策略
     * @param fieldNames     字段名称列表
     * @throws DataForgeException 当生成失败时
     */
    private void generateDataConcurrently(ForgeConfig config, OutputStrategy outputStrategy,
            List<String> fieldNames) throws DataForgeException {

        logger.debug("Starting concurrent data generation for {} records with {} threads",
                config.getCount(), config.getThreads());

        ExecutorService executor = Executors.newFixedThreadPool(config.getThreads());

        try {
            // 计算每个线程处理的记录数
            int recordsPerThread = config.getCount() / config.getThreads();
            int remainingRecords = config.getCount() % config.getThreads();

            List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();

            int startIndex = 0;
            for (int threadIndex = 0; threadIndex < config.getThreads(); threadIndex++) {
                int recordCount = recordsPerThread + (threadIndex < remainingRecords ? 1 : 0);
                int finalStartIndex = startIndex;

                // 为每个线程创建独立的随机数生成器
                Random threadRandom = config.getSeed() != null ? new Random(config.getSeed() + threadIndex)
                        : new Random();

                CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                    return generateRecordsBatch(config.getFields(), finalStartIndex, recordCount, threadRandom);
                }, executor);

                futures.add(future);
                startIndex += recordCount;
            }

            // 等待所有线程完成并收集结果
            for (CompletableFuture<List<Map<String, Object>>> future : futures) {
                List<Map<String, Object>> records = future.get();
                outputStrategy.writeRecords(records);
            }

        } catch (Exception e) {
            throw new DataForgeException("Concurrent data generation failed", e);
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 批量生成记录。
     * 
     * @param fields     字段配置列表
     * @param startIndex 起始索引
     * @param count      记录数量
     * @param random     随机数生成器
     * @return 生成的记录列表
     */
    private List<Map<String, Object>> generateRecordsBatch(List<FieldConfigWrapper> fields,
            int startIndex, int count, Random random) {
        List<Map<String, Object>> records = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            DataForgeContext context = new DataForgeContext();
            context.setCurrentRecordIndex(startIndex + i);

            Map<String, Object> record = generateSingleRecord(fields, context, random);
            records.add(record);
        }

        return records;
    }

    /**
     * 生成单条记录。
     * 
     * @param fields  字段配置列表
     * @param context 生成上下文
     * @param random  随机数生成器（可选，用于可重现的生成）
     * @return 生成的记录
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> generateSingleRecord(List<FieldConfigWrapper> fields,
            DataForgeContext context, Random random) {
        Map<String, Object> record = new HashMap<>();

        // 按字段配置顺序生成数据，确保关联性正确处理
        for (FieldConfigWrapper field : fields) {
            try {
                DataGenerator<Object, FieldConfig> generator = (DataGenerator<Object, FieldConfig>) generatorFactory
                        .getGenerator(field.getType());

                if (generator == null) {
                    logger.warn("No generator found for field type: {}, using null value", field.getType());
                    record.put(field.getName(), null);
                    continue;
                }

                // 生成字段值
                Object value = generator.generate(field, context);
                record.put(field.getName(), value);

                logger.trace("Generated field: {}={}", field.getName(), value);

            } catch (Exception e) {
                logger.error("Failed to generate field: {}", field.getName(), e);
                // 继续处理其他字段，将当前字段设为null
                record.put(field.getName(), null);
            }
        }

        return record;
    }

    /**
     * 获取可用的数据生成器类型。
     * 
     * @return 生成器类型集合
     */
    public java.util.Set<String> getAvailableGeneratorTypes() {
        return generatorFactory.getAvailableTypes();
    }

    /**
     * 获取生成器的详细信息。
     * 
     * @return 生成器信息映射
     */
    public Map<String, String> getGeneratorInfo() {
        return generatorFactory.getGeneratorInfo();
    }

    /**
     * 获取可用的输出格式。
     * 
     * @return 输出格式列表
     */
    public List<OutputConfig.Format> getAvailableOutputFormats() {
        List<OutputConfig.Format> formats = new ArrayList<>();
        for (OutputStrategy strategy : outputStrategies) {
            formats.add(strategy.getSupportedFormat());
        }
        return formats;
    }
}