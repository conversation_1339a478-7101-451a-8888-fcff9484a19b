<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PortGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">PortGenerator.java</span></div><h1>PortGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 端口号生成器
 * 
 * 支持的参数：
 * - type: 端口类型 (WELL_KNOWN|REGISTERED|DYNAMIC|COMMON|ANY)
 * - min: 最小端口号 (0-65535)
 * - max: 最大端口号 (0-65535)
 * - protocol: 协议类型 (TCP|UDP|BOTH)
 * - service: 服务类型 (HTTP|HTTPS|FTP|SSH|SMTP|DNS|ANY)
 * - exclude_reserved: 是否排除保留端口 (true|false)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class PortGenerator implements DataGenerator&lt;Integer, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(PortGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 端口范围定义
    private static final int WELL_KNOWN_MIN = 0;
    private static final int WELL_KNOWN_MAX = 1023;
    private static final int REGISTERED_MIN = 1024;
    private static final int REGISTERED_MAX = 49151;
    private static final int DYNAMIC_MIN = 49152;
    private static final int DYNAMIC_MAX = 65535;

    // 常见服务端口映射
<span class="nc" id="L38">    private static final Map&lt;String, List&lt;ServicePort&gt;&gt; SERVICE_PORTS = new HashMap&lt;&gt;();</span>

    // 常用端口列表
<span class="nc" id="L41">    private static final List&lt;Integer&gt; COMMON_PORTS = Arrays.asList(</span>
<span class="nc" id="L42">            21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995,</span>
<span class="nc" id="L43">            3000, 3306, 5432, 6379, 8000, 8080, 8443, 8888, 9000);</span>

    // 保留端口列表（通常不应该使用）
<span class="nc" id="L46">    private static final Set&lt;Integer&gt; RESERVED_PORTS = new HashSet&lt;&gt;(Arrays.asList(</span>
<span class="nc" id="L47">            0, 7, 9, 13, 17, 19, 37, 42, 43, 67, 68, 69, 70, 79, 87, 95, 101, 102, 103, 104, 109, 111, 113, 115, 117,</span>
<span class="nc" id="L48">            119, 123, 135, 137, 138, 139, 143, 161, 162, 163, 164, 174, 177, 178, 179, 191, 194, 199, 201, 202, 204,</span>
<span class="nc" id="L49">            206, 209, 210, 213, 220, 245, 347, 363, 369, 370, 372, 389, 427, 434, 435, 443, 444, 445, 464, 465, 497,</span>
<span class="nc" id="L50">            500, 512, 513, 514, 515, 526, 530, 531, 532, 533, 540, 556, 563, 587, 601, 636, 639, 646, 647, 648, 652,</span>
<span class="nc" id="L51">            654, 665, 666, 674, 691, 692, 695, 696, 704, 711, 712, 720, 749, 750, 751, 752, 754, 760, 782, 783, 829,</span>
<span class="nc" id="L52">            860, 873, 888, 898, 900, 901, 902, 903, 911, 912, 981, 987, 990, 991, 992, 993, 995, 996, 997, 998, 999));</span>

    // 服务端口信息类
    private static class ServicePort {
        final int port;
        final String protocol;
        final String description;

<span class="nc" id="L60">        ServicePort(int port, String protocol, String description) {</span>
<span class="nc" id="L61">            this.port = port;</span>
<span class="nc" id="L62">            this.protocol = protocol;</span>
<span class="nc" id="L63">            this.description = description;</span>
<span class="nc" id="L64">        }</span>
    }

    static {
<span class="nc" id="L68">        initializeServicePorts();</span>
<span class="nc" id="L69">    }</span>

    private static void initializeServicePorts() {
        // HTTP服务
<span class="nc" id="L73">        SERVICE_PORTS.put(&quot;HTTP&quot;, Arrays.asList(</span>
                new ServicePort(80, &quot;TCP&quot;, &quot;HTTP&quot;),
                new ServicePort(8080, &quot;TCP&quot;, &quot;HTTP Alternate&quot;),
                new ServicePort(8000, &quot;TCP&quot;, &quot;HTTP Alternate&quot;),
                new ServicePort(3000, &quot;TCP&quot;, &quot;HTTP Development&quot;),
                new ServicePort(8888, &quot;TCP&quot;, &quot;HTTP Alternate&quot;)));

        // HTTPS服务
<span class="nc" id="L81">        SERVICE_PORTS.put(&quot;HTTPS&quot;, Arrays.asList(</span>
                new ServicePort(443, &quot;TCP&quot;, &quot;HTTPS&quot;),
                new ServicePort(8443, &quot;TCP&quot;, &quot;HTTPS Alternate&quot;),
                new ServicePort(9443, &quot;TCP&quot;, &quot;HTTPS Alternate&quot;)));

        // FTP服务
<span class="nc" id="L87">        SERVICE_PORTS.put(&quot;FTP&quot;, Arrays.asList(</span>
                new ServicePort(21, &quot;TCP&quot;, &quot;FTP Control&quot;),
                new ServicePort(20, &quot;TCP&quot;, &quot;FTP Data&quot;),
                new ServicePort(990, &quot;TCP&quot;, &quot;FTPS&quot;)));

        // SSH服务
<span class="nc" id="L93">        SERVICE_PORTS.put(&quot;SSH&quot;, Arrays.asList(</span>
                new ServicePort(22, &quot;TCP&quot;, &quot;SSH&quot;),
                new ServicePort(2222, &quot;TCP&quot;, &quot;SSH Alternate&quot;)));

        // SMTP服务
<span class="nc" id="L98">        SERVICE_PORTS.put(&quot;SMTP&quot;, Arrays.asList(</span>
                new ServicePort(25, &quot;TCP&quot;, &quot;SMTP&quot;),
                new ServicePort(587, &quot;TCP&quot;, &quot;SMTP Submission&quot;),
                new ServicePort(465, &quot;TCP&quot;, &quot;SMTPS&quot;)));

        // DNS服务
<span class="nc" id="L104">        SERVICE_PORTS.put(&quot;DNS&quot;, Arrays.asList(</span>
                new ServicePort(53, &quot;UDP&quot;, &quot;DNS&quot;),
                new ServicePort(53, &quot;TCP&quot;, &quot;DNS&quot;)));

        // 数据库服务
<span class="nc" id="L109">        SERVICE_PORTS.put(&quot;DATABASE&quot;, Arrays.asList(</span>
                new ServicePort(3306, &quot;TCP&quot;, &quot;MySQL&quot;),
                new ServicePort(5432, &quot;TCP&quot;, &quot;PostgreSQL&quot;),
                new ServicePort(1433, &quot;TCP&quot;, &quot;SQL Server&quot;),
                new ServicePort(1521, &quot;TCP&quot;, &quot;Oracle&quot;),
                new ServicePort(27017, &quot;TCP&quot;, &quot;MongoDB&quot;),
                new ServicePort(6379, &quot;TCP&quot;, &quot;Redis&quot;)));

        // Web服务器
<span class="nc" id="L118">        SERVICE_PORTS.put(&quot;WEBSERVER&quot;, Arrays.asList(</span>
                new ServicePort(80, &quot;TCP&quot;, &quot;Apache/Nginx&quot;),
                new ServicePort(443, &quot;TCP&quot;, &quot;Apache/Nginx HTTPS&quot;),
                new ServicePort(8080, &quot;TCP&quot;, &quot;Tomcat&quot;),
                new ServicePort(9000, &quot;TCP&quot;, &quot;PHP-FPM&quot;),
                new ServicePort(3000, &quot;TCP&quot;, &quot;Node.js&quot;)));

        // 邮件服务
<span class="nc" id="L126">        SERVICE_PORTS.put(&quot;MAIL&quot;, Arrays.asList(</span>
                new ServicePort(25, &quot;TCP&quot;, &quot;SMTP&quot;),
                new ServicePort(110, &quot;TCP&quot;, &quot;POP3&quot;),
                new ServicePort(143, &quot;TCP&quot;, &quot;IMAP&quot;),
                new ServicePort(993, &quot;TCP&quot;, &quot;IMAPS&quot;),
                new ServicePort(995, &quot;TCP&quot;, &quot;POP3S&quot;)));

        // 游戏服务
<span class="nc" id="L134">        SERVICE_PORTS.put(&quot;GAMING&quot;, Arrays.asList(</span>
                new ServicePort(25565, &quot;TCP&quot;, &quot;Minecraft&quot;),
                new ServicePort(27015, &quot;UDP&quot;, &quot;Steam&quot;),
                new ServicePort(3724, &quot;TCP&quot;, &quot;World of Warcraft&quot;),
                new ServicePort(6112, &quot;TCP&quot;, &quot;Battle.net&quot;)));
<span class="nc" id="L139">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L143">        return &quot;port&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L148">        return FieldConfig.class;</span>
    }

    @Override
    public Integer generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L155">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L156">            int min = Integer.parseInt(config.getParam(&quot;min&quot;, String.class, &quot;0&quot;));</span>
<span class="nc" id="L157">            int max = Integer.parseInt(config.getParam(&quot;max&quot;, String.class, &quot;65535&quot;));</span>
<span class="nc" id="L158">            String protocol = config.getParam(&quot;protocol&quot;, String.class, &quot;BOTH&quot;);</span>
<span class="nc" id="L159">            String service = config.getParam(&quot;service&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L160">            boolean excludeReserved = Boolean.parseBoolean(config.getParam(&quot;exclude_reserved&quot;, String.class, &quot;true&quot;));</span>

            // 生成端口号
<span class="nc" id="L163">            int port = generatePort(type, min, max, protocol, service, excludeReserved);</span>

            // 将端口信息存入上下文
<span class="nc" id="L166">            context.put(&quot;port&quot;, port);</span>
<span class="nc" id="L167">            context.put(&quot;port_type&quot;, determinePortType(port));</span>
<span class="nc" id="L168">            context.put(&quot;port_protocol&quot;, protocol);</span>
<span class="nc" id="L169">            context.put(&quot;port_service&quot;, getServiceName(port));</span>

<span class="nc" id="L171">            logger.debug(&quot;Generated port: {}&quot;, port);</span>
<span class="nc" id="L172">            return port;</span>

<span class="nc" id="L174">        } catch (Exception e) {</span>
<span class="nc" id="L175">            logger.error(&quot;Error generating port&quot;, e);</span>
<span class="nc" id="L176">            return 8080;</span>
        }
    }

    private int generatePort(String type, int min, int max, String protocol, String service, boolean excludeReserved) {
        // 处理服务类型
<span class="nc bnc" id="L182" title="All 2 branches missed.">        if (!&quot;ANY&quot;.equalsIgnoreCase(service)) {</span>
<span class="nc" id="L183">            return generateServicePort(service, protocol);</span>
        }

        // 处理端口类型
<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (&quot;COMMON&quot;.equalsIgnoreCase(type)) {</span>
<span class="nc" id="L188">            return generateCommonPort(excludeReserved);</span>
        }

        // 确定端口范围
<span class="nc" id="L192">        int[] range = determinePortRange(type, min, max);</span>
<span class="nc" id="L193">        int finalMin = range[0];</span>
<span class="nc" id="L194">        int finalMax = range[1];</span>

        // 生成端口
        int port;
<span class="nc" id="L198">        int attempts = 0;</span>
        do {
<span class="nc" id="L200">            port = finalMin + random.nextInt(finalMax - finalMin + 1);</span>
<span class="nc" id="L201">            attempts++;</span>
<span class="nc bnc" id="L202" title="All 6 branches missed.">        } while (excludeReserved &amp;&amp; RESERVED_PORTS.contains(port) &amp;&amp; attempts &lt; 100);</span>

<span class="nc" id="L204">        return port;</span>
    }

    private int generateServicePort(String service, String protocol) {
<span class="nc" id="L208">        List&lt;ServicePort&gt; servicePorts = SERVICE_PORTS.get(service.toUpperCase());</span>
<span class="nc bnc" id="L209" title="All 4 branches missed.">        if (servicePorts == null || servicePorts.isEmpty()) {</span>
            // 如果服务不存在，返回常用端口
<span class="nc" id="L211">            return COMMON_PORTS.get(random.nextInt(COMMON_PORTS.size()));</span>
        }

        // 过滤协议
<span class="nc" id="L215">        List&lt;ServicePort&gt; filteredPorts = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">        for (ServicePort servicePort : servicePorts) {</span>
<span class="nc bnc" id="L217" title="All 2 branches missed.">            if (&quot;BOTH&quot;.equalsIgnoreCase(protocol) ||</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">                    protocol.equalsIgnoreCase(servicePort.protocol)) {</span>
<span class="nc" id="L219">                filteredPorts.add(servicePort);</span>
            }
<span class="nc" id="L221">        }</span>

<span class="nc bnc" id="L223" title="All 2 branches missed.">        if (filteredPorts.isEmpty()) {</span>
<span class="nc" id="L224">            filteredPorts = servicePorts; // 如果过滤后为空，使用所有端口</span>
        }

<span class="nc" id="L227">        ServicePort selectedPort = filteredPorts.get(random.nextInt(filteredPorts.size()));</span>
<span class="nc" id="L228">        return selectedPort.port;</span>
    }

    private int generateCommonPort(boolean excludeReserved) {
<span class="nc" id="L232">        List&lt;Integer&gt; availablePorts = new ArrayList&lt;&gt;(COMMON_PORTS);</span>

<span class="nc bnc" id="L234" title="All 2 branches missed.">        if (excludeReserved) {</span>
<span class="nc" id="L235">            availablePorts.removeAll(RESERVED_PORTS);</span>
        }

<span class="nc bnc" id="L238" title="All 2 branches missed.">        if (availablePorts.isEmpty()) {</span>
<span class="nc" id="L239">            return 8080; // 默认端口</span>
        }

<span class="nc" id="L242">        return availablePorts.get(random.nextInt(availablePorts.size()));</span>
    }

    private int[] determinePortRange(String type, int min, int max) {
<span class="nc bnc" id="L246" title="All 4 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;WELL_KNOWN&quot;:
<span class="nc" id="L248">                return new int[] { Math.max(min, WELL_KNOWN_MIN), Math.min(max, WELL_KNOWN_MAX) };</span>

            case &quot;REGISTERED&quot;:
<span class="nc" id="L251">                return new int[] { Math.max(min, REGISTERED_MIN), Math.min(max, REGISTERED_MAX) };</span>

            case &quot;DYNAMIC&quot;:
<span class="nc" id="L254">                return new int[] { Math.max(min, DYNAMIC_MIN), Math.min(max, DYNAMIC_MAX) };</span>

            case &quot;ANY&quot;:
            default:
<span class="nc" id="L258">                return new int[] { Math.max(min, 0), Math.min(max, 65535) };</span>
        }
    }

    private String determinePortType(int port) {
<span class="nc bnc" id="L263" title="All 4 branches missed.">        if (port &gt;= WELL_KNOWN_MIN &amp;&amp; port &lt;= WELL_KNOWN_MAX) {</span>
<span class="nc" id="L264">            return &quot;WELL_KNOWN&quot;;</span>
<span class="nc bnc" id="L265" title="All 4 branches missed.">        } else if (port &gt;= REGISTERED_MIN &amp;&amp; port &lt;= REGISTERED_MAX) {</span>
<span class="nc" id="L266">            return &quot;REGISTERED&quot;;</span>
<span class="nc bnc" id="L267" title="All 4 branches missed.">        } else if (port &gt;= DYNAMIC_MIN &amp;&amp; port &lt;= DYNAMIC_MAX) {</span>
<span class="nc" id="L268">            return &quot;DYNAMIC&quot;;</span>
        } else {
<span class="nc" id="L270">            return &quot;UNKNOWN&quot;;</span>
        }
    }

    private String getServiceName(int port) {
        // 查找端口对应的服务名
<span class="nc bnc" id="L276" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;ServicePort&gt;&gt; entry : SERVICE_PORTS.entrySet()) {</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">            for (ServicePort servicePort : entry.getValue()) {</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">                if (servicePort.port == port) {</span>
<span class="nc" id="L279">                    return entry.getKey() + &quot; (&quot; + servicePort.description + &quot;)&quot;;</span>
                }
<span class="nc" id="L281">            }</span>
<span class="nc" id="L282">        }</span>

        // 检查是否为常用端口
<span class="nc bnc" id="L285" title="All 2 branches missed.">        if (COMMON_PORTS.contains(port)) {</span>
<span class="nc" id="L286">            return &quot;COMMON&quot;;</span>
        }

<span class="nc" id="L289">        return &quot;UNKNOWN&quot;;</span>
    }

    /**
     * 验证端口号是否有效
     */
    public boolean validatePort(int port) {
<span class="nc bnc" id="L296" title="All 4 branches missed.">        return port &gt;= 0 &amp;&amp; port &lt;= 65535;</span>
    }

    /**
     * 检查端口是否为保留端口
     */
    public boolean isReservedPort(int port) {
<span class="nc" id="L303">        return RESERVED_PORTS.contains(port);</span>
    }

    /**
     * 检查端口是否为知名端口
     */
    public boolean isWellKnownPort(int port) {
<span class="nc bnc" id="L310" title="All 4 branches missed.">        return port &gt;= WELL_KNOWN_MIN &amp;&amp; port &lt;= WELL_KNOWN_MAX;</span>
    }

    /**
     * 生成端口范围
     */
    public String generatePortRange(int count) {
<span class="nc" id="L317">        List&lt;Integer&gt; ports = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L318">        Set&lt;Integer&gt; usedPorts = new HashSet&lt;&gt;();</span>

<span class="nc bnc" id="L320" title="All 2 branches missed.">        while (ports.size() &lt; count) {</span>
<span class="nc" id="L321">            int port = generatePort(&quot;ANY&quot;, 1024, 65535, &quot;BOTH&quot;, &quot;ANY&quot;, true);</span>
<span class="nc bnc" id="L322" title="All 2 branches missed.">            if (!usedPorts.contains(port)) {</span>
<span class="nc" id="L323">                ports.add(port);</span>
<span class="nc" id="L324">                usedPorts.add(port);</span>
            }
<span class="nc" id="L326">        }</span>

<span class="nc" id="L328">        Collections.sort(ports);</span>

<span class="nc bnc" id="L330" title="All 2 branches missed.">        if (count == 2) {</span>
<span class="nc" id="L331">            return ports.get(0) + &quot;-&quot; + ports.get(1);</span>
        } else {
<span class="nc" id="L333">            return ports.toString().replaceAll(&quot;[\\[\\]]&quot;, &quot;&quot;);</span>
        }
    }

    /**
     * 生成防火墙规则端口
     */
    public String generateFirewallPort() {
        // 防火墙规则通常使用常见服务端口
<span class="nc" id="L342">        String[] services = { &quot;HTTP&quot;, &quot;HTTPS&quot;, &quot;SSH&quot;, &quot;FTP&quot;, &quot;SMTP&quot;, &quot;DNS&quot; };</span>
<span class="nc" id="L343">        String service = services[random.nextInt(services.length)];</span>

<span class="nc" id="L345">        int port = generateServicePort(service, &quot;BOTH&quot;);</span>
<span class="nc" id="L346">        return String.valueOf(port);</span>
    }

    /**
     * 生成恶意端口（用于安全测试）
     */
    public int generateMaliciousPort() {
        // 一些常见的恶意软件使用的端口
<span class="nc" id="L354">        int[] maliciousPorts = {</span>
                1337, 31337, 12345, 54321, 9999, 6666, 1234, 4321,
                6969, 1981, 1999, 2001, 5555, 7777, 8888, 9876
        };

<span class="nc" id="L359">        return maliciousPorts[random.nextInt(maliciousPorts.length)];</span>
    }

    /**
     * 生成高权限端口（需要管理员权限）
     */
    public int generatePrivilegedPort() {
        // 小于1024的端口通常需要管理员权限
<span class="nc" id="L367">        return random.nextInt(1024);</span>
    }

    /**
     * 生成开发环境常用端口
     */
    public int generateDevelopmentPort() {
<span class="nc" id="L374">        int[] devPorts = { 3000, 3001, 4000, 5000, 8000, 8080, 8888, 9000, 9090 };</span>
<span class="nc" id="L375">        return devPorts[random.nextInt(devPorts.length)];</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>