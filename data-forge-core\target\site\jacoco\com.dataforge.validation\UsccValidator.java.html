<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UsccValidator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_source">UsccValidator.java</span></div><h1>UsccValidator.java</h1><pre class="source lang-java linenums">package com.dataforge.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一社会信用代码校验器。
 * 
 * &lt;p&gt;
 * 实现中国统一社会信用代码（USCC）的校验算法，遵循GB32100-2015标准。
 * 
 * &lt;p&gt;
 * 统一社会信用代码结构（18位）：
 * - 第1位：登记管理部门代码
 * - 第2位：机构类别代码
 * - 第3-8位：登记管理机关行政区划码
 * - 第9-17位：主体标识码（组织机构代码）
 * - 第18位：校验码
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L27">public class UsccValidator implements Validator&lt;String&gt; {</span>

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(UsccValidator.class);</span>

    /**
     * 代码字符集。
     */
    private static final String CODE_SET = &quot;0123456789ABCDEFGHJKLMNPQRTUWXY&quot;;

    /**
     * 字符到数值的映射。
     */
<span class="nc" id="L39">    private static final Map&lt;Character, Integer&gt; CHAR_TO_VALUE = new HashMap&lt;&gt;();</span>

    /**
     * 权重数组。
     */
<span class="nc" id="L44">    private static final int[] WEIGHTS = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };</span>

    static {
        // 初始化字符到数值的映射
<span class="nc bnc" id="L48" title="All 2 branches missed.">        for (int i = 0; i &lt; CODE_SET.length(); i++) {</span>
<span class="nc" id="L49">            CHAR_TO_VALUE.put(CODE_SET.charAt(i), i);</span>
        }
    }

    /**
     * 登记管理部门代码映射。
     */
<span class="nc" id="L56">    private static final Map&lt;Character, String&gt; REGISTRATION_DEPT = new HashMap&lt;&gt;();</span>

    /**
     * 机构类别代码映射。
     */
<span class="nc" id="L61">    private static final Map&lt;Character, String&gt; ORGANIZATION_TYPE = new HashMap&lt;&gt;();</span>

    static {
        // 登记管理部门代码
<span class="nc" id="L65">        REGISTRATION_DEPT.put('1', &quot;机构编制&quot;);</span>
<span class="nc" id="L66">        REGISTRATION_DEPT.put('5', &quot;民政&quot;);</span>
<span class="nc" id="L67">        REGISTRATION_DEPT.put('9', &quot;工商&quot;);</span>
<span class="nc" id="L68">        REGISTRATION_DEPT.put('Y', &quot;其他&quot;);</span>

        // 机构类别代码
<span class="nc" id="L71">        ORGANIZATION_TYPE.put('1', &quot;企业&quot;);</span>
<span class="nc" id="L72">        ORGANIZATION_TYPE.put('2', &quot;事业单位&quot;);</span>
<span class="nc" id="L73">        ORGANIZATION_TYPE.put('3', &quot;社会团体&quot;);</span>
<span class="nc" id="L74">        ORGANIZATION_TYPE.put('9', &quot;其他&quot;);</span>
<span class="nc" id="L75">        ORGANIZATION_TYPE.put('Y', &quot;其他&quot;);</span>
<span class="nc" id="L76">    }</span>

    @Override
    public boolean isValid(String data) {
<span class="nc" id="L80">        return validate(data).isValid();</span>
    }

    @Override
    public ValidationResult validate(String data) {
<span class="nc bnc" id="L85" title="All 2 branches missed.">        if (data == null) {</span>
<span class="nc" id="L86">            return ValidationResult.failure(&quot;USCC cannot be null&quot;);</span>
        }

        // 移除所有非字母数字字符并转换为大写
<span class="nc" id="L90">        String cleanData = data.replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;).toUpperCase();</span>

<span class="nc bnc" id="L92" title="All 2 branches missed.">        if (cleanData.isEmpty()) {</span>
<span class="nc" id="L93">            return ValidationResult.failure(&quot;USCC cannot be empty&quot;);</span>
        }

        // 长度校验
<span class="nc bnc" id="L97" title="All 2 branches missed.">        if (cleanData.length() != 18) {</span>
<span class="nc" id="L98">            return ValidationResult.failure(&quot;USCC must be exactly 18 characters long&quot;);</span>
        }

        try {
            // 字符集校验
<span class="nc" id="L103">            ValidationResult charSetResult = validateCharacterSet(cleanData);</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">            if (!charSetResult.isValid()) {</span>
<span class="nc" id="L105">                return charSetResult;</span>
            }

            // 登记管理部门代码校验
<span class="nc" id="L109">            ValidationResult deptResult = validateRegistrationDepartment(cleanData.charAt(0));</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">            if (!deptResult.isValid()) {</span>
<span class="nc" id="L111">                return deptResult;</span>
            }

            // 机构类别代码校验
<span class="nc" id="L115">            ValidationResult typeResult = validateOrganizationType(cleanData.charAt(1));</span>
<span class="nc bnc" id="L116" title="All 2 branches missed.">            if (!typeResult.isValid()) {</span>
<span class="nc" id="L117">                return typeResult;</span>
            }

            // 行政区划代码校验
<span class="nc" id="L121">            ValidationResult regionResult = validateRegionCode(cleanData.substring(2, 8));</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">            if (!regionResult.isValid()) {</span>
<span class="nc" id="L123">                return regionResult;</span>
            }

            // 校验码校验
<span class="nc" id="L127">            ValidationResult checkCodeResult = validateCheckCode(cleanData);</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">            if (!checkCodeResult.isValid()) {</span>
<span class="nc" id="L129">                return checkCodeResult;</span>
            }

<span class="nc" id="L132">            logger.debug(&quot;USCC validation passed for: {}&quot;, maskUscc(data));</span>
<span class="nc" id="L133">            return ValidationResult.success();</span>

<span class="nc" id="L135">        } catch (Exception e) {</span>
<span class="nc" id="L136">            logger.error(&quot;Error during USCC validation for: {}&quot;, maskUscc(data), e);</span>
<span class="nc" id="L137">            return ValidationResult.failure(&quot;Error during USCC validation: &quot; + e.getMessage());</span>
        }
    }

    /**
     * 校验字符集。
     * 
     * @param uscc USCC代码
     * @return 校验结果
     */
    private ValidationResult validateCharacterSet(String uscc) {
<span class="nc bnc" id="L148" title="All 2 branches missed.">        for (char c : uscc.toCharArray()) {</span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">            if (!CHAR_TO_VALUE.containsKey(c)) {</span>
<span class="nc" id="L150">                return ValidationResult.failure(&quot;Invalid character: &quot; + c + &quot;. Valid characters: &quot; + CODE_SET);</span>
            }
        }
<span class="nc" id="L153">        return ValidationResult.success();</span>
    }

    /**
     * 校验登记管理部门代码。
     * 
     * @param deptCode 登记管理部门代码
     * @return 校验结果
     */
    private ValidationResult validateRegistrationDepartment(char deptCode) {
<span class="nc bnc" id="L163" title="All 2 branches missed.">        if (!REGISTRATION_DEPT.containsKey(deptCode)) {</span>
<span class="nc" id="L164">            return ValidationResult.failure(&quot;Invalid registration department code: &quot; + deptCode);</span>
        }
<span class="nc" id="L166">        return ValidationResult.success();</span>
    }

    /**
     * 校验机构类别代码。
     * 
     * @param typeCode 机构类别代码
     * @return 校验结果
     */
    private ValidationResult validateOrganizationType(char typeCode) {
<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (!ORGANIZATION_TYPE.containsKey(typeCode)) {</span>
<span class="nc" id="L177">            return ValidationResult.failure(&quot;Invalid organization type code: &quot; + typeCode);</span>
        }
<span class="nc" id="L179">        return ValidationResult.success();</span>
    }

    /**
     * 校验行政区划代码。
     * 
     * @param regionCode 6位行政区划代码
     * @return 校验结果
     */
    private ValidationResult validateRegionCode(String regionCode) {
<span class="nc bnc" id="L189" title="All 2 branches missed.">        if (regionCode.length() != 6) {</span>
<span class="nc" id="L190">            return ValidationResult.failure(&quot;Region code must be 6 characters&quot;);</span>
        }

        // 检查是否全为数字
<span class="nc bnc" id="L194" title="All 2 branches missed.">        if (!regionCode.matches(&quot;\\d{6}&quot;)) {</span>
<span class="nc" id="L195">            return ValidationResult.failure(&quot;Region code must contain only digits&quot;);</span>
        }

        // 基本的行政区划代码格式校验
<span class="nc" id="L199">        int provinceCode = Integer.parseInt(regionCode.substring(0, 2));</span>
<span class="nc bnc" id="L200" title="All 4 branches missed.">        if (provinceCode &lt; 11 || provinceCode &gt; 82) {</span>
<span class="nc" id="L201">            return ValidationResult.failure(&quot;Invalid province code: &quot; + provinceCode);</span>
        }

<span class="nc" id="L204">        return ValidationResult.success();</span>
    }

    /**
     * 校验校验码。
     * 
     * @param uscc 完整的18位USCC代码
     * @return 校验结果
     */
    private ValidationResult validateCheckCode(String uscc) {
<span class="nc" id="L214">        String first17 = uscc.substring(0, 17);</span>
<span class="nc" id="L215">        char actualCheckCode = uscc.charAt(17);</span>
<span class="nc" id="L216">        char expectedCheckCode = calculateCheckCode(first17);</span>

<span class="nc bnc" id="L218" title="All 2 branches missed.">        if (actualCheckCode == expectedCheckCode) {</span>
<span class="nc" id="L219">            return ValidationResult.success();</span>
        } else {
<span class="nc" id="L221">            return ValidationResult.failure(</span>
<span class="nc" id="L222">                    String.format(&quot;Check code mismatch. Expected: %c, Actual: %c&quot;,</span>
<span class="nc" id="L223">                            expectedCheckCode, actualCheckCode));</span>
        }
    }

    /**
     * 计算USCC的校验码。
     * 
     * &lt;p&gt;
     * 算法步骤：
     * 1. 将前17位字符转换为对应的数值
     * 2. 每位数值乘以对应的权重
     * 3. 求和后对31取模
     * 4. 用31减去模值得到校验码对应的数值
     * 5. 将数值转换为对应的字符
     * 
     * @param first17 前17位字符
     * @return 校验码字符
     */
    public char calculateCheckCode(String first17) {
<span class="nc bnc" id="L242" title="All 4 branches missed.">        if (first17 == null || first17.length() != 17) {</span>
<span class="nc" id="L243">            throw new IllegalArgumentException(&quot;First 17 characters must be exactly 17 characters&quot;);</span>
        }

        // 校验字符集
<span class="nc bnc" id="L247" title="All 2 branches missed.">        for (char c : first17.toCharArray()) {</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">            if (!CHAR_TO_VALUE.containsKey(c)) {</span>
<span class="nc" id="L249">                throw new IllegalArgumentException(&quot;Invalid character: &quot; + c);</span>
            }
        }

<span class="nc" id="L253">        int sum = 0;</span>
<span class="nc bnc" id="L254" title="All 2 branches missed.">        for (int i = 0; i &lt; 17; i++) {</span>
<span class="nc" id="L255">            char c = first17.charAt(i);</span>
<span class="nc" id="L256">            int value = CHAR_TO_VALUE.get(c);</span>
<span class="nc" id="L257">            sum += value * WEIGHTS[i];</span>
        }

<span class="nc" id="L260">        int remainder = sum % 31;</span>
<span class="nc" id="L261">        int checkValue = (31 - remainder) % 31;</span>

<span class="nc" id="L263">        return CODE_SET.charAt(checkValue);</span>
    }

    /**
     * 生成完整的有效USCC代码。
     * 
     * @param first17 前17位字符
     * @return 完整的18位USCC代码
     */
    public String generateValidUscc(String first17) {
<span class="nc" id="L273">        char checkCode = calculateCheckCode(first17);</span>
<span class="nc" id="L274">        return first17 + checkCode;</span>
    }

    /**
     * 解析USCC代码信息。
     * 
     * @param uscc USCC代码
     * @return 解析结果
     */
    public UsccInfo parseUscc(String uscc) {
<span class="nc bnc" id="L284" title="All 4 branches missed.">        if (uscc == null || uscc.length() != 18) {</span>
<span class="nc" id="L285">            return null;</span>
        }

<span class="nc" id="L288">        String cleanUscc = uscc.replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;).toUpperCase();</span>
<span class="nc bnc" id="L289" title="All 2 branches missed.">        if (cleanUscc.length() != 18) {</span>
<span class="nc" id="L290">            return null;</span>
        }

        try {
<span class="nc" id="L294">            UsccInfo info = new UsccInfo();</span>
<span class="nc" id="L295">            info.setUscc(cleanUscc);</span>
<span class="nc" id="L296">            info.setRegistrationDepartment(REGISTRATION_DEPT.get(cleanUscc.charAt(0)));</span>
<span class="nc" id="L297">            info.setOrganizationType(ORGANIZATION_TYPE.get(cleanUscc.charAt(1)));</span>
<span class="nc" id="L298">            info.setRegionCode(cleanUscc.substring(2, 8));</span>
<span class="nc" id="L299">            info.setOrganizationCode(cleanUscc.substring(8, 17));</span>
<span class="nc" id="L300">            info.setCheckCode(cleanUscc.charAt(17));</span>

<span class="nc" id="L302">            return info;</span>
<span class="nc" id="L303">        } catch (Exception e) {</span>
<span class="nc" id="L304">            logger.warn(&quot;Failed to parse USCC: {}&quot;, maskUscc(uscc), e);</span>
<span class="nc" id="L305">            return null;</span>
        }
    }

    /**
     * 掩码USCC代码用于日志记录。
     * 
     * @param uscc 原始USCC代码
     * @return 掩码后的USCC代码
     */
    private String maskUscc(String uscc) {
<span class="nc bnc" id="L316" title="All 4 branches missed.">        if (uscc == null || uscc.length() &lt; 8) {</span>
<span class="nc" id="L317">            return &quot;****&quot;;</span>
        }

        // 显示前4位和后4位，中间用*代替
<span class="nc" id="L321">        String prefix = uscc.substring(0, 4);</span>
<span class="nc" id="L322">        String suffix = uscc.substring(uscc.length() - 4);</span>
<span class="nc" id="L323">        int maskLength = uscc.length() - 8;</span>
<span class="nc" id="L324">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="nc" id="L326">        return prefix + mask + suffix;</span>
    }

    @Override
    public String getName() {
<span class="nc" id="L331">        return &quot;USCC&quot;;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L336">        return &quot;Unified Social Credit Code validator (GB32100-2015)&quot;;</span>
    }

    /**
     * USCC信息类。
     */
<span class="nc" id="L342">    public static class UsccInfo {</span>
        private String uscc;
        private String registrationDepartment;
        private String organizationType;
        private String regionCode;
        private String organizationCode;
        private char checkCode;

        // Getters and Setters
        public String getUscc() {
<span class="nc" id="L352">            return uscc;</span>
        }

        public void setUscc(String uscc) {
<span class="nc" id="L356">            this.uscc = uscc;</span>
<span class="nc" id="L357">        }</span>

        public String getRegistrationDepartment() {
<span class="nc" id="L360">            return registrationDepartment;</span>
        }

        public void setRegistrationDepartment(String registrationDepartment) {
<span class="nc" id="L364">            this.registrationDepartment = registrationDepartment;</span>
<span class="nc" id="L365">        }</span>

        public String getOrganizationType() {
<span class="nc" id="L368">            return organizationType;</span>
        }

        public void setOrganizationType(String organizationType) {
<span class="nc" id="L372">            this.organizationType = organizationType;</span>
<span class="nc" id="L373">        }</span>

        public String getRegionCode() {
<span class="nc" id="L376">            return regionCode;</span>
        }

        public void setRegionCode(String regionCode) {
<span class="nc" id="L380">            this.regionCode = regionCode;</span>
<span class="nc" id="L381">        }</span>

        public String getOrganizationCode() {
<span class="nc" id="L384">            return organizationCode;</span>
        }

        public void setOrganizationCode(String organizationCode) {
<span class="nc" id="L388">            this.organizationCode = organizationCode;</span>
<span class="nc" id="L389">        }</span>

        public char getCheckCode() {
<span class="nc" id="L392">            return checkCode;</span>
        }

        public void setCheckCode(char checkCode) {
<span class="nc" id="L396">            this.checkCode = checkCode;</span>
<span class="nc" id="L397">        }</span>

        @Override
        public String toString() {
<span class="nc" id="L401">            return &quot;UsccInfo{&quot; +</span>
                    &quot;uscc='&quot; + uscc + '\'' +
                    &quot;, registrationDepartment='&quot; + registrationDepartment + '\'' +
                    &quot;, organizationType='&quot; + organizationType + '\'' +
                    &quot;, regionCode='&quot; + regionCode + '\'' +
                    &quot;, organizationCode='&quot; + organizationCode + '\'' +
                    &quot;, checkCode=&quot; + checkCode +
                    '}';
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>