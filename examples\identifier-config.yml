dataforge:
  count: 10
  output:
    format: csv
    file: "output/identifier-test.csv"
  fields:
    # IP地址生成器测试
    - name: "ipv4_address"
      type: "ip"
      params:
        version: "IPV4"
        type: "ANY"
        
    - name: "ipv6_address"
      type: "ip"
      params:
        version: "IPV6"
        format: "COMPRESSED"
        
    - name: "private_ip"
      type: "ip"
      params:
        version: "IPV4"
        type: "PRIVATE"
        
    - name: "subnet_ip"
      type: "ip"
      params:
        subnet: "***********/24"
        
    # MAC地址生成器测试
    - name: "mac_address"
      type: "mac"
      params:
        format: "COLON"
        case: "LOWER"
        
    - name: "mac_vmware"
      type: "mac"
      params:
        vendor: "VMWARE"
        format: "HYPHEN"
        case: "UPPER"
        
    - name: "mac_broadcast"
      type: "mac"
      params:
        type: "BROADCAST"
        format: "DOT"
        
    # URL生成器测试
    - name: "website_url"
      type: "url"
      params:
        scheme: "HTTPS"
        path_depth: 3
        include_query: true
        
    - name: "api_url"
      type: "url"
      params:
        scheme: "HTTPS"
        domain: "api.example.com"
        path_depth: 2
        include_query: true
        
    - name: "ftp_url"
      type: "url"
      params:
        scheme: "FTP"
        port: "21"
        path_depth: 1
        
    # 域名生成器测试
    - name: "generic_domain"
      type: "domain"
      params:
        type: "GENERIC"
        tld: "com"
        length: "8,12"
        
    - name: "brand_domain"
      type: "domain"
      params:
        type: "BRAND"
        tld: "ANY"
        include_subdomain: true
        
    - name: "tech_domain"
      type: "domain"
      params:
        type: "DICTIONARY"
        tld: "tech"
        length: "5,10"
        
    - name: "chinese_domain"
      type: "domain"
      params:
        type: "RANDOM"
        tld: "cn"
        international: true
        
    # API密钥生成器测试
    - name: "api_key"
      type: "apikey"
      params:
        type: "CUSTOM"
        length: 32
        format: "ALPHANUMERIC"
        prefix: "sk_"
        
    - name: "jwt_token"
      type: "apikey"
      params:
        type: "JWT"
        secure: true
        
    - name: "bearer_token"
      type: "apikey"
      params:
        type: "BEARER"
        length: 40
        format: "BASE64"
        
    - name: "hex_key"
      type: "apikey"
      params:
        type: "CUSTOM"
        length: 24
        format: "HEX"
        include_checksum: true