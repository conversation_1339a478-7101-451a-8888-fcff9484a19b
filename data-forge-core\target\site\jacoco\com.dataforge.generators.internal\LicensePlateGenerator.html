<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LicensePlateGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">LicensePlateGenerator</span></div><h1>LicensePlateGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">910 of 910</td><td class="ctr2">0%</td><td class="bar">65 of 65</td><td class="ctr2">0%</td><td class="ctr1">54</td><td class="ctr2">54</td><td class="ctr1">133</td><td class="ctr2">133</td><td class="ctr1">20</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a19"><a href="LicensePlateGenerator.java.html#L28" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="430" alt="430"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h0">39</td><td class="ctr2" id="i0">39</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a18"><a href="LicensePlateGenerator.java.html#L188" class="el_method">selectProvinceCode(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="69" alt="69"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="LicensePlateGenerator.java.html#L348" class="el_method">generateInvalidCharsPlate()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="62" alt="62"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="LicensePlateGenerator.java.html#L316" class="el_method">generateWrongLengthPlate()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="56" alt="56"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="LicensePlateGenerator.java.html#L110" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="49" alt="49"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h2">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="LicensePlateGenerator.java.html#L260" class="el_method">generateNewEnergyNumberPart(boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="43" alt="43"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a17"><a href="LicensePlateGenerator.java.html#L223" class="el_method">selectCityCode(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="33" alt="33"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><a href="LicensePlateGenerator.java.html#L282" class="el_method">generateTraditionalNumberPart(boolean)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="30" alt="30"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a11"><a href="LicensePlateGenerator.java.html#L388" class="el_method">getBooleanParam(FieldConfig, String, boolean)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="27" alt="27"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="LicensePlateGenerator.java.html#L153" class="el_method">generateValidLicensePlate(String, String, String, boolean)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="22" alt="22"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h10">5</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="LicensePlateGenerator.java.html#L299" class="el_method">generateInvalidLicensePlate()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="19" alt="19"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a14"><a href="LicensePlateGenerator.java.html#L376" class="el_method">getStringParam(FieldConfig, String, String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="19" alt="19"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h11">4</td><td class="ctr2" id="i11">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="LicensePlateGenerator.java.html#L174" class="el_method">determineNewEnergyType(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="17" alt="17"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a5"><a href="LicensePlateGenerator.java.html#L246" class="el_method">generateNumberPart(boolean, boolean)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h13">3</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="LicensePlateGenerator.java.html#L26" class="el_method">LicensePlateGenerator()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a6"><a href="LicensePlateGenerator.java.html#L365" class="el_method">generateOtherInvalidPlate()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h14">3</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a15"><a href="LicensePlateGenerator.java.html#L103" class="el_method">getType()</a></td><td class="bar" id="b16"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a12"><a href="LicensePlateGenerator.java.html#L139" class="el_method">getConfigClass()</a></td><td class="bar" id="b17"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a9"><a href="LicensePlateGenerator.java.html#L339" class="el_method">generateWrongFormatPlate()</a></td><td class="bar" id="b18"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a13"><a href="LicensePlateGenerator.java.html#L406" class="el_method">getDescription()</a></td><td class="bar" id="b19"/><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>