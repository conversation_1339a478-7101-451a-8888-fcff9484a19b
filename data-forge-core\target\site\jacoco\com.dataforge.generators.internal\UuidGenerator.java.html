<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UuidGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">UuidGenerator.java</span></div><h1>UuidGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * UUID生成器。
 * 
 * &lt;p&gt;
 * 生成符合RFC 4122标准的UUID字符串。
 * 支持UUID4（随机UUID）和UUID1（基于时间的UUID）。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L22">public class UuidGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(UuidGenerator.class);</span>

    @Override
    public String getType() {
<span class="nc" id="L28">        return &quot;uuid&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 从参数中获取UUID类型，默认为UUID4
<span class="nc" id="L35">            String uuidType = getStringParam(config, &quot;type&quot;, &quot;UUID4&quot;);</span>

<span class="nc bnc" id="L37" title="All 3 branches missed.">            return switch (uuidType.toUpperCase()) {</span>
<span class="nc" id="L38">                case &quot;UUID1&quot; -&gt; generateUuid1();</span>
<span class="nc" id="L39">                case &quot;UUID4&quot; -&gt; generateUuid4();</span>
                default -&gt; {
<span class="nc" id="L41">                    logger.warn(&quot;Unknown UUID type: {}, using UUID4&quot;, uuidType);</span>
<span class="nc" id="L42">                    yield generateUuid4();</span>
                }
            };

<span class="nc" id="L46">        } catch (Exception e) {</span>
<span class="nc" id="L47">            logger.error(&quot;Failed to generate UUID&quot;, e);</span>
            // 返回一个默认的UUID4作为fallback
<span class="nc" id="L49">            return UUID.randomUUID().toString();</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L55">        return FieldConfig.class;</span>
    }

    /**
     * 生成UUID4（随机UUID）。
     * 
     * @return UUID4字符串
     */
    private String generateUuid4() {
<span class="nc" id="L64">        return UUID.randomUUID().toString();</span>
    }

    /**
     * 生成UUID1（基于时间的UUID）。
     * 
     * &lt;p&gt;
     * 注意：Java标准库不直接支持UUID1生成，这里使用简化实现。
     * 实际应用中可能需要使用第三方库如java-uuid-generator。
     * 
     * @return UUID1字符串
     */
    private String generateUuid1() {
        // 简化的UUID1实现
        // 实际应用中应该使用专门的UUID1生成库
<span class="nc" id="L79">        long timestamp = System.currentTimeMillis();</span>
<span class="nc" id="L80">        long randomPart = UUID.randomUUID().getMostSignificantBits();</span>

        // 构造一个类似UUID1格式的字符串
        // 这不是真正的UUID1，只是为了演示
<span class="nc" id="L84">        String timeHex = Long.toHexString(timestamp);</span>
<span class="nc" id="L85">        String randomHex = Long.toHexString(Math.abs(randomPart));</span>

        // 确保长度
<span class="nc bnc" id="L88" title="All 2 branches missed.">        while (timeHex.length() &lt; 12) {</span>
<span class="nc" id="L89">            timeHex = &quot;0&quot; + timeHex;</span>
        }
<span class="nc bnc" id="L91" title="All 2 branches missed.">        while (randomHex.length() &lt; 16) {</span>
<span class="nc" id="L92">            randomHex = &quot;0&quot; + randomHex;</span>
        }

        // 格式化为UUID格式
<span class="nc" id="L96">        String uuid = timeHex.substring(0, 8) + &quot;-&quot; +</span>
<span class="nc" id="L97">                timeHex.substring(8, 12) + &quot;-&quot; +</span>
<span class="nc" id="L98">                &quot;1&quot; + randomHex.substring(0, 3) + &quot;-&quot; +</span>
<span class="nc" id="L99">                randomHex.substring(3, 7) + &quot;-&quot; +</span>
<span class="nc" id="L100">                randomHex.substring(7, 19);</span>

<span class="nc" id="L102">        return uuid;</span>
    }

    /**
     * 从配置中获取字符串参数。
     * 
     * @param config       字段配置
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L114" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L115">            return defaultValue;</span>
        }

<span class="nc" id="L118">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L124">        return &quot;UUID generator - generates RFC 4122 compliant UUID strings (UUID1/UUID4)&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>