<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EthnicityGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">EthnicityGenerator.java</span></div><h1>EthnicityGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 民族生成器
 * 
 * 支持的参数：
 * - type: 民族类型 (HAN|MINOR|ANY)
 * - han_ratio: 汉族占比 (0.0-1.0)
 * - file: 自定义民族列表文件路径
 * - weights: 民族权重配置 (如 &quot;汉族:90,维吾尔族:3,回族:2&quot;)
 * - format: 输出格式 (CHINESE|CODE)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class EthnicityGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(EthnicityGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 中国56个民族列表（按人口数量排序）
<span class="nc" id="L30">    private static final List&lt;String&gt; CHINESE_ETHNICITIES = Arrays.asList(</span>
            &quot;汉族&quot;, &quot;壮族&quot;, &quot;维吾尔族&quot;, &quot;回族&quot;, &quot;满族&quot;, &quot;苗族&quot;, &quot;彝族&quot;, &quot;土家族&quot;, &quot;藏族&quot;, &quot;蒙古族&quot;,
            &quot;侗族&quot;, &quot;布依族&quot;, &quot;瑶族&quot;, &quot;白族&quot;, &quot;朝鲜族&quot;, &quot;哈尼族&quot;, &quot;哈萨克族&quot;, &quot;黎族&quot;, &quot;傣族&quot;, &quot;畲族&quot;,
            &quot;傈僳族&quot;, &quot;东乡族&quot;, &quot;仡佬族&quot;, &quot;拉祜族&quot;, &quot;水族&quot;, &quot;佤族&quot;, &quot;纳西族&quot;, &quot;羌族&quot;, &quot;土族&quot;, &quot;仫佬族&quot;,
            &quot;锡伯族&quot;, &quot;柯尔克孜族&quot;, &quot;景颇族&quot;, &quot;达斡尔族&quot;, &quot;撒拉族&quot;, &quot;布朗族&quot;, &quot;毛南族&quot;, &quot;塔吉克族&quot;, &quot;普米族&quot;, &quot;阿昌族&quot;,
            &quot;怒族&quot;, &quot;鄂温克族&quot;, &quot;京族&quot;, &quot;基诺族&quot;, &quot;德昂族&quot;, &quot;保安族&quot;, &quot;俄罗斯族&quot;, &quot;裕固族&quot;, &quot;乌孜别克族&quot;, &quot;门巴族&quot;,
            &quot;鄂伦春族&quot;, &quot;独龙族&quot;, &quot;塔塔尔族&quot;, &quot;赫哲族&quot;, &quot;高山族&quot;, &quot;珞巴族&quot;);

    // 民族代码映射（按国家标准）
<span class="nc" id="L39">    private static final Map&lt;String, String&gt; ETHNICITY_CODES = new HashMap&lt;&gt;();</span>

    // 现实分布权重（基于中国人口普查数据）
<span class="nc" id="L42">    private static final Map&lt;String, Double&gt; REALISTIC_WEIGHTS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L45">        initializeEthnicityCodes();</span>
<span class="nc" id="L46">        initializeRealisticWeights();</span>
<span class="nc" id="L47">    }</span>

    private static void initializeEthnicityCodes() {
        // 主要民族的标准代码
<span class="nc" id="L51">        ETHNICITY_CODES.put(&quot;汉族&quot;, &quot;01&quot;);</span>
<span class="nc" id="L52">        ETHNICITY_CODES.put(&quot;蒙古族&quot;, &quot;02&quot;);</span>
<span class="nc" id="L53">        ETHNICITY_CODES.put(&quot;回族&quot;, &quot;03&quot;);</span>
<span class="nc" id="L54">        ETHNICITY_CODES.put(&quot;藏族&quot;, &quot;04&quot;);</span>
<span class="nc" id="L55">        ETHNICITY_CODES.put(&quot;维吾尔族&quot;, &quot;05&quot;);</span>
<span class="nc" id="L56">        ETHNICITY_CODES.put(&quot;苗族&quot;, &quot;06&quot;);</span>
<span class="nc" id="L57">        ETHNICITY_CODES.put(&quot;彝族&quot;, &quot;07&quot;);</span>
<span class="nc" id="L58">        ETHNICITY_CODES.put(&quot;壮族&quot;, &quot;08&quot;);</span>
<span class="nc" id="L59">        ETHNICITY_CODES.put(&quot;布依族&quot;, &quot;09&quot;);</span>
<span class="nc" id="L60">        ETHNICITY_CODES.put(&quot;朝鲜族&quot;, &quot;10&quot;);</span>
<span class="nc" id="L61">        ETHNICITY_CODES.put(&quot;满族&quot;, &quot;11&quot;);</span>
<span class="nc" id="L62">        ETHNICITY_CODES.put(&quot;侗族&quot;, &quot;12&quot;);</span>
<span class="nc" id="L63">        ETHNICITY_CODES.put(&quot;瑶族&quot;, &quot;13&quot;);</span>
<span class="nc" id="L64">        ETHNICITY_CODES.put(&quot;白族&quot;, &quot;14&quot;);</span>
<span class="nc" id="L65">        ETHNICITY_CODES.put(&quot;土家族&quot;, &quot;15&quot;);</span>
<span class="nc" id="L66">        ETHNICITY_CODES.put(&quot;哈尼族&quot;, &quot;16&quot;);</span>
<span class="nc" id="L67">        ETHNICITY_CODES.put(&quot;哈萨克族&quot;, &quot;17&quot;);</span>
<span class="nc" id="L68">        ETHNICITY_CODES.put(&quot;傣族&quot;, &quot;18&quot;);</span>
<span class="nc" id="L69">        ETHNICITY_CODES.put(&quot;黎族&quot;, &quot;19&quot;);</span>
<span class="nc" id="L70">        ETHNICITY_CODES.put(&quot;傈僳族&quot;, &quot;20&quot;);</span>
        // 其他民族使用递增编号
<span class="nc bnc" id="L72" title="All 2 branches missed.">        for (int i = 0; i &lt; CHINESE_ETHNICITIES.size(); i++) {</span>
<span class="nc" id="L73">            String ethnicity = CHINESE_ETHNICITIES.get(i);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">            if (!ETHNICITY_CODES.containsKey(ethnicity)) {</span>
<span class="nc" id="L75">                ETHNICITY_CODES.put(ethnicity, String.format(&quot;%02d&quot;, i + 1));</span>
            }
        }
<span class="nc" id="L78">    }</span>

    private static void initializeRealisticWeights() {
        // 基于中国第七次人口普查数据的近似分布
<span class="nc" id="L82">        REALISTIC_WEIGHTS.put(&quot;汉族&quot;, 91.11); // 91.11%</span>
<span class="nc" id="L83">        REALISTIC_WEIGHTS.put(&quot;壮族&quot;, 1.27); // 1.27%</span>
<span class="nc" id="L84">        REALISTIC_WEIGHTS.put(&quot;维吾尔族&quot;, 0.79); // 0.79%</span>
<span class="nc" id="L85">        REALISTIC_WEIGHTS.put(&quot;回族&quot;, 0.79); // 0.79%</span>
<span class="nc" id="L86">        REALISTIC_WEIGHTS.put(&quot;满族&quot;, 0.78); // 0.78%</span>
<span class="nc" id="L87">        REALISTIC_WEIGHTS.put(&quot;苗族&quot;, 0.67); // 0.67%</span>
<span class="nc" id="L88">        REALISTIC_WEIGHTS.put(&quot;彝族&quot;, 0.65); // 0.65%</span>
<span class="nc" id="L89">        REALISTIC_WEIGHTS.put(&quot;土家族&quot;, 0.62); // 0.62%</span>
<span class="nc" id="L90">        REALISTIC_WEIGHTS.put(&quot;藏族&quot;, 0.47); // 0.47%</span>
<span class="nc" id="L91">        REALISTIC_WEIGHTS.put(&quot;蒙古族&quot;, 0.45); // 0.45%</span>

        // 其他民族使用较小的权重
<span class="nc bnc" id="L94" title="All 2 branches missed.">        for (String ethnicity : CHINESE_ETHNICITIES) {</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">            if (!REALISTIC_WEIGHTS.containsKey(ethnicity)) {</span>
<span class="nc" id="L96">                REALISTIC_WEIGHTS.put(ethnicity, 0.1); // 其他民族各占约0.1%</span>
            }
<span class="nc" id="L98">        }</span>
<span class="nc" id="L99">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L103">        return &quot;ethnicity&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L108">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L115">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L116">            double hanRatio = Double.parseDouble(config.getParam(&quot;han_ratio&quot;, String.class, &quot;0.91&quot;));</span>
<span class="nc" id="L117">            String weightsParam = config.getParam(&quot;weights&quot;, String.class, null);</span>
<span class="nc" id="L118">            String format = config.getParam(&quot;format&quot;, String.class, &quot;CHINESE&quot;);</span>

            // 加载民族数据
<span class="nc" id="L121">            List&lt;String&gt; ethnicities = loadEthnicities(config, type);</span>

            // 根据权重选择民族
<span class="nc" id="L124">            String ethnicity = selectEthnicity(ethnicities, type, hanRatio, weightsParam);</span>

            // 将民族信息存入上下文
<span class="nc" id="L127">            context.put(&quot;ethnicity&quot;, ethnicity);</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">            context.put(&quot;is_han&quot;, &quot;汉族&quot;.equals(ethnicity) ? &quot;true&quot; : &quot;false&quot;);</span>

            // 格式化输出
<span class="nc" id="L131">            String result = formatEthnicity(ethnicity, format);</span>

<span class="nc" id="L133">            logger.debug(&quot;Generated ethnicity: {}&quot;, result);</span>
<span class="nc" id="L134">            return result;</span>

<span class="nc" id="L136">        } catch (Exception e) {</span>
<span class="nc" id="L137">            logger.error(&quot;Error generating ethnicity&quot;, e);</span>
<span class="nc" id="L138">            return &quot;汉族&quot;;</span>
        }
    }

    private List&lt;String&gt; loadEthnicities(FieldConfig config, String type) {
<span class="nc" id="L143">        String customFile = config.getParam(&quot;file&quot;, String.class, null);</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (customFile != null) {</span>
            try {
<span class="nc" id="L146">                return DataLoader.loadDataFromFile(customFile);</span>
<span class="nc" id="L147">            } catch (Exception e) {</span>
<span class="nc" id="L148">                logger.warn(&quot;Failed to load custom ethnicity file: {}&quot;, customFile, e);</span>
            }
        }

        // 使用内置民族数据
<span class="nc" id="L153">        List&lt;String&gt; ethnicities = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L155" title="All 3 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;HAN&quot;:
<span class="nc" id="L157">                ethnicities.add(&quot;汉族&quot;);</span>
<span class="nc" id="L158">                break;</span>

            case &quot;MINOR&quot;:
                // 少数民族（除汉族外的所有民族）
<span class="nc bnc" id="L162" title="All 2 branches missed.">                for (String ethnicity : CHINESE_ETHNICITIES) {</span>
<span class="nc bnc" id="L163" title="All 2 branches missed.">                    if (!&quot;汉族&quot;.equals(ethnicity)) {</span>
<span class="nc" id="L164">                        ethnicities.add(ethnicity);</span>
                    }
<span class="nc" id="L166">                }</span>
<span class="nc" id="L167">                break;</span>

            case &quot;ANY&quot;:
            default:
<span class="nc" id="L171">                ethnicities.addAll(CHINESE_ETHNICITIES);</span>
                break;
        }

<span class="nc" id="L175">        return ethnicities;</span>
    }

    private String selectEthnicity(List&lt;String&gt; ethnicities, String type, double hanRatio, String weightsParam) {
        // 如果只有一个选项，直接返回
<span class="nc bnc" id="L180" title="All 2 branches missed.">        if (ethnicities.size() == 1) {</span>
<span class="nc" id="L181">            return ethnicities.get(0);</span>
        }

        // 如果有自定义权重，使用自定义权重
<span class="nc bnc" id="L185" title="All 4 branches missed.">        if (weightsParam != null &amp;&amp; !weightsParam.isEmpty()) {</span>
<span class="nc" id="L186">            return selectWithCustomWeights(ethnicities, weightsParam);</span>
        }

        // 如果指定了汉族比例且包含汉族
<span class="nc bnc" id="L190" title="All 4 branches missed.">        if (ethnicities.contains(&quot;汉族&quot;) &amp;&amp; !&quot;MINOR&quot;.equals(type.toUpperCase())) {</span>
<span class="nc" id="L191">            double randomValue = random.nextDouble();</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">            if (randomValue &lt; hanRatio) {</span>
<span class="nc" id="L193">                return &quot;汉族&quot;;</span>
            } else {
                // 从少数民族中选择
<span class="nc" id="L196">                List&lt;String&gt; minorities = new ArrayList&lt;&gt;(ethnicities);</span>
<span class="nc" id="L197">                minorities.remove(&quot;汉族&quot;);</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">                if (!minorities.isEmpty()) {</span>
<span class="nc" id="L199">                    return selectWithRealisticWeights(minorities);</span>
                }
            }
        }

        // 使用现实分布权重
<span class="nc" id="L205">        return selectWithRealisticWeights(ethnicities);</span>
    }

    private String selectWithRealisticWeights(List&lt;String&gt; ethnicities) {
        // 计算总权重
<span class="nc" id="L210">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L211" title="All 2 branches missed.">        for (String ethnicity : ethnicities) {</span>
<span class="nc" id="L212">            totalWeight += REALISTIC_WEIGHTS.getOrDefault(ethnicity, 0.1);</span>
<span class="nc" id="L213">        }</span>

<span class="nc bnc" id="L215" title="All 2 branches missed.">        if (totalWeight &lt;= 0) {</span>
            // 如果没有权重，使用均匀分布
<span class="nc" id="L217">            return ethnicities.get(random.nextInt(ethnicities.size()));</span>
        }

        // 随机选择
<span class="nc" id="L221">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L222">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L224" title="All 2 branches missed.">        for (String ethnicity : ethnicities) {</span>
<span class="nc" id="L225">            currentWeight += REALISTIC_WEIGHTS.getOrDefault(ethnicity, 0.1);</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L227">                return ethnicity;</span>
            }
<span class="nc" id="L229">        }</span>

        // 默认返回第一个
<span class="nc" id="L232">        return ethnicities.get(0);</span>
    }

    private String selectWithCustomWeights(List&lt;String&gt; ethnicities, String weightsParam) {
<span class="nc" id="L236">        Map&lt;String, Integer&gt; customWeights = parseWeights(weightsParam);</span>

        // 计算总权重
<span class="nc" id="L239">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">        for (String ethnicity : ethnicities) {</span>
<span class="nc" id="L241">            totalWeight += customWeights.getOrDefault(ethnicity, 1);</span>
<span class="nc" id="L242">        }</span>

        // 随机选择
<span class="nc" id="L245">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L246">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L248" title="All 2 branches missed.">        for (String ethnicity : ethnicities) {</span>
<span class="nc" id="L249">            currentWeight += customWeights.getOrDefault(ethnicity, 1);</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L251">                return ethnicity;</span>
            }
<span class="nc" id="L253">        }</span>

        // 默认返回第一个
<span class="nc" id="L256">        return ethnicities.get(0);</span>
    }

    private Map&lt;String, Integer&gt; parseWeights(String weightsParam) {
<span class="nc" id="L260">        Map&lt;String, Integer&gt; weights = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L263">            String[] pairs = weightsParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">            for (String pair : pairs) {</span>
<span class="nc" id="L265">                String[] parts = pair.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L266" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L267">                    String ethnicity = parts[0].trim();</span>
<span class="nc" id="L268">                    int weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L269">                    weights.put(ethnicity, weight);</span>
                }
            }
<span class="nc" id="L272">        } catch (Exception e) {</span>
<span class="nc" id="L273">            logger.warn(&quot;Failed to parse weights: {}&quot;, weightsParam, e);</span>
<span class="nc" id="L274">        }</span>

<span class="nc" id="L276">        return weights;</span>
    }

    private String formatEthnicity(String ethnicity, String format) {
<span class="nc bnc" id="L280" title="All 3 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;CHINESE&quot;:
            case &quot;CN&quot;:
<span class="nc" id="L283">                return ethnicity;</span>

            case &quot;CODE&quot;:
<span class="nc" id="L286">                return ETHNICITY_CODES.getOrDefault(ethnicity, &quot;99&quot;);</span>

            default:
<span class="nc" id="L289">                logger.warn(&quot;Unknown ethnicity format: {}. Using CHINESE format.&quot;, format);</span>
<span class="nc" id="L290">                return ethnicity;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>