<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UrlGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">UrlGenerator.java</span></div><h1>UrlGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * URL生成器
 * 
 * 支持的参数：
 * - scheme: 协议方案 (HTTP|HTTPS|FTP|FILE|ANY)
 * - domain: 域名 (指定域名或使用随机域名)
 * - path_depth: 路径深度 (0-10)
 * - include_query: 是否包含查询参数 (true|false)
 * - include_fragment: 是否包含片段标识符 (true|false)
 * - port: 端口号 (指定端口或随机端口)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class UrlGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(UrlGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 常见域名后缀
<span class="nc" id="L30">    private static final List&lt;String&gt; COMMON_TLDS = Arrays.asList(</span>
            &quot;com&quot;, &quot;org&quot;, &quot;net&quot;, &quot;edu&quot;, &quot;gov&quot;, &quot;mil&quot;, &quot;int&quot;,
            &quot;cn&quot;, &quot;uk&quot;, &quot;de&quot;, &quot;fr&quot;, &quot;jp&quot;, &quot;au&quot;, &quot;ca&quot;, &quot;ru&quot;,
            &quot;info&quot;, &quot;biz&quot;, &quot;name&quot;, &quot;pro&quot;, &quot;museum&quot;, &quot;travel&quot;);

    // 常见子域名
<span class="nc" id="L36">    private static final List&lt;String&gt; COMMON_SUBDOMAINS = Arrays.asList(</span>
            &quot;www&quot;, &quot;api&quot;, &quot;app&quot;, &quot;admin&quot;, &quot;blog&quot;, &quot;shop&quot;, &quot;mail&quot;, &quot;ftp&quot;,
            &quot;cdn&quot;, &quot;static&quot;, &quot;img&quot;, &quot;media&quot;, &quot;dev&quot;, &quot;test&quot;, &quot;staging&quot;);

    // 常见路径词汇
<span class="nc" id="L41">    private static final List&lt;String&gt; PATH_WORDS = Arrays.asList(</span>
            &quot;home&quot;, &quot;about&quot;, &quot;contact&quot;, &quot;products&quot;, &quot;services&quot;, &quot;blog&quot;, &quot;news&quot;,
            &quot;user&quot;, &quot;admin&quot;, &quot;api&quot;, &quot;v1&quot;, &quot;v2&quot;, &quot;public&quot;, &quot;private&quot;, &quot;secure&quot;,
            &quot;data&quot;, &quot;files&quot;, &quot;images&quot;, &quot;docs&quot;, &quot;help&quot;, &quot;support&quot;, &quot;login&quot;,
            &quot;register&quot;, &quot;profile&quot;, &quot;settings&quot;, &quot;dashboard&quot;, &quot;reports&quot;, &quot;analytics&quot;);

    // 常见查询参数
<span class="nc" id="L48">    private static final List&lt;String&gt; QUERY_PARAMS = Arrays.asList(</span>
            &quot;id&quot;, &quot;name&quot;, &quot;type&quot;, &quot;category&quot;, &quot;page&quot;, &quot;size&quot;, &quot;limit&quot;, &quot;offset&quot;,
            &quot;sort&quot;, &quot;order&quot;, &quot;filter&quot;, &quot;search&quot;, &quot;q&quot;, &quot;lang&quot;, &quot;locale&quot;, &quot;format&quot;,
            &quot;version&quot;, &quot;timestamp&quot;, &quot;token&quot;, &quot;key&quot;, &quot;session&quot;, &quot;user&quot;, &quot;ref&quot;);

    @Override
    public String getType() {
<span class="nc" id="L55">        return &quot;url&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L60">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L67">            String scheme = config.getParam(&quot;scheme&quot;, String.class, &quot;HTTPS&quot;);</span>
<span class="nc" id="L68">            String domain = config.getParam(&quot;domain&quot;, String.class, null);</span>
<span class="nc" id="L69">            int pathDepth = Integer.parseInt(config.getParam(&quot;path_depth&quot;, String.class, &quot;2&quot;));</span>
<span class="nc" id="L70">            boolean includeQuery = Boolean.parseBoolean(config.getParam(&quot;include_query&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L71">            boolean includeFragment = Boolean.parseBoolean(config.getParam(&quot;include_fragment&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L72">            String port = config.getParam(&quot;port&quot;, String.class, null);</span>

            // 生成URL
<span class="nc" id="L75">            String url = generateUrl(scheme, domain, pathDepth, includeQuery, includeFragment, port);</span>

            // 将URL信息存入上下文
<span class="nc" id="L78">            context.put(&quot;url&quot;, url);</span>
<span class="nc" id="L79">            context.put(&quot;url_scheme&quot;, extractScheme(url));</span>
<span class="nc" id="L80">            context.put(&quot;url_domain&quot;, extractDomain(url));</span>

<span class="nc" id="L82">            logger.debug(&quot;Generated URL: {}&quot;, url);</span>
<span class="nc" id="L83">            return url;</span>

<span class="nc" id="L85">        } catch (Exception e) {</span>
<span class="nc" id="L86">            logger.error(&quot;Error generating URL&quot;, e);</span>
<span class="nc" id="L87">            return &quot;https://www.example.com/api/v1/users&quot;;</span>
        }
    }

    private String generateUrl(String scheme, String domain, int pathDepth,
            boolean includeQuery, boolean includeFragment, String port) {

<span class="nc" id="L94">        StringBuilder url = new StringBuilder();</span>

        // 1. 生成协议方案
<span class="nc" id="L97">        String urlScheme = selectScheme(scheme);</span>
<span class="nc" id="L98">        url.append(urlScheme).append(&quot;://&quot;);</span>

        // 2. 生成域名
<span class="nc" id="L101">        String hostname = generateHostname(domain);</span>
<span class="nc" id="L102">        url.append(hostname);</span>

        // 3. 生成端口号
<span class="nc" id="L105">        String portNumber = generatePort(port, urlScheme);</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (portNumber != null) {</span>
<span class="nc" id="L107">            url.append(&quot;:&quot;).append(portNumber);</span>
        }

        // 4. 生成路径
<span class="nc" id="L111">        String path = generatePath(pathDepth);</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">        if (!path.isEmpty()) {</span>
<span class="nc" id="L113">            url.append(path);</span>
        }

        // 5. 生成查询参数
<span class="nc bnc" id="L117" title="All 4 branches missed.">        if (includeQuery &amp;&amp; random.nextDouble() &lt; 0.7) {</span>
<span class="nc" id="L118">            String query = generateQuery();</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">            if (!query.isEmpty()) {</span>
<span class="nc" id="L120">                url.append(&quot;?&quot;).append(query);</span>
            }
        }

        // 6. 生成片段标识符
<span class="nc bnc" id="L125" title="All 4 branches missed.">        if (includeFragment &amp;&amp; random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L126">            String fragment = generateFragment();</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">            if (!fragment.isEmpty()) {</span>
<span class="nc" id="L128">                url.append(&quot;#&quot;).append(fragment);</span>
            }
        }

<span class="nc" id="L132">        return url.toString();</span>
    }

    private String selectScheme(String scheme) {
<span class="nc bnc" id="L136" title="All 5 branches missed.">        switch (scheme.toUpperCase()) {</span>
            case &quot;HTTP&quot;:
<span class="nc" id="L138">                return &quot;http&quot;;</span>
            case &quot;HTTPS&quot;:
<span class="nc" id="L140">                return &quot;https&quot;;</span>
            case &quot;FTP&quot;:
<span class="nc" id="L142">                return &quot;ftp&quot;;</span>
            case &quot;FILE&quot;:
<span class="nc" id="L144">                return &quot;file&quot;;</span>
            case &quot;ANY&quot;:
            default:
                // 70%概率选择HTTPS
<span class="nc bnc" id="L148" title="All 2 branches missed.">                return random.nextDouble() &lt; 0.7 ? &quot;https&quot; : &quot;http&quot;;</span>
        }
    }

    private String generateHostname(String domain) {
<span class="nc bnc" id="L153" title="All 4 branches missed.">        if (domain != null &amp;&amp; !domain.isEmpty()) {</span>
<span class="nc" id="L154">            return domain;</span>
        }

<span class="nc" id="L157">        StringBuilder hostname = new StringBuilder();</span>

        // 添加子域名（30%概率）
<span class="nc bnc" id="L160" title="All 2 branches missed.">        if (random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L161">            String subdomain = COMMON_SUBDOMAINS.get(random.nextInt(COMMON_SUBDOMAINS.size()));</span>
<span class="nc" id="L162">            hostname.append(subdomain).append(&quot;.&quot;);</span>
        }

        // 生成主域名
<span class="nc" id="L166">        String mainDomain = generateRandomDomainName();</span>
<span class="nc" id="L167">        hostname.append(mainDomain);</span>

        // 添加顶级域名
<span class="nc" id="L170">        String tld = COMMON_TLDS.get(random.nextInt(COMMON_TLDS.size()));</span>
<span class="nc" id="L171">        hostname.append(&quot;.&quot;).append(tld);</span>

<span class="nc" id="L173">        return hostname.toString();</span>
    }

    private String generateRandomDomainName() {
        // 生成随机域名
<span class="nc" id="L178">        String[] prefixes = { &quot;tech&quot;, &quot;data&quot;, &quot;cloud&quot;, &quot;web&quot;, &quot;app&quot;, &quot;digital&quot;, &quot;smart&quot;, &quot;global&quot; };</span>
<span class="nc" id="L179">        String[] suffixes = { &quot;corp&quot;, &quot;inc&quot;, &quot;ltd&quot;, &quot;group&quot;, &quot;systems&quot;, &quot;solutions&quot;, &quot;services&quot;, &quot;labs&quot; };</span>

<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L182">            return prefixes[random.nextInt(prefixes.length)] +</span>
<span class="nc" id="L183">                    suffixes[random.nextInt(suffixes.length)];</span>
        } else {
            // 生成随机字符串
<span class="nc" id="L186">            StringBuilder name = new StringBuilder();</span>
<span class="nc" id="L187">            int length = 5 + random.nextInt(8);</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">            for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L189">                name.append((char) ('a' + random.nextInt(26)));</span>
            }
<span class="nc" id="L191">            return name.toString();</span>
        }
    }

    private String generatePort(String port, String scheme) {
<span class="nc bnc" id="L196" title="All 4 branches missed.">        if (port != null &amp;&amp; !port.isEmpty()) {</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">            if (&quot;random&quot;.equalsIgnoreCase(port)) {</span>
<span class="nc" id="L198">                return String.valueOf(1024 + random.nextInt(64512)); // 1024-65535</span>
            } else {
<span class="nc" id="L200">                return port;</span>
            }
        }

        // 10%概率添加非默认端口
<span class="nc bnc" id="L205" title="All 2 branches missed.">        if (random.nextDouble() &lt; 0.1) {</span>
<span class="nc" id="L206">            int[] commonPorts = { 8080, 8443, 3000, 8000, 9000, 8888, 8090, 9090 };</span>
<span class="nc" id="L207">            return String.valueOf(commonPorts[random.nextInt(commonPorts.length)]);</span>
        }

<span class="nc" id="L210">        return null; // 使用默认端口</span>
    }

    private String generatePath(int maxDepth) {
<span class="nc bnc" id="L214" title="All 2 branches missed.">        if (maxDepth &lt;= 0) {</span>
<span class="nc" id="L215">            return &quot;&quot;;</span>
        }

<span class="nc" id="L218">        StringBuilder path = new StringBuilder();</span>
<span class="nc" id="L219">        int depth = random.nextInt(maxDepth) + 1;</span>

<span class="nc bnc" id="L221" title="All 2 branches missed.">        for (int i = 0; i &lt; depth; i++) {</span>
<span class="nc" id="L222">            path.append(&quot;/&quot;);</span>

<span class="nc bnc" id="L224" title="All 4 branches missed.">            if (i == depth - 1 &amp;&amp; random.nextDouble() &lt; 0.3) {</span>
                // 最后一级可能是文件
<span class="nc" id="L226">                path.append(generateFileName());</span>
            } else {
                // 路径段
<span class="nc" id="L229">                String segment = PATH_WORDS.get(random.nextInt(PATH_WORDS.size()));</span>

                // 30%概率添加数字或ID
<span class="nc bnc" id="L232" title="All 2 branches missed.">                if (random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L233">                    segment += random.nextInt(1000);</span>
                }

<span class="nc" id="L236">                path.append(segment);</span>
            }
        }

<span class="nc" id="L240">        return path.toString();</span>
    }

    private String generateFileName() {
<span class="nc" id="L244">        String[] names = { &quot;index&quot;, &quot;home&quot;, &quot;about&quot;, &quot;contact&quot;, &quot;data&quot;, &quot;config&quot;, &quot;settings&quot; };</span>
<span class="nc" id="L245">        String[] extensions = { &quot;html&quot;, &quot;php&quot;, &quot;jsp&quot;, &quot;asp&quot;, &quot;json&quot;, &quot;xml&quot;, &quot;txt&quot;, &quot;pdf&quot; };</span>

<span class="nc" id="L247">        String name = names[random.nextInt(names.length)];</span>
<span class="nc" id="L248">        String ext = extensions[random.nextInt(extensions.length)];</span>

<span class="nc" id="L250">        return name + &quot;.&quot; + ext;</span>
    }

    private String generateQuery() {
<span class="nc" id="L254">        StringBuilder query = new StringBuilder();</span>
<span class="nc" id="L255">        int paramCount = 1 + random.nextInt(4); // 1-4个参数</span>

<span class="nc" id="L257">        Set&lt;String&gt; usedParams = new HashSet&lt;&gt;();</span>

<span class="nc bnc" id="L259" title="All 2 branches missed.">        for (int i = 0; i &lt; paramCount; i++) {</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L261">                query.append(&quot;&amp;&quot;);</span>
            }

            String param;
            do {
<span class="nc" id="L266">                param = QUERY_PARAMS.get(random.nextInt(QUERY_PARAMS.size()));</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">            } while (usedParams.contains(param));</span>

<span class="nc" id="L269">            usedParams.add(param);</span>

<span class="nc" id="L271">            String value = generateQueryValue(param);</span>
<span class="nc" id="L272">            query.append(param).append(&quot;=&quot;).append(value);</span>
        }

<span class="nc" id="L275">        return query.toString();</span>
    }

    private String generateQueryValue(String param) {
<span class="nc bnc" id="L279" title="All 7 branches missed.">        switch (param) {</span>
            case &quot;id&quot;:
<span class="nc" id="L281">                return String.valueOf(random.nextInt(10000));</span>
            case &quot;page&quot;:
<span class="nc" id="L283">                return String.valueOf(1 + random.nextInt(100));</span>
            case &quot;size&quot;:
            case &quot;limit&quot;:
<span class="nc" id="L286">                return String.valueOf(10 + random.nextInt(90));</span>
            case &quot;sort&quot;:
<span class="nc bnc" id="L288" title="All 2 branches missed.">                return random.nextBoolean() ? &quot;asc&quot; : &quot;desc&quot;;</span>
            case &quot;format&quot;:
<span class="nc bnc" id="L290" title="All 2 branches missed.">                return random.nextBoolean() ? &quot;json&quot; : &quot;xml&quot;;</span>
            case &quot;lang&quot;:
<span class="nc" id="L292">                String[] langs = { &quot;en&quot;, &quot;zh&quot;, &quot;fr&quot;, &quot;de&quot;, &quot;ja&quot;, &quot;ko&quot; };</span>
<span class="nc" id="L293">                return langs[random.nextInt(langs.length)];</span>
            default:
                // 生成随机值
<span class="nc bnc" id="L296" title="All 2 branches missed.">                if (random.nextBoolean()) {</span>
<span class="nc" id="L297">                    return String.valueOf(random.nextInt(1000));</span>
                } else {
<span class="nc" id="L299">                    return &quot;value&quot; + random.nextInt(100);</span>
                }
        }
    }

    private String generateFragment() {
<span class="nc" id="L305">        String[] fragments = {</span>
                &quot;top&quot;, &quot;bottom&quot;, &quot;header&quot;, &quot;footer&quot;, &quot;content&quot;, &quot;sidebar&quot;, &quot;nav&quot;, &quot;menu&quot;,
                &quot;section1&quot;, &quot;section2&quot;, &quot;chapter1&quot;, &quot;chapter2&quot;, &quot;page1&quot;, &quot;page2&quot;
        };

<span class="nc" id="L310">        return fragments[random.nextInt(fragments.length)];</span>
    }

    private String extractScheme(String url) {
<span class="nc" id="L314">        int index = url.indexOf(&quot;://&quot;);</span>
<span class="nc bnc" id="L315" title="All 2 branches missed.">        if (index &gt; 0) {</span>
<span class="nc" id="L316">            return url.substring(0, index);</span>
        }
<span class="nc" id="L318">        return &quot;unknown&quot;;</span>
    }

    private String extractDomain(String url) {
        try {
<span class="nc" id="L323">            int start = url.indexOf(&quot;://&quot;) + 3;</span>
<span class="nc" id="L324">            int end = url.indexOf(&quot;/&quot;, start);</span>
<span class="nc bnc" id="L325" title="All 2 branches missed.">            if (end == -1) {</span>
<span class="nc" id="L326">                end = url.indexOf(&quot;?&quot;, start);</span>
            }
<span class="nc bnc" id="L328" title="All 2 branches missed.">            if (end == -1) {</span>
<span class="nc" id="L329">                end = url.indexOf(&quot;#&quot;, start);</span>
            }
<span class="nc bnc" id="L331" title="All 2 branches missed.">            if (end == -1) {</span>
<span class="nc" id="L332">                end = url.length();</span>
            }

<span class="nc" id="L335">            String hostPort = url.substring(start, end);</span>
<span class="nc" id="L336">            int portIndex = hostPort.indexOf(&quot;:&quot;);</span>
<span class="nc bnc" id="L337" title="All 2 branches missed.">            if (portIndex &gt; 0) {</span>
<span class="nc" id="L338">                return hostPort.substring(0, portIndex);</span>
            }
<span class="nc" id="L340">            return hostPort;</span>
<span class="nc" id="L341">        } catch (Exception e) {</span>
<span class="nc" id="L342">            return &quot;unknown&quot;;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>