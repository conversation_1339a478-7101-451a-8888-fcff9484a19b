<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ProxyGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">ProxyGenerator.java</span></div><h1>ProxyGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * 代理配置生成器
 * 
 * &lt;p&gt;
 * 支持生成各种代理服务器配置信息，包括HTTP代理、SOCKS代理、
 * 透明代理等，用于网络测试、爬虫开发、负载均衡测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;format: 输出格式 (URL|JSON|CONFIG|PAC) 默认: URL&lt;/li&gt;
 * &lt;li&gt;type: 代理类型 (HTTP|HTTPS|SOCKS4|SOCKS5|TRANSPARENT|RANDOM) 默认: HTTP&lt;/li&gt;
 * &lt;li&gt;host: 代理服务器地址（如果不指定则随机生成）&lt;/li&gt;
 * &lt;li&gt;port: 代理服务器端口（如果不指定则根据类型生成）&lt;/li&gt;
 * &lt;li&gt;username: 代理用户名（可选）&lt;/li&gt;
 * &lt;li&gt;password: 代理密码（可选）&lt;/li&gt;
 * &lt;li&gt;auth_required: 是否需要认证 默认: false&lt;/li&gt;
 * &lt;li&gt;anonymous: 是否为匿名代理 默认: false&lt;/li&gt;
 * &lt;li&gt;country: 代理服务器所在国家（可选）&lt;/li&gt;
 * &lt;li&gt;city: 代理服务器所在城市（可选）&lt;/li&gt;
 * &lt;li&gt;speed: 代理速度等级 (SLOW|MEDIUM|FAST|RANDOM) 默认: MEDIUM&lt;/li&gt;
 * &lt;li&gt;reliability: 可靠性等级 (LOW|MEDIUM|HIGH|RANDOM) 默认: MEDIUM&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L39">public class ProxyGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L41">    private static final Logger logger = LoggerFactory.getLogger(ProxyGenerator.class);</span>
<span class="fc" id="L42">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 输出格式枚举
<span class="fc" id="L45">    public enum OutputFormat {</span>
<span class="fc" id="L46">        URL(&quot;代理URL格式&quot;),</span>
<span class="fc" id="L47">        JSON(&quot;JSON配置格式&quot;),</span>
<span class="fc" id="L48">        CONFIG(&quot;配置对象格式&quot;),</span>
<span class="fc" id="L49">        PAC(&quot;PAC脚本格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L53">        OutputFormat(String description) {</span>
<span class="fc" id="L54">            this.description = description;</span>
<span class="fc" id="L55">        }</span>
        
        public String getDescription() {
<span class="nc" id="L58">            return description;</span>
        }
    }
    
    // 代理类型枚举
<span class="fc" id="L63">    public enum ProxyType {</span>
<span class="fc" id="L64">        HTTP(&quot;HTTP代理&quot;),</span>
<span class="fc" id="L65">        HTTPS(&quot;HTTPS代理&quot;),</span>
<span class="fc" id="L66">        SOCKS4(&quot;SOCKS4代理&quot;),</span>
<span class="fc" id="L67">        SOCKS5(&quot;SOCKS5代理&quot;),</span>
<span class="fc" id="L68">        TRANSPARENT(&quot;透明代理&quot;),</span>
<span class="fc" id="L69">        RANDOM(&quot;随机类型&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L73">        ProxyType(String description) {</span>
<span class="fc" id="L74">            this.description = description;</span>
<span class="fc" id="L75">        }</span>
        
        public String getDescription() {
<span class="nc" id="L78">            return description;</span>
        }
    }
    
    // 速度等级枚举
<span class="fc" id="L83">    public enum SpeedLevel {</span>
<span class="fc" id="L84">        SLOW(&quot;慢速&quot;), MEDIUM(&quot;中速&quot;), FAST(&quot;快速&quot;), RANDOM(&quot;随机&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L88">        SpeedLevel(String description) {</span>
<span class="fc" id="L89">            this.description = description;</span>
<span class="fc" id="L90">        }</span>
        
        public String getDescription() {
<span class="nc" id="L93">            return description;</span>
        }
    }
    
    // 可靠性等级枚举
<span class="fc" id="L98">    public enum ReliabilityLevel {</span>
<span class="fc" id="L99">        LOW(&quot;低&quot;), MEDIUM(&quot;中&quot;), HIGH(&quot;高&quot;), RANDOM(&quot;随机&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L103">        ReliabilityLevel(String description) {</span>
<span class="fc" id="L104">            this.description = description;</span>
<span class="fc" id="L105">        }</span>
        
        public String getDescription() {
<span class="nc" id="L108">            return description;</span>
        }
    }
    
    // 常见代理服务器地址
<span class="fc" id="L113">    private static final List&lt;String&gt; COMMON_PROXY_HOSTS = Arrays.asList(</span>
        &quot;proxy.example.com&quot;, &quot;proxy1.example.com&quot;, &quot;proxy2.example.com&quot;,
        &quot;cache.example.com&quot;, &quot;gateway.example.com&quot;, &quot;forward.example.com&quot;,
        &quot;squid.example.com&quot;, &quot;nginx.example.com&quot;, &quot;haproxy.example.com&quot;
    );
    
    // 代理端口映射
<span class="fc" id="L120">    private static final Map&lt;ProxyType, List&lt;Integer&gt;&gt; PROXY_PORTS = new HashMap&lt;&gt;();</span>
    
    static {
<span class="fc" id="L123">        PROXY_PORTS.put(ProxyType.HTTP, Arrays.asList(8080, 3128, 8000, 8888, 9999));</span>
<span class="fc" id="L124">        PROXY_PORTS.put(ProxyType.HTTPS, Arrays.asList(8443, 3129, 8001, 8889, 9998));</span>
<span class="fc" id="L125">        PROXY_PORTS.put(ProxyType.SOCKS4, Arrays.asList(1080, 1081, 9050, 9051));</span>
<span class="fc" id="L126">        PROXY_PORTS.put(ProxyType.SOCKS5, Arrays.asList(1080, 1081, 1082, 9050, 9051));</span>
<span class="fc" id="L127">        PROXY_PORTS.put(ProxyType.TRANSPARENT, Arrays.asList(8080, 3128, 8000));</span>
    }
    
    // 国家和城市映射
<span class="fc" id="L131">    private static final Map&lt;String, List&lt;String&gt;&gt; COUNTRY_CITIES = new HashMap&lt;&gt;();</span>
    
    static {
<span class="fc" id="L134">        COUNTRY_CITIES.put(&quot;US&quot;, Arrays.asList(&quot;New York&quot;, &quot;Los Angeles&quot;, &quot;Chicago&quot;, &quot;Houston&quot;, &quot;Phoenix&quot;));</span>
<span class="fc" id="L135">        COUNTRY_CITIES.put(&quot;UK&quot;, Arrays.asList(&quot;London&quot;, &quot;Manchester&quot;, &quot;Birmingham&quot;, &quot;Leeds&quot;, &quot;Glasgow&quot;));</span>
<span class="fc" id="L136">        COUNTRY_CITIES.put(&quot;DE&quot;, Arrays.asList(&quot;Berlin&quot;, &quot;Munich&quot;, &quot;Hamburg&quot;, &quot;Cologne&quot;, &quot;Frankfurt&quot;));</span>
<span class="fc" id="L137">        COUNTRY_CITIES.put(&quot;JP&quot;, Arrays.asList(&quot;Tokyo&quot;, &quot;Osaka&quot;, &quot;Yokohama&quot;, &quot;Nagoya&quot;, &quot;Sapporo&quot;));</span>
<span class="fc" id="L138">        COUNTRY_CITIES.put(&quot;CN&quot;, Arrays.asList(&quot;Beijing&quot;, &quot;Shanghai&quot;, &quot;Guangzhou&quot;, &quot;Shenzhen&quot;, &quot;Hangzhou&quot;));</span>
<span class="fc" id="L139">    }</span>
    
    // 代理配置信息类
    public static class ProxyConfig {
        private final ProxyType type;
        private final String host;
        private final int port;
        private final String username;
        private final String password;
        private final boolean authRequired;
        private final boolean anonymous;
        private final String country;
        private final String city;
        private final SpeedLevel speed;
        private final ReliabilityLevel reliability;
        
        public ProxyConfig(ProxyType type, String host, int port, String username, String password,
                          boolean authRequired, boolean anonymous, String country, String city,
<span class="fc" id="L157">                          SpeedLevel speed, ReliabilityLevel reliability) {</span>
<span class="fc" id="L158">            this.type = type;</span>
<span class="fc" id="L159">            this.host = host;</span>
<span class="fc" id="L160">            this.port = port;</span>
<span class="fc" id="L161">            this.username = username;</span>
<span class="fc" id="L162">            this.password = password;</span>
<span class="fc" id="L163">            this.authRequired = authRequired;</span>
<span class="fc" id="L164">            this.anonymous = anonymous;</span>
<span class="fc" id="L165">            this.country = country;</span>
<span class="fc" id="L166">            this.city = city;</span>
<span class="fc" id="L167">            this.speed = speed;</span>
<span class="fc" id="L168">            this.reliability = reliability;</span>
<span class="fc" id="L169">        }</span>
        
        // Getters
<span class="fc" id="L172">        public ProxyType getType() { return type; }</span>
<span class="fc" id="L173">        public String getHost() { return host; }</span>
<span class="fc" id="L174">        public int getPort() { return port; }</span>
<span class="fc" id="L175">        public String getUsername() { return username; }</span>
<span class="fc" id="L176">        public String getPassword() { return password; }</span>
<span class="fc" id="L177">        public boolean isAuthRequired() { return authRequired; }</span>
<span class="fc" id="L178">        public boolean isAnonymous() { return anonymous; }</span>
<span class="fc" id="L179">        public String getCountry() { return country; }</span>
<span class="fc" id="L180">        public String getCity() { return city; }</span>
<span class="fc" id="L181">        public SpeedLevel getSpeed() { return speed; }</span>
<span class="fc" id="L182">        public ReliabilityLevel getReliability() { return reliability; }</span>
    }

    @Override
    public String getType() {
<span class="fc" id="L187">        return &quot;proxy&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L192">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取输出格式
<span class="fc" id="L199">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;URL&quot;);</span>
<span class="fc" id="L200">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 生成代理配置
<span class="fc" id="L203">            ProxyConfig proxyConfig = generateProxyConfig(config);</span>
            
            // 格式化输出
<span class="fc" id="L206">            return formatProxyConfig(proxyConfig, format);</span>
            
<span class="nc" id="L208">        } catch (Exception e) {</span>
<span class="nc" id="L209">            logger.error(&quot;Failed to generate proxy configuration&quot;, e);</span>
            // 返回一个默认的代理配置作为fallback
<span class="nc" id="L211">            return &quot;http://proxy.example.com:8080&quot;;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L220">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L221">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L222">            logger.warn(&quot;Invalid output format: {}, using URL as default&quot;, formatStr);</span>
<span class="nc" id="L223">            return OutputFormat.URL;</span>
        }
    }

    /**
     * 生成代理配置
     */
    private ProxyConfig generateProxyConfig(FieldConfig config) {
        // 获取代理类型
<span class="fc" id="L232">        String typeStr = getStringParam(config, &quot;type&quot;, &quot;HTTP&quot;);</span>
<span class="fc" id="L233">        ProxyType type = parseProxyType(typeStr);</span>
<span class="pc bpc" id="L234" title="1 of 2 branches missed.">        if (type == ProxyType.RANDOM) {</span>
<span class="nc" id="L235">            ProxyType[] types = {ProxyType.HTTP, ProxyType.HTTPS, ProxyType.SOCKS4, ProxyType.SOCKS5};</span>
<span class="nc" id="L236">            type = types[random.nextInt(types.length)];</span>
        }
        
        // 获取主机地址
<span class="fc" id="L240">        String host = getStringParam(config, &quot;host&quot;, null);</span>
<span class="fc bfc" id="L241" title="All 2 branches covered.">        if (host == null) {</span>
<span class="fc" id="L242">            host = generateProxyHost();</span>
        }
        
        // 获取端口
<span class="fc" id="L246">        int port = getIntParam(config, &quot;port&quot;, -1);</span>
<span class="fc bfc" id="L247" title="All 2 branches covered.">        if (port == -1) {</span>
<span class="fc" id="L248">            port = generateProxyPort(type);</span>
        }
        
        // 获取认证信息
<span class="fc" id="L252">        boolean authRequired = getBooleanParam(config, &quot;auth_required&quot;, false);</span>
<span class="fc" id="L253">        String username = null;</span>
<span class="fc" id="L254">        String password = null;</span>
        
<span class="fc bfc" id="L256" title="All 2 branches covered.">        if (authRequired) {</span>
<span class="fc" id="L257">            username = getStringParam(config, &quot;username&quot;, generateUsername());</span>
<span class="fc" id="L258">            password = getStringParam(config, &quot;password&quot;, generatePassword());</span>
        }
        
        // 获取其他属性
<span class="fc" id="L262">        boolean anonymous = getBooleanParam(config, &quot;anonymous&quot;, false);</span>
<span class="fc" id="L263">        String country = getStringParam(config, &quot;country&quot;, null);</span>
<span class="fc" id="L264">        String city = getStringParam(config, &quot;city&quot;, null);</span>
        
        // 如果指定了国家但没有指定城市，随机选择该国家的城市
<span class="pc bpc" id="L267" title="1 of 4 branches missed.">        if (country != null &amp;&amp; city == null) {</span>
<span class="fc" id="L268">            List&lt;String&gt; cities = COUNTRY_CITIES.get(country.toUpperCase());</span>
<span class="pc bpc" id="L269" title="2 of 4 branches missed.">            if (cities != null &amp;&amp; !cities.isEmpty()) {</span>
<span class="fc" id="L270">                city = cities.get(random.nextInt(cities.size()));</span>
            }
        }
        
        // 如果都没有指定，随机选择国家和城市
<span class="pc bpc" id="L275" title="1 of 4 branches missed.">        if (country == null &amp;&amp; city == null) {</span>
<span class="fc" id="L276">            String[] countries = COUNTRY_CITIES.keySet().toArray(new String[0]);</span>
<span class="fc" id="L277">            country = countries[random.nextInt(countries.length)];</span>
<span class="fc" id="L278">            List&lt;String&gt; cities = COUNTRY_CITIES.get(country);</span>
<span class="fc" id="L279">            city = cities.get(random.nextInt(cities.size()));</span>
        }
        
        // 获取速度和可靠性等级
<span class="fc" id="L283">        String speedStr = getStringParam(config, &quot;speed&quot;, &quot;MEDIUM&quot;);</span>
<span class="fc" id="L284">        SpeedLevel speed = parseSpeedLevel(speedStr);</span>
<span class="pc bpc" id="L285" title="1 of 2 branches missed.">        if (speed == SpeedLevel.RANDOM) {</span>
<span class="nc" id="L286">            SpeedLevel[] speeds = {SpeedLevel.SLOW, SpeedLevel.MEDIUM, SpeedLevel.FAST};</span>
<span class="nc" id="L287">            speed = speeds[random.nextInt(speeds.length)];</span>
        }
        
<span class="fc" id="L290">        String reliabilityStr = getStringParam(config, &quot;reliability&quot;, &quot;MEDIUM&quot;);</span>
<span class="fc" id="L291">        ReliabilityLevel reliability = parseReliabilityLevel(reliabilityStr);</span>
<span class="pc bpc" id="L292" title="1 of 2 branches missed.">        if (reliability == ReliabilityLevel.RANDOM) {</span>
<span class="nc" id="L293">            ReliabilityLevel[] reliabilities = {ReliabilityLevel.LOW, ReliabilityLevel.MEDIUM, ReliabilityLevel.HIGH};</span>
<span class="nc" id="L294">            reliability = reliabilities[random.nextInt(reliabilities.length)];</span>
        }
        
<span class="fc" id="L297">        return new ProxyConfig(type, host, port, username, password, authRequired,</span>
                             anonymous, country, city, speed, reliability);
    }

    /**
     * 解析代理类型
     */
    private ProxyType parseProxyType(String typeStr) {
        try {
<span class="fc" id="L306">            return ProxyType.valueOf(typeStr.toUpperCase());</span>
<span class="fc" id="L307">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L308">            logger.warn(&quot;Invalid proxy type: {}, using HTTP as default&quot;, typeStr);</span>
<span class="fc" id="L309">            return ProxyType.HTTP;</span>
        }
    }

    /**
     * 解析速度等级
     */
    private SpeedLevel parseSpeedLevel(String speedStr) {
        try {
<span class="fc" id="L318">            return SpeedLevel.valueOf(speedStr.toUpperCase());</span>
<span class="nc" id="L319">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L320">            logger.warn(&quot;Invalid speed level: {}, using MEDIUM as default&quot;, speedStr);</span>
<span class="nc" id="L321">            return SpeedLevel.MEDIUM;</span>
        }
    }

    /**
     * 解析可靠性等级
     */
    private ReliabilityLevel parseReliabilityLevel(String reliabilityStr) {
        try {
<span class="fc" id="L330">            return ReliabilityLevel.valueOf(reliabilityStr.toUpperCase());</span>
<span class="nc" id="L331">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L332">            logger.warn(&quot;Invalid reliability level: {}, using MEDIUM as default&quot;, reliabilityStr);</span>
<span class="nc" id="L333">            return ReliabilityLevel.MEDIUM;</span>
        }
    }

    /**
     * 生成代理主机地址
     */
    private String generateProxyHost() {
<span class="fc bfc" id="L341" title="All 2 branches covered.">        if (random.nextBoolean()) {</span>
            // 使用预定义的主机名
<span class="fc" id="L343">            return COMMON_PROXY_HOSTS.get(random.nextInt(COMMON_PROXY_HOSTS.size()));</span>
        } else {
            // 生成IP地址
<span class="fc" id="L346">            return generateRandomIP();</span>
        }
    }

    /**
     * 生成随机IP地址
     */
    private String generateRandomIP() {
        // 生成公网IP地址范围
<span class="fc" id="L355">        int[] ranges = {</span>
            1, 126,    // A类地址范围（排除127.x.x.x）
            128, 191,  // B类地址范围
            192, 223   // C类地址范围
        };
        
<span class="fc" id="L361">        int rangeIndex = random.nextInt(3) * 2;</span>
<span class="fc" id="L362">        int firstOctet = ranges[rangeIndex] + random.nextInt(ranges[rangeIndex + 1] - ranges[rangeIndex] + 1);</span>
        
<span class="fc" id="L364">        int secondOctet = random.nextInt(256);</span>
<span class="fc" id="L365">        int thirdOctet = random.nextInt(256);</span>
<span class="fc" id="L366">        int fourthOctet = 1 + random.nextInt(254); // 避免0和255</span>
        
<span class="fc" id="L368">        return String.format(&quot;%d.%d.%d.%d&quot;, firstOctet, secondOctet, thirdOctet, fourthOctet);</span>
    }

    /**
     * 生成代理端口
     */
    private int generateProxyPort(ProxyType type) {
<span class="fc" id="L375">        List&lt;Integer&gt; ports = PROXY_PORTS.get(type);</span>
<span class="pc bpc" id="L376" title="2 of 4 branches missed.">        if (ports != null &amp;&amp; !ports.isEmpty()) {</span>
<span class="fc" id="L377">            return ports.get(random.nextInt(ports.size()));</span>
        }
<span class="nc" id="L379">        return 8080; // 默认端口</span>
    }

    /**
     * 生成用户名
     */
    private String generateUsername() {
<span class="fc" id="L386">        String[] prefixes = {&quot;user&quot;, &quot;proxy&quot;, &quot;client&quot;, &quot;guest&quot;, &quot;test&quot;};</span>
<span class="fc" id="L387">        String prefix = prefixes[random.nextInt(prefixes.length)];</span>
<span class="fc" id="L388">        int number = 1000 + random.nextInt(9000);</span>
<span class="fc" id="L389">        return prefix + number;</span>
    }

    /**
     * 生成密码
     */
    private String generatePassword() {
<span class="fc" id="L396">        String chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&amp;*&quot;;</span>
<span class="fc" id="L397">        StringBuilder password = new StringBuilder();</span>
<span class="fc" id="L398">        int length = 8 + random.nextInt(8); // 8-15位密码</span>
        
<span class="fc bfc" id="L400" title="All 2 branches covered.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="fc" id="L401">            password.append(chars.charAt(random.nextInt(chars.length())));</span>
        }
        
<span class="fc" id="L404">        return password.toString();</span>
    }

    /**
     * 格式化代理配置
     */
    private String formatProxyConfig(ProxyConfig config, OutputFormat format) {
<span class="pc bpc" id="L411" title="2 of 5 branches missed.">        switch (format) {</span>
            case URL:
<span class="fc" id="L413">                return formatAsUrl(config);</span>
            case JSON:
<span class="fc" id="L415">                return formatAsJson(config);</span>
            case CONFIG:
<span class="nc" id="L417">                return formatAsConfig(config);</span>
            case PAC:
<span class="fc" id="L419">                return formatAsPac(config);</span>
            default:
<span class="nc" id="L421">                return formatAsUrl(config);</span>
        }
    }

    /**
     * 格式化为URL格式
     */
    private String formatAsUrl(ProxyConfig config) {
<span class="fc" id="L429">        StringBuilder url = new StringBuilder();</span>
        
        // 协议部分
<span class="pc bpc" id="L432" title="3 of 6 branches missed.">        switch (config.getType()) {</span>
            case HTTP:
<span class="fc" id="L434">                url.append(&quot;http://&quot;);</span>
<span class="fc" id="L435">                break;</span>
            case HTTPS:
<span class="fc" id="L437">                url.append(&quot;https://&quot;);</span>
<span class="fc" id="L438">                break;</span>
            case SOCKS4:
<span class="nc" id="L440">                url.append(&quot;socks4://&quot;);</span>
<span class="nc" id="L441">                break;</span>
            case SOCKS5:
<span class="fc" id="L443">                url.append(&quot;socks5://&quot;);</span>
<span class="fc" id="L444">                break;</span>
            case TRANSPARENT:
<span class="nc" id="L446">                url.append(&quot;http://&quot;);</span>
                break;
        }
        
        // 认证信息
<span class="pc bpc" id="L451" title="2 of 6 branches missed.">        if (config.isAuthRequired() &amp;&amp; config.getUsername() != null &amp;&amp; config.getPassword() != null) {</span>
<span class="fc" id="L452">            url.append(config.getUsername()).append(&quot;:&quot;).append(config.getPassword()).append(&quot;@&quot;);</span>
        }
        
        // 主机和端口
<span class="fc" id="L456">        url.append(config.getHost()).append(&quot;:&quot;).append(config.getPort());</span>
        
<span class="fc" id="L458">        return url.toString();</span>
    }

    /**
     * 格式化为JSON格式
     */
    private String formatAsJson(ProxyConfig config) {
<span class="fc" id="L465">        StringBuilder json = new StringBuilder(&quot;{&quot;);</span>
<span class="fc" id="L466">        json.append(&quot;\&quot;type\&quot;:\&quot;&quot;).append(config.getType().name().toLowerCase()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L467">        json.append(&quot;\&quot;host\&quot;:\&quot;&quot;).append(config.getHost()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L468">        json.append(&quot;\&quot;port\&quot;:&quot;).append(config.getPort()).append(&quot;,&quot;);</span>
<span class="fc" id="L469">        json.append(&quot;\&quot;authRequired\&quot;:&quot;).append(config.isAuthRequired()).append(&quot;,&quot;);</span>
<span class="fc" id="L470">        json.append(&quot;\&quot;anonymous\&quot;:&quot;).append(config.isAnonymous());</span>
        
<span class="pc bpc" id="L472" title="1 of 2 branches missed.">        if (config.getUsername() != null) {</span>
<span class="nc" id="L473">            json.append(&quot;,\&quot;username\&quot;:\&quot;&quot;).append(config.getUsername()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L476" title="1 of 2 branches missed.">        if (config.getPassword() != null) {</span>
<span class="nc" id="L477">            json.append(&quot;,\&quot;password\&quot;:\&quot;&quot;).append(config.getPassword()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L480" title="1 of 2 branches missed.">        if (config.getCountry() != null) {</span>
<span class="fc" id="L481">            json.append(&quot;,\&quot;country\&quot;:\&quot;&quot;).append(config.getCountry()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L484" title="1 of 2 branches missed.">        if (config.getCity() != null) {</span>
<span class="fc" id="L485">            json.append(&quot;,\&quot;city\&quot;:\&quot;&quot;).append(config.getCity()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="fc" id="L488">        json.append(&quot;,\&quot;speed\&quot;:\&quot;&quot;).append(config.getSpeed().name().toLowerCase()).append(&quot;\&quot;&quot;);</span>
<span class="fc" id="L489">        json.append(&quot;,\&quot;reliability\&quot;:\&quot;&quot;).append(config.getReliability().name().toLowerCase()).append(&quot;\&quot;&quot;);</span>
<span class="fc" id="L490">        json.append(&quot;,\&quot;url\&quot;:\&quot;&quot;).append(formatAsUrl(config)).append(&quot;\&quot;&quot;);</span>
        
<span class="fc" id="L492">        json.append(&quot;}&quot;);</span>
<span class="fc" id="L493">        return json.toString();</span>
    }

    /**
     * 格式化为配置格式
     */
    private String formatAsConfig(ProxyConfig config) {
<span class="nc" id="L500">        StringBuilder configStr = new StringBuilder();</span>
<span class="nc" id="L501">        configStr.append(&quot;Proxy Configuration:\n&quot;);</span>
<span class="nc" id="L502">        configStr.append(&quot;  Type: &quot;).append(config.getType().name()).append(&quot;\n&quot;);</span>
<span class="nc" id="L503">        configStr.append(&quot;  Host: &quot;).append(config.getHost()).append(&quot;\n&quot;);</span>
<span class="nc" id="L504">        configStr.append(&quot;  Port: &quot;).append(config.getPort()).append(&quot;\n&quot;);</span>
<span class="nc" id="L505">        configStr.append(&quot;  URL: &quot;).append(formatAsUrl(config)).append(&quot;\n&quot;);</span>
<span class="nc bnc" id="L506" title="All 2 branches missed.">        configStr.append(&quot;  Authentication: &quot;).append(config.isAuthRequired() ? &quot;Required&quot; : &quot;Not Required&quot;).append(&quot;\n&quot;);</span>
<span class="nc bnc" id="L507" title="All 2 branches missed.">        configStr.append(&quot;  Anonymous: &quot;).append(config.isAnonymous() ? &quot;Yes&quot; : &quot;No&quot;).append(&quot;\n&quot;);</span>
        
<span class="nc bnc" id="L509" title="All 2 branches missed.">        if (config.getCountry() != null) {</span>
<span class="nc" id="L510">            configStr.append(&quot;  Country: &quot;).append(config.getCountry()).append(&quot;\n&quot;);</span>
        }
        
<span class="nc bnc" id="L513" title="All 2 branches missed.">        if (config.getCity() != null) {</span>
<span class="nc" id="L514">            configStr.append(&quot;  City: &quot;).append(config.getCity()).append(&quot;\n&quot;);</span>
        }
        
<span class="nc" id="L517">        configStr.append(&quot;  Speed: &quot;).append(config.getSpeed().getDescription()).append(&quot;\n&quot;);</span>
<span class="nc" id="L518">        configStr.append(&quot;  Reliability: &quot;).append(config.getReliability().getDescription());</span>
        
<span class="nc" id="L520">        return configStr.toString();</span>
    }

    /**
     * 格式化为PAC脚本格式
     */
    private String formatAsPac(ProxyConfig config) {
<span class="fc" id="L527">        String proxyUrl = formatAsUrl(config);</span>
<span class="fc" id="L528">        return String.format(</span>
            &quot;function FindProxyForURL(url, host) {\n&quot; +
            &quot;    return \&quot;PROXY %s:%d\&quot;;\n&quot; +
            &quot;}&quot;,
<span class="fc" id="L532">            config.getHost(), config.getPort()</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>