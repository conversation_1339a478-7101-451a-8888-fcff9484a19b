dataforge:
  count: 20
  output:
    format: csv
    file: "output/optimized-test.csv"
  fields:
    # 测试优化后的姓名生成器
    - name: "name"
      type: "name"
      params:
        type: "CN"
        gender: "ANY"
        use_weight: true
        allow_combination: true
    
    # 测试优化后的手机号生成器
    - name: "phone"
      type: "phone"
      params:
        region: "CN"
        operator: "ANY"
        use_weight: true
        valid: true
    
    # 测试优化后的银行卡生成器
    - name: "bankcard"
      type: "bankcard"
      params:
        type: "BOTH"
        issuer: "ANY"
        use_weight: true
        valid: true
    
    # 测试优化后的身份证生成器
    - name: "idcard"
      type: "idcard"
      params:
        region: "330106"  # 杭州西湖区
        birth_date_range: "1990-01-01,2000-12-31"
        gender: "ANY"
        valid: true
    
    # 测试优化后的邮箱生成器
    - name: "email"
      type: "email"
      params:
        type: "PERSONAL"
        prefix_name: true
        username_length: "6,12"
        use_weight: true
        valid: true
    
    # 测试车牌号生成器
    - name: "license_plate"
      type: "licenseplate"
      params:
        type: "FUEL"
        province: "浙"
        valid: true
    
    # 测试UUID生成器
    - name: "uuid"
      type: "uuid"
      params:
        type: "UUID4"