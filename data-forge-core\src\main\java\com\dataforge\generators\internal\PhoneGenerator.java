package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 手机号码生成器。
 * 
 * <p>
 * 生成符合中国大陆运营商号段规则的11位手机号码。
 * 支持指定运营商前缀和生成有效/无效号码。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class PhoneGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(PhoneGenerator.class);

    /**
     * 中国移动号段前缀。
     */
    private static final List<String> CHINA_MOBILE_PREFIXES = Arrays.asList(
            "134", "135", "136", "137", "138", "139", "147", "148", "150", "151",
            "152", "157", "158", "159", "172", "178", "182", "183", "184", "187",
            "188", "195", "197", "198");

    /**
     * 中国联通号段前缀。
     */
    private static final List<String> CHINA_UNICOM_PREFIXES = Arrays.asList(
            "130", "131", "132", "145", "146", "155", "156", "166", "167", "171",
            "175", "176", "185", "186", "196");

    /**
     * 中国电信号段前缀。
     */
    private static final List<String> CHINA_TELECOM_PREFIXES = Arrays.asList(
            "133", "149", "153", "173", "174", "177", "180", "181", "189", "191",
            "193", "199");

    /**
     * 虚拟运营商号段前缀。
     */
    private static final List<String> VIRTUAL_OPERATOR_PREFIXES = Arrays.asList(
            "162", "165", "167", "170", "171");

    /**
     * 所有有效号段前缀。
     */
    private static final List<String> ALL_VALID_PREFIXES;

    static {
        ALL_VALID_PREFIXES = new java.util.ArrayList<>();
        ALL_VALID_PREFIXES.addAll(CHINA_MOBILE_PREFIXES);
        ALL_VALID_PREFIXES.addAll(CHINA_UNICOM_PREFIXES);
        ALL_VALID_PREFIXES.addAll(CHINA_TELECOM_PREFIXES);
        ALL_VALID_PREFIXES.addAll(VIRTUAL_OPERATOR_PREFIXES);
    }

    private final Random random = new Random();

    @Override
    public String getType() {
        return "phone";
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 从参数中获取地区，默认为中国大陆
            String region = getStringParam(config, "region", "CN");

            if (!"CN".equalsIgnoreCase(region)) {
                logger.warn("Unsupported region: {}, using CN", region);
            }

            // 从参数中获取是否生成有效号码
            boolean valid = getBooleanParam(config, "valid", true);

            if (!valid) {
                return generateInvalidPhone();
            }

            // 从参数中获取指定的前缀
            String prefixParam = getStringParam(config, "prefix", null);
            List<String> allowedPrefixes = parseAllowedPrefixes(prefixParam);

            // 从参数中获取运营商类型
            String operator = getStringParam(config, "operator", "ANY");

            return generateValidPhone(allowedPrefixes, operator);

        } catch (Exception e) {
            logger.error("Failed to generate phone number", e);
            // 返回一个默认手机号作为fallback
            return "13800138000";
        }
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    /**
     * 生成有效的手机号码。
     * 
     * @param allowedPrefixes 允许的前缀列表
     * @param operator        运营商类型
     * @return 有效的手机号码
     */
    private String generateValidPhone(List<String> allowedPrefixes, String operator) {
        List<String> prefixes = selectPrefixesByOperator(operator);

        // 如果指定了允许的前缀，取交集
        if (allowedPrefixes != null && !allowedPrefixes.isEmpty()) {
            prefixes = prefixes.stream()
                    .filter(allowedPrefixes::contains)
                    .collect(java.util.stream.Collectors.toList());
        }

        if (prefixes.isEmpty()) {
            logger.warn("No valid prefixes found, using all valid prefixes");
            prefixes = ALL_VALID_PREFIXES;
        }

        // 随机选择前缀
        String prefix = prefixes.get(random.nextInt(prefixes.size()));

        // 生成后8位数字
        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 8; i++) {
            phone.append(random.nextInt(10));
        }

        return phone.toString();
    }

    /**
     * 生成无效的手机号码。
     * 
     * @return 无效的手机号码
     */
    private String generateInvalidPhone() {
        int type = random.nextInt(4);

        return switch (type) {
            case 0 -> generateWrongLengthPhone();
            case 1 -> generateWrongPrefixPhone();
            case 2 -> generateNonNumericPhone();
            default -> generateOtherInvalidPhone();
        };
    }

    /**
     * 生成长度错误的手机号码。
     * 
     * @return 长度错误的手机号码
     */
    private String generateWrongLengthPhone() {
        int length = random.nextBoolean() ? random.nextInt(5) + 5 : // 5-9位
                random.nextInt(5) + 12; // 12-16位

        StringBuilder phone = new StringBuilder();
        for (int i = 0; i < length; i++) {
            phone.append(random.nextInt(10));
        }

        return phone.toString();
    }

    /**
     * 生成前缀错误的手机号码。
     * 
     * @return 前缀错误的手机号码
     */
    private String generateWrongPrefixPhone() {
        // 生成不存在的前缀
        String[] invalidPrefixes = { "100", "101", "102", "110", "111", "120", "121", "122", "123", "124" };
        String prefix = invalidPrefixes[random.nextInt(invalidPrefixes.length)];

        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 8; i++) {
            phone.append(random.nextInt(10));
        }

        return phone.toString();
    }

    /**
     * 生成包含非数字字符的手机号码。
     * 
     * @return 包含非数字字符的手机号码
     */
    private String generateNonNumericPhone() {
        String validPrefix = ALL_VALID_PREFIXES.get(random.nextInt(ALL_VALID_PREFIXES.size()));
        StringBuilder phone = new StringBuilder(validPrefix);

        // 在后8位中随机插入字母
        for (int i = 0; i < 8; i++) {
            if (random.nextInt(4) == 0) { // 25%概率插入字母
                phone.append((char) ('A' + random.nextInt(26)));
            } else {
                phone.append(random.nextInt(10));
            }
        }

        return phone.toString();
    }

    /**
     * 生成其他类型的无效手机号码。
     * 
     * @return 其他无效手机号码
     */
    private String generateOtherInvalidPhone() {
        // 生成全0或全相同数字的号码
        if (random.nextBoolean()) {
            return "00000000000";
        } else {
            int digit = random.nextInt(10);
            return String.valueOf(digit).repeat(11);
        }
    }

    /**
     * 根据运营商类型选择前缀。
     * 
     * @param operator 运营商类型
     * @return 前缀列表
     */
    private List<String> selectPrefixesByOperator(String operator) {
        return switch (operator.toUpperCase()) {
            case "MOBILE", "CHINA_MOBILE" -> CHINA_MOBILE_PREFIXES;
            case "UNICOM", "CHINA_UNICOM" -> CHINA_UNICOM_PREFIXES;
            case "TELECOM", "CHINA_TELECOM" -> CHINA_TELECOM_PREFIXES;
            case "VIRTUAL" -> VIRTUAL_OPERATOR_PREFIXES;
            default -> ALL_VALID_PREFIXES;
        };
    }

    /**
     * 解析允许的前缀参数。
     * 
     * @param prefixParam 前缀参数字符串
     * @return 前缀列表
     */
    private List<String> parseAllowedPrefixes(String prefixParam) {
        if (prefixParam == null || prefixParam.trim().isEmpty()) {
            return null;
        }

        return Arrays.stream(prefixParam.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 从配置中获取字符串参数。
     * 
     * @param config       字段配置
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
        if (config == null || config.getParams() == null) {
            return defaultValue;
        }

        Object value = config.getParams().get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 从配置中获取布尔参数。
     * 
     * @param config       字段配置
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
        if (config == null || config.getParams() == null) {
            return defaultValue;
        }

        Object value = config.getParams().get(key);
        if (value == null) {
            return defaultValue;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        return Boolean.parseBoolean(value.toString());
    }

    @Override
    public String getDescription() {
        return "Phone number generator - generates Chinese mobile phone numbers with operator support";
    }
}