<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.dataforge.generators.internal.NewGeneratorsTest" time="0.448" tests="7" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="G:\nifa\DataForge-spring\data-forge-core\target\test-classes;G:\nifa\DataForge-spring\data-forge-core\target\classes;D:\maven-repo\org\springframework\boot\spring-boot-starter\3.2.1\spring-boot-starter-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot\3.2.1\spring-boot-3.2.1.jar;D:\maven-repo\org\springframework\spring-context\6.1.2\spring-context-6.1.2.jar;D:\maven-repo\org\springframework\spring-aop\6.1.2\spring-aop-6.1.2.jar;D:\maven-repo\org\springframework\spring-beans\6.1.2\spring-beans-6.1.2.jar;D:\maven-repo\org\springframework\spring-expression\6.1.2\spring-expression-6.1.2.jar;D:\maven-repo\io\micrometer\micrometer-observation\1.12.1\micrometer-observation-1.12.1.jar;D:\maven-repo\io\micrometer\micrometer-commons\1.12.1\micrometer-commons-1.12.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-autoconfigure\3.2.1\spring-boot-autoconfigure-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-logging\3.2.1\spring-boot-starter-logging-3.2.1.jar;D:\maven-repo\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;D:\maven-repo\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;D:\maven-repo\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\maven-repo\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\maven-repo\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven-repo\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven-repo\org\springframework\spring-core\6.1.2\spring-core-6.1.2.jar;D:\maven-repo\org\springframework\spring-jcl\6.1.2\spring-jcl-6.1.2.jar;D:\maven-repo\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\maven-repo\org\springframework\boot\spring-boot-configuration-processor\3.2.1\spring-boot-configuration-processor-3.2.1.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-validation\3.2.1\spring-boot-starter-validation-3.2.1.jar;D:\maven-repo\org\apache\tomcat\embed\tomcat-embed-el\10.1.17\tomcat-embed-el-10.1.17.jar;D:\maven-repo\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\maven-repo\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven-repo\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\maven-repo\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-test\3.2.1\spring-boot-starter-test-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-test\3.2.1\spring-boot-test-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-test-autoconfigure\3.2.1\spring-boot-test-autoconfigure-3.2.1.jar;D:\maven-repo\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\maven-repo\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\maven-repo\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\maven-repo\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\maven-repo\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\maven-repo\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\maven-repo\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\maven-repo\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\maven-repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\maven-repo\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\maven-repo\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\maven-repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\maven-repo\org\springframework\spring-test\6.1.2\spring-test-6.1.2.jar;D:\maven-repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\maven-repo\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\maven-repo\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\maven-repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\maven-repo\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\maven-repo\org\mockito\mockito-core\5.8.0\mockito-core-5.8.0.jar;D:\maven-repo\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\maven-repo\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\maven-repo\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\maven-repo\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Java21\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire12730540548418571764\surefirebooter-20250815180009171_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire12730540548418571764 2025-08-15T18-00-08_951-jvmRun1 surefire-20250815180009171_1tmp surefire_0-20250815180009171_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="NewGeneratorsTest"/>
    <property name="surefire.test.class.path" value="G:\nifa\DataForge-spring\data-forge-core\target\test-classes;G:\nifa\DataForge-spring\data-forge-core\target\classes;D:\maven-repo\org\springframework\boot\spring-boot-starter\3.2.1\spring-boot-starter-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot\3.2.1\spring-boot-3.2.1.jar;D:\maven-repo\org\springframework\spring-context\6.1.2\spring-context-6.1.2.jar;D:\maven-repo\org\springframework\spring-aop\6.1.2\spring-aop-6.1.2.jar;D:\maven-repo\org\springframework\spring-beans\6.1.2\spring-beans-6.1.2.jar;D:\maven-repo\org\springframework\spring-expression\6.1.2\spring-expression-6.1.2.jar;D:\maven-repo\io\micrometer\micrometer-observation\1.12.1\micrometer-observation-1.12.1.jar;D:\maven-repo\io\micrometer\micrometer-commons\1.12.1\micrometer-commons-1.12.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-autoconfigure\3.2.1\spring-boot-autoconfigure-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-logging\3.2.1\spring-boot-starter-logging-3.2.1.jar;D:\maven-repo\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;D:\maven-repo\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;D:\maven-repo\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\maven-repo\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\maven-repo\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven-repo\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven-repo\org\springframework\spring-core\6.1.2\spring-core-6.1.2.jar;D:\maven-repo\org\springframework\spring-jcl\6.1.2\spring-jcl-6.1.2.jar;D:\maven-repo\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\maven-repo\org\springframework\boot\spring-boot-configuration-processor\3.2.1\spring-boot-configuration-processor-3.2.1.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\maven-repo\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-validation\3.2.1\spring-boot-starter-validation-3.2.1.jar;D:\maven-repo\org\apache\tomcat\embed\tomcat-embed-el\10.1.17\tomcat-embed-el-10.1.17.jar;D:\maven-repo\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\maven-repo\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven-repo\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\maven-repo\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\maven-repo\org\springframework\boot\spring-boot-starter-test\3.2.1\spring-boot-starter-test-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-test\3.2.1\spring-boot-test-3.2.1.jar;D:\maven-repo\org\springframework\boot\spring-boot-test-autoconfigure\3.2.1\spring-boot-test-autoconfigure-3.2.1.jar;D:\maven-repo\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\maven-repo\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\maven-repo\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\maven-repo\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\maven-repo\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\maven-repo\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\maven-repo\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\maven-repo\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\maven-repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\maven-repo\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\maven-repo\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\maven-repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\maven-repo\org\springframework\spring-test\6.1.2\spring-test-6.1.2.jar;D:\maven-repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\maven-repo\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\maven-repo\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\maven-repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\maven-repo\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\maven-repo\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\maven-repo\org\mockito\mockito-core\5.8.0\mockito-core-5.8.0.jar;D:\maven-repo\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\maven-repo\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\maven-repo\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\maven-repo\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java21"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="G:\nifa\DataForge-spring\data-forge-core"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire12730540548418571764\surefirebooter-20250815180009171_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.1+12-LTS-29"/>
    <property name="user.name" value="Lenovo"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\maven-repo"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.1"/>
    <property name="user.dir" value="G:\nifa\DataForge-spring\data-forge-core"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Java21\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\PowerShell\7;d:\cursor\resources\app\bin;C:\Program Files\Volta;D:\python\Scripts;D:\python;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;D:\starship\bin;D:\xftp;D:\xshell;C:\Users\<USER>\AppData\Local\Volta\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\PyCharm 2025.1.2\bin;d:\cursor\resources\app\bin;D:\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Windsurf\bin;D:\Microsoft VS Code\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;D:\Ollama;D:\xshell\;D:\xftp\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\RedHat\Podman\;C:\Program Files\PowerShell\7\;D:\nvim\bin;D:\mingw64\bin;D:\Git\Git\cmd;C:\Users\<USER>\AppData\Local\Volta\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\PyCharm 2025.1.2\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\Kiro\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.1+12-LTS-29"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testWebSocketGenerator" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.293"/>
  <testcase name="testRandomNumberGenerator" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.022"/>
  <testcase name="testDecimalGenerator" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.03"/>
  <testcase name="testProxyGenerator" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.017"/>
  <testcase name="testGeneratorConfigClasses" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.004"/>
  <testcase name="testErrorHandling" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.014">
    <system-out><![CDATA[18:00:10.617 [main] WARN com.dataforge.generators.internal.WebSocketGenerator -- Invalid output format: INVALID_FORMAT, using URL as default
18:00:10.625 [main] WARN com.dataforge.generators.internal.ProxyGenerator -- Invalid proxy type: INVALID_TYPE, using HTTP as default
18:00:10.625 [main] WARN com.dataforge.generators.internal.RandomNumberGenerator -- Invalid number type: INVALID_TYPE, using INT as default
18:00:10.625 [main] WARN com.dataforge.generators.internal.RandomNumberGenerator -- Invalid distribution type: INVALID_DIST, using UNIFORM as default
18:00:10.625 [main] WARN com.dataforge.generators.internal.DecimalGenerator -- Invalid decimal type: INVALID_TYPE, using DOUBLE as default
18:00:10.625 [main] WARN com.dataforge.generators.internal.DecimalGenerator -- Invalid output format: INVALID_FORMAT, using PLAIN as default
]]></system-out>
  </testcase>
  <testcase name="testGeneratorTypes" classname="com.dataforge.generators.internal.NewGeneratorsTest" time="0.002"/>
</testsuite>