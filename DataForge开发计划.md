<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
------------------------------------------------------------------------------------->
#### **阶段一：核心框架与基础设施 (Core Framework & Infrastructure)**

- A-1: 创建 data-forge-core 和 data-forge-cli Maven 模块。

- A-2: 配置 Checkstyle, PMD, JaCoCo 插件并集成到构建流程。

- A-3: 初始化 Git 仓库，建立 main 和 develop 分支。

- B-1: 定义 DataGenerator<T, C> 核心接口。

- B-2: 实现 GeneratorFactory，使用 Java ServiceLoader (SPI) 机制动态加载所有 DataGenerator 实现。

- C-1: 实现 DataForgeContext 类，用于在单次生成任务中传递和共享字段值。

- D-1: 创建基于 @ConfigurationProperties 的 ForgeConfig, FieldConfig 等配置模型类。

- D-2: 实现从 YAML/JSON 配置文件加载 ForgeConfig 的逻辑。

- D-3: 实现命令行参数到 ForgeConfig 的解析与合并逻辑。

- E-1: 定义 OutputStrategy 接口。

- E-2: 实现 ConsoleOutputStrategy (标准输出)。

- E-3: 实现 FileOutputStrategy (文件输出)。

- E-4: 实现 CsvFormatter，支持流式写入 CSV。

- E-5: 实现 JsonFormatter，支持流式写入 JSON。

- E-6: 实现 SqlInsertFormatter，生成 SQL INSERT 语句。

- F-1: 实现 LuhnValidator (用于银行卡号、IMEI)。

- F-2: 实现身份证号码校验位算法。

- F-3: 实现统一社会信用代码校验算法（GB32100-2015）。

- F-4: 实现组织机构代码校验算法（GB 11714-1997）。

- G-1: 创建 DataForgeService，作为编排核心逻辑的主服务。

- G-2: 在 DataForgeService 中实现主生成循环，处理 count、字段迭代、上下文传递和数据输出。

- H-1: 使用 Picocli 创建 GenerateCommand。

- H-2: 在 GenerateCommand 中实现对所有核心 CLI 参数的定义和接收。

- H-3: 将 GenerateCommand 与 DataForgeService 连接。

---

#### **阶段二：数据类型生成器开发 (Data Type Generator Development)**

- **1.1 姓名 (Name)**

  - 1.1.1: 实现中文姓名生成（基于姓氏频率）。

  - 1.1.2: 实现英文姓名生成。

  - 1.1.3: 支持 --name.type 参数 (CN|EN|BOTH)。

  - 1.1.4: 支持 --name.gender 参数，并与上下文的性别字段关联。

  - 1.1.5: 支持自定义姓氏/名字库文件加载。

  - 1.1.6: 实现可选的姓名拼音生成。

- **1.2 手机号码 (Phone Number)**

  - 1.2.1: 实现基于国内运营商号段的手机号生成。

  - 1.2.2: 支持 --phone.prefix 指定号段。

  - 1.2.3: 支持 --phone.valid 生成非法号码。

- **1.3 银行卡号 (Bank Card / Credit Card Number)**

  - 1.3.1: 实现基于 Luhn 算法的卡号生成。

  - 1.3.2: 内置常见银行 BIN 码库。

  - 1.3.3: 支持 --bankcard.type, --bankcard.issuer, --bankcard.bank 参数。

  - 1.3.4: 支持 --bankcard.valid 生成不合规卡号。

- **1.4 身份证号码 (ID Card Number)**

  - 1.4.1: 实现18位身份证号生成逻辑（地区码 + 生日 + 顺序码 + 校验位）。

  - 1.4.2: 内置最新行政区划代码库。

  - 1.4.3: 支持 --idcard.region 指定地区。

  - 1.4.4: 支持 --idcard.birth_date_range 指定生日范围。

  - 1.4.5: 支持 --idcard.gender 并与顺序码关联。

  - 1.4.6: 支持 --idcard.valid 生成非法身份证号。

  - 1.4.7: 实现与 DataForgeContext 的数据交换（输出生日、性别、地区）。

- **1.5 车牌号 (License Plate Number)**

  - 1.5.1: 实现燃油车牌生成。

  - 1.5.2: 实现新能源车牌生成。

  - 1.5.3: 支持 --licenseplate.type, --licenseplate.province, --licenseplate.city 参数。

- **1.6 地址 (Address)**

  - 1.6.1: 实现基于行政区划数据的层级地址生成。

  - 1.6.2: 支持 --address.province, city, district 参数。

  - 1.6.3: 支持 --address.detail_level 控制详细程度。

- **1.7 企业名称 (Company Name)**

  - 1.7.1: 实现基于行业关键词和公司类型的组合名称生成。

  - 1.7.2: 支持 --company.industry, type, prefix_region 参数。

- **1.8 统一社会信用代码 (USCC)**

  - 1.8.1: 实现基于 GB32100-2015 标准的 USCC 生成。

  - 1.8.2: 支持 --uscc.region 指定行政区划。

  - 1.8.3: 支持 --uscc.valid 生成非法代码。

- **1.9 组织机构代码 (Organization Code)**

  - 1.9.1: 实现基于 GB 11714-1997 标准的组织机构代码生成。

  - 1.9.2: 支持 --orgcode.valid 生成非法代码。

- **1.10 LEI码 (Legal Entity Identifier)**

  - 1.10.1: 实现基于 ISO 17442 标准的 LEI 码生成。

  - 1.10.2: 支持 --lei.valid 生成非法代码。

- **1.11 年龄 (Age)**

  - 1.11.1: 实现指定范围的年龄生成。

  - 1.11.2: 支持 --age.min, max 参数。

  - 1.11.3: 实现与上下文的出生日期字段关联。

- **1.12 邮箱 (Email)**

  - 1.12.1: 实现用户名+域名的邮箱生成。

  - 1.12.2: 支持 --email.domains 指定域名。

  - 1.12.3: 支持 --email.prefix_name 与上下文的姓名拼音关联。

- **1.13 账号名 (Account Name / Username)**

  - 1.13.1: 实现指定长度和字符集的账号名生成。

  - 1.13.2: 支持 --accountname.length, chars, prefix, suffix 参数。

- **1.14 密码 (Password)**

  - 1.14.1: 实现指定长度和复杂度的密码生成。

  - 1.14.2: 支持 --password.length, complexity 参数。

- **1.15 性别 (Gender)**

  - 1.15.1: 实现基于权重选择的性别生成。

  - 1.15.2: 支持 --gender.type, male_ratio 参数。

  - 1.15.3: 实现与上下文的身份证号、姓名字段关联。

- **1.16 职业/职位 (Occupation / Position)**

  - 1.16.1: 实现从预设库中随机选择职业/职位。

  - 1.16.2: 支持 --occupation.industry, level 参数。

- **1.17 学历 (Education Level)**

  - 1.17.1: 实现从预设学历层次中随机选择。

  - 1.17.2: 支持 --education.levels, distribution 参数。

- **1.18 婚姻状况 (Marital Status)**

  - 1.18.1: 实现从预设选项中随机选择婚姻状况。

  - 1.18.2: 支持 --marital.status, married_ratio 参数。

- **1.19 血型 (Blood Type)**

  - 1.19.1: 实现从预设选项中随机选择血型。

  - 1.19.2: 支持 --bloodtype.group, rh 参数。

- **1.20 星座 (Zodiac Sign)**

  - 1.20.1: 实现从十二星座中随机选择。

  - 1.20.2: 实现与上下文的出生日期字段关联。

- **1.21 民族 (Ethnicity)**

  - 1.21.1: 实现从56个民族中随机选择。

  - 1.21.2: 支持 --ethnicity.type, han_ratio 参数。

- **1.22 宗教信仰 (Religion)**

  - 1.22.1: 实现从预设宗教类型中随机选择。

  - 1.22.2: 支持 --religion.type, none_ratio 参数。

- **2.1 全局唯一 ID (Global Unique ID)**

  - 2.1.1: 实现 UUID (v1, v4) 生成。

  - 2.1.2: 实现 ULID 生成。

  - 2.1.3: 实现 Snowflake ID 算法。

  - 2.1.4: 支持 --uuid.type 及 Snowflake 相关参数。

- **2.2 业务单据号 (Business Document Number)**

  - 2.2.1: 实现基于模板的单据号生成（前缀+日期+序列号）。

  - 2.2.2: 支持 --docnum.prefix, date_format, sequence_length, sequence_mode 等参数。

- **2.3 产品编码 (Product Code)**

  - 2.3.1: 实现 SKU 生成。

  - 2.3.2: 实现 GTIN (EAN/UPC) 生成。

  - 2.3.3: 实现 ISBN (10/13) 生成。

  - 2.3.4: 实现 ISSN 生成。

  - 2.3.5: 支持 --productcode.type 及相关参数。

- **2.4 社保／医保号 (Social Security / Medical Insurance Number)**

  - 2.4.1: 实现社保/医保号生成。

  - 2.4.2: 支持 --social_id.link_idcard 与上下文的身份证号关联。

- **2.5 护照号、签证号、驾驶证号 (Passport, Visa, Driver's License Number)**

  - 2.5.1: 实现护照号生成。

  - 2.5.2: 实现签证号生成。

  - 2.5.3: 实现驾驶证号生成（关联身份证）。

  - 2.5.4: 支持 --doc_id.type, country 参数。

- **2.6 优惠券码/促销码 (Coupon Code / Promo Code)**

  - 2.6.1: 实现指定长度和字符集的优惠券码生成。

  - 2.6.2: 支持 --coupon.length, chars, prefix, suffix 参数。

- **2.7 物流单号/运单号 (Tracking Number / Waybill Number)**

  - 2.7.1: 实现物流单号生成。

  - 2.7.2: 支持 --tracking.carrier, prefix, length 参数。

- **3.1/3.2 邮箱/短信验证码 (Verification Code)**

  - 3.1.1: 实现指定长度和字符集的验证码生成。

  - 3.1.2: 支持 --email_code.length, chars 参数。

  - 3.2.1: 支持 --sms_code.length, chars 参数。

- **3.3/3.4 传真/固话号码 (Fax / Landline Number)**

  - 3.3.1: 实现区号+号码格式的号码生成。

  - 3.3.2: 支持 --fax.region, prefix 参数。

  - 3.4.1: 支持 --landline.area_code, extension_length 参数。

- **3.5 URL／URI**

  - 3.5.1: 实现基于各部分（协议、域名、路径、参数）组合的 URL 生成。

  - 3.5.2: 支持 --url.protocol, path_depth, param_count 等参数。

- **3.6 文件路径 (File Path)**

  - 3.6.1: 实现 Windows 和 Unix 风格的文件路径生成。

  - 3.6.2: 支持 --filepath.os, type, depth, extension 参数。

- **3.7 MIME Type**

  - 3.7.1: 实现从常用 MIME 类型库中随机选择。

  - 3.7.2: 支持 --mimetype.category 参数。

- **4.1 IP 地址 (IP Address)**

  - 4.1.1: 实现 IPv4 和 IPv6 地址生成。

  - 4.1.2: 支持 --ip.version, type, cidr 参数。

- **4.2 域名 (Domain Name)**

  - 4.2.1: 实现二级域名+顶级域名的域名生成。

  - 4.2.2: 支持 --domain.tld, length 参数。

- **4.3 MAC 地址 (MAC Address)**

  - 4.3.1: 实现 MAC 地址生成。

  - 4.3.2: 支持 --mac.format, oui 参数。

- **4.4 端口号 (Port Number)**

  - 4.4.1: 实现指定范围的端口号生成。

  - 4.4.2: 支持 --port.min, max, type 参数。

- **4.5 HTTP 头 (HTTP Header)**

  - 4.5.1: 实现常见 HTTP 请求头的生成。

  - 4.5.2: 支持 --http_header.name, value_type 参数。

- **4.6 Session ID／Token**

  - 4.6.1: 实现随机字符串和模拟 JWT 结构的 Token 生成。

  - 4.6.2: 支持 --token.type, length, jwt.payload 参数。

- **4.7 设备 ID／IMEI／IMSI**

  - 4.7.1: 实现 IMEI (含Luhn校验) 和 IMSI 生成。

  - 4.7.2: 支持 --device_id.type 及相关参数。

- **4.8 地理坐标 (Geographical Coordinates)**

  - 4.8.1: 实现经纬度、高度生成。

  - 4.8.2: 支持在指定区域内生成坐标。

- **4.9 时区标识 (Time Zone Identifier)**

  - 4.9.1: 实现 IANA 时区标识和 UTC 偏移量生成。

- **5.1 长文本 (Long Text)**

  - 5.1.1: 实现指定长度和语言的随机文本生成。

- **5.2 富文本／HTML、Markdown 片段**

  - 5.2.1: 实现包含 HTML 标签或 Markdown 语法的文本片段生成。

- **5.3 Unicode 边界字符**

  - 5.3.1: 实现包含 Emoji、控制字符、零宽字符的文本生成。

- **5.4 多语言示例**

  - 5.4.1: 实现包含多种语言（中、英、日、韩、阿拉伯文等）的文本生成。

- **5.5 特殊字符**

  - 5.5.1: 实现包含空格、符号、转义字符的文本生成。

- **5.6 用户行为数据**

  - 5.6.1: 实现点击流、搜索关键词、购物车内容的模拟数据生成。

- **6.1 JSON 对象／数组**

  - 6.1.1: 实现基于 JSON Schema 的数据生成。

  - 6.1.2: 实现基于模板文件和占位符的数据生成。

  - 6.1.3: 支持引用其他 DataGenerator 填充字段值。

- **6.2 XML 文档**

  - 6.2.1: 实现基于 XSD 的数据生成。

  - 6.2.2: 实现基于模板文件和占位符的数据生成。

- **6.3 YAML**

  - 6.3.1: 实现基于模板的 YAML 数据生成。

- **6.4 CSV／TSV**

  - 6.4.1: 实现基于列定义的 CSV/TSV 文件生成。

- **6.5 表单数据**

  - 6.5.1: 实现 application/x-www-form-urlencoded 和 multipart/form-data 格式数据生成。

- **7.1 可定义长度的随机数**

  - 7.1.1: 实现指定位数的随机整数生成。

- **7.2/7.3/7.4 小数/整数/负数**

  - 7.2.1: 实现指定范围和精度的数值生成。

- **7.5 币种**

  - 7.5.1: 实现 ISO 4217 货币代码、符号、名称生成。

- **7.11 颜色值**

  - 7.11.1: 实现 RGB, HEX, HSL 格式的颜色值生成。

- **8.1/8.2/8.3 日期/时间/时间戳**

  - 8.1.1: 实现指定范围和格式的日期、时间、时间戳生成。

- **8.8 Cron 表达式**

  - 8.8.1: 实现 Cron 表达式生成。

- **9.1 SQL 注入 payload**

  - 9.1.1: 内置常见 SQL 注入 payload 库。

  - 9.1.2: 支持 --sql_inject.type, db_type 参数。

- **9.2 XSS 攻击脚本**

  - 9.2.1: 内置常见 XSS payload 库。

- **9.3 命令注入**

  - 9.3.1: 内置常见命令注入 payload 库。

- **9.4 路径穿越**

  - 9.4.1: 内置常见路径穿越 payload 库。

- **9.5 二进制／Base64 编码数据**

  - 9.5.1: 实现随机二进制数据的 Base64 编码生成。

- **10.1 图像文件头**

  - 10.1.1: 实现常见图像文件（PNG, JPEG, GIF）的魔数生成。

- **10.7/10.9/10.11 文件扩展名/大小/图片尺寸**

  - 10.7.1: 实现文件元数据（扩展名、大小、尺寸）的生成。

- **11.1 HTTP 状态码**

  - 11.1.1: 实现标准 HTTP 状态码生成。

- **11.3 布尔型**

  - 11.3.1: 实现多种格式（true/false, 0/1, Y/N）的布尔值生成。

- **11.4 枚举**

  - 11.4.1: 实现从用户提供的列表中选择值的枚举生成器。

---

#### **阶段三：特殊场景数据与高级功能**

- **12.1 空值/Null值 (Empty / Null Values)**

  - 12.1.1: 实现一个 EmptyGenerator。

  - 12.1.2: 在主服务中根据频率概率性地调用 EmptyGenerator。

- **12.2 边界值/极端值 (Boundary / Extreme Values)**

  - 12.2.1: 实现边界值生成逻辑，并注入到相关数值、字符串、日期生成器中。

- **12.3 非法/异常数据 (Invalid / Exception Data)**

  - 12.3.1: 修改所有相关生成器，使其支持生成不合规的数据。

- **12.4 可自定义长度格式的业务编号**

  - 12.4.1: 实现一个通用的、可高度自定义格式的 ID 生成器。

- **12.5 重复数据 (Duplicate Data)**

  - 12.5.1: 在主服务中实现逻辑，以生成重复记录或字段。

- **12.6 排序数据 (Sorted Data)**

  - 12.6.1: 在主服务中实现对最终生成结果集进行排序的功能。

- **12.7 并发/竞争数据 (Concurrent / Contention Data)**

  - 12.7.1: 实现主服务的多线程生成能力。

  - 12.7.2: 实现 --concurrent.threads 参数以启动并发生成。

### **3. 后续步骤 (Next Steps)**

1. **任务分解:** 将此清单中的每一个条目（例如 "1.1.1: 实现中文姓名生成"）转换为项目管理工具中的一个独立任务。

2. **迭代规划:** 基于此清单，规划第一个迭代（Sprint），目标是完成 **阶段一** 的所有任务。

3. **启动开发:** 按照此清单的优先级顺序，从 A-1 开始，严格执行开发、测试、集成流程。
