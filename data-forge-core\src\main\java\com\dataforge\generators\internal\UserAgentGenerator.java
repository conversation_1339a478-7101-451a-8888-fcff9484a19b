package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * User-Agent生成器
 * 
 * <p>
 * 支持生成各种浏览器和操作系统的User-Agent字符串，用于Web应用测试、
 * 爬虫开发、浏览器兼容性测试等场景。
 * 
 * <p>
 * 支持的参数：
 * <ul>
 * <li>browser: 浏览器类型 (CHROME|FIREFOX|SAFARI|EDGE|IE|OPERA|RANDOM) 默认: RANDOM</li>
 * <li>os: 操作系统 (WINDOWS|MACOS|LINUX|ANDROID|IOS|RANDOM) 默认: RANDOM</li>
 * <li>device: 设备类型 (DESKTOP|MOBILE|TABLET|RANDOM) 默认: RANDOM</li>
 * <li>version: 是否包含版本号 默认: true</li>
 * <li>realistic: 是否生成真实的User-Agent 默认: true</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class UserAgentGenerator extends BaseGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(UserAgentGenerator.class);
    private static final SecureRandom random = new SecureRandom();
    
    // 浏览器类型枚举
    public enum BrowserType {
        CHROME, FIREFOX, SAFARI, EDGE, IE, OPERA, RANDOM
    }
    
    // 操作系统类型枚举
    public enum OSType {
        WINDOWS, MACOS, LINUX, ANDROID, IOS, RANDOM
    }
    
    // 设备类型枚举
    public enum DeviceType {
        DESKTOP, MOBILE, TABLET, RANDOM
    }
    
    // Chrome User-Agent模板
    private static final List<String> CHROME_TEMPLATES = Arrays.asList(
        "Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36",
        "Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Mobile Safari/537.36"
    );
    
    // Firefox User-Agent模板
    private static final List<String> FIREFOX_TEMPLATES = Arrays.asList(
        "Mozilla/5.0 ({os}; rv:{version}) Gecko/20100101 Firefox/{version}",
        "Mozilla/5.0 (Mobile; rv:{version}) Gecko/{version} Firefox/{version}"
    );
    
    // Safari User-Agent模板
    private static final List<String> SAFARI_TEMPLATES = Arrays.asList(
        "Mozilla/5.0 ({os}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Safari/{webkit_version}",
        "Mozilla/5.0 ({os}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Mobile/15E148 Safari/{webkit_version}"
    );
    
    // Edge User-Agent模板
    private static final List<String> EDGE_TEMPLATES = Arrays.asList(
        "Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 Edg/{version}",
        "Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 EdgA/{version}"
    );
    
    // 操作系统字符串映射
    private static final Map<OSType, List<String>> OS_STRINGS = new HashMap<>();
    
    static {
        OS_STRINGS.put(OSType.WINDOWS, Arrays.asList(
            "Windows NT 10.0; Win64; x64",
            "Windows NT 10.0; WOW64",
            "Windows NT 6.3; Win64; x64",
            "Windows NT 6.1; Win64; x64",
            "Windows NT 6.1; WOW64"
        ));
        
        OS_STRINGS.put(OSType.MACOS, Arrays.asList(
            "Macintosh; Intel Mac OS X 10_15_7",
            "Macintosh; Intel Mac OS X 10_14_6",
            "Macintosh; Intel Mac OS X 10_13_6",
            "Macintosh; Intel Mac OS X 11_2_3",
            "Macintosh; Intel Mac OS X 12_1"
        ));
        
        OS_STRINGS.put(OSType.LINUX, Arrays.asList(
            "X11; Linux x86_64",
            "X11; Ubuntu; Linux x86_64",
            "X11; Linux i686",
            "X11; CrOS x86_64"
        ));
        
        OS_STRINGS.put(OSType.ANDROID, Arrays.asList(
            "Linux; Android 11; SM-G991B",
            "Linux; Android 10; SM-A505F",
            "Linux; Android 9; SM-G960F",
            "Linux; Android 12; Pixel 6",
            "Linux; Android 11; OnePlus 9"
        ));
        
        OS_STRINGS.put(OSType.IOS, Arrays.asList(
            "iPhone; CPU iPhone OS 15_0 like Mac OS X",
            "iPhone; CPU iPhone OS 14_7_1 like Mac OS X",
            "iPad; CPU OS 15_0 like Mac OS X",
            "iPhone; CPU iPhone OS 13_7 like Mac OS X"
        ));
    }

    @Override
    public String getType() {
        return "user_agent";
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取浏览器类型
            String browserStr = getStringParam(config, "browser", "RANDOM");
            BrowserType browser = parseBrowserType(browserStr);
            
            // 获取操作系统类型
            String osStr = getStringParam(config, "os", "RANDOM");
            OSType os = parseOSType(osStr);
            
            // 获取设备类型
            String deviceStr = getStringParam(config, "device", "RANDOM");
            DeviceType device = parseDeviceType(deviceStr);
            
            // 获取其他参数
            boolean includeVersion = getBooleanParam(config, "version", true);
            boolean realistic = getBooleanParam(config, "realistic", true);
            
            // 如果是随机类型，则随机选择
            if (browser == BrowserType.RANDOM) {
                BrowserType[] browsers = {BrowserType.CHROME, BrowserType.FIREFOX, BrowserType.SAFARI, BrowserType.EDGE};
                browser = browsers[random.nextInt(browsers.length)];
            }
            
            if (os == OSType.RANDOM) {
                OSType[] oses = {OSType.WINDOWS, OSType.MACOS, OSType.LINUX, OSType.ANDROID, OSType.IOS};
                os = oses[random.nextInt(oses.length)];
            }
            
            if (device == DeviceType.RANDOM) {
                DeviceType[] devices = {DeviceType.DESKTOP, DeviceType.MOBILE, DeviceType.TABLET};
                device = devices[random.nextInt(devices.length)];
            }
            
            // 确保操作系统和设备类型的兼容性
            adjustOSAndDevice(os, device);
            
            return generateUserAgent(browser, os, device, includeVersion, realistic);
            
        } catch (Exception e) {
            logger.error("Failed to generate user agent", e);
            // 返回一个默认的Chrome User-Agent作为fallback
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        }
    }

    /**
     * 解析浏览器类型
     */
    private BrowserType parseBrowserType(String browserStr) {
        try {
            return BrowserType.valueOf(browserStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid browser type: {}, using RANDOM as default", browserStr);
            return BrowserType.RANDOM;
        }
    }

    /**
     * 解析操作系统类型
     */
    private OSType parseOSType(String osStr) {
        try {
            return OSType.valueOf(osStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid OS type: {}, using RANDOM as default", osStr);
            return OSType.RANDOM;
        }
    }

    /**
     * 解析设备类型
     */
    private DeviceType parseDeviceType(String deviceStr) {
        try {
            return DeviceType.valueOf(deviceStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid device type: {}, using RANDOM as default", deviceStr);
            return DeviceType.RANDOM;
        }
    }

    /**
     * 调整操作系统和设备类型的兼容性
     */
    private void adjustOSAndDevice(OSType os, DeviceType device) {
        // 确保移动操作系统与移动设备匹配
        if ((os == OSType.ANDROID || os == OSType.IOS) && device == DeviceType.DESKTOP) {
            // 移动操作系统不能是桌面设备，调整为移动设备
            device = DeviceType.MOBILE;
        }
        
        if ((os == OSType.WINDOWS || os == OSType.MACOS || os == OSType.LINUX) && 
            (device == DeviceType.MOBILE || device == DeviceType.TABLET)) {
            // 桌面操作系统通常是桌面设备
            device = DeviceType.DESKTOP;
        }
    }

    /**
     * 生成User-Agent字符串
     */
    private String generateUserAgent(BrowserType browser, OSType os, DeviceType device, 
                                   boolean includeVersion, boolean realistic) {
        
        String osString = getRandomOSString(os);
        String template = getRandomTemplate(browser, device);
        
        Map<String, String> replacements = new HashMap<>();
        replacements.put("{os}", osString);
        
        if (includeVersion) {
            replacements.put("{version}", generateVersion(browser, realistic));
            replacements.put("{webkit_version}", generateWebKitVersion(realistic));
            replacements.put("{chrome_version}", generateChromeVersion(realistic));
        } else {
            replacements.put("{version}", "1.0");
            replacements.put("{webkit_version}", "537.36");
            replacements.put("{chrome_version}", "91.0.4472.124");
        }
        
        String userAgent = template;
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            userAgent = userAgent.replace(entry.getKey(), entry.getValue());
        }
        
        return userAgent;
    }

    /**
     * 获取随机操作系统字符串
     */
    private String getRandomOSString(OSType os) {
        List<String> osStrings = OS_STRINGS.get(os);
        if (osStrings == null || osStrings.isEmpty()) {
            return "Windows NT 10.0; Win64; x64";
        }
        return osStrings.get(random.nextInt(osStrings.size()));
    }

    /**
     * 获取随机模板
     */
    private String getRandomTemplate(BrowserType browser, DeviceType device) {
        List<String> templates;
        
        switch (browser) {
            case CHROME:
                templates = CHROME_TEMPLATES;
                break;
            case FIREFOX:
                templates = FIREFOX_TEMPLATES;
                break;
            case SAFARI:
                templates = SAFARI_TEMPLATES;
                break;
            case EDGE:
                templates = EDGE_TEMPLATES;
                break;
            default:
                templates = CHROME_TEMPLATES;
                break;
        }
        
        // 根据设备类型选择合适的模板
        if (device == DeviceType.MOBILE && templates.size() > 1) {
            return templates.get(1); // 通常第二个模板是移动版本
        }
        
        return templates.get(0);
    }

    /**
     * 生成浏览器版本号
     */
    private String generateVersion(BrowserType browser, boolean realistic) {
        if (!realistic) {
            return "1.0.0";
        }
        
        switch (browser) {
            case CHROME:
                return String.format("%d.0.%d.%d", 
                    90 + random.nextInt(20), 
                    4000 + random.nextInt(1000), 
                    100 + random.nextInt(200));
            case FIREFOX:
                return String.format("%d.0", 80 + random.nextInt(20));
            case SAFARI:
                return String.format("%d.%d.%d", 
                    14 + random.nextInt(3), 
                    random.nextInt(10), 
                    random.nextInt(10));
            case EDGE:
                return String.format("%d.0.%d.%d", 
                    90 + random.nextInt(20), 
                    1000 + random.nextInt(1000), 
                    random.nextInt(100));
            default:
                return "1.0.0";
        }
    }

    /**
     * 生成WebKit版本号
     */
    private String generateWebKitVersion(boolean realistic) {
        if (!realistic) {
            return "537.36";
        }
        return String.format("537.%d", 30 + random.nextInt(10));
    }

    /**
     * 生成Chrome版本号（用于Edge等基于Chromium的浏览器）
     */
    private String generateChromeVersion(boolean realistic) {
        if (!realistic) {
            return "91.0.4472.124";
        }
        return String.format("%d.0.%d.%d", 
            90 + random.nextInt(20), 
            4000 + random.nextInt(1000), 
            100 + random.nextInt(200));
    }
}
