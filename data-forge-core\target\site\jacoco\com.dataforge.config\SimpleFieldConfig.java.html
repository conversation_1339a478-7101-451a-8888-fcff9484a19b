<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SimpleFieldConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.config</a> &gt; <span class="el_source">SimpleFieldConfig.java</span></div><h1>SimpleFieldConfig.java</h1><pre class="source lang-java linenums">package com.dataforge.config;

import com.dataforge.model.FieldConfig;
import java.util.Map;

/**
 * 简单字段配置实现类
 * 
 * 用于Jackson反序列化，因为Jackson无法直接实例化抽象类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SimpleFieldConfig extends FieldConfig {

    /**
     * 默认构造函数
     */
    public SimpleFieldConfig() {
<span class="nc" id="L20">        super();</span>
<span class="nc" id="L21">    }</span>

    /**
     * 构造函数
     * 
     * @param name 字段名称
     * @param type 数据类型标识符
     */
    public SimpleFieldConfig(String name, String type) {
<span class="nc" id="L30">        super(name, type);</span>
<span class="nc" id="L31">    }</span>

    /**
     * 构造函数
     * 
     * @param name   字段名称
     * @param type   数据类型标识符
     * @param params 参数映射
     */
    public SimpleFieldConfig(String name, String type, Map&lt;String, Object&gt; params) {
<span class="nc" id="L41">        super(name, type);</span>
<span class="nc" id="L42">        setParams(params);</span>
<span class="nc" id="L43">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>