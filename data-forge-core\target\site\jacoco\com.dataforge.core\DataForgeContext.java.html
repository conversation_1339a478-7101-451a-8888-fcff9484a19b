<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataForgeContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.core</a> &gt; <span class="el_source">DataForgeContext.java</span></div><h1>DataForgeContext.java</h1><pre class="source lang-java linenums">package com.dataforge.core;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据生成上下文。
 * 
 * &lt;p&gt;
 * 在一次生成请求的生命周期内共享数据，用于解决字段间的关联性问题。
 * 该类是线程安全的，支持并发访问。
 * 
 * &lt;p&gt;
 * 典型使用场景：
 * &lt;ul&gt;
 * &lt;li&gt;身份证号生成器将出生日期、性别、地区信息存入上下文&lt;/li&gt;
 * &lt;li&gt;年龄生成器从上下文获取出生日期，计算对应年龄&lt;/li&gt;
 * &lt;li&gt;地址生成器从上下文获取地区信息，生成对应地区的详细地址&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class DataForgeContext {

<span class="fc" id="L30">    private static final Logger logger = LoggerFactory.getLogger(DataForgeContext.class);</span>

    /**
     * 使用 ConcurrentHashMap 保证多线程生成时的线程安全。
     */
<span class="fc" id="L35">    private final Map&lt;String, Object&gt; contextMap = new ConcurrentHashMap&lt;&gt;();</span>

    /**
     * 上下文创建时间，用于调试和日志记录。
     */
    private final LocalDateTime createdAt;

    /**
     * 当前记录的索引，用于批量生成时的记录标识。
     */
<span class="fc" id="L45">    private volatile int currentRecordIndex = 0;</span>

    /**
     * 构造函数，初始化上下文。
     */
<span class="fc" id="L50">    public DataForgeContext() {</span>
<span class="fc" id="L51">        this.createdAt = LocalDateTime.now();</span>
<span class="fc" id="L52">        logger.debug(&quot;DataForgeContext created at {}&quot;, createdAt);</span>
<span class="fc" id="L53">    }</span>

    /**
     * 向上下文中存储键值对。
     * 
     * &lt;p&gt;
     * 如果键已存在，将覆盖原有值。存储操作是线程安全的。
     * 
     * @param key   键，不能为null或空字符串
     * @param value 值，可以为null
     * @throws IllegalArgumentException 当key为null或空字符串时
     */
    public void put(String key, Object value) {
<span class="nc bnc" id="L66" title="All 4 branches missed.">        if (key == null || key.trim().isEmpty()) {</span>
<span class="nc" id="L67">            throw new IllegalArgumentException(&quot;Key cannot be null or empty&quot;);</span>
        }

<span class="nc" id="L70">        Object oldValue = contextMap.put(key, value);</span>
<span class="nc" id="L71">        logger.trace(&quot;Context put: key={}, value={}, oldValue={}&quot;, key, value, oldValue);</span>
<span class="nc" id="L72">    }</span>

    /**
     * 从上下文中获取指定类型的值。
     * 
     * &lt;p&gt;
     * 该方法是类型安全的，只有当存储的值确实是指定类型的实例时才会返回。
     * 
     * @param &lt;V&gt;  期望的值类型
     * @param key  键，不能为null或空字符串
     * @param type 期望的值类型的Class对象
     * @return 包含值的Optional，如果键不存在或类型不匹配则返回空Optional
     * @throws IllegalArgumentException 当key为null或空字符串，或type为null时
     */
    @SuppressWarnings(&quot;unchecked&quot;)
    public &lt;V&gt; Optional&lt;V&gt; get(String key, Class&lt;V&gt; type) {
<span class="nc bnc" id="L88" title="All 4 branches missed.">        if (key == null || key.trim().isEmpty()) {</span>
<span class="nc" id="L89">            throw new IllegalArgumentException(&quot;Key cannot be null or empty&quot;);</span>
        }
<span class="nc bnc" id="L91" title="All 2 branches missed.">        if (type == null) {</span>
<span class="nc" id="L92">            throw new IllegalArgumentException(&quot;Type cannot be null&quot;);</span>
        }

<span class="nc" id="L95">        Object value = contextMap.get(key);</span>
<span class="nc bnc" id="L96" title="All 4 branches missed.">        if (value != null &amp;&amp; type.isInstance(value)) {</span>
<span class="nc" id="L97">            logger.trace(&quot;Context get: key={}, type={}, value={}&quot;, key, type.getSimpleName(), value);</span>
<span class="nc" id="L98">            return Optional.of((V) value);</span>
        }

<span class="nc" id="L101">        logger.trace(&quot;Context get: key={}, type={}, value not found or type mismatch&quot;,</span>
<span class="nc" id="L102">                key, type.getSimpleName());</span>
<span class="nc" id="L103">        return Optional.empty();</span>
    }

    /**
     * 从上下文中获取值，不进行类型检查。
     * 
     * @param key 键
     * @return 值的Optional，如果键不存在则返回空Optional
     */
    public Optional&lt;Object&gt; get(String key) {
<span class="nc bnc" id="L113" title="All 4 branches missed.">        if (key == null || key.trim().isEmpty()) {</span>
<span class="nc" id="L114">            return Optional.empty();</span>
        }
<span class="nc" id="L116">        return Optional.ofNullable(contextMap.get(key));</span>
    }

    /**
     * 检查上下文中是否包含指定的键。
     * 
     * @param key 键
     * @return 如果包含该键返回true，否则返回false
     */
    public boolean containsKey(String key) {
<span class="nc bnc" id="L126" title="All 4 branches missed.">        if (key == null || key.trim().isEmpty()) {</span>
<span class="nc" id="L127">            return false;</span>
        }
<span class="nc" id="L129">        return contextMap.containsKey(key);</span>
    }

    /**
     * 从上下文中移除指定的键值对。
     * 
     * @param key 要移除的键
     * @return 被移除的值的Optional，如果键不存在则返回空Optional
     */
    public Optional&lt;Object&gt; remove(String key) {
<span class="nc bnc" id="L139" title="All 4 branches missed.">        if (key == null || key.trim().isEmpty()) {</span>
<span class="nc" id="L140">            return Optional.empty();</span>
        }

<span class="nc" id="L143">        Object removedValue = contextMap.remove(key);</span>
<span class="nc" id="L144">        logger.trace(&quot;Context remove: key={}, removedValue={}&quot;, key, removedValue);</span>
<span class="nc" id="L145">        return Optional.ofNullable(removedValue);</span>
    }

    /**
     * 清空上下文中的所有数据。
     * 
     * &lt;p&gt;
     * 通常在开始生成新记录时调用，以避免数据污染。
     */
    public void clear() {
<span class="nc" id="L155">        int size = contextMap.size();</span>
<span class="nc" id="L156">        contextMap.clear();</span>
<span class="nc" id="L157">        logger.debug(&quot;Context cleared, removed {} entries&quot;, size);</span>
<span class="nc" id="L158">    }</span>

    /**
     * 获取上下文中存储的键值对数量。
     * 
     * @return 键值对数量
     */
    public int size() {
<span class="nc" id="L166">        return contextMap.size();</span>
    }

    /**
     * 检查上下文是否为空。
     * 
     * @return 如果上下文为空返回true，否则返回false
     */
    public boolean isEmpty() {
<span class="nc" id="L175">        return contextMap.isEmpty();</span>
    }

    /**
     * 获取当前记录的索引。
     * 
     * @return 当前记录索引，从0开始
     */
    public int getCurrentRecordIndex() {
<span class="nc" id="L184">        return currentRecordIndex;</span>
    }

    /**
     * 设置当前记录的索引。
     * 
     * &lt;p&gt;
     * 通常由框架在批量生成时自动调用。
     * 
     * @param index 记录索引，应该大于等于0
     * @throws IllegalArgumentException 当index小于0时
     */
    public void setCurrentRecordIndex(int index) {
<span class="nc bnc" id="L197" title="All 2 branches missed.">        if (index &lt; 0) {</span>
<span class="nc" id="L198">            throw new IllegalArgumentException(&quot;Record index cannot be negative&quot;);</span>
        }
<span class="nc" id="L200">        this.currentRecordIndex = index;</span>
<span class="nc" id="L201">        logger.trace(&quot;Current record index set to {}&quot;, index);</span>
<span class="nc" id="L202">    }</span>

    /**
     * 递增当前记录索引。
     * 
     * @return 递增后的索引值
     */
    public int incrementRecordIndex() {
<span class="nc" id="L210">        int newIndex = ++currentRecordIndex;</span>
<span class="nc" id="L211">        logger.trace(&quot;Record index incremented to {}&quot;, newIndex);</span>
<span class="nc" id="L212">        return newIndex;</span>
    }

    /**
     * 获取上下文创建时间。
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreatedAt() {
<span class="nc" id="L221">        return createdAt;</span>
    }

    /**
     * 获取上下文的字符串表示，用于调试。
     * 
     * @return 上下文的字符串表示
     */
    @Override
    public String toString() {
<span class="nc" id="L231">        return String.format(&quot;DataForgeContext{size=%d, recordIndex=%d, createdAt=%s}&quot;,</span>
<span class="nc" id="L232">                size(), currentRecordIndex, createdAt);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>