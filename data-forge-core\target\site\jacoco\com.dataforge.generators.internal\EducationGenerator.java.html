<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EducationGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">EducationGenerator.java</span></div><h1>EducationGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 学历生成器
 * 
 * 支持的参数：
 * - levels: 学历层次范围
 * (PRIMARY|JUNIOR_HIGH|HIGH_SCHOOL|COLLEGE|BACHELOR|MASTER|PHD|ANY)
 * - distribution: 学历分布方式 (UNIFORM|WEIGHTED|REALISTIC)
 * - weights: 自定义权重配置 (如 &quot;本科:50,硕士:30,博士:10&quot;)
 * - age_related: 是否与年龄关联 (true|false)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L23">public class EducationGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(EducationGenerator.class);</span>
<span class="nc" id="L26">    private static final Random random = new Random();</span>

    // 学历层次定义
<span class="nc" id="L29">    private static final Map&lt;String, String&gt; EDUCATION_LEVELS = new LinkedHashMap&lt;&gt;();</span>

    // 现实分布权重（基于中国教育统计）
<span class="nc" id="L32">    private static final Map&lt;String, Integer&gt; REALISTIC_WEIGHTS = new HashMap&lt;&gt;();</span>

    // 年龄与学历的合理性映射
<span class="nc" id="L35">    private static final Map&lt;String, Integer&gt; MIN_AGE_FOR_EDUCATION = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L38">        initializeEducationLevels();</span>
<span class="nc" id="L39">        initializeRealisticWeights();</span>
<span class="nc" id="L40">        initializeAgeMapping();</span>
<span class="nc" id="L41">    }</span>

    private static void initializeEducationLevels() {
<span class="nc" id="L44">        EDUCATION_LEVELS.put(&quot;PRIMARY&quot;, &quot;小学&quot;);</span>
<span class="nc" id="L45">        EDUCATION_LEVELS.put(&quot;JUNIOR_HIGH&quot;, &quot;初中&quot;);</span>
<span class="nc" id="L46">        EDUCATION_LEVELS.put(&quot;HIGH_SCHOOL&quot;, &quot;高中&quot;);</span>
<span class="nc" id="L47">        EDUCATION_LEVELS.put(&quot;COLLEGE&quot;, &quot;大专&quot;);</span>
<span class="nc" id="L48">        EDUCATION_LEVELS.put(&quot;BACHELOR&quot;, &quot;本科&quot;);</span>
<span class="nc" id="L49">        EDUCATION_LEVELS.put(&quot;MASTER&quot;, &quot;硕士&quot;);</span>
<span class="nc" id="L50">        EDUCATION_LEVELS.put(&quot;PHD&quot;, &quot;博士&quot;);</span>
<span class="nc" id="L51">    }</span>

    private static void initializeRealisticWeights() {
        // 基于中国成年人口教育结构的近似分布
<span class="nc" id="L55">        REALISTIC_WEIGHTS.put(&quot;小学&quot;, 15);</span>
<span class="nc" id="L56">        REALISTIC_WEIGHTS.put(&quot;初中&quot;, 25);</span>
<span class="nc" id="L57">        REALISTIC_WEIGHTS.put(&quot;高中&quot;, 20);</span>
<span class="nc" id="L58">        REALISTIC_WEIGHTS.put(&quot;大专&quot;, 15);</span>
<span class="nc" id="L59">        REALISTIC_WEIGHTS.put(&quot;本科&quot;, 20);</span>
<span class="nc" id="L60">        REALISTIC_WEIGHTS.put(&quot;硕士&quot;, 4);</span>
<span class="nc" id="L61">        REALISTIC_WEIGHTS.put(&quot;博士&quot;, 1);</span>
<span class="nc" id="L62">    }</span>

    private static void initializeAgeMapping() {
        // 各学历层次的最小合理年龄
<span class="nc" id="L66">        MIN_AGE_FOR_EDUCATION.put(&quot;小学&quot;, 12);</span>
<span class="nc" id="L67">        MIN_AGE_FOR_EDUCATION.put(&quot;初中&quot;, 15);</span>
<span class="nc" id="L68">        MIN_AGE_FOR_EDUCATION.put(&quot;高中&quot;, 18);</span>
<span class="nc" id="L69">        MIN_AGE_FOR_EDUCATION.put(&quot;大专&quot;, 20);</span>
<span class="nc" id="L70">        MIN_AGE_FOR_EDUCATION.put(&quot;本科&quot;, 22);</span>
<span class="nc" id="L71">        MIN_AGE_FOR_EDUCATION.put(&quot;硕士&quot;, 25);</span>
<span class="nc" id="L72">        MIN_AGE_FOR_EDUCATION.put(&quot;博士&quot;, 28);</span>
<span class="nc" id="L73">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L77">        return &quot;education&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L82">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L89">            String levels = config.getParam(&quot;levels&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L90">            String distribution = config.getParam(&quot;distribution&quot;, String.class, &quot;REALISTIC&quot;);</span>
<span class="nc" id="L91">            String weightsParam = config.getParam(&quot;weights&quot;, String.class, null);</span>
<span class="nc" id="L92">            boolean ageRelated = Boolean.parseBoolean(config.getParam(&quot;age_related&quot;, String.class, &quot;true&quot;));</span>

            // 获取可选的学历列表
<span class="nc" id="L95">            List&lt;String&gt; availableEducations = getAvailableEducations(levels);</span>

            // 如果启用年龄关联，根据年龄过滤学历
<span class="nc bnc" id="L98" title="All 2 branches missed.">            if (ageRelated) {</span>
<span class="nc" id="L99">                Integer age = context.get(&quot;age&quot;, Integer.class).orElse(null);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">                if (age != null) {</span>
<span class="nc" id="L101">                    availableEducations = filterByAge(availableEducations, age);</span>
                }
            }

            // 根据分布方式选择学历
<span class="nc" id="L106">            String education = selectEducation(availableEducations, distribution, weightsParam);</span>

<span class="nc" id="L108">            logger.debug(&quot;Generated education: {}&quot;, education);</span>
<span class="nc" id="L109">            return education;</span>

<span class="nc" id="L111">        } catch (Exception e) {</span>
<span class="nc" id="L112">            logger.error(&quot;Error generating education&quot;, e);</span>
<span class="nc" id="L113">            return &quot;本科&quot;;</span>
        }
    }

    private List&lt;String&gt; getAvailableEducations(String levels) {
<span class="nc" id="L118">        List&lt;String&gt; educations = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L120" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(levels)) {</span>
<span class="nc" id="L121">            educations.addAll(EDUCATION_LEVELS.values());</span>
        } else {
<span class="nc" id="L123">            String[] levelArray = levels.split(&quot;\\|&quot;);</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">            for (String level : levelArray) {</span>
<span class="nc" id="L125">                String education = EDUCATION_LEVELS.get(level.trim());</span>
<span class="nc bnc" id="L126" title="All 2 branches missed.">                if (education != null) {</span>
<span class="nc" id="L127">                    educations.add(education);</span>
                }
            }
        }

        // 如果没有找到合适的学历，使用默认列表
<span class="nc bnc" id="L133" title="All 2 branches missed.">        if (educations.isEmpty()) {</span>
<span class="nc" id="L134">            educations.addAll(EDUCATION_LEVELS.values());</span>
        }

<span class="nc" id="L137">        return educations;</span>
    }

    private List&lt;String&gt; filterByAge(List&lt;String&gt; educations, int age) {
<span class="nc" id="L141">        List&lt;String&gt; filteredEducations = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L143" title="All 2 branches missed.">        for (String education : educations) {</span>
<span class="nc" id="L144">            Integer minAge = MIN_AGE_FOR_EDUCATION.get(education);</span>
<span class="nc bnc" id="L145" title="All 4 branches missed.">            if (minAge != null &amp;&amp; age &gt;= minAge) {</span>
<span class="nc" id="L146">                filteredEducations.add(education);</span>
            }
<span class="nc" id="L148">        }</span>

        // 如果过滤后没有合适的学历，返回最低学历
<span class="nc bnc" id="L151" title="All 2 branches missed.">        if (filteredEducations.isEmpty()) {</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">            if (age &gt;= 12) {</span>
<span class="nc" id="L153">                filteredEducations.add(&quot;小学&quot;);</span>
            }
<span class="nc bnc" id="L155" title="All 2 branches missed.">            if (age &gt;= 15) {</span>
<span class="nc" id="L156">                filteredEducations.add(&quot;初中&quot;);</span>
            }
<span class="nc bnc" id="L158" title="All 2 branches missed.">            if (age &gt;= 18) {</span>
<span class="nc" id="L159">                filteredEducations.add(&quot;高中&quot;);</span>
            }
        }

<span class="nc bnc" id="L163" title="All 2 branches missed.">        return filteredEducations.isEmpty() ? Arrays.asList(&quot;小学&quot;) : filteredEducations;</span>
    }

    private String selectEducation(List&lt;String&gt; educations, String distribution, String weightsParam) {
<span class="nc bnc" id="L167" title="All 3 branches missed.">        switch (distribution) {</span>
            case &quot;UNIFORM&quot;:
<span class="nc" id="L169">                return educations.get(random.nextInt(educations.size()));</span>

            case &quot;WEIGHTED&quot;:
<span class="nc bnc" id="L172" title="All 4 branches missed.">                if (weightsParam != null &amp;&amp; !weightsParam.isEmpty()) {</span>
<span class="nc" id="L173">                    return selectWithCustomWeights(educations, weightsParam);</span>
                }
                // 如果没有自定义权重，使用现实分布
<span class="nc" id="L176">                return selectWithRealisticWeights(educations);</span>

            case &quot;REALISTIC&quot;:
            default:
<span class="nc" id="L180">                return selectWithRealisticWeights(educations);</span>
        }
    }

    private String selectWithRealisticWeights(List&lt;String&gt; educations) {
        // 计算总权重
<span class="nc" id="L186">        int totalWeight = 0;</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">        for (String education : educations) {</span>
<span class="nc" id="L188">            totalWeight += REALISTIC_WEIGHTS.getOrDefault(education, 1);</span>
<span class="nc" id="L189">        }</span>

        // 随机选择
<span class="nc" id="L192">        int randomValue = random.nextInt(totalWeight);</span>
<span class="nc" id="L193">        int currentWeight = 0;</span>

<span class="nc bnc" id="L195" title="All 2 branches missed.">        for (String education : educations) {</span>
<span class="nc" id="L196">            currentWeight += REALISTIC_WEIGHTS.getOrDefault(education, 1);</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">            if (randomValue &lt; currentWeight) {</span>
<span class="nc" id="L198">                return education;</span>
            }
<span class="nc" id="L200">        }</span>

        // 默认返回第一个
<span class="nc" id="L203">        return educations.get(0);</span>
    }

    private String selectWithCustomWeights(List&lt;String&gt; educations, String weightsParam) {
<span class="nc" id="L207">        Map&lt;String, Integer&gt; customWeights = parseWeights(weightsParam);</span>

        // 计算总权重
<span class="nc" id="L210">        int totalWeight = 0;</span>
<span class="nc bnc" id="L211" title="All 2 branches missed.">        for (String education : educations) {</span>
<span class="nc" id="L212">            totalWeight += customWeights.getOrDefault(education, 1);</span>
<span class="nc" id="L213">        }</span>

        // 随机选择
<span class="nc" id="L216">        int randomValue = random.nextInt(totalWeight);</span>
<span class="nc" id="L217">        int currentWeight = 0;</span>

<span class="nc bnc" id="L219" title="All 2 branches missed.">        for (String education : educations) {</span>
<span class="nc" id="L220">            currentWeight += customWeights.getOrDefault(education, 1);</span>
<span class="nc bnc" id="L221" title="All 2 branches missed.">            if (randomValue &lt; currentWeight) {</span>
<span class="nc" id="L222">                return education;</span>
            }
<span class="nc" id="L224">        }</span>

        // 默认返回第一个
<span class="nc" id="L227">        return educations.get(0);</span>
    }

    private Map&lt;String, Integer&gt; parseWeights(String weightsParam) {
<span class="nc" id="L231">        Map&lt;String, Integer&gt; weights = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L234">            String[] pairs = weightsParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L235" title="All 2 branches missed.">            for (String pair : pairs) {</span>
<span class="nc" id="L236">                String[] parts = pair.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L238">                    String education = parts[0].trim();</span>
<span class="nc" id="L239">                    int weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L240">                    weights.put(education, weight);</span>
                }
            }
<span class="nc" id="L243">        } catch (Exception e) {</span>
<span class="nc" id="L244">            logger.warn(&quot;Failed to parse weights: {}&quot;, weightsParam, e);</span>
<span class="nc" id="L245">        }</span>

<span class="nc" id="L247">        return weights;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>