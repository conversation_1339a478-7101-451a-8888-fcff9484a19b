<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IdCardGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">IdCardGenerator.java</span></div><h1>IdCardGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import com.dataforge.validation.IdCardValidator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 身份证号码生成器。
 * 
 * &lt;p&gt;
 * 生成符合中国大陆18位身份证号码规则的身份证号，支持大规模身份证生成。
 * 支持地区代码、出生日期范围、性别等参数配置和权重选择。
 * 通过配置文件管理行政区划数据，支持生成数十亿唯一身份证号。
 * 生成的身份证号会自动关联到上下文中，供其他字段使用。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L33">public class IdCardGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L35">    private static final Logger logger = LoggerFactory.getLogger(IdCardGenerator.class);</span>

    @Autowired
    private IdCardValidator idCardValidator;

    /**
     * 行政区划数据文件路径。
     */
    private static final String ADMINISTRATIVE_DIVISIONS_PATH = &quot;data/administrative-divisions.txt&quot;;

    /**
     * 缓存的行政区划数据。
     */
    private volatile Map&lt;String, RegionInfo&gt; regionCodes;
    private volatile List&lt;String&gt; allRegionCodes;
    private volatile Map&lt;String, List&lt;String&gt;&gt; regionsByProvince;
    private volatile Map&lt;String, List&lt;String&gt;&gt; regionsByCity;

    /**
     * Fallback地区代码映射（当文件加载失败时使用）。
     */
<span class="nc" id="L56">    private static final Map&lt;String, String&gt; FALLBACK_REGION_CODES = new HashMap&lt;&gt;();</span>

    static {
        // 初始化fallback数据
<span class="nc" id="L60">        FALLBACK_REGION_CODES.put(&quot;110101&quot;, &quot;北京市东城区&quot;);</span>
<span class="nc" id="L61">        FALLBACK_REGION_CODES.put(&quot;110102&quot;, &quot;北京市西城区&quot;);</span>
<span class="nc" id="L62">        FALLBACK_REGION_CODES.put(&quot;110105&quot;, &quot;北京市朝阳区&quot;);</span>
<span class="nc" id="L63">        FALLBACK_REGION_CODES.put(&quot;110106&quot;, &quot;北京市丰台区&quot;);</span>
<span class="nc" id="L64">        FALLBACK_REGION_CODES.put(&quot;110108&quot;, &quot;北京市海淀区&quot;);</span>
<span class="nc" id="L65">        FALLBACK_REGION_CODES.put(&quot;120101&quot;, &quot;天津市和平区&quot;);</span>
<span class="nc" id="L66">        FALLBACK_REGION_CODES.put(&quot;120102&quot;, &quot;天津市河东区&quot;);</span>
<span class="nc" id="L67">        FALLBACK_REGION_CODES.put(&quot;310101&quot;, &quot;上海市黄浦区&quot;);</span>
<span class="nc" id="L68">        FALLBACK_REGION_CODES.put(&quot;310104&quot;, &quot;上海市徐汇区&quot;);</span>
<span class="nc" id="L69">        FALLBACK_REGION_CODES.put(&quot;310105&quot;, &quot;上海市长宁区&quot;);</span>
<span class="nc" id="L70">        FALLBACK_REGION_CODES.put(&quot;500101&quot;, &quot;重庆市万州区&quot;);</span>
<span class="nc" id="L71">        FALLBACK_REGION_CODES.put(&quot;500102&quot;, &quot;重庆市涪陵区&quot;);</span>
<span class="nc" id="L72">        FALLBACK_REGION_CODES.put(&quot;330106&quot;, &quot;浙江省杭州市西湖区&quot;);</span>
<span class="nc" id="L73">        FALLBACK_REGION_CODES.put(&quot;440100&quot;, &quot;广东省广州市&quot;);</span>
<span class="nc" id="L74">        FALLBACK_REGION_CODES.put(&quot;440300&quot;, &quot;广东省深圳市&quot;);</span>
<span class="nc" id="L75">        FALLBACK_REGION_CODES.put(&quot;510100&quot;, &quot;四川省成都市&quot;);</span>
<span class="nc" id="L76">    }</span>

    /**
     * 地区信息类。
     */
    private static class RegionInfo {
        final String code;
        final String province;
        final String city;
        final String district;

<span class="nc" id="L87">        RegionInfo(String code, String province, String city, String district, int weight) {</span>
<span class="nc" id="L88">            this.code = code;</span>
<span class="nc" id="L89">            this.province = province;</span>
<span class="nc" id="L90">            this.city = city;</span>
<span class="nc" id="L91">            this.district = district;</span>
<span class="nc" id="L92">        }</span>
    }

    @Override
    public String getType() {
<span class="nc" id="L97">        return &quot;idcard&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 延迟加载数据
<span class="nc" id="L104">            ensureDataLoaded(config);</span>

            // 从参数中获取地区代码
<span class="nc" id="L107">            String region = getStringParam(config, &quot;region&quot;, null);</span>

            // 从参数中获取出生日期范围
<span class="nc" id="L110">            String birthDateRange = getStringParam(config, &quot;birth_date_range&quot;, &quot;1980-01-01,2000-12-31&quot;);</span>

            // 从参数中获取性别
<span class="nc" id="L113">            String gender = getStringParam(config, &quot;gender&quot;, &quot;ANY&quot;);</span>

            // 从上下文中获取性别（优先级更高）
<span class="nc" id="L116">            String contextGender = context.get(&quot;gender&quot;, String.class).orElse(null);</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">            if (contextGender != null) {</span>
<span class="nc" id="L118">                gender = contextGender;</span>
            }

            // 从参数中获取是否生成有效身份证号
<span class="nc" id="L122">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>

<span class="nc bnc" id="L124" title="All 2 branches missed.">            if (!valid) {</span>
<span class="nc" id="L125">                return generateInvalidIdCard();</span>
            }

<span class="nc" id="L128">            String idCard = generateValidIdCard(region, birthDateRange, gender);</span>

            // 将生成的身份证信息放入上下文
<span class="nc" id="L131">            putIdCardInfoToContext(context, idCard);</span>

<span class="nc" id="L133">            return idCard;</span>

<span class="nc" id="L135">        } catch (Exception e) {</span>
<span class="nc" id="L136">            logger.error(&quot;Failed to generate ID card number&quot;, e);</span>
            // 返回一个默认身份证号作为fallback
<span class="nc" id="L138">            return &quot;11010119800101001X&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L144">        return FieldConfig.class;</span>
    }

    /**
     * 确保数据已加载。
     * 
     * @param config 配置
     */
    private void ensureDataLoaded(FieldConfig config) {
<span class="nc bnc" id="L153" title="All 2 branches missed.">        if (regionCodes == null) {</span>
<span class="nc" id="L154">            synchronized (this) {</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">                if (regionCodes == null) {</span>
<span class="nc" id="L156">                    loadData(config);</span>
                }
<span class="nc" id="L158">            }</span>
        }
<span class="nc" id="L160">    }</span>

    /**
     * 加载行政区划数据。
     * 
     * @param config 配置
     */
    private void loadData(FieldConfig config) {
        try {
            // 检查是否有自定义数据文件路径
<span class="nc" id="L170">            String customRegionsPath = getStringParam(config, &quot;regions_file&quot;, null);</span>

            List&lt;String&gt; lines;
<span class="nc bnc" id="L173" title="All 2 branches missed.">            if (customRegionsPath != null) {</span>
<span class="nc" id="L174">                lines = DataLoader.loadDataFromFile(customRegionsPath);</span>
            } else {
<span class="nc" id="L176">                lines = DataLoader.loadDataFromResource(ADMINISTRATIVE_DIVISIONS_PATH);</span>
            }

<span class="nc" id="L179">            regionCodes = new HashMap&lt;&gt;();</span>
<span class="nc" id="L180">            regionsByProvince = new HashMap&lt;&gt;();</span>
<span class="nc" id="L181">            regionsByCity = new HashMap&lt;&gt;();</span>

<span class="nc bnc" id="L183" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc" id="L184">                String[] parts = line.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L185" title="All 2 branches missed.">                if (parts.length &gt;= 4) {</span>
<span class="nc" id="L186">                    String code = parts[0].trim();</span>
<span class="nc" id="L187">                    String province = parts[1].trim();</span>
<span class="nc" id="L188">                    String city = parts[2].trim();</span>
<span class="nc" id="L189">                    String district = parts[3].trim();</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">                    int weight = parts.length &gt; 4 ? parseWeight(parts[4].trim()) : 1;</span>

<span class="nc" id="L192">                    RegionInfo info = new RegionInfo(code, province, city, district, weight);</span>
<span class="nc" id="L193">                    regionCodes.put(code, info);</span>

<span class="nc" id="L195">                    regionsByProvince.computeIfAbsent(province, k -&gt; new java.util.ArrayList&lt;&gt;()).add(code);</span>
<span class="nc" id="L196">                    regionsByCity.computeIfAbsent(city, k -&gt; new java.util.ArrayList&lt;&gt;()).add(code);</span>
                }
<span class="nc" id="L198">            }</span>

<span class="nc" id="L200">            allRegionCodes = new java.util.ArrayList&lt;&gt;(regionCodes.keySet());</span>

            // 如果加载失败，使用fallback数据
<span class="nc bnc" id="L203" title="All 2 branches missed.">            if (regionCodes.isEmpty()) {</span>
<span class="nc" id="L204">                initializeFallbackData();</span>
            }

<span class="nc" id="L207">            logger.info(&quot;Administrative division data loaded - Total regions: {}, Provinces: {}&quot;,</span>
<span class="nc" id="L208">                    regionCodes.size(), regionsByProvince.keySet().size());</span>

<span class="nc" id="L210">        } catch (Exception e) {</span>
<span class="nc" id="L211">            logger.error(&quot;Failed to load administrative division data, using fallback&quot;, e);</span>
<span class="nc" id="L212">            initializeFallbackData();</span>
<span class="nc" id="L213">        }</span>
<span class="nc" id="L214">    }</span>

    /**
     * 解析权重值。
     * 
     * @param weightStr 权重字符串
     * @return 权重值
     */
    private int parseWeight(String weightStr) {
        try {
<span class="nc" id="L224">            return Integer.parseInt(weightStr);</span>
<span class="nc" id="L225">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L226">            return 1;</span>
        }
    }

    /**
     * 初始化fallback数据。
     */
    private void initializeFallbackData() {
<span class="nc" id="L234">        regionCodes = new HashMap&lt;&gt;();</span>
<span class="nc" id="L235">        regionsByProvince = new HashMap&lt;&gt;();</span>
<span class="nc" id="L236">        regionsByCity = new HashMap&lt;&gt;();</span>

        // 添加fallback数据
<span class="nc bnc" id="L239" title="All 2 branches missed.">        for (Map.Entry&lt;String, String&gt; entry : FALLBACK_REGION_CODES.entrySet()) {</span>
<span class="nc" id="L240">            String code = entry.getKey();</span>
<span class="nc" id="L241">            String fullName = entry.getValue();</span>

            // 简单解析省市区信息
<span class="nc" id="L244">            String[] parts = fullName.split(&quot;省|市&quot;);</span>
<span class="nc bnc" id="L245" title="All 4 branches missed.">            String province = parts.length &gt; 0 ? parts[0] + (fullName.contains(&quot;省&quot;) ? &quot;省&quot; : &quot;市&quot;) : fullName;</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">            String city = parts.length &gt; 1 ? parts[1] : &quot;&quot;;</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">            String district = parts.length &gt; 2 ? parts[2] : &quot;&quot;;</span>

<span class="nc" id="L249">            RegionInfo info = new RegionInfo(code, province, city, district, 1);</span>
<span class="nc" id="L250">            regionCodes.put(code, info);</span>

<span class="nc" id="L252">            regionsByProvince.computeIfAbsent(province, k -&gt; new java.util.ArrayList&lt;&gt;()).add(code);</span>
<span class="nc bnc" id="L253" title="All 2 branches missed.">            if (!city.isEmpty()) {</span>
<span class="nc" id="L254">                regionsByCity.computeIfAbsent(city, k -&gt; new java.util.ArrayList&lt;&gt;()).add(code);</span>
            }
<span class="nc" id="L256">        }</span>

<span class="nc" id="L258">        allRegionCodes = new java.util.ArrayList&lt;&gt;(regionCodes.keySet());</span>
<span class="nc" id="L259">    }</span>

    /**
     * 生成有效的身份证号码。
     * 
     * @param region         地区代码
     * @param birthDateRange 出生日期范围
     * @param gender         性别
     * @return 有效的身份证号码
     */
    private String generateValidIdCard(String region, String birthDateRange, String gender) {
        // 1. 生成地区代码（前6位）
<span class="nc" id="L271">        String regionCode = selectRegionCode(region);</span>

        // 2. 生成出生日期（第7-14位）
<span class="nc" id="L274">        LocalDate birthDate = generateBirthDate(birthDateRange);</span>
<span class="nc" id="L275">        String birthDateStr = birthDate.format(DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;));</span>

        // 3. 生成顺序码（第15-17位）
<span class="nc" id="L278">        String sequenceCode = generateSequenceCode(gender);</span>

        // 4. 组合前17位
<span class="nc" id="L281">        String first17 = regionCode + birthDateStr + sequenceCode;</span>

        // 5. 计算校验位（第18位）
<span class="nc" id="L284">        char checkCode = idCardValidator.calculateCheckCode(first17);</span>

<span class="nc" id="L286">        String idCard = first17 + checkCode;</span>

        // 验证生成的身份证号
<span class="nc bnc" id="L289" title="All 2 branches missed.">        if (!idCardValidator.isValid(idCard)) {</span>
<span class="nc" id="L290">            logger.error(&quot;Generated invalid ID card: {}&quot;, maskIdCard(idCard));</span>
            // 重新生成
<span class="nc" id="L292">            return generateValidIdCard(region, birthDateRange, gender);</span>
        }

<span class="nc" id="L295">        logger.debug(&quot;Generated valid ID card: {} (region: {}, birth: {}, gender: {})&quot;,</span>
<span class="nc" id="L296">                maskIdCard(idCard), regionCode, birthDate, gender);</span>

<span class="nc" id="L298">        return idCard;</span>
    }

    /**
     * 生成无效的身份证号码。
     * 
     * @return 无效的身份证号码
     */
    private String generateInvalidIdCard() {
<span class="nc" id="L307">        int type = ThreadLocalRandom.current().nextInt(4);</span>

<span class="nc bnc" id="L309" title="All 4 branches missed.">        return switch (type) {</span>
<span class="nc" id="L310">            case 0 -&gt; generateWrongLengthIdCard();</span>
<span class="nc" id="L311">            case 1 -&gt; generateWrongChecksumIdCard();</span>
<span class="nc" id="L312">            case 2 -&gt; generateInvalidDateIdCard();</span>
<span class="nc" id="L313">            default -&gt; generateOtherInvalidIdCard();</span>
        };
    }

    /**
     * 生成长度错误的身份证号。
     * 
     * @return 长度错误的身份证号
     */
    private String generateWrongLengthIdCard() {
<span class="nc" id="L323">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc bnc" id="L324" title="All 2 branches missed.">        int length = random.nextBoolean() ? random.nextInt(10) + 5 : // 5-14位</span>
<span class="nc" id="L325">                random.nextInt(5) + 19; // 19-23位</span>

<span class="nc" id="L327">        StringBuilder idCard = new StringBuilder();</span>
<span class="nc bnc" id="L328" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc bnc" id="L329" title="All 4 branches missed.">            if (i == length - 1 &amp;&amp; random.nextBoolean()) {</span>
<span class="nc" id="L330">                idCard.append('X');</span>
            } else {
<span class="nc" id="L332">                idCard.append(random.nextInt(10));</span>
            }
        }

<span class="nc" id="L336">        return idCard.toString();</span>
    }

    /**
     * 生成校验位错误的身份证号。
     * 
     * @return 校验位错误的身份证号
     */
    private String generateWrongChecksumIdCard() {
        // 先生成一个有效的身份证号
<span class="nc" id="L346">        String validIdCard = generateValidIdCard(null, &quot;1980-01-01,2000-12-31&quot;, &quot;ANY&quot;);</span>

        // 修改最后一位校验位
<span class="nc" id="L349">        StringBuilder invalidIdCard = new StringBuilder(validIdCard.substring(0, 17));</span>
<span class="nc" id="L350">        char lastChar = validIdCard.charAt(17);</span>

<span class="nc bnc" id="L352" title="All 2 branches missed.">        if (lastChar == 'X') {</span>
<span class="nc" id="L353">            invalidIdCard.append('0');</span>
<span class="nc bnc" id="L354" title="All 2 branches missed.">        } else if (lastChar == '0') {</span>
<span class="nc" id="L355">            invalidIdCard.append('X');</span>
        } else {
<span class="nc" id="L357">            int digit = Character.getNumericValue(lastChar);</span>
<span class="nc" id="L358">            int wrongDigit = (digit + 1) % 10;</span>
<span class="nc" id="L359">            invalidIdCard.append(wrongDigit);</span>
        }

<span class="nc" id="L362">        return invalidIdCard.toString();</span>
    }

    /**
     * 生成日期无效的身份证号。
     * 
     * @return 日期无效的身份证号
     */
    private String generateInvalidDateIdCard() {
<span class="nc" id="L371">        String regionCode = selectRegionCode(null);</span>

        // 生成无效日期
<span class="nc" id="L374">        String[] invalidDates = {</span>
                &quot;19800230&quot;, // 2月30日
                &quot;19801301&quot;, // 13月
                &quot;19800001&quot;, // 0月
                &quot;19800100&quot;, // 0日
                &quot;19800132&quot;, // 32日
                &quot;20250101&quot; // 未来日期
        };

<span class="nc" id="L383">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L384">        String invalidDate = invalidDates[random.nextInt(invalidDates.length)];</span>
<span class="nc" id="L385">        String sequenceCode = generateSequenceCode(&quot;ANY&quot;);</span>
<span class="nc" id="L386">        String first17 = regionCode + invalidDate + sequenceCode;</span>

        // 计算校验位（即使日期无效，校验位仍然正确）
<span class="nc" id="L389">        char checkCode = idCardValidator.calculateCheckCode(first17);</span>

<span class="nc" id="L391">        return first17 + checkCode;</span>
    }

    /**
     * 生成其他类型的无效身份证号。
     * 
     * @return 其他无效身份证号
     */
    private String generateOtherInvalidIdCard() {
<span class="nc" id="L400">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
        // 生成全0或全相同数字的身份证号
<span class="nc bnc" id="L402" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L403">            return &quot;000000000000000000&quot;;</span>
        } else {
<span class="nc" id="L405">            int digit = random.nextInt(10);</span>
<span class="nc" id="L406">            return String.valueOf(digit).repeat(18);</span>
        }
    }

    /**
     * 选择地区代码。
     * 
     * @param region 指定的地区代码
     * @return 地区代码
     */
    private String selectRegionCode(String region) {
<span class="nc bnc" id="L417" title="All 4 branches missed.">        if (region != null &amp;&amp; !region.trim().isEmpty()) {</span>
<span class="nc" id="L418">            String cleanRegion = region.trim();</span>

            // 如果是6位数字，直接使用
<span class="nc bnc" id="L421" title="All 2 branches missed.">            if (cleanRegion.matches(&quot;\\d{6}&quot;)) {</span>
<span class="nc" id="L422">                return cleanRegion;</span>
            }

            // 如果是省市名称，查找对应代码
<span class="nc bnc" id="L426" title="All 2 branches missed.">            for (RegionInfo info : regionCodes.values()) {</span>
<span class="nc bnc" id="L427" title="All 2 branches missed.">                if (info.province.contains(cleanRegion) ||</span>
<span class="nc bnc" id="L428" title="All 2 branches missed.">                        info.city.contains(cleanRegion) ||</span>
<span class="nc bnc" id="L429" title="All 2 branches missed.">                        info.district.contains(cleanRegion)) {</span>
<span class="nc" id="L430">                    return info.code;</span>
                }
<span class="nc" id="L432">            }</span>

<span class="nc" id="L434">            logger.warn(&quot;Unknown region: {}, using random region&quot;, region);</span>
        }

        // 随机选择一个地区代码
<span class="nc" id="L438">        return allRegionCodes.get(ThreadLocalRandom.current().nextInt(allRegionCodes.size()));</span>
    }

    /**
     * 生成出生日期。
     * 
     * @param birthDateRange 出生日期范围
     * @return 出生日期
     */
    private LocalDate generateBirthDate(String birthDateRange) {
<span class="nc" id="L448">        LocalDate startDate = LocalDate.of(1980, 1, 1);</span>
<span class="nc" id="L449">        LocalDate endDate = LocalDate.of(2000, 12, 31);</span>

<span class="nc bnc" id="L451" title="All 4 branches missed.">        if (birthDateRange != null &amp;&amp; !birthDateRange.trim().isEmpty()) {</span>
            try {
<span class="nc" id="L453">                String[] parts = birthDateRange.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L454" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L455">                    startDate = LocalDate.parse(parts[0].trim());</span>
<span class="nc" id="L456">                    endDate = LocalDate.parse(parts[1].trim());</span>
                }
<span class="nc" id="L458">            } catch (DateTimeParseException e) {</span>
<span class="nc" id="L459">                logger.warn(&quot;Invalid birth date range: {}, using default&quot;, birthDateRange);</span>
<span class="nc" id="L460">            }</span>
        }

        // 确保日期范围合理
<span class="nc" id="L464">        LocalDate minDate = LocalDate.of(1900, 1, 1);</span>
<span class="nc" id="L465">        LocalDate maxDate = LocalDate.now().minusYears(1); // 至少1岁</span>

<span class="nc bnc" id="L467" title="All 2 branches missed.">        if (startDate.isBefore(minDate)) {</span>
<span class="nc" id="L468">            startDate = minDate;</span>
        }
<span class="nc bnc" id="L470" title="All 2 branches missed.">        if (endDate.isAfter(maxDate)) {</span>
<span class="nc" id="L471">            endDate = maxDate;</span>
        }
<span class="nc bnc" id="L473" title="All 2 branches missed.">        if (startDate.isAfter(endDate)) {</span>
<span class="nc" id="L474">            startDate = endDate.minusYears(20);</span>
        }

        // 随机生成日期
<span class="nc" id="L478">        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);</span>
<span class="nc" id="L479">        long randomDays = ThreadLocalRandom.current().nextLong(daysBetween + 1);</span>

<span class="nc" id="L481">        return startDate.plusDays(randomDays);</span>
    }

    /**
     * 生成顺序码。
     * 
     * @param gender 性别
     * @return 3位顺序码
     */
    private String generateSequenceCode(String gender) {
<span class="nc" id="L491">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

        // 前两位随机
<span class="nc" id="L494">        int first = random.nextInt(10);</span>
<span class="nc" id="L495">        int second = random.nextInt(10);</span>

        // 第三位决定性别（奇数男性，偶数女性）
        int third;
<span class="nc bnc" id="L499" title="All 4 branches missed.">        if (&quot;MALE&quot;.equalsIgnoreCase(gender) || &quot;M&quot;.equalsIgnoreCase(gender)) {</span>
            // 男性：奇数
<span class="nc" id="L501">            third = random.nextInt(5) * 2 + 1; // 1, 3, 5, 7, 9</span>
<span class="nc bnc" id="L502" title="All 4 branches missed.">        } else if (&quot;FEMALE&quot;.equalsIgnoreCase(gender) || &quot;F&quot;.equalsIgnoreCase(gender)) {</span>
            // 女性：偶数
<span class="nc" id="L504">            third = random.nextInt(5) * 2; // 0, 2, 4, 6, 8</span>
        } else {
            // 随机性别
<span class="nc" id="L507">            third = random.nextInt(10);</span>
        }

<span class="nc" id="L510">        return String.format(&quot;%d%d%d&quot;, first, second, third);</span>
    }

    /**
     * 将身份证信息放入上下文。
     * 
     * @param context 上下文
     * @param idCard  身份证号
     */
    private void putIdCardInfoToContext(DataForgeContext context, String idCard) {
        try {
            // 提取并存储相关信息
<span class="nc" id="L522">            LocalDate birthDate = idCardValidator.extractBirthDate(idCard);</span>
<span class="nc bnc" id="L523" title="All 2 branches missed.">            if (birthDate != null) {</span>
<span class="nc" id="L524">                context.put(&quot;birthDate&quot;, birthDate);</span>
<span class="nc" id="L525">                context.put(&quot;birth_date&quot;, birthDate.toString());</span>

                // 计算年龄
<span class="nc" id="L528">                int age = java.time.Period.between(birthDate, LocalDate.now()).getYears();</span>
<span class="nc" id="L529">                context.put(&quot;age&quot;, age);</span>
            }

<span class="nc" id="L532">            String gender = idCardValidator.extractGender(idCard);</span>
<span class="nc bnc" id="L533" title="All 2 branches missed.">            if (gender != null) {</span>
<span class="nc" id="L534">                context.put(&quot;gender&quot;, gender);</span>
<span class="nc bnc" id="L535" title="All 2 branches missed.">                context.put(&quot;gender_cn&quot;, &quot;M&quot;.equals(gender) ? &quot;男&quot; : &quot;女&quot;);</span>
            }

<span class="nc" id="L538">            String regionCode = idCardValidator.extractRegionCode(idCard);</span>
<span class="nc bnc" id="L539" title="All 2 branches missed.">            if (regionCode != null) {</span>
<span class="nc" id="L540">                context.put(&quot;regionCode&quot;, regionCode);</span>
<span class="nc" id="L541">                RegionInfo regionInfo = regionCodes.get(regionCode);</span>
<span class="nc bnc" id="L542" title="All 2 branches missed.">                if (regionInfo != null) {</span>
<span class="nc" id="L543">                    context.put(&quot;region_name&quot;, regionInfo.province + regionInfo.city + regionInfo.district);</span>
<span class="nc" id="L544">                    context.put(&quot;province&quot;, regionInfo.province);</span>
<span class="nc" id="L545">                    context.put(&quot;city&quot;, regionInfo.city);</span>
<span class="nc" id="L546">                    context.put(&quot;district&quot;, regionInfo.district);</span>
                } else {
<span class="nc" id="L548">                    context.put(&quot;region_name&quot;, &quot;未知地区&quot;);</span>
                }
            }

<span class="nc" id="L552">        } catch (Exception e) {</span>
<span class="nc" id="L553">            logger.warn(&quot;Failed to extract ID card info for context&quot;, e);</span>
<span class="nc" id="L554">        }</span>
<span class="nc" id="L555">    }</span>

    /**
     * 掩码身份证号用于日志记录。
     * 
     * @param idCard 原始身份证号
     * @return 掩码后的身份证号
     */
    private String maskIdCard(String idCard) {
<span class="nc bnc" id="L564" title="All 4 branches missed.">        if (idCard == null || idCard.length() &lt; 8) {</span>
<span class="nc" id="L565">            return &quot;****&quot;;</span>
        }

        // 显示前4位和后4位，中间用*代替
<span class="nc" id="L569">        String prefix = idCard.substring(0, 4);</span>
<span class="nc" id="L570">        String suffix = idCard.substring(idCard.length() - 4);</span>
<span class="nc" id="L571">        int maskLength = idCard.length() - 8;</span>
<span class="nc" id="L572">        String mask = &quot;*&quot;.repeat(Math.max(0, maskLength));</span>

<span class="nc" id="L574">        return prefix + mask + suffix;</span>
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L581" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L582">            return defaultValue;</span>
        }

<span class="nc" id="L585">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L586" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L593" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L594">            return defaultValue;</span>
        }

<span class="nc" id="L597">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L598" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L599">            return defaultValue;</span>
        }

<span class="nc bnc" id="L602" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L603">            return (Boolean) value;</span>
        }

<span class="nc" id="L606">        return Boolean.parseBoolean(value.toString());</span>
    }

    /**
     * 获取行政区划统计信息。
     * 
     * @return 统计信息
     */
    public String getRegionStats() {
<span class="nc" id="L615">        ensureDataLoaded(null);</span>

<span class="nc" id="L617">        StringBuilder stats = new StringBuilder();</span>
<span class="nc" id="L618">        stats.append(&quot;Total regions: &quot;).append(regionCodes.size()).append(&quot;\n&quot;);</span>

<span class="nc" id="L620">        stats.append(&quot;Provinces: &quot;).append(regionsByProvince.keySet().size()).append(&quot;\n&quot;);</span>
<span class="nc bnc" id="L621" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;String&gt;&gt; entry : regionsByProvince.entrySet()) {</span>
<span class="nc" id="L622">            stats.append(&quot;  &quot;).append(entry.getKey()).append(&quot;: &quot;).append(entry.getValue().size()).append(&quot; regions\n&quot;);</span>
<span class="nc" id="L623">        }</span>

        // 计算理论组合数
        // 每个地区代码 × 日期范围 × 1000个顺序码 = 总组合数
<span class="nc" id="L627">        long dateRange = java.time.temporal.ChronoUnit.DAYS.between(</span>
<span class="nc" id="L628">                LocalDate.of(1900, 1, 1), LocalDate.now().minusYears(1));</span>
<span class="nc" id="L629">        long totalCombinations = (long) regionCodes.size() * dateRange * 1000;</span>

<span class="nc" id="L631">        stats.append(&quot;\nTotal possible combinations: &quot;).append(String.format(&quot;%,d&quot;, totalCombinations));</span>

<span class="nc" id="L633">        return stats.toString();</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L638">        return &quot;Chinese ID card number generator - generates 18-digit ID cards with comprehensive region, birth date and gender support&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>