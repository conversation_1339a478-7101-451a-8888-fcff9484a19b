<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MimeTypeGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">MimeTypeGenerator</span></div><h1>MimeTypeGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,329 of 1,329</td><td class="ctr2">0%</td><td class="bar">65 of 65</td><td class="ctr2">0%</td><td class="ctr1">55</td><td class="ctr2">55</td><td class="ctr1">137</td><td class="ctr2">137</td><td class="ctr1">20</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a14"><a href="MimeTypeGenerator.java.html#L51" class="el_method">initializeMimeTypes()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="581" alt="581"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="MimeTypeGenerator.java.html#L294" class="el_method">generateMimeTypeByExtension(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="105" alt="105"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h0">22</td><td class="ctr2" id="i0">22</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a13"><a href="MimeTypeGenerator.java.html#L103" class="el_method">initializeMimeParameters()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="81" alt="81"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">5</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a18"><a href="MimeTypeGenerator.java.html#L25" class="el_method">static {...}</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="78" alt="78"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="MimeTypeGenerator.java.html#L129" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="73" alt="73"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="MimeTypeGenerator.java.html#L205" class="el_method">generateParameters(MimeTypeGenerator.MimeCategory, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="67" alt="67"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="MimeTypeGenerator.java.html#L157" class="el_method">generateMimeType(String, String, String, boolean, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="51" alt="51"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a19"><a href="MimeTypeGenerator.java.html#L327" class="el_method">validateMimeType(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="51" alt="51"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h4">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="MimeTypeGenerator.java.html#L366" class="el_method">generateMaliciousMimeType()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="46" alt="46"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="MimeTypeGenerator.java.html#L234" class="el_method">generateParameterValue(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="36" alt="36"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="9" alt="9"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a10"><a href="MimeTypeGenerator.java.html#L354" class="el_method">generateWebSafeMimeType()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="36" alt="36"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="MimeTypeGenerator.java.html#L279" class="el_method">extractSubtype(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="27" alt="27"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h9">6</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="MimeTypeGenerator.java.html#L260" class="el_method">generateRandomString(int)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="25" alt="25"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h11">5</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a0"><a href="MimeTypeGenerator.java.html#L183" class="el_method">determineMimeCategory(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="24" alt="24"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a17"><a href="MimeTypeGenerator.java.html#L197" class="el_method">selectRandomSubtype(MimeTypeGenerator.MimeCategory)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="20" alt="20"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h13">4</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a1"><a href="MimeTypeGenerator.java.html#L271" class="el_method">extractCategory(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h14">4</td><td class="ctr2" id="i14">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a16"><a href="MimeTypeGenerator.java.html#L230" class="el_method">selectRandomCharset()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="8" alt="8"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a15"><a href="MimeTypeGenerator.java.html#L23" class="el_method">MimeTypeGenerator()</a></td><td class="bar" id="b17"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a12"><a href="MimeTypeGenerator.java.html#L117" class="el_method">getType()</a></td><td class="bar" id="b18"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a11"><a href="MimeTypeGenerator.java.html#L122" class="el_method">getConfigClass()</a></td><td class="bar" id="b19"/><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>