<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VerificationCodeGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">VerificationCodeGenerator.java</span></div><h1>VerificationCodeGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * 验证码生成器
 * 
 * 支持的参数：
 * - type: 验证码类型 (EMAIL|SMS|CAPTCHA|NUMERIC|ALPHANUMERIC)
 * - length: 验证码长度 (默认6)
 * - chars: 字符集
 * (NUMERIC|ALPHANUMERIC|ALPHANUMERIC_UPPER|ALPHANUMERIC_LOWER|CUSTOM)
 * - custom_chars: 自定义字符集
 * - exclude_ambiguous: 是否排除易混淆字符 (true|false)
 * - case_sensitive: 是否区分大小写 (true|false)
 * - expiry_minutes: 有效期分钟数
 * 
 * <AUTHOR>
 */
<span class="nc" id="L27">public class VerificationCodeGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(VerificationCodeGenerator.class);</span>
<span class="nc" id="L30">    private static final Random random = new Random();</span>
<span class="nc" id="L31">    private static final SecureRandom secureRandom = new SecureRandom();</span>

    // 验证码类型枚举
<span class="nc" id="L34">    private enum CodeType {</span>
<span class="nc" id="L35">        EMAIL, // 邮箱验证码</span>
<span class="nc" id="L36">        SMS, // 短信验证码</span>
<span class="nc" id="L37">        CAPTCHA, // 图形验证码</span>
<span class="nc" id="L38">        NUMERIC, // 纯数字验证码</span>
<span class="nc" id="L39">        ALPHANUMERIC // 字母数字验证码</span>
    }

    // 字符集定义
    private static final String NUMERIC_CHARS = &quot;0123456789&quot;;
    private static final String ALPHA_LOWER_CHARS = &quot;abcdefghijklmnopqrstuvwxyz&quot;;
    private static final String ALPHA_UPPER_CHARS = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;;
    private static final String ALPHANUMERIC_CHARS = NUMERIC_CHARS + ALPHA_LOWER_CHARS + ALPHA_UPPER_CHARS;

    // 易混淆字符（排除0O、1lI等）
    private static final String AMBIGUOUS_CHARS = &quot;0O1lI&quot;;
    private static final String CLEAR_NUMERIC_CHARS = &quot;23456789&quot;;
    private static final String CLEAR_ALPHA_CHARS = &quot;abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ&quot;;
    private static final String CLEAR_ALPHANUMERIC_CHARS = CLEAR_NUMERIC_CHARS + CLEAR_ALPHA_CHARS;

    // 验证码模板
<span class="nc" id="L55">    private static final Map&lt;CodeType, CodeTemplate&gt; CODE_TEMPLATES = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L58">        initializeCodeTemplates();</span>
<span class="nc" id="L59">    }</span>

    private static void initializeCodeTemplates() {
<span class="nc" id="L62">        CODE_TEMPLATES.put(CodeType.EMAIL, new CodeTemplate(6, NUMERIC_CHARS, true));</span>
<span class="nc" id="L63">        CODE_TEMPLATES.put(CodeType.SMS, new CodeTemplate(6, NUMERIC_CHARS, true));</span>
<span class="nc" id="L64">        CODE_TEMPLATES.put(CodeType.CAPTCHA, new CodeTemplate(4, CLEAR_ALPHANUMERIC_CHARS, false));</span>
<span class="nc" id="L65">        CODE_TEMPLATES.put(CodeType.NUMERIC, new CodeTemplate(6, NUMERIC_CHARS, true));</span>
<span class="nc" id="L66">        CODE_TEMPLATES.put(CodeType.ALPHANUMERIC, new CodeTemplate(6, ALPHANUMERIC_CHARS, false));</span>
<span class="nc" id="L67">    }</span>

    // 验证码模板类
    private static class CodeTemplate {
        final int defaultLength;
        final String defaultChars;
        final boolean excludeAmbiguous;

<span class="nc" id="L75">        CodeTemplate(int defaultLength, String defaultChars, boolean excludeAmbiguous) {</span>
<span class="nc" id="L76">            this.defaultLength = defaultLength;</span>
<span class="nc" id="L77">            this.defaultChars = defaultChars;</span>
<span class="nc" id="L78">            this.excludeAmbiguous = excludeAmbiguous;</span>
<span class="nc" id="L79">        }</span>
    }

    @Override
    public String getType() {
<span class="nc" id="L84">        return &quot;verification_code&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L89">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L96">            String type = config.getParam(&quot;type&quot;, String.class, &quot;SMS&quot;);</span>
<span class="nc" id="L97">            int length = Integer.parseInt(config.getParam(&quot;length&quot;, String.class, &quot;6&quot;));</span>
<span class="nc" id="L98">            String chars = config.getParam(&quot;chars&quot;, String.class, &quot;AUTO&quot;);</span>
<span class="nc" id="L99">            String customChars = config.getParam(&quot;custom_chars&quot;, String.class, null);</span>
<span class="nc" id="L100">            boolean excludeAmbiguous = Boolean.parseBoolean(config.getParam(&quot;exclude_ambiguous&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L101">            boolean caseSensitive = Boolean.parseBoolean(config.getParam(&quot;case_sensitive&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L102">            int expiryMinutes = Integer.parseInt(config.getParam(&quot;expiry_minutes&quot;, String.class, &quot;5&quot;));</span>

            // 生成验证码
<span class="nc" id="L105">            String verificationCode = generateVerificationCode(type, length, chars, customChars, excludeAmbiguous,</span>
                    caseSensitive);

            // 计算过期时间
<span class="nc" id="L109">            long expiryTime = System.currentTimeMillis() + (expiryMinutes * 60 * 1000L);</span>

            // 将验证码信息存入上下文
<span class="nc" id="L112">            context.put(&quot;verification_code&quot;, verificationCode);</span>
<span class="nc" id="L113">            context.put(&quot;verification_type&quot;, type);</span>
<span class="nc" id="L114">            context.put(&quot;verification_expiry&quot;, expiryTime);</span>
<span class="nc" id="L115">            context.put(&quot;verification_length&quot;, verificationCode.length());</span>

<span class="nc" id="L117">            logger.debug(&quot;Generated verification code: {}*** (type: {}, length: {})&quot;,</span>
<span class="nc" id="L118">                    verificationCode.substring(0, Math.min(2, verificationCode.length())), type,</span>
<span class="nc" id="L119">                    verificationCode.length());</span>
<span class="nc" id="L120">            return verificationCode;</span>

<span class="nc" id="L122">        } catch (Exception e) {</span>
<span class="nc" id="L123">            logger.error(&quot;Error generating verification code&quot;, e);</span>
<span class="nc" id="L124">            return &quot;123456&quot;;</span>
        }
    }

    private String generateVerificationCode(String type, int length, String chars, String customChars,
            boolean excludeAmbiguous, boolean caseSensitive) {

        // 确定验证码类型
<span class="nc" id="L132">        CodeType codeType = determineCodeType(type);</span>
<span class="nc" id="L133">        CodeTemplate template = CODE_TEMPLATES.get(codeType);</span>

        // 确定长度
<span class="nc bnc" id="L136" title="All 2 branches missed.">        int finalLength = length &gt; 0 ? length : template.defaultLength;</span>

        // 确定字符集
<span class="nc" id="L139">        String charset = determineCharset(chars, customChars, template, excludeAmbiguous, caseSensitive);</span>

        // 生成验证码
<span class="nc" id="L142">        StringBuilder code = new StringBuilder();</span>
<span class="nc bnc" id="L143" title="All 2 branches missed.">        for (int i = 0; i &lt; finalLength; i++) {</span>
<span class="nc" id="L144">            int index = secureRandom.nextInt(charset.length());</span>
<span class="nc" id="L145">            code.append(charset.charAt(index));</span>
        }

<span class="nc" id="L148">        String result = code.toString();</span>

        // 应用大小写规则
<span class="nc bnc" id="L151" title="All 4 branches missed.">        if (!caseSensitive &amp;&amp; containsAlpha(result)) {</span>
<span class="nc" id="L152">            result = applyCase(result, codeType);</span>
        }

<span class="nc" id="L155">        return result;</span>
    }

    private CodeType determineCodeType(String type) {
        try {
<span class="nc" id="L160">            return CodeType.valueOf(type.toUpperCase());</span>
<span class="nc" id="L161">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L162">            logger.warn(&quot;Unknown verification code type: {}. Using SMS.&quot;, type);</span>
<span class="nc" id="L163">            return CodeType.SMS;</span>
        }
    }

    private String determineCharset(String chars, String customChars, CodeTemplate template,
            boolean excludeAmbiguous, boolean caseSensitive) {

        String charset;

<span class="nc bnc" id="L172" title="All 4 branches missed.">        if (customChars != null &amp;&amp; !customChars.isEmpty()) {</span>
<span class="nc" id="L173">            charset = customChars;</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">        } else if (&quot;AUTO&quot;.equals(chars)) {</span>
<span class="nc" id="L175">            charset = template.defaultChars;</span>
<span class="nc" id="L176">            excludeAmbiguous = template.excludeAmbiguous;</span>
        } else {
<span class="nc bnc" id="L178" title="All 5 branches missed.">            switch (chars.toUpperCase()) {</span>
                case &quot;NUMERIC&quot;:
<span class="nc" id="L180">                    charset = NUMERIC_CHARS;</span>
<span class="nc" id="L181">                    break;</span>
                case &quot;ALPHANUMERIC&quot;:
<span class="nc" id="L183">                    charset = ALPHANUMERIC_CHARS;</span>
<span class="nc" id="L184">                    break;</span>
                case &quot;ALPHANUMERIC_UPPER&quot;:
<span class="nc" id="L186">                    charset = NUMERIC_CHARS + ALPHA_UPPER_CHARS;</span>
<span class="nc" id="L187">                    break;</span>
                case &quot;ALPHANUMERIC_LOWER&quot;:
<span class="nc" id="L189">                    charset = NUMERIC_CHARS + ALPHA_LOWER_CHARS;</span>
<span class="nc" id="L190">                    break;</span>
                default:
<span class="nc" id="L192">                    charset = template.defaultChars;</span>
                    break;
            }
        }

        // 排除易混淆字符
<span class="nc bnc" id="L198" title="All 2 branches missed.">        if (excludeAmbiguous) {</span>
<span class="nc" id="L199">            charset = removeAmbiguousChars(charset);</span>
        }

<span class="nc" id="L202">        return charset;</span>
    }

    private String removeAmbiguousChars(String charset) {
<span class="nc" id="L206">        StringBuilder result = new StringBuilder();</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">        for (char c : charset.toCharArray()) {</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">            if (AMBIGUOUS_CHARS.indexOf(c) == -1) {</span>
<span class="nc" id="L209">                result.append(c);</span>
            }
        }
<span class="nc" id="L212">        return result.toString();</span>
    }

    private boolean containsAlpha(String text) {
<span class="nc bnc" id="L216" title="All 2 branches missed.">        for (char c : text.toCharArray()) {</span>
<span class="nc bnc" id="L217" title="All 2 branches missed.">            if (Character.isLetter(c)) {</span>
<span class="nc" id="L218">                return true;</span>
            }
        }
<span class="nc" id="L221">        return false;</span>
    }

    private String applyCase(String code, CodeType codeType) {
<span class="nc bnc" id="L225" title="All 4 branches missed.">        switch (codeType) {</span>
            case EMAIL:
            case SMS:
                // 邮箱和短信验证码通常使用大写
<span class="nc" id="L229">                return code.toUpperCase();</span>

            case CAPTCHA:
                // 图形验证码保持混合大小写
<span class="nc" id="L233">                return code;</span>

            case NUMERIC:
                // 数字验证码不涉及大小写
<span class="nc" id="L237">                return code;</span>

            case ALPHANUMERIC:
            default:
                // 字母数字验证码使用大写
<span class="nc" id="L242">                return code.toUpperCase();</span>
        }
    }

    /**
     * 验证验证码是否有效
     */
    public boolean validateCode(String inputCode, String expectedCode, boolean caseSensitive) {
<span class="nc bnc" id="L250" title="All 4 branches missed.">        if (inputCode == null || expectedCode == null) {</span>
<span class="nc" id="L251">            return false;</span>
        }

<span class="nc bnc" id="L254" title="All 2 branches missed.">        if (caseSensitive) {</span>
<span class="nc" id="L255">            return inputCode.equals(expectedCode);</span>
        } else {
<span class="nc" id="L257">            return inputCode.equalsIgnoreCase(expectedCode);</span>
        }
    }

    /**
     * 检查验证码是否过期
     */
    public boolean isExpired(long expiryTime) {
<span class="nc bnc" id="L265" title="All 2 branches missed.">        return System.currentTimeMillis() &gt; expiryTime;</span>
    }

    /**
     * 生成带格式的验证码（如：123-456）
     */
    public String generateFormattedCode(String code, String separator, int groupSize) {
<span class="nc bnc" id="L272" title="All 6 branches missed.">        if (separator == null || groupSize &lt;= 0 || code.length() &lt;= groupSize) {</span>
<span class="nc" id="L273">            return code;</span>
        }

<span class="nc" id="L276">        StringBuilder formatted = new StringBuilder();</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">        for (int i = 0; i &lt; code.length(); i += groupSize) {</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L279">                formatted.append(separator);</span>
            }

<span class="nc" id="L282">            int endIndex = Math.min(i + groupSize, code.length());</span>
<span class="nc" id="L283">            formatted.append(code.substring(i, endIndex));</span>
        }

<span class="nc" id="L286">        return formatted.toString();</span>
    }

    /**
     * 生成数学验证码（如：3+5=?）
     */
    public String generateMathCode() {
<span class="nc" id="L293">        int a = random.nextInt(10);</span>
<span class="nc" id="L294">        int b = random.nextInt(10);</span>
<span class="nc" id="L295">        int operator = random.nextInt(2); // 0: +, 1: -</span>

<span class="nc bnc" id="L297" title="All 2 branches missed.">        if (operator == 0) {</span>
<span class="nc" id="L298">            return String.format(&quot;%d+%d=%d&quot;, a, b, a + b);</span>
        } else {
            // 确保结果为正数
<span class="nc bnc" id="L301" title="All 2 branches missed.">            if (a &lt; b) {</span>
<span class="nc" id="L302">                int temp = a;</span>
<span class="nc" id="L303">                a = b;</span>
<span class="nc" id="L304">                b = temp;</span>
            }
<span class="nc" id="L306">            return String.format(&quot;%d-%d=%d&quot;, a, b, a - b);</span>
        }
    }

    /**
     * 生成中文验证码
     */
    public String generateChineseCode(int length) {
<span class="nc" id="L314">        String[] chineseNumbers = { &quot;零&quot;, &quot;一&quot;, &quot;二&quot;, &quot;三&quot;, &quot;四&quot;, &quot;五&quot;, &quot;六&quot;, &quot;七&quot;, &quot;八&quot;, &quot;九&quot; };</span>
<span class="nc" id="L315">        StringBuilder code = new StringBuilder();</span>

<span class="nc bnc" id="L317" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L318">            code.append(chineseNumbers[random.nextInt(chineseNumbers.length)]);</span>
        }

<span class="nc" id="L321">        return code.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>