<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ApiKeyGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">ApiKeyGenerator.java</span></div><h1>ApiKeyGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * API密钥生成器
 * 
 * 支持的参数：
 * - type: 密钥类型 (JWT|BEARER|BASIC|CUSTOM)
 * - length: 密钥长度 (默认32)
 * - format: 输出格式 (BASE64|HEX|ALPHANUMERIC)
 * - prefix: 密钥前缀 (如 &quot;sk_&quot;, &quot;pk_&quot;)
 * - include_checksum: 是否包含校验和 (true|false)
 * - secure: 是否使用安全随机数生成器 (true|false)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L25">public class ApiKeyGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(ApiKeyGenerator.class);</span>
<span class="nc" id="L28">    private static final Random random = new Random();</span>
<span class="nc" id="L29">    private static final SecureRandom secureRandom = new SecureRandom();</span>

    // 常见API密钥前缀
<span class="nc" id="L32">    private static final Map&lt;String, List&lt;String&gt;&gt; KEY_PREFIXES = new HashMap&lt;&gt;();</span>

    // Base64字符集
    private static final String BASE64_CHARS = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/&quot;;

    // 十六进制字符集
    private static final String HEX_CHARS = &quot;0123456789abcdef&quot;;

    // 字母数字字符集
    private static final String ALPHANUMERIC_CHARS = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;;

    static {
<span class="nc" id="L44">        initializeKeyPrefixes();</span>
<span class="nc" id="L45">    }</span>

    private static void initializeKeyPrefixes() {
        // 不同服务的API密钥前缀
<span class="nc" id="L49">        KEY_PREFIXES.put(&quot;STRIPE&quot;, Arrays.asList(&quot;sk_&quot;, &quot;pk_&quot;, &quot;rk_&quot;));</span>
<span class="nc" id="L50">        KEY_PREFIXES.put(&quot;GITHUB&quot;, Arrays.asList(&quot;ghp_&quot;, &quot;gho_&quot;, &quot;ghu_&quot;, &quot;ghs_&quot;, &quot;ghr_&quot;));</span>
<span class="nc" id="L51">        KEY_PREFIXES.put(&quot;OPENAI&quot;, Arrays.asList(&quot;sk-&quot;));</span>
<span class="nc" id="L52">        KEY_PREFIXES.put(&quot;AWS&quot;, Arrays.asList(&quot;AKIA&quot;, &quot;ASIA&quot;));</span>
<span class="nc" id="L53">        KEY_PREFIXES.put(&quot;GOOGLE&quot;, Arrays.asList(&quot;AIza&quot;));</span>
<span class="nc" id="L54">        KEY_PREFIXES.put(&quot;SLACK&quot;, Arrays.asList(&quot;xoxb-&quot;, &quot;xoxp-&quot;, &quot;xoxa-&quot;));</span>
<span class="nc" id="L55">        KEY_PREFIXES.put(&quot;DISCORD&quot;, Arrays.asList(&quot;Bot &quot;, &quot;Bearer &quot;));</span>
<span class="nc" id="L56">        KEY_PREFIXES.put(&quot;TWITTER&quot;, Arrays.asList(&quot;&quot;));</span>
<span class="nc" id="L57">        KEY_PREFIXES.put(&quot;GENERIC&quot;, Arrays.asList(&quot;api_&quot;, &quot;key_&quot;, &quot;token_&quot;, &quot;&quot;));</span>
<span class="nc" id="L58">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L62">        return &quot;apikey&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L67">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L74">            String type = config.getParam(&quot;type&quot;, String.class, &quot;CUSTOM&quot;);</span>
<span class="nc" id="L75">            int length = Integer.parseInt(config.getParam(&quot;length&quot;, String.class, &quot;32&quot;));</span>
<span class="nc" id="L76">            String format = config.getParam(&quot;format&quot;, String.class, &quot;ALPHANUMERIC&quot;);</span>
<span class="nc" id="L77">            String prefix = config.getParam(&quot;prefix&quot;, String.class, null);</span>
<span class="nc" id="L78">            boolean includeChecksum = Boolean.parseBoolean(config.getParam(&quot;include_checksum&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L79">            boolean secure = Boolean.parseBoolean(config.getParam(&quot;secure&quot;, String.class, &quot;true&quot;));</span>

            // 生成API密钥
<span class="nc" id="L82">            String apiKey = generateApiKey(type, length, format, prefix, includeChecksum, secure);</span>

            // 将API密钥信息存入上下文
<span class="nc" id="L85">            context.put(&quot;api_key&quot;, apiKey);</span>
<span class="nc" id="L86">            context.put(&quot;api_key_type&quot;, type);</span>
<span class="nc" id="L87">            context.put(&quot;api_key_format&quot;, format);</span>

<span class="nc" id="L89">            logger.debug(&quot;Generated API key: {}***&quot;, apiKey.substring(0, Math.min(8, apiKey.length())));</span>
<span class="nc" id="L90">            return apiKey;</span>

<span class="nc" id="L92">        } catch (Exception e) {</span>
<span class="nc" id="L93">            logger.error(&quot;Error generating API key&quot;, e);</span>
<span class="nc" id="L94">            return &quot;sk_test_&quot; + generateRandomString(32, ALPHANUMERIC_CHARS, false);</span>
        }
    }

    private String generateApiKey(String type, int length, String format, String prefix,
            boolean includeChecksum, boolean secure) {

<span class="nc" id="L101">        StringBuilder apiKey = new StringBuilder();</span>

        // 1. 添加前缀
<span class="nc" id="L104">        String keyPrefix = determinePrefix(type, prefix);</span>
<span class="nc bnc" id="L105" title="All 4 branches missed.">        if (keyPrefix != null &amp;&amp; !keyPrefix.isEmpty()) {</span>
<span class="nc" id="L106">            apiKey.append(keyPrefix);</span>
        }

        // 2. 生成主体部分
<span class="nc" id="L110">        int mainLength = length;</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">        if (includeChecksum) {</span>
<span class="nc" id="L112">            mainLength -= 4; // 为校验和预留4个字符</span>
        }

<span class="nc" id="L115">        String mainPart = generateMainPart(mainLength, format, secure);</span>
<span class="nc" id="L116">        apiKey.append(mainPart);</span>

        // 3. 添加校验和（如果需要）
<span class="nc bnc" id="L119" title="All 2 branches missed.">        if (includeChecksum) {</span>
<span class="nc" id="L120">            String checksum = generateChecksum(apiKey.toString());</span>
<span class="nc" id="L121">            apiKey.append(checksum);</span>
        }

        // 4. 根据类型进行特殊处理
<span class="nc" id="L125">        return processApiKeyByType(apiKey.toString(), type);</span>
    }

    private String determinePrefix(String type, String customPrefix) {
<span class="nc bnc" id="L129" title="All 2 branches missed.">        if (customPrefix != null) {</span>
<span class="nc" id="L130">            return customPrefix;</span>
        }

<span class="nc bnc" id="L133" title="All 4 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;JWT&quot;:
<span class="nc" id="L135">                return &quot;&quot;; // JWT通常没有前缀</span>

            case &quot;BEARER&quot;:
<span class="nc" id="L138">                return &quot;Bearer &quot;;</span>

            case &quot;BASIC&quot;:
<span class="nc" id="L141">                return &quot;Basic &quot;;</span>

            case &quot;CUSTOM&quot;:
            default:
                // 随机选择一个通用前缀
<span class="nc" id="L146">                List&lt;String&gt; genericPrefixes = KEY_PREFIXES.get(&quot;GENERIC&quot;);</span>
<span class="nc" id="L147">                return genericPrefixes.get(random.nextInt(genericPrefixes.size()));</span>
        }
    }

    private String generateMainPart(int length, String format, boolean secure) {
<span class="nc bnc" id="L152" title="All 3 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;BASE64&quot;:
<span class="nc" id="L154">                return generateRandomString(length, BASE64_CHARS, secure);</span>

            case &quot;HEX&quot;:
<span class="nc" id="L157">                return generateRandomString(length, HEX_CHARS, secure);</span>

            case &quot;ALPHANUMERIC&quot;:
            default:
<span class="nc" id="L161">                return generateRandomString(length, ALPHANUMERIC_CHARS, secure);</span>
        }
    }

    private String generateRandomString(int length, String charset, boolean secure) {
<span class="nc" id="L166">        StringBuilder result = new StringBuilder();</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">        Random rng = secure ? secureRandom : random;</span>

<span class="nc bnc" id="L169" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L170">            result.append(charset.charAt(rng.nextInt(charset.length())));</span>
        }

<span class="nc" id="L173">        return result.toString();</span>
    }

    private String generateChecksum(String input) {
        // 简单的校验和算法
<span class="nc" id="L178">        int sum = 0;</span>
<span class="nc bnc" id="L179" title="All 2 branches missed.">        for (char c : input.toCharArray()) {</span>
<span class="nc" id="L180">            sum += c;</span>
        }

<span class="nc" id="L183">        return String.format(&quot;%04x&quot;, sum % 0x10000);</span>
    }

    private String processApiKeyByType(String apiKey, String type) {
<span class="nc bnc" id="L187" title="All 3 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;JWT&quot;:
<span class="nc" id="L189">                return generateJwtLikeToken(apiKey);</span>

            case &quot;BEARER&quot;:
            case &quot;BASIC&quot;:
<span class="nc" id="L193">                return apiKey; // 已经包含了前缀</span>

            case &quot;CUSTOM&quot;:
            default:
<span class="nc" id="L197">                return apiKey;</span>
        }
    }

    private String generateJwtLikeToken(String payload) {
        // 生成类似JWT的三段式token
<span class="nc" id="L203">        String header = generateJwtSegment(20);</span>
<span class="nc" id="L204">        String body = generateJwtSegment(Math.max(40, payload.length()));</span>
<span class="nc" id="L205">        String signature = generateJwtSegment(16);</span>

<span class="nc" id="L207">        return header + &quot;.&quot; + body + &quot;.&quot; + signature;</span>
    }

    private String generateJwtSegment(int length) {
        // JWT使用URL安全的Base64编码
<span class="nc" id="L212">        String urlSafeBase64Chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_&quot;;</span>
<span class="nc" id="L213">        return generateRandomString(length, urlSafeBase64Chars, true);</span>
    }

    /**
     * 生成特定服务风格的API密钥
     */
    public String generateServiceApiKey(String service, int length) {
<span class="nc" id="L220">        List&lt;String&gt; prefixes = KEY_PREFIXES.get(service.toUpperCase());</span>
<span class="nc bnc" id="L221" title="All 4 branches missed.">        if (prefixes == null || prefixes.isEmpty()) {</span>
<span class="nc" id="L222">            prefixes = KEY_PREFIXES.get(&quot;GENERIC&quot;);</span>
        }

<span class="nc" id="L225">        String prefix = prefixes.get(random.nextInt(prefixes.size()));</span>
<span class="nc" id="L226">        String mainPart = generateRandomString(length - prefix.length(), ALPHANUMERIC_CHARS, true);</span>

<span class="nc" id="L228">        return prefix + mainPart;</span>
    }

    /**
     * 验证API密钥格式
     */
    public boolean validateApiKeyFormat(String apiKey, String expectedType) {
<span class="nc bnc" id="L235" title="All 4 branches missed.">        if (apiKey == null || apiKey.isEmpty()) {</span>
<span class="nc" id="L236">            return false;</span>
        }

<span class="nc bnc" id="L239" title="All 4 branches missed.">        switch (expectedType.toUpperCase()) {</span>
            case &quot;JWT&quot;:
<span class="nc bnc" id="L241" title="All 2 branches missed.">                return apiKey.split(&quot;\\.&quot;).length == 3;</span>

            case &quot;BEARER&quot;:
<span class="nc" id="L244">                return apiKey.startsWith(&quot;Bearer &quot;);</span>

            case &quot;BASIC&quot;:
<span class="nc" id="L247">                return apiKey.startsWith(&quot;Basic &quot;);</span>

            case &quot;CUSTOM&quot;:
            default:
<span class="nc bnc" id="L251" title="All 2 branches missed.">                return apiKey.length() &gt;= 16; // 最小长度要求</span>
        }
    }

    /**
     * 生成API密钥对（公钥/私钥）
     */
    public Map&lt;String, String&gt; generateApiKeyPair() {
<span class="nc" id="L259">        Map&lt;String, String&gt; keyPair = new HashMap&lt;&gt;();</span>

<span class="nc" id="L261">        String publicKey = &quot;pk_&quot; + generateRandomString(32, ALPHANUMERIC_CHARS, true);</span>
<span class="nc" id="L262">        String secretKey = &quot;sk_&quot; + generateRandomString(32, ALPHANUMERIC_CHARS, true);</span>

<span class="nc" id="L264">        keyPair.put(&quot;public_key&quot;, publicKey);</span>
<span class="nc" id="L265">        keyPair.put(&quot;secret_key&quot;, secretKey);</span>

<span class="nc" id="L267">        return keyPair;</span>
    }

    /**
     * 生成带有过期时间的临时token
     */
    public String generateTemporaryToken(int validityMinutes) {
<span class="nc" id="L274">        long expirationTime = System.currentTimeMillis() + (validityMinutes * 60 * 1000L);</span>
<span class="nc" id="L275">        String token = generateRandomString(24, ALPHANUMERIC_CHARS, true);</span>

        // 将过期时间编码到token中（简化版本）
<span class="nc" id="L278">        String expiration = Long.toHexString(expirationTime);</span>

<span class="nc" id="L280">        return &quot;tmp_&quot; + token + &quot;_&quot; + expiration;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>