#!/bin/bash

# DataForge 示例运行脚本

set -e

echo "========================================="
echo "DataForge Example Runner"
echo "========================================="

# 确保项目已构建
if [ ! -f "data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar" ]; then
    echo "Project not built. Running build script..."
    ./build.sh
fi

# 创建输出目录
mkdir -p output

echo ""
echo "Running DataForge examples..."
echo ""

# 示例1：生成10条基本数据到控制台
echo "Example 1: Generate 10 records to console"
echo "-----------------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 10 \
    --format console \
    --fields "id:uuid,name:name"

echo ""
echo "Example 2: Generate 20 records to CSV file"
echo "-------------------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 20 \
    --format csv \
    --output output/sample-data.csv \
    --fields "id:uuid,name:name,englishName:name"

echo ""
echo "Example 3: Generate 5 records to JSON file"
echo "-------------------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 5 \
    --format json \
    --output output/sample-data.json \
    --fields "id:uuid,name:name"

echo ""
echo "Example 4: Generate SQL INSERT statements"
echo "-----------------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 5 \
    --format sql \
    --output output/sample-data.sql \
    --sql-table users \
    --fields "id:uuid,name:name"

echo ""
echo "Example 5: Generate comprehensive data with all generators"
echo "---------------------------------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 10 \
    --format csv \
    --output output/comprehensive-data.csv \
    --fields "id:uuid,name:name,phone:phone,email:email,idcard:idcard,bankcard:bankcard"

echo ""
echo "Example 6: Use configuration file"
echo "---------------------------------"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --config examples/comprehensive-config.yml

echo ""
echo "========================================="
echo "Examples completed!"
echo "========================================="

echo ""
echo "Generated files:"
ls -la output/

echo ""
echo "To view generated CSV file:"
echo "cat output/sample-data.csv"

echo ""
echo "To view generated JSON file:"
echo "cat output/sample-data.json"

echo ""
echo "To view generated SQL file:"
echo "cat output/sample-data.sql"