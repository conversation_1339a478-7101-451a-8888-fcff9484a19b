<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldConfigWrapper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.config</a> &gt; <span class="el_source">FieldConfigWrapper.java</span></div><h1>FieldConfigWrapper.java</h1><pre class="source lang-java linenums">package com.dataforge.config;

import java.util.Map;

/**
 * 字段配置包装类。
 * 
 * &lt;p&gt;
 * 用于处理不同类型的字段配置参数，提供灵活的参数接收机制。
 * 该类继承自SimpleFieldConfig，主要用于配置文件的反序列化。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class FieldConfigWrapper extends SimpleFieldConfig {

    /**
     * 默认构造函数。
     */
    public FieldConfigWrapper() {
<span class="nc" id="L21">        super();</span>
<span class="nc" id="L22">    }</span>

    /**
     * 构造函数。
     * 
     * @param name 字段名称
     * @param type 数据类型标识符
     */
    public FieldConfigWrapper(String name, String type) {
<span class="nc" id="L31">        super(name, type);</span>
<span class="nc" id="L32">    }</span>

    /**
     * 构造函数。
     * 
     * @param name   字段名称
     * @param type   数据类型标识符
     * @param params 参数映射
     */
    public FieldConfigWrapper(String name, String type, Map&lt;String, Object&gt; params) {
<span class="nc" id="L42">        super(name, type);</span>
<span class="nc" id="L43">        setParams(params);</span>
<span class="nc" id="L44">    }</span>

    /**
     * 创建字段配置包装器的便捷方法。
     * 
     * @param name 字段名称
     * @param type 数据类型标识符
     * @return 新的字段配置包装器实例
     */
    public static FieldConfigWrapper of(String name, String type) {
<span class="nc" id="L54">        return new FieldConfigWrapper(name, type);</span>
    }

    /**
     * 创建字段配置包装器的便捷方法。
     * 
     * @param name   字段名称
     * @param type   数据类型标识符
     * @param params 参数映射
     * @return 新的字段配置包装器实例
     */
    public static FieldConfigWrapper of(String name, String type, Map&lt;String, Object&gt; params) {
<span class="nc" id="L66">        return new FieldConfigWrapper(name, type, params);</span>
    }

    /**
     * 添加参数的便捷方法，支持链式调用。
     * 
     * @param key   参数键
     * @param value 参数值
     * @return 当前实例，支持链式调用
     */
    public FieldConfigWrapper withParam(String key, Object value) {
<span class="nc" id="L77">        setParam(key, value);</span>
<span class="nc" id="L78">        return this;</span>
    }

    /**
     * 批量添加参数的便捷方法，支持链式调用。
     * 
     * @param params 参数映射
     * @return 当前实例，支持链式调用
     */
    public FieldConfigWrapper withParams(Map&lt;String, Object&gt; params) {
<span class="nc bnc" id="L88" title="All 2 branches missed.">        if (params != null) {</span>
<span class="nc" id="L89">            getParams().putAll(params);</span>
        }
<span class="nc" id="L91">        return this;</span>
    }

    /**
     * 设置字段描述的便捷方法，支持链式调用。
     * 
     * @param description 字段描述
     * @return 当前实例，支持链式调用
     */
    public FieldConfigWrapper withDescription(String description) {
<span class="nc" id="L101">        setDescription(description);</span>
<span class="nc" id="L102">        return this;</span>
    }

    /**
     * 设置是否必填的便捷方法，支持链式调用。
     * 
     * @param required 是否必填
     * @return 当前实例，支持链式调用
     */
    public FieldConfigWrapper withRequired(boolean required) {
<span class="nc" id="L112">        setRequired(required);</span>
<span class="nc" id="L113">        return this;</span>
    }

    /**
     * 获取字符串类型的参数值。
     * 
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或类型不匹配返回默认值
     */
    public String getStringParam(String key, String defaultValue) {
<span class="nc" id="L124">        return getParam(key, String.class, defaultValue);</span>
    }

    /**
     * 获取整数类型的参数值。
     * 
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或类型不匹配返回默认值
     */
    public Integer getIntParam(String key, Integer defaultValue) {
<span class="nc" id="L135">        Object value = getParam(key);</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L137">            return ((Number) value).intValue();</span>
        }
<span class="nc bnc" id="L139" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L141">                return Integer.parseInt((String) value);</span>
<span class="nc" id="L142">            } catch (NumberFormatException e) {</span>
                // 忽略解析错误，返回默认值
            }
        }
<span class="nc" id="L146">        return defaultValue;</span>
    }

    /**
     * 获取布尔类型的参数值。
     * 
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或类型不匹配返回默认值
     */
    public Boolean getBooleanParam(String key, Boolean defaultValue) {
<span class="nc" id="L157">        Object value = getParam(key);</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L159">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L161" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L162">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L164">        return defaultValue;</span>
    }

    /**
     * 获取双精度浮点数类型的参数值。
     * 
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或类型不匹配返回默认值
     */
    public Double getDoubleParam(String key, Double defaultValue) {
<span class="nc" id="L175">        Object value = getParam(key);</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L177">            return ((Number) value).doubleValue();</span>
        }
<span class="nc bnc" id="L179" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L181">                return Double.parseDouble((String) value);</span>
<span class="nc" id="L182">            } catch (NumberFormatException e) {</span>
                // 忽略解析错误，返回默认值
            }
        }
<span class="nc" id="L186">        return defaultValue;</span>
    }

    /**
     * 验证配置的有效性。
     * 
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValidConfig() {
<span class="nc bnc" id="L195" title="All 4 branches missed.">        return getName() != null &amp;&amp; !getName().trim().isEmpty()</span>
<span class="nc bnc" id="L196" title="All 4 branches missed.">                &amp;&amp; getType() != null &amp;&amp; !getType().trim().isEmpty();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>