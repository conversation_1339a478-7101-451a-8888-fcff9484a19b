<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IpAddressGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">IpAddressGenerator</span></div><h1>IpAddressGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">645 of 645</td><td class="ctr2">0%</td><td class="bar">69 of 69</td><td class="ctr2">0%</td><td class="ctr1">54</td><td class="ctr2">54</td><td class="ctr1">117</td><td class="ctr2">117</td><td class="ctr1">16</td><td class="ctr2">16</td></tr></tfoot><tbody><tr><td id="a9"><a href="IpAddressGenerator.java.html#L191" class="el_method">generateIpv6(String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="102" alt="102"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="IpAddressGenerator.java.html#L99" class="el_method">generateIpv4FromSubnet(String, int)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="93" alt="93"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="IpAddressGenerator.java.html#L48" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="73" alt="73"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h0">16</td><td class="ctr2" id="i0">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="IpAddressGenerator.java.html#L227" class="el_method">generateIpv6FromSubnet(String, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="56" alt="56"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="IpAddressGenerator.java.html#L129" class="el_method">generateIpv4(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="51" alt="51"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="7" alt="7"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="IpAddressGenerator.java.html#L270" class="el_method">expandIpv6(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="49" alt="49"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h6">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="IpAddressGenerator.java.html#L150" class="el_method">generateIpv4Private()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="48" alt="48"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="IpAddressGenerator.java.html#L78" class="el_method">generateFromSubnet(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="45" alt="45"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="IpAddressGenerator.java.html#L176" class="el_method">isReservedIpv4(int, int, int, int)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="39" alt="39"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h4">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="IpAddressGenerator.java.html#L165" class="el_method">generateIpv4Public()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="31" alt="31"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h9">6</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a15"><a href="IpAddressGenerator.java.html#L24" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="24" alt="24"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">3</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="IpAddressGenerator.java.html#L249" class="el_method">formatIpv6(String, String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="19" alt="19"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="3" alt="3"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="IpAddressGenerator.java.html#L264" class="el_method">compressIpv6(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="8" alt="8"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a13"><a href="IpAddressGenerator.java.html#L22" class="el_method">IpAddressGenerator()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a12"><a href="IpAddressGenerator.java.html#L36" class="el_method">getType()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a11"><a href="IpAddressGenerator.java.html#L41" class="el_method">getConfigClass()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>