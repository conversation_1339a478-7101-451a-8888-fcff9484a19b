<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrganizationCodeGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">OrganizationCodeGenerator.java</span></div><h1>OrganizationCodeGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.validation.OrganizationCodeValidator;
import com.dataforge.validation.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * 组织机构代码生成器
 * 
 * 基于GB 11714-1997标准生成9位组织机构代码
 * 结构：8位主体代码 + 1位校验码
 * 
 * 支持的参数：
 * - valid: 是否生成有效代码 (true|false)
 * - prefix: 代码前缀 (8位以内的字符串)
 * 
 * 注意：组织机构代码已被统一社会信用代码替代，主要用于历史数据兼容
 * 
 * <AUTHOR>
 */
<span class="nc" id="L27">public class OrganizationCodeGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(OrganizationCodeGenerator.class);</span>
<span class="nc" id="L30">    private static final Random random = new Random();</span>

    // 组织机构代码字符集（数字和大写字母，排除I、O、S、V、Z）
    private static final String CODE_CHARS = &quot;0123456789ABCDEFGHJKLMNPQRTUWXY&quot;;

    // 校验码权重
<span class="nc" id="L36">    private static final int[] WEIGHTS = { 3, 7, 9, 10, 5, 8, 4, 2 };</span>

    @Override
    public String getType() {
<span class="nc" id="L40">        return &quot;orgcode&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L45">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L52">            boolean valid = Boolean.parseBoolean(config.getParam(&quot;valid&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L53">            String prefix = config.getParam(&quot;prefix&quot;, String.class, null);</span>

            // 生成组织机构代码
<span class="nc" id="L56">            String orgCode = generateOrgCode(prefix, valid);</span>

            // 验证生成的代码
<span class="nc bnc" id="L59" title="All 2 branches missed.">            if (valid) {</span>
<span class="nc" id="L60">                OrganizationCodeValidator validator = new OrganizationCodeValidator();</span>
<span class="nc" id="L61">                ValidationResult validation = validator.validate(orgCode);</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">                if (!validation.isValid()) {</span>
<span class="nc" id="L63">                    logger.warn(&quot;Generated invalid organization code: {}, errors: {}&quot;, orgCode,</span>
<span class="nc" id="L64">                            validation.getErrorMessages());</span>
                    // 重新生成
<span class="nc" id="L66">                    orgCode = generateOrgCode(prefix, true);</span>
                }
            }

<span class="nc" id="L70">            logger.debug(&quot;Generated organization code: {}&quot;, orgCode);</span>
<span class="nc" id="L71">            return orgCode;</span>

<span class="nc" id="L73">        } catch (Exception e) {</span>
<span class="nc" id="L74">            logger.error(&quot;Error generating organization code&quot;, e);</span>
<span class="nc" id="L75">            return &quot;12345678-9&quot;; // 默认有效代码</span>
        }
    }

    private String generateOrgCode(String prefix, boolean valid) {
<span class="nc" id="L80">        StringBuilder code = new StringBuilder();</span>

        // 1. 生成8位主体代码
<span class="nc" id="L83">        String mainCode = generateMainCode(prefix);</span>
<span class="nc" id="L84">        code.append(mainCode);</span>

        // 2. 添加分隔符
<span class="nc" id="L87">        code.append(&quot;-&quot;);</span>

        // 3. 生成校验码
        String checkCode;
<span class="nc bnc" id="L91" title="All 2 branches missed.">        if (valid) {</span>
<span class="nc" id="L92">            checkCode = calculateCheckCode(mainCode);</span>
        } else {
            // 生成错误的校验码
            do {
<span class="nc" id="L96">                checkCode = String.valueOf(CODE_CHARS.charAt(random.nextInt(CODE_CHARS.length())));</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">            } while (checkCode.equals(calculateCheckCode(mainCode)));</span>
        }
<span class="nc" id="L99">        code.append(checkCode);</span>

<span class="nc" id="L101">        return code.toString();</span>
    }

    private String generateMainCode(String prefix) {
<span class="nc" id="L105">        StringBuilder mainCode = new StringBuilder();</span>

        // 如果有前缀，使用前缀
<span class="nc bnc" id="L108" title="All 4 branches missed.">        if (prefix != null &amp;&amp; !prefix.isEmpty()) {</span>
            // 确保前缀只包含有效字符
<span class="nc" id="L110">            String validPrefix = prefix.toUpperCase().replaceAll(&quot;[^0-9A-Z]&quot;, &quot;&quot;);</span>
<span class="nc" id="L111">            validPrefix = validPrefix.replaceAll(&quot;[IOSVZ]&quot;, &quot;&quot;); // 排除无效字符</span>

<span class="nc bnc" id="L113" title="All 2 branches missed.">            if (validPrefix.length() &gt; 8) {</span>
<span class="nc" id="L114">                validPrefix = validPrefix.substring(0, 8);</span>
            }

<span class="nc" id="L117">            mainCode.append(validPrefix);</span>
        }

        // 补充剩余位数
<span class="nc bnc" id="L121" title="All 2 branches missed.">        while (mainCode.length() &lt; 8) {</span>
<span class="nc" id="L122">            char c = CODE_CHARS.charAt(random.nextInt(CODE_CHARS.length()));</span>
<span class="nc" id="L123">            mainCode.append(c);</span>
<span class="nc" id="L124">        }</span>

<span class="nc" id="L126">        return mainCode.toString();</span>
    }

    private String calculateCheckCode(String mainCode) {
<span class="nc" id="L130">        int sum = 0;</span>

        // 计算加权和
<span class="nc bnc" id="L133" title="All 2 branches missed.">        for (int i = 0; i &lt; 8; i++) {</span>
<span class="nc" id="L134">            char c = mainCode.charAt(i);</span>
            int value;

<span class="nc bnc" id="L137" title="All 2 branches missed.">            if (Character.isDigit(c)) {</span>
<span class="nc" id="L138">                value = c - '0';</span>
            } else {
                // 字母转换为数值
<span class="nc" id="L141">                value = CODE_CHARS.indexOf(c);</span>
            }

<span class="nc" id="L144">            sum += value * WEIGHTS[i];</span>
        }

        // 计算校验码
<span class="nc" id="L148">        int remainder = 11 - (sum % 11);</span>

<span class="nc bnc" id="L150" title="All 2 branches missed.">        if (remainder == 10) {</span>
<span class="nc" id="L151">            return &quot;X&quot;;</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">        } else if (remainder == 11) {</span>
<span class="nc" id="L153">            return &quot;0&quot;;</span>
        } else {
<span class="nc" id="L155">            return String.valueOf(remainder);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>