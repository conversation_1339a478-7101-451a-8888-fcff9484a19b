<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ZodiacGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">ZodiacGenerator</span></div><h1>ZodiacGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">491 of 491</td><td class="ctr2">0%</td><td class="bar">45 of 45</td><td class="ctr2">0%</td><td class="ctr1">35</td><td class="ctr2">35</td><td class="ctr1">78</td><td class="ctr2">78</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a7"><a href="ZodiacGenerator.java.html#L80" class="el_method">initializeZodiacInfo()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="193" alt="193"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="ZodiacGenerator.java.html#L151" class="el_method">getZodiacFromBirthDate(DataForgeContext, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="68" alt="68"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h0">17</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="ZodiacGenerator.java.html#L218" class="el_method">isDateInZodiacRange(int, int, ZodiacGenerator.ZodiacInfo)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="61" alt="61"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">14</td><td class="ctr2" id="g0">14</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="ZodiacGenerator.java.html#L120" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="57" alt="57"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="ZodiacGenerator.java.html#L232" class="el_method">formatZodiac(ZodiacGenerator.ZodiacSign, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="33" alt="33"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="5" alt="5"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="ZodiacGenerator.java.html#L188" class="el_method">getSpecifiedZodiac(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="30" alt="30"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="ZodiacGenerator.java.html#L203" class="el_method">calculateZodiacSign(LocalDate)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="29" alt="29"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="ZodiacGenerator.java.html#L27" class="el_method">static {...}</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a10"><a href="ZodiacGenerator.java.html#L25" class="el_method">ZodiacGenerator()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="ZodiacGenerator.java.html#L108" class="el_method">getType()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="ZodiacGenerator.java.html#L113" class="el_method">getConfigClass()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>