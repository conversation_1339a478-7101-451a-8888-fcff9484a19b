<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EducationGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">EducationGenerator</span></div><h1>EducationGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">561 of 561</td><td class="ctr2">0%</td><td class="bar">51 of 51</td><td class="ctr2">0%</td><td class="ctr1">40</td><td class="ctr2">40</td><td class="ctr1">119</td><td class="ctr2">119</td><td class="ctr1">14</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a2"><a href="EducationGenerator.java.html#L89" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="70" alt="70"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="EducationGenerator.java.html#L141" class="el_method">filterByAge(List, int)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="114" height="10" title="67" alt="67"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="EducationGenerator.java.html#L207" class="el_method">selectWithCustomWeights(List, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="64" alt="64"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a12"><a href="EducationGenerator.java.html#L186" class="el_method">selectWithRealisticWeights(List)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="60" alt="60"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="EducationGenerator.java.html#L231" class="el_method">parseWeights(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="99" height="10" title="58" alt="58"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="EducationGenerator.java.html#L118" class="el_method">getAvailableEducations(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="56" alt="56"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h5">11</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="EducationGenerator.java.html#L55" class="el_method">initializeRealisticWeights()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="43" alt="43"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="EducationGenerator.java.html#L66" class="el_method">initializeAgeMapping()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="43" alt="43"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="EducationGenerator.java.html#L44" class="el_method">initializeEducationLevels()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="EducationGenerator.java.html#L167" class="el_method">selectEducation(List, String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="34" alt="34"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="7" alt="7"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a13"><a href="EducationGenerator.java.html#L25" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="23" alt="23"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h6">9</td><td class="ctr2" id="i6">9</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="EducationGenerator.java.html#L23" class="el_method">EducationGenerator()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="EducationGenerator.java.html#L77" class="el_method">getType()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a4"><a href="EducationGenerator.java.html#L82" class="el_method">getConfigClass()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>