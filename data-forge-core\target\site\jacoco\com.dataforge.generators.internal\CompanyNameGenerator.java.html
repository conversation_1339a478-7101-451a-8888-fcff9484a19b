<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CompanyNameGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">CompanyNameGenerator.java</span></div><h1>CompanyNameGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 企业名称生成器
 * 
 * 支持的参数：
 * - industry: 行业类型 (IT|FINANCE|RETAIL|MANUFACTURING|EDUCATION|HEALTHCARE|ANY)
 * - type: 公司类型 (CO_LTD|GROUP|INSTITUTE|CORP|ANY)
 * - prefix_region: 是否添加地区前缀 (true|false)
 * - keywords_file: 自定义行业关键词文件路径
 * - suffix_file: 自定义公司类型后缀文件路径
 * - length_range: 公司名称长度范围 (如 &quot;6,20&quot;)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L25">public class CompanyNameGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(CompanyNameGenerator.class);</span>
<span class="nc" id="L28">    private static final Random random = new Random();</span>

    // 行业关键词
<span class="nc" id="L31">    private static final Map&lt;String, List&lt;String&gt;&gt; INDUSTRY_KEYWORDS = new HashMap&lt;&gt;();</span>

    // 公司类型后缀
<span class="nc" id="L34">    private static final Map&lt;String, List&lt;String&gt;&gt; COMPANY_TYPES = new HashMap&lt;&gt;();</span>

    // 地区前缀
<span class="nc" id="L37">    private static final List&lt;String&gt; REGION_PREFIXES = Arrays.asList(</span>
            &quot;北京&quot;, &quot;上海&quot;, &quot;广州&quot;, &quot;深圳&quot;, &quot;杭州&quot;, &quot;南京&quot;, &quot;苏州&quot;, &quot;成都&quot;, &quot;武汉&quot;, &quot;西安&quot;,
            &quot;天津&quot;, &quot;重庆&quot;, &quot;青岛&quot;, &quot;大连&quot;, &quot;宁波&quot;, &quot;厦门&quot;, &quot;长沙&quot;, &quot;郑州&quot;, &quot;济南&quot;, &quot;合肥&quot;,
            &quot;福州&quot;, &quot;昆明&quot;, &quot;南昌&quot;, &quot;贵阳&quot;, &quot;太原&quot;, &quot;石家庄&quot;, &quot;哈尔滨&quot;, &quot;长春&quot;, &quot;沈阳&quot;, &quot;兰州&quot;);

    // 通用词汇
<span class="nc" id="L43">    private static final List&lt;String&gt; COMMON_WORDS = Arrays.asList(</span>
            &quot;创新&quot;, &quot;智能&quot;, &quot;数字&quot;, &quot;云端&quot;, &quot;未来&quot;, &quot;新兴&quot;, &quot;优质&quot;, &quot;精品&quot;, &quot;卓越&quot;, &quot;领先&quot;,
            &quot;专业&quot;, &quot;高端&quot;, &quot;品质&quot;, &quot;服务&quot;, &quot;发展&quot;, &quot;建设&quot;, &quot;管理&quot;, &quot;咨询&quot;, &quot;投资&quot;, &quot;贸易&quot;,
            &quot;实业&quot;, &quot;集团&quot;, &quot;控股&quot;, &quot;产业&quot;, &quot;科技&quot;, &quot;信息&quot;, &quot;网络&quot;, &quot;系统&quot;, &quot;工程&quot;, &quot;设计&quot;);

    static {
<span class="nc" id="L49">        initializeIndustryKeywords();</span>
<span class="nc" id="L50">        initializeCompanyTypes();</span>
<span class="nc" id="L51">    }</span>

    private static void initializeIndustryKeywords() {
<span class="nc" id="L54">        INDUSTRY_KEYWORDS.put(&quot;IT&quot;, Arrays.asList(</span>
                &quot;科技&quot;, &quot;信息&quot;, &quot;网络&quot;, &quot;软件&quot;, &quot;数据&quot;, &quot;云计算&quot;, &quot;人工智能&quot;, &quot;大数据&quot;, &quot;物联网&quot;, &quot;区块链&quot;,
                &quot;互联网&quot;, &quot;电子&quot;, &quot;通信&quot;, &quot;计算机&quot;, &quot;系统&quot;, &quot;技术&quot;, &quot;创新&quot;, &quot;数字&quot;, &quot;智能&quot;, &quot;在线&quot;));

<span class="nc" id="L58">        INDUSTRY_KEYWORDS.put(&quot;FINANCE&quot;, Arrays.asList(</span>
                &quot;金融&quot;, &quot;投资&quot;, &quot;资本&quot;, &quot;基金&quot;, &quot;证券&quot;, &quot;银行&quot;, &quot;保险&quot;, &quot;信托&quot;, &quot;财富&quot;, &quot;资产&quot;,
                &quot;理财&quot;, &quot;融资&quot;, &quot;风控&quot;, &quot;支付&quot;, &quot;金服&quot;, &quot;普惠&quot;, &quot;小贷&quot;, &quot;担保&quot;, &quot;租赁&quot;, &quot;期货&quot;));

<span class="nc" id="L62">        INDUSTRY_KEYWORDS.put(&quot;RETAIL&quot;, Arrays.asList(</span>
                &quot;商贸&quot;, &quot;零售&quot;, &quot;批发&quot;, &quot;电商&quot;, &quot;购物&quot;, &quot;商城&quot;, &quot;超市&quot;, &quot;便利&quot;, &quot;连锁&quot;, &quot;品牌&quot;,
                &quot;时尚&quot;, &quot;服装&quot;, &quot;家居&quot;, &quot;美妆&quot;, &quot;食品&quot;, &quot;母婴&quot;, &quot;数码&quot;, &quot;家电&quot;, &quot;汽车&quot;, &quot;珠宝&quot;));

<span class="nc" id="L66">        INDUSTRY_KEYWORDS.put(&quot;MANUFACTURING&quot;, Arrays.asList(</span>
                &quot;制造&quot;, &quot;工业&quot;, &quot;机械&quot;, &quot;设备&quot;, &quot;生产&quot;, &quot;加工&quot;, &quot;材料&quot;, &quot;化工&quot;, &quot;钢铁&quot;, &quot;有色&quot;,
                &quot;纺织&quot;, &quot;服装&quot;, &quot;食品&quot;, &quot;医药&quot;, &quot;汽车&quot;, &quot;船舶&quot;, &quot;航空&quot;, &quot;电子&quot;, &quot;仪器&quot;, &quot;模具&quot;));

<span class="nc" id="L70">        INDUSTRY_KEYWORDS.put(&quot;EDUCATION&quot;, Arrays.asList(</span>
                &quot;教育&quot;, &quot;培训&quot;, &quot;学校&quot;, &quot;学院&quot;, &quot;大学&quot;, &quot;幼儿&quot;, &quot;职业&quot;, &quot;技能&quot;, &quot;语言&quot;, &quot;艺术&quot;,
                &quot;体育&quot;, &quot;音乐&quot;, &quot;美术&quot;, &quot;舞蹈&quot;, &quot;书法&quot;, &quot;围棋&quot;, &quot;编程&quot;, &quot;机器人&quot;, &quot;科学&quot;, &quot;实验&quot;));

<span class="nc" id="L74">        INDUSTRY_KEYWORDS.put(&quot;HEALTHCARE&quot;, Arrays.asList(</span>
                &quot;医疗&quot;, &quot;健康&quot;, &quot;医院&quot;, &quot;诊所&quot;, &quot;药业&quot;, &quot;生物&quot;, &quot;康复&quot;, &quot;养老&quot;, &quot;护理&quot;, &quot;美容&quot;,
                &quot;口腔&quot;, &quot;眼科&quot;, &quot;妇产&quot;, &quot;儿科&quot;, &quot;中医&quot;, &quot;针灸&quot;, &quot;推拿&quot;, &quot;理疗&quot;, &quot;体检&quot;, &quot;疫苗&quot;));
<span class="nc" id="L77">    }</span>

    private static void initializeCompanyTypes() {
<span class="nc" id="L80">        COMPANY_TYPES.put(&quot;CO_LTD&quot;, Arrays.asList(</span>
                &quot;有限公司&quot;, &quot;有限责任公司&quot;));

<span class="nc" id="L83">        COMPANY_TYPES.put(&quot;GROUP&quot;, Arrays.asList(</span>
                &quot;集团有限公司&quot;, &quot;控股集团有限公司&quot;, &quot;产业集团有限公司&quot;));

<span class="nc" id="L86">        COMPANY_TYPES.put(&quot;INSTITUTE&quot;, Arrays.asList(</span>
                &quot;研究院&quot;, &quot;技术研究院&quot;, &quot;科学研究院&quot;, &quot;工程技术研究院&quot;));

<span class="nc" id="L89">        COMPANY_TYPES.put(&quot;CORP&quot;, Arrays.asList(</span>
                &quot;股份有限公司&quot;, &quot;实业有限公司&quot;, &quot;投资有限公司&quot;, &quot;贸易有限公司&quot;));
<span class="nc" id="L91">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L95">        return &quot;company&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L100">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L107">            String industry = config.getParam(&quot;industry&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L108">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L109">            boolean prefixRegion = Boolean.parseBoolean(config.getParam(&quot;prefix_region&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L110">            String lengthRange = config.getParam(&quot;length_range&quot;, String.class, &quot;6,20&quot;);</span>

            // 解析长度范围
<span class="nc" id="L113">            String[] lengthParts = lengthRange.split(&quot;,&quot;);</span>
<span class="nc" id="L114">            int minLength = Integer.parseInt(lengthParts[0].trim());</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">            int maxLength = lengthParts.length &gt; 1 ? Integer.parseInt(lengthParts[1].trim()) : minLength;</span>

            // 加载自定义数据
<span class="nc" id="L118">            List&lt;String&gt; keywords = loadKeywords(config, industry);</span>
<span class="nc" id="L119">            List&lt;String&gt; suffixes = loadSuffixes(config, type);</span>

            // 生成公司名称
<span class="nc" id="L122">            StringBuilder companyName = new StringBuilder();</span>

            // 添加地区前缀
<span class="nc bnc" id="L125" title="All 4 branches missed.">            if (prefixRegion &amp;&amp; random.nextBoolean()) {</span>
<span class="nc" id="L126">                String region = REGION_PREFIXES.get(random.nextInt(REGION_PREFIXES.size()));</span>
<span class="nc" id="L127">                companyName.append(region);</span>
            }

            // 添加主体名称
<span class="nc" id="L131">            String mainName = generateMainName(keywords, minLength, maxLength, companyName.length());</span>
<span class="nc" id="L132">            companyName.append(mainName);</span>

            // 添加公司类型后缀
<span class="nc" id="L135">            String suffix = suffixes.get(random.nextInt(suffixes.size()));</span>
<span class="nc" id="L136">            companyName.append(suffix);</span>

<span class="nc" id="L138">            String result = companyName.toString();</span>

            // 长度控制
<span class="nc bnc" id="L141" title="All 2 branches missed.">            if (result.length() &gt; maxLength) {</span>
                // 如果太长，尝试去掉地区前缀
<span class="nc bnc" id="L143" title="All 4 branches missed.">                if (prefixRegion &amp;&amp; companyName.toString().startsWith(REGION_PREFIXES.get(0))) {</span>
<span class="nc" id="L144">                    result = mainName + suffix;</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">                    if (result.length() &gt; maxLength) {</span>
                        // 如果还是太长，截断主体名称
<span class="nc" id="L147">                        int maxMainLength = maxLength - suffix.length();</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">                        if (maxMainLength &gt; 2) {</span>
<span class="nc" id="L149">                            mainName = mainName.substring(0, Math.min(mainName.length(), maxMainLength));</span>
<span class="nc" id="L150">                            result = mainName + suffix;</span>
                        }
                    }
                }
            }

<span class="nc" id="L156">            logger.debug(&quot;Generated company name: {}&quot;, result);</span>
<span class="nc" id="L157">            return result;</span>

<span class="nc" id="L159">        } catch (Exception e) {</span>
<span class="nc" id="L160">            logger.error(&quot;Error generating company name&quot;, e);</span>
<span class="nc" id="L161">            return &quot;示例科技有限公司&quot;;</span>
        }
    }

    private List&lt;String&gt; loadKeywords(FieldConfig config, String industry) {
<span class="nc" id="L166">        String keywordsFile = config.getParam(&quot;keywords_file&quot;, String.class, null);</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">        if (keywordsFile != null) {</span>
            try {
<span class="nc" id="L169">                return DataLoader.loadDataFromFile(keywordsFile);</span>
<span class="nc" id="L170">            } catch (Exception e) {</span>
<span class="nc" id="L171">                logger.warn(&quot;Failed to load custom keywords file: {}&quot;, keywordsFile, e);</span>
            }
        }

        // 使用内置关键词
<span class="nc" id="L176">        List&lt;String&gt; keywords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(industry)) {</span>
            // 混合所有行业关键词
<span class="nc" id="L179">            INDUSTRY_KEYWORDS.values().forEach(keywords::addAll);</span>
<span class="nc" id="L180">            keywords.addAll(COMMON_WORDS);</span>
        } else {
<span class="nc" id="L182">            keywords.addAll(INDUSTRY_KEYWORDS.getOrDefault(industry, COMMON_WORDS));</span>
        }

<span class="nc" id="L185">        return keywords;</span>
    }

    private List&lt;String&gt; loadSuffixes(FieldConfig config, String type) {
<span class="nc" id="L189">        String suffixFile = config.getParam(&quot;suffix_file&quot;, String.class, null);</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">        if (suffixFile != null) {</span>
            try {
<span class="nc" id="L192">                return DataLoader.loadDataFromFile(suffixFile);</span>
<span class="nc" id="L193">            } catch (Exception e) {</span>
<span class="nc" id="L194">                logger.warn(&quot;Failed to load custom suffix file: {}&quot;, suffixFile, e);</span>
            }
        }

        // 使用内置后缀
<span class="nc" id="L199">        List&lt;String&gt; suffixes = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(type)) {</span>
<span class="nc" id="L201">            COMPANY_TYPES.values().forEach(suffixes::addAll);</span>
        } else {
<span class="nc" id="L203">            suffixes.addAll(COMPANY_TYPES.getOrDefault(type, COMPANY_TYPES.get(&quot;CO_LTD&quot;)));</span>
        }

<span class="nc" id="L206">        return suffixes;</span>
    }

    private String generateMainName(List&lt;String&gt; keywords, int minLength, int maxLength, int prefixLength) {
<span class="nc" id="L210">        StringBuilder mainName = new StringBuilder();</span>
<span class="nc" id="L211">        int targetLength = random.nextInt(Math.max(1, maxLength - prefixLength - 5))</span>
<span class="nc" id="L212">                + Math.max(2, minLength - prefixLength);</span>

        // 随机选择1-3个关键词组合
<span class="nc" id="L215">        int wordCount = random.nextInt(3) + 1;</span>
<span class="nc" id="L216">        Set&lt;String&gt; usedWords = new HashSet&lt;&gt;();</span>

<span class="nc bnc" id="L218" title="All 4 branches missed.">        for (int i = 0; i &lt; wordCount &amp;&amp; mainName.length() &lt; targetLength; i++) {</span>
<span class="nc" id="L219">            String word = keywords.get(random.nextInt(keywords.size()));</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">            if (!usedWords.contains(word)) {</span>
<span class="nc" id="L221">                usedWords.add(word);</span>
<span class="nc" id="L222">                mainName.append(word);</span>
            }
        }

        // 如果名称太短，添加通用词汇
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (mainName.length() &lt; 2) {</span>
<span class="nc" id="L228">            String commonWord = COMMON_WORDS.get(random.nextInt(COMMON_WORDS.size()));</span>
<span class="nc" id="L229">            mainName.append(commonWord);</span>
        }

<span class="nc" id="L232">        return mainName.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>