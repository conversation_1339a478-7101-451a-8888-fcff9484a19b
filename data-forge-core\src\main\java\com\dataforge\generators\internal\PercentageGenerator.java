package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 百分比生成器
 * 
 * <p>
 * 支持生成各种格式的百分比数据，包括进度百分比、统计百分比、
 * 比率百分比等，用于数据分析、报表生成、进度显示等场景。
 * 
 * <p>
 * 支持的参数：
 * <ul>
 * <li>min: 最小百分比值 默认: 0.0</li>
 * <li>max: 最大百分比值 默认: 100.0</li>
 * <li>precision: 小数位数 默认: 1</li>
 * <li>format: 输出格式 (SYMBOL|DECIMAL|FRACTION|RATIO|JSON) 默认: SYMBOL</li>
 * <li>symbol: 百分比符号 默认: %</li>
 * <li>locale: 本地化设置 默认: en_US</li>
 * <li>distribution: 分布类型 (UNIFORM|NORMAL|BETA|EXPONENTIAL) 默认: UNIFORM</li>
 * <li>mean: 正态分布的均值 默认: 50.0</li>
 * <li>stddev: 正态分布的标准差 默认: 15.0</li>
 * <li>alpha: Beta分布的α参数 默认: 2.0</li>
 * <li>beta: Beta分布的β参数 默认: 2.0</li>
 * <li>lambda: 指数分布的λ参数 默认: 0.1</li>
 * <li>rounding: 舍入模式 (UP|DOWN|HALF_UP|HALF_DOWN) 默认: HALF_UP</li>
 * <li>include_sign: 是否包含正负号 默认: false</li>
 * <li>allow_over_100: 是否允许超过100% 默认: false</li>
 * <li>grouping: 是否使用千分位分隔符 默认: false</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class PercentageGenerator extends BaseGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(PercentageGenerator.class);
    private static final SecureRandom random = new SecureRandom();
    
    // 输出格式枚举
    public enum OutputFormat {
        SYMBOL("符号格式"),
        DECIMAL("小数格式"),
        FRACTION("分数格式"),
        RATIO("比率格式"),
        JSON("JSON格式");
        
        private final String description;
        
        OutputFormat(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 分布类型枚举
    public enum DistributionType {
        UNIFORM("均匀分布"),
        NORMAL("正态分布"),
        BETA("Beta分布"),
        EXPONENTIAL("指数分布");
        
        private final String description;
        
        DistributionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Override
    public String getType() {
        return "percentage";
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取分布类型
            String distributionStr = getStringParam(config, "distribution", "UNIFORM");
            DistributionType distribution = parseDistributionType(distributionStr);
            
            // 获取输出格式
            String formatStr = getStringParam(config, "format", "SYMBOL");
            OutputFormat format = parseOutputFormat(formatStr);
            
            // 生成百分比值
            BigDecimal percentage = generatePercentage(distribution, config);
            
            // 格式化输出
            return formatPercentage(percentage, format, config);
            
        } catch (Exception e) {
            logger.error("Failed to generate percentage", e);
            // 返回一个默认的百分比作为fallback
            return "50.0%";
        }
    }

    /**
     * 解析分布类型
     */
    private DistributionType parseDistributionType(String distributionStr) {
        try {
            return DistributionType.valueOf(distributionStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid distribution type: {}, using UNIFORM as default", distributionStr);
            return DistributionType.UNIFORM;
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
            return OutputFormat.valueOf(formatStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid output format: {}, using SYMBOL as default", formatStr);
            return OutputFormat.SYMBOL;
        }
    }

    /**
     * 生成百分比值
     */
    private BigDecimal generatePercentage(DistributionType distribution, FieldConfig config) {
        // 获取范围参数
        double min = getDoubleParam(config, "min", 0.0);
        double max = getDoubleParam(config, "max", 100.0);
        boolean allowOver100 = getBooleanParam(config, "allow_over_100", false);
        
        // 如果不允许超过100%，调整最大值
        if (!allowOver100 && max > 100.0) {
            max = 100.0;
        }
        
        // 根据分布类型生成数值
        double value;
        switch (distribution) {
            case UNIFORM:
                value = generateUniform(min, max);
                break;
            case NORMAL:
                value = generateNormal(config, min, max);
                break;
            case BETA:
                value = generateBeta(config, min, max);
                break;
            case EXPONENTIAL:
                value = generateExponential(config, min, max);
                break;
            default:
                value = generateUniform(min, max);
                break;
        }
        
        // 应用精度和舍入
        int precision = getIntParam(config, "precision", 1);
        String roundingStr = getStringParam(config, "rounding", "HALF_UP");
        RoundingMode rounding = parseRoundingMode(roundingStr);
        
        return BigDecimal.valueOf(value).setScale(precision, rounding);
    }

    /**
     * 生成均匀分布随机数
     */
    private double generateUniform(double min, double max) {
        if (min >= max) {
            return min;
        }
        return min + random.nextDouble() * (max - min);
    }

    /**
     * 生成正态分布随机数
     */
    private double generateNormal(FieldConfig config, double min, double max) {
        double mean = getDoubleParam(config, "mean", 50.0);
        double stddev = getDoubleParam(config, "stddev", 15.0);
        
        double value;
        int attempts = 0;
        do {
            value = random.nextGaussian() * stddev + mean;
            attempts++;
        } while ((value < min || value > max) && attempts < 100);
        
        // 如果100次尝试后仍不在范围内，使用边界值
        if (value < min) value = min;
        if (value > max) value = max;
        
        return value;
    }

    /**
     * 生成Beta分布随机数
     */
    private double generateBeta(FieldConfig config, double min, double max) {
        double alpha = getDoubleParam(config, "alpha", 2.0);
        double beta = getDoubleParam(config, "beta", 2.0);
        
        // 使用简化的Beta分布生成算法
        double x = generateGamma(alpha);
        double y = generateGamma(beta);
        double betaValue = x / (x + y);
        
        // 将[0,1]范围的Beta分布值映射到[min,max]
        return min + betaValue * (max - min);
    }

    /**
     * 生成Gamma分布随机数（用于Beta分布）
     */
    private double generateGamma(double shape) {
        if (shape < 1.0) {
            // 对于shape < 1的情况，使用Ahrens-Dieter算法
            double c = (1.0 / shape);
            double d = ((shape < 0.07) ? (0.0078 * shape + 0.218) : (1.0 / Math.sqrt(2.0 * Math.PI * shape)));
            
            while (true) {
                double u = random.nextDouble();
                double v = random.nextDouble();
                double w = u * (1.0 - u);
                double y = Math.sqrt(d / w) * (u - 0.5);
                double x = c + y;
                if (x >= 0) {
                    double z = 64.0 * w * w * w * v * v;
                    if (z <= 1.0 - 2.0 * y * y / x) {
                        return x;
                    }
                    if (Math.log(z) <= 2.0 * (c * Math.log(x / c) - y)) {
                        return x;
                    }
                }
            }
        } else {
            // 对于shape >= 1的情况，使用Marsaglia and Tsang算法
            double d = shape - 1.0 / 3.0;
            double c = 1.0 / Math.sqrt(9.0 * d);
            
            while (true) {
                double x, v;
                do {
                    x = random.nextGaussian();
                    v = 1.0 + c * x;
                } while (v <= 0);
                
                v = v * v * v;
                double u = random.nextDouble();
                
                if (u < 1.0 - 0.0331 * x * x * x * x) {
                    return d * v;
                }
                
                if (Math.log(u) < 0.5 * x * x + d * (1.0 - v + Math.log(v))) {
                    return d * v;
                }
            }
        }
    }

    /**
     * 生成指数分布随机数
     */
    private double generateExponential(FieldConfig config, double min, double max) {
        double lambda = getDoubleParam(config, "lambda", 0.1);
        
        double value;
        do {
            value = -Math.log(1 - random.nextDouble()) / lambda + min;
        } while (value > max);
        
        return value;
    }

    /**
     * 解析舍入模式
     */
    private RoundingMode parseRoundingMode(String roundingStr) {
        try {
            return RoundingMode.valueOf(roundingStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid rounding mode: {}, using HALF_UP as default", roundingStr);
            return RoundingMode.HALF_UP;
        }
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(BigDecimal percentage, OutputFormat format, FieldConfig config) {
        switch (format) {
            case SYMBOL:
                return formatWithSymbol(percentage, config);
            case DECIMAL:
                return formatAsDecimal(percentage, config);
            case FRACTION:
                return formatAsFraction(percentage, config);
            case RATIO:
                return formatAsRatio(percentage, config);
            case JSON:
                return formatAsJson(percentage, config);
            default:
                return formatWithSymbol(percentage, config);
        }
    }

    /**
     * 格式化为符号格式
     */
    private String formatWithSymbol(BigDecimal percentage, FieldConfig config) {
        String symbol = getStringParam(config, "symbol", "%");
        boolean includeSign = getBooleanParam(config, "include_sign", false);
        boolean grouping = getBooleanParam(config, "grouping", false);
        String localeStr = getStringParam(config, "locale", "en_US");
        
        String valueStr;
        if (grouping) {
            Locale locale = parseLocale(localeStr);
            NumberFormat formatter = NumberFormat.getNumberInstance(locale);
            formatter.setMaximumFractionDigits(percentage.scale());
            formatter.setMinimumFractionDigits(percentage.scale());
            valueStr = formatter.format(percentage);
        } else {
            valueStr = percentage.toPlainString();
        }
        
        if (includeSign && percentage.compareTo(BigDecimal.ZERO) > 0) {
            valueStr = "+" + valueStr;
        }
        
        return valueStr + symbol;
    }

    /**
     * 格式化为小数格式
     */
    private String formatAsDecimal(BigDecimal percentage, FieldConfig config) {
        boolean includeSign = getBooleanParam(config, "include_sign", false);
        
        // 将百分比转换为小数（除以100）
        BigDecimal decimal = percentage.divide(BigDecimal.valueOf(100), percentage.scale() + 2, RoundingMode.HALF_UP);
        
        String valueStr = decimal.toPlainString();
        
        if (includeSign && decimal.compareTo(BigDecimal.ZERO) > 0) {
            valueStr = "+" + valueStr;
        }
        
        return valueStr;
    }

    /**
     * 格式化为分数格式
     */
    private String formatAsFraction(BigDecimal percentage, FieldConfig config) {
        // 将百分比转换为分数形式（分母为100）
        BigDecimal numerator = percentage;
        BigDecimal denominator = BigDecimal.valueOf(100);
        
        // 简化分数
        int gcd = findGCD(numerator.intValue(), denominator.intValue());
        if (gcd > 1) {
            numerator = numerator.divide(BigDecimal.valueOf(gcd), RoundingMode.HALF_UP);
            denominator = denominator.divide(BigDecimal.valueOf(gcd), RoundingMode.HALF_UP);
        }
        
        return numerator.intValue() + "/" + denominator.intValue();
    }

    /**
     * 计算最大公约数
     */
    private int findGCD(int a, int b) {
        if (b == 0) {
            return a;
        }
        return findGCD(b, a % b);
    }

    /**
     * 格式化为比率格式
     */
    private String formatAsRatio(BigDecimal percentage, FieldConfig config) {
        // 将百分比转换为比率形式（如 3:7 表示30%）
        BigDecimal ratio = percentage.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
        BigDecimal complement = BigDecimal.ONE.subtract(ratio);
        
        // 找到合适的整数比率
        int scale = 10; // 精度
        int ratioInt = ratio.multiply(BigDecimal.valueOf(scale)).intValue();
        int complementInt = complement.multiply(BigDecimal.valueOf(scale)).intValue();
        
        // 简化比率
        int gcd = findGCD(ratioInt, complementInt);
        if (gcd > 1) {
            ratioInt /= gcd;
            complementInt /= gcd;
        }
        
        return ratioInt + ":" + complementInt;
    }

    /**
     * 格式化为JSON格式
     */
    private String formatAsJson(BigDecimal percentage, FieldConfig config) {
        String symbol = getStringParam(config, "symbol", "%");
        BigDecimal decimal = percentage.divide(BigDecimal.valueOf(100), percentage.scale() + 2, RoundingMode.HALF_UP);
        
        StringBuilder json = new StringBuilder("{");
        json.append("\"percentage\":").append(percentage.toPlainString()).append(",");
        json.append("\"decimal\":").append(decimal.toPlainString()).append(",");
        json.append("\"symbol\":\"").append(symbol).append("\",");
        json.append("\"formatted\":\"").append(formatWithSymbol(percentage, config)).append("\"");
        json.append("}");
        
        return json.toString();
    }

    /**
     * 解析本地化设置
     */
    private Locale parseLocale(String localeStr) {
        try {
            return Locale.forLanguageTag(localeStr.replace("_", "-"));
        } catch (Exception e) {
            logger.warn("Invalid locale: {}, using English as default", localeStr);
            return Locale.ENGLISH;
        }
    }
}
