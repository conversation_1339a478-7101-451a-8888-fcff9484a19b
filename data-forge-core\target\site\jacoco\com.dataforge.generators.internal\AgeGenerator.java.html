<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AgeGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">AgeGenerator.java</span></div><h1>AgeGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 年龄生成器
 * 
 * 支持功能：
 * 1. 指定年龄范围生成
 * 2. 与身份证号的出生日期关联
 * 3. 支持精确年龄计算
 * 4. 支持年龄分布控制
 * 
 * 参数配置：
 * - min: 最小年龄（默认18）
 * - max: 最大年龄（默认60）
 * - precision: 年龄精度，1表示整数年龄，0.5表示可以有半岁（默认1）
 * - distribution: 年龄分布类型 UNIFORM|NORMAL（默认UNIFORM）
 * - link_birth_date: 是否关联出生日期（默认true）
 * 
 * 关联字段：
 * - birth_date: 从上下文中获取出生日期，精确计算年龄
 * - idcard: 从身份证号中提取出生日期
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L38">public class AgeGenerator implements DataGenerator&lt;Integer, FieldConfig&gt; {</span>

<span class="nc" id="L40">    private static final Logger log = LoggerFactory.getLogger(AgeGenerator.class);</span>

    private static final String TYPE = &quot;age&quot;;
    private static final int DEFAULT_MIN_AGE = 18;
    private static final int DEFAULT_MAX_AGE = 60;
    private static final double DEFAULT_PRECISION = 1.0;
    private static final String DEFAULT_DISTRIBUTION = &quot;UNIFORM&quot;;
    private static final boolean DEFAULT_LINK_BIRTH_DATE = true;

    // 上下文键名
    private static final String CONTEXT_BIRTH_DATE = &quot;birth_date&quot;;
    private static final String CONTEXT_ID_CARD = &quot;idcard&quot;;

    @Override
    public String getType() {
<span class="nc" id="L55">        return TYPE;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L60">        return FieldConfig.class;</span>
    }

    @Override
    public Integer generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L65">        Map&lt;String, Object&gt; params = config.getParams();</span>

        // 解析配置参数
<span class="nc" id="L68">        int minAge = getIntParam(params, &quot;min&quot;, DEFAULT_MIN_AGE);</span>
<span class="nc" id="L69">        int maxAge = getIntParam(params, &quot;max&quot;, DEFAULT_MAX_AGE);</span>
<span class="nc" id="L70">        double precision = getDoubleParam(params, &quot;precision&quot;, DEFAULT_PRECISION);</span>
<span class="nc" id="L71">        String distribution = getStringParam(params, &quot;distribution&quot;, DEFAULT_DISTRIBUTION);</span>
<span class="nc" id="L72">        boolean linkBirthDate = getBooleanParam(params, &quot;link_birth_date&quot;, DEFAULT_LINK_BIRTH_DATE);</span>

        // 参数校验
<span class="nc bnc" id="L75" title="All 6 branches missed.">        if (minAge &lt; 0 || maxAge &lt; 0 || minAge &gt; maxAge) {</span>
<span class="nc" id="L76">            log.warn(&quot;Invalid age range: min={}, max={}. Using defaults.&quot;, minAge, maxAge);</span>
<span class="nc" id="L77">            minAge = DEFAULT_MIN_AGE;</span>
<span class="nc" id="L78">            maxAge = DEFAULT_MAX_AGE;</span>
        }

        // 尝试从上下文获取出生日期
<span class="nc bnc" id="L82" title="All 2 branches missed.">        if (linkBirthDate) {</span>
<span class="nc" id="L83">            Integer ageFromContext = getAgeFromContext(context);</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">            if (ageFromContext != null) {</span>
<span class="nc" id="L85">                log.debug(&quot;Using age from context: {}&quot;, ageFromContext);</span>
<span class="nc" id="L86">                return ageFromContext;</span>
            }
        }

        // 生成随机年龄
<span class="nc" id="L91">        return generateRandomAge(minAge, maxAge, precision, distribution);</span>
    }

    /**
     * 从上下文中获取年龄
     */
    private Integer getAgeFromContext(DataForgeContext context) {
        // 1. 直接从上下文获取出生日期
<span class="nc" id="L99">        LocalDate birthDate = context.get(CONTEXT_BIRTH_DATE, LocalDate.class).orElse(null);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        if (birthDate != null) {</span>
<span class="nc" id="L101">            return calculateAge(birthDate);</span>
        }

        // 2. 从身份证号中提取出生日期
<span class="nc" id="L105">        String idCard = context.get(CONTEXT_ID_CARD, String.class).orElse(null);</span>
<span class="nc bnc" id="L106" title="All 4 branches missed.">        if (idCard != null &amp;&amp; idCard.length() &gt;= 14) {</span>
            try {
<span class="nc" id="L108">                String birthDateStr = idCard.substring(6, 14);</span>
<span class="nc" id="L109">                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;);</span>
<span class="nc" id="L110">                birthDate = LocalDate.parse(birthDateStr, formatter);</span>
<span class="nc" id="L111">                return calculateAge(birthDate);</span>
<span class="nc" id="L112">            } catch (Exception e) {</span>
<span class="nc" id="L113">                log.debug(&quot;Failed to extract birth date from ID card: {}&quot;, idCard, e);</span>
            }
        }

<span class="nc" id="L117">        return null;</span>
    }

    /**
     * 计算年龄
     */
    private Integer calculateAge(LocalDate birthDate) {
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (birthDate == null) {</span>
<span class="nc" id="L125">            return null;</span>
        }

<span class="nc" id="L128">        LocalDate now = LocalDate.now();</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">        if (birthDate.isAfter(now)) {</span>
<span class="nc" id="L130">            log.warn(&quot;Birth date is in the future: {}&quot;, birthDate);</span>
<span class="nc" id="L131">            return null;</span>
        }

<span class="nc" id="L134">        Period period = Period.between(birthDate, now);</span>
<span class="nc" id="L135">        return period.getYears();</span>
    }

    /**
     * 生成随机年龄
     */
    private Integer generateRandomAge(int minAge, int maxAge, double precision, String distribution) {
<span class="nc" id="L142">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (&quot;NORMAL&quot;.equalsIgnoreCase(distribution)) {</span>
            // 正态分布：均值为中位数，标准差为范围的1/6
<span class="nc" id="L146">            double mean = (minAge + maxAge) / 2.0;</span>
<span class="nc" id="L147">            double stdDev = (maxAge - minAge) / 6.0;</span>

            double age;
            do {
<span class="nc" id="L151">                age = random.nextGaussian() * stdDev + mean;</span>
<span class="nc bnc" id="L152" title="All 4 branches missed.">            } while (age &lt; minAge || age &gt; maxAge);</span>

<span class="nc" id="L154">            return (int) Math.round(age / precision) * (int) precision;</span>
        } else {
            // 均匀分布
<span class="nc bnc" id="L157" title="All 2 branches missed.">            if (precision == 1.0) {</span>
<span class="nc" id="L158">                return random.nextInt(minAge, maxAge + 1);</span>
            } else {
                // 支持小数精度
<span class="nc" id="L161">                double range = maxAge - minAge;</span>
<span class="nc" id="L162">                double randomValue = random.nextDouble() * range + minAge;</span>
<span class="nc" id="L163">                return (int) Math.round(randomValue / precision) * (int) precision;</span>
            }
        }
    }

    // 工具方法
    private int getIntParam(Map&lt;String, Object&gt; params, String key, int defaultValue) {
<span class="nc" id="L170">        Object value = params.get(key);</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L172">            return ((Number) value).intValue();</span>
        }
<span class="nc bnc" id="L174" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L176">                return Integer.parseInt((String) value);</span>
<span class="nc" id="L177">            } catch (NumberFormatException e) {</span>
<span class="nc" id="L178">                log.warn(&quot;Invalid integer parameter '{}': {}&quot;, key, value);</span>
            }
        }
<span class="nc" id="L181">        return defaultValue;</span>
    }

    private double getDoubleParam(Map&lt;String, Object&gt; params, String key, double defaultValue) {
<span class="nc" id="L185">        Object value = params.get(key);</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L187">            return ((Number) value).doubleValue();</span>
        }
<span class="nc bnc" id="L189" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L191">                return Double.parseDouble((String) value);</span>
<span class="nc" id="L192">            } catch (NumberFormatException e) {</span>
<span class="nc" id="L193">                log.warn(&quot;Invalid double parameter '{}': {}&quot;, key, value);</span>
            }
        }
<span class="nc" id="L196">        return defaultValue;</span>
    }

    private String getStringParam(Map&lt;String, Object&gt; params, String key, String defaultValue) {
<span class="nc" id="L200">        Object value = params.get(key);</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    private boolean getBooleanParam(Map&lt;String, Object&gt; params, String key, boolean defaultValue) {
<span class="nc" id="L205">        Object value = params.get(key);</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L207">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L209" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L210">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L212">        return defaultValue;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>