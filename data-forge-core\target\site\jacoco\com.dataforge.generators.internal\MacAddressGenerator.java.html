<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MacAddressGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">MacAddressGenerator.java</span></div><h1>MacAddressGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * MAC地址生成器
 * 
 * 支持的参数：
 * - format: 输出格式 (COLON|HYPHEN|DOT|NONE)
 * - case: 大小写 (UPPER|LOWER|MIXED)
 * - vendor: 厂商OUI (如 &quot;VMWARE&quot;, &quot;INTEL&quot;)
 * - type: 地址类型 (UNICAST|MULTICAST|BROADCAST|ANY)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L22">public class MacAddressGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(MacAddressGenerator.class);</span>
<span class="nc" id="L25">    private static final Random random = new Random();</span>

    // 常见厂商OUI（前3字节）
<span class="nc" id="L28">    private static final Map&lt;String, String&gt; VENDOR_OUIS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L31">        initializeVendorOuis();</span>
<span class="nc" id="L32">    }</span>

    private static void initializeVendorOuis() {
<span class="nc" id="L35">        VENDOR_OUIS.put(&quot;INTEL&quot;, &quot;00:15:17&quot;);</span>
<span class="nc" id="L36">        VENDOR_OUIS.put(&quot;CISCO&quot;, &quot;00:1B:0D&quot;);</span>
<span class="nc" id="L37">        VENDOR_OUIS.put(&quot;APPLE&quot;, &quot;00:1B:63&quot;);</span>
<span class="nc" id="L38">        VENDOR_OUIS.put(&quot;DELL&quot;, &quot;00:14:22&quot;);</span>
<span class="nc" id="L39">        VENDOR_OUIS.put(&quot;HP&quot;, &quot;00:1F:29&quot;);</span>
<span class="nc" id="L40">        VENDOR_OUIS.put(&quot;VMWARE&quot;, &quot;00:50:56&quot;);</span>
<span class="nc" id="L41">        VENDOR_OUIS.put(&quot;MICROSOFT&quot;, &quot;00:15:5D&quot;);</span>
<span class="nc" id="L42">        VENDOR_OUIS.put(&quot;BROADCOM&quot;, &quot;00:10:18&quot;);</span>
<span class="nc" id="L43">        VENDOR_OUIS.put(&quot;REALTEK&quot;, &quot;00:E0:4C&quot;);</span>
<span class="nc" id="L44">        VENDOR_OUIS.put(&quot;QUALCOMM&quot;, &quot;00:03:7F&quot;);</span>
<span class="nc" id="L45">        VENDOR_OUIS.put(&quot;SAMSUNG&quot;, &quot;00:16:32&quot;);</span>
<span class="nc" id="L46">        VENDOR_OUIS.put(&quot;HUAWEI&quot;, &quot;00:E0:FC&quot;);</span>
<span class="nc" id="L47">        VENDOR_OUIS.put(&quot;XIAOMI&quot;, &quot;34:CE:00&quot;);</span>
<span class="nc" id="L48">        VENDOR_OUIS.put(&quot;TPLINK&quot;, &quot;50:C7:BF&quot;);</span>
<span class="nc" id="L49">        VENDOR_OUIS.put(&quot;NETGEAR&quot;, &quot;20:4E:7F&quot;);</span>
<span class="nc" id="L50">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L54">        return &quot;mac&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L59">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L66">            String format = config.getParam(&quot;format&quot;, String.class, &quot;COLON&quot;);</span>
<span class="nc" id="L67">            String caseParam = config.getParam(&quot;case&quot;, String.class, &quot;LOWER&quot;);</span>
<span class="nc" id="L68">            String vendor = config.getParam(&quot;vendor&quot;, String.class, null);</span>
<span class="nc" id="L69">            String type = config.getParam(&quot;type&quot;, String.class, &quot;UNICAST&quot;);</span>

            // 生成MAC地址
<span class="nc" id="L72">            String macAddress = generateMacAddress(vendor, type);</span>

            // 应用格式和大小写
<span class="nc" id="L75">            macAddress = applyFormat(macAddress, format);</span>
<span class="nc" id="L76">            macAddress = applyCase(macAddress, caseParam);</span>

            // 将MAC信息存入上下文
<span class="nc" id="L79">            context.put(&quot;mac_address&quot;, macAddress);</span>
<span class="nc" id="L80">            context.put(&quot;mac_vendor&quot;, extractVendor(macAddress));</span>

<span class="nc" id="L82">            logger.debug(&quot;Generated MAC address: {}&quot;, macAddress);</span>
<span class="nc" id="L83">            return macAddress;</span>

<span class="nc" id="L85">        } catch (Exception e) {</span>
<span class="nc" id="L86">            logger.error(&quot;Error generating MAC address&quot;, e);</span>
<span class="nc" id="L87">            return &quot;00:50:56:12:34:56&quot;;</span>
        }
    }

    private String generateMacAddress(String vendor, String type) {
<span class="nc" id="L92">        byte[] macBytes = new byte[6];</span>

        // 处理厂商OUI
<span class="nc bnc" id="L95" title="All 4 branches missed.">        if (vendor != null &amp;&amp; !vendor.isEmpty()) {</span>
<span class="nc" id="L96">            String oui = getVendorOui(vendor);</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">            if (oui != null) {</span>
<span class="nc" id="L98">                String[] ouiParts = oui.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L99" title="All 4 branches missed.">                for (int i = 0; i &lt; 3 &amp;&amp; i &lt; ouiParts.length; i++) {</span>
<span class="nc" id="L100">                    macBytes[i] = (byte) Integer.parseInt(ouiParts[i], 16);</span>
                }
<span class="nc" id="L102">            } else {</span>
<span class="nc" id="L103">                generateRandomOui(macBytes);</span>
            }
<span class="nc" id="L105">        } else {</span>
<span class="nc" id="L106">            generateRandomOui(macBytes);</span>
        }

        // 设置地址类型标志位
<span class="nc" id="L110">        setAddressTypeFlags(macBytes, type);</span>

        // 生成后3字节（设备标识符）
<span class="nc bnc" id="L113" title="All 2 branches missed.">        for (int i = 3; i &lt; 6; i++) {</span>
<span class="nc" id="L114">            macBytes[i] = (byte) random.nextInt(256);</span>
        }

        // 转换为十六进制字符串
<span class="nc" id="L118">        StringBuilder mac = new StringBuilder();</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">        for (int i = 0; i &lt; macBytes.length; i++) {</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L121">                mac.append(&quot;:&quot;);</span>
            }
<span class="nc" id="L123">            mac.append(String.format(&quot;%02x&quot;, macBytes[i] &amp; 0xFF));</span>
        }

<span class="nc" id="L126">        return mac.toString();</span>
    }

    private String getVendorOui(String vendor) {
<span class="nc" id="L130">        return VENDOR_OUIS.get(vendor.toUpperCase());</span>
    }

    private void generateRandomOui(byte[] macBytes) {
        // 生成随机的前3字节
<span class="nc bnc" id="L135" title="All 2 branches missed.">        for (int i = 0; i &lt; 3; i++) {</span>
<span class="nc" id="L136">            macBytes[i] = (byte) random.nextInt(256);</span>
        }
<span class="nc" id="L138">    }</span>

    private void setAddressTypeFlags(byte[] macBytes, String type) {
        // MAC地址的第一个字节的最低位是组播位（I/G位）
        // 第二低位是本地管理位（U/L位）

<span class="nc" id="L144">        byte firstByte = macBytes[0];</span>

        // 设置组播位
<span class="nc bnc" id="L147" title="All 3 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;MULTICAST&quot;:
<span class="nc" id="L149">                firstByte |= 0x01; // 设置组播位</span>
<span class="nc" id="L150">                break;</span>
            case &quot;BROADCAST&quot;:
                // 广播地址是特殊的组播地址
<span class="nc" id="L153">                Arrays.fill(macBytes, (byte) 0xFF);</span>
<span class="nc" id="L154">                return;</span>
            case &quot;UNICAST&quot;:
            default:
<span class="nc" id="L157">                firstByte &amp;= 0xFE; // 清除组播位</span>
                break;
        }

<span class="nc" id="L161">        macBytes[0] = firstByte;</span>
<span class="nc" id="L162">    }</span>

    private String applyFormat(String macAddress, String format) {
        // 移除所有分隔符
<span class="nc" id="L166">        String cleanMac = macAddress.replace(&quot;:&quot;, &quot;&quot;).replace(&quot;-&quot;, &quot;&quot;).replace(&quot;.&quot;, &quot;&quot;);</span>

<span class="nc bnc" id="L168" title="All 5 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;COLON&quot;:
<span class="nc" id="L170">                return formatWithSeparator(cleanMac, &quot;:&quot;, 2);</span>

            case &quot;HYPHEN&quot;:
<span class="nc" id="L173">                return formatWithSeparator(cleanMac, &quot;-&quot;, 2);</span>

            case &quot;DOT&quot;:
<span class="nc" id="L176">                return formatWithSeparator(cleanMac, &quot;.&quot;, 4);</span>

            case &quot;NONE&quot;:
<span class="nc" id="L179">                return cleanMac;</span>

            default:
<span class="nc" id="L182">                logger.warn(&quot;Unknown MAC format: {}. Using COLON format.&quot;, format);</span>
<span class="nc" id="L183">                return formatWithSeparator(cleanMac, &quot;:&quot;, 2);</span>
        }
    }

    private String formatWithSeparator(String cleanMac, String separator, int groupSize) {
<span class="nc" id="L188">        StringBuilder formatted = new StringBuilder();</span>

<span class="nc bnc" id="L190" title="All 2 branches missed.">        for (int i = 0; i &lt; cleanMac.length(); i += groupSize) {</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">            if (i &gt; 0) {</span>
<span class="nc" id="L192">                formatted.append(separator);</span>
            }

<span class="nc" id="L195">            int endIndex = Math.min(i + groupSize, cleanMac.length());</span>
<span class="nc" id="L196">            formatted.append(cleanMac.substring(i, endIndex));</span>
        }

<span class="nc" id="L199">        return formatted.toString();</span>
    }

    private String applyCase(String macAddress, String caseParam) {
<span class="nc bnc" id="L203" title="All 4 branches missed.">        switch (caseParam.toUpperCase()) {</span>
            case &quot;UPPER&quot;:
<span class="nc" id="L205">                return macAddress.toUpperCase();</span>

            case &quot;LOWER&quot;:
<span class="nc" id="L208">                return macAddress.toLowerCase();</span>

            case &quot;MIXED&quot;:
<span class="nc" id="L211">                StringBuilder mixed = new StringBuilder();</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">                for (int i = 0; i &lt; macAddress.length(); i++) {</span>
<span class="nc" id="L213">                    char c = macAddress.charAt(i);</span>
<span class="nc bnc" id="L214" title="All 2 branches missed.">                    if (Character.isLetter(c)) {</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">                        mixed.append(random.nextBoolean() ? Character.toUpperCase(c) : Character.toLowerCase(c));</span>
                    } else {
<span class="nc" id="L217">                        mixed.append(c);</span>
                    }
                }
<span class="nc" id="L220">                return mixed.toString();</span>

            default:
<span class="nc" id="L223">                logger.warn(&quot;Unknown MAC case: {}. Using LOWER case.&quot;, caseParam);</span>
<span class="nc" id="L224">                return macAddress.toLowerCase();</span>
        }
    }

    private String extractVendor(String macAddress) {
        // 提取前3字节作为OUI
<span class="nc" id="L230">        String cleanMac = macAddress.replace(&quot;:&quot;, &quot;&quot;).replace(&quot;-&quot;, &quot;&quot;).replace(&quot;.&quot;, &quot;&quot;);</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">        if (cleanMac.length() &gt;= 6) {</span>
<span class="nc" id="L232">            String oui = cleanMac.substring(0, 6);</span>
<span class="nc" id="L233">            oui = oui.substring(0, 2) + &quot;:&quot; + oui.substring(2, 4) + &quot;:&quot; + oui.substring(4, 6);</span>

            // 查找对应的厂商
<span class="nc bnc" id="L236" title="All 2 branches missed.">            for (Map.Entry&lt;String, String&gt; entry : VENDOR_OUIS.entrySet()) {</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">                if (entry.getValue().equalsIgnoreCase(oui)) {</span>
<span class="nc" id="L238">                    return entry.getKey();</span>
                }
<span class="nc" id="L240">            }</span>
        }

<span class="nc" id="L243">        return &quot;UNKNOWN&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>