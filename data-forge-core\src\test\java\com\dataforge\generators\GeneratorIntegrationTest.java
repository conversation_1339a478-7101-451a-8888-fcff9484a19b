package com.dataforge.generators;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.internal.*;
import com.dataforge.model.FieldConfig;
import com.dataforge.validation.IdCardValidator;
import com.dataforge.validation.LuhnValidator;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据生成器集成测试。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class GeneratorIntegrationTest {

    private DataForgeContext context;
    private LuhnValidator luhnValidator;
    private IdCardValidator idCardValidator;

    @BeforeEach
    void setUp() {
        context = new DataForgeContext();
        luhnValidator = new LuhnValidator();
        idCardValidator = new IdCardValidator();
    }

    @Test
    void testUuidGenerator() {
        UuidGenerator generator = new UuidGenerator();

        String uuid = generator.generate(createConfig("type", "UUID4"), context);

        assertNotNull(uuid);
        assertTrue(uuid.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
        assertEquals("uuid", generator.getType());
    }

    @Test
    void testNameGenerator() {
        NameGenerator generator = new NameGenerator();

        // 测试中文姓名
        String chineseName = generator.generate(createConfig("type", "CN"), context);
        assertNotNull(chineseName);
        assertTrue(chineseName.length() >= 2);

        // 测试英文姓名
        String englishName = generator.generate(createConfig("type", "EN"), context);
        assertNotNull(englishName);
        assertTrue(englishName.contains(" "));

        assertEquals("name", generator.getType());
    }

    @Test
    void testPhoneGenerator() {
        PhoneGenerator generator = new PhoneGenerator();

        String phone = generator.generate(createConfig("region", "CN"), context);

        assertNotNull(phone);
        assertEquals(11, phone.length());
        assertTrue(phone.matches("\\d{11}"));
        assertEquals("phone", generator.getType());
    }

    @Test
    void testBankCardGenerator() {
        BankCardGenerator generator = new BankCardGenerator();
        // 注入依赖
        try {
            java.lang.reflect.Field field = BankCardGenerator.class.getDeclaredField("luhnValidator");
            field.setAccessible(true);
            field.set(generator, luhnValidator);
        } catch (Exception e) {
            fail("Failed to inject LuhnValidator: " + e.getMessage());
        }

        String bankCard = generator.generate(createConfig("type", "DEBIT"), context);

        assertNotNull(bankCard);
        assertTrue(bankCard.length() >= 13 && bankCard.length() <= 19);
        assertTrue(bankCard.matches("\\d+"));
        assertTrue(luhnValidator.isValid(bankCard));
        assertEquals("bankcard", generator.getType());
    }

    @Test
    void testIdCardGenerator() {
        IdCardGenerator generator = new IdCardGenerator();
        // 注入依赖
        try {
            java.lang.reflect.Field field = IdCardGenerator.class.getDeclaredField("idCardValidator");
            field.setAccessible(true);
            field.set(generator, idCardValidator);
        } catch (Exception e) {
            fail("Failed to inject IdCardValidator: " + e.getMessage());
        }

        String idCard = generator.generate(createConfig("region", "110101"), context);

        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        assertTrue(idCardValidator.isValid(idCard));
        assertEquals("idcard", generator.getType());

        // 验证上下文中是否有相关信息
        assertTrue(context.get("age", Integer.class).isPresent());
        assertTrue(context.get("gender", String.class).isPresent());
    }

    @Test
    void testEmailGenerator() {
        EmailGenerator generator = new EmailGenerator();

        String email = generator.generate(createConfig("domains", "test.com"), context);

        assertNotNull(email);
        assertTrue(email.contains("@"));
        assertTrue(email.endsWith("test.com"));
        assertEquals("email", generator.getType());
    }

    @Test
    void testDataCorrelation() {
        // 测试数据关联性：先生成姓名，再生成邮箱
        NameGenerator nameGenerator = new NameGenerator();
        EmailGenerator emailGenerator = new EmailGenerator();

        // 生成中文姓名
        String name = nameGenerator.generate(createConfig("type", "CN"), context);
        context.put("name", name);

        // 生成关联的邮箱
        Map<String, Object> emailParams = new HashMap<>();
        emailParams.put("prefix_name", true);
        emailParams.put("domains", "test.com");
        FieldConfig emailConfig = createConfig(emailParams);

        String email = emailGenerator.generate(emailConfig, context);

        assertNotNull(email);
        assertTrue(email.contains("@test.com"));

        // 邮箱用户名应该包含姓名相关信息
        String username = email.split("@")[0];
        assertNotNull(username);
        assertTrue(username.length() > 0);
    }

    /**
     * 创建测试用的字段配置。
     */
    private FieldConfig createConfig(String key, Object value) {
        Map<String, Object> params = new HashMap<>();
        params.put(key, value);
        return createConfig(params);
    }

    /**
     * 创建测试用的字段配置。
     */
    private FieldConfig createConfig(Map<String, Object> params) {
        return new TestFieldConfig("test", "test", params);
    }

    /**
     * 测试用的FieldConfig实现类。
     */
    private static class TestFieldConfig extends FieldConfig {
        public TestFieldConfig(String name, String type, Map<String, Object> params) {
            super(name, type);
            setParams(params);
        }
    }
}