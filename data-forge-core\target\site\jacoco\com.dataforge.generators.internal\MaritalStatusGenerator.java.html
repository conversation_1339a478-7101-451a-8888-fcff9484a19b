<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MaritalStatusGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">MaritalStatusGenerator.java</span></div><h1>MaritalStatusGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 婚姻状况生成器
 * 
 * 支持的参数：
 * - status: 指定状态 (SINGLE|MARRIED|DIVORCED|WIDOWED|ANY)
 * - married_ratio: 已婚占比 (0.0-1.0)
 * - divorced_ratio: 离异占比 (0.0-1.0)
 * - widowed_ratio: 丧偶占比 (0.0-1.0)
 * - age_related: 是否与年龄关联 (true|false)
 * - format: 输出格式 (CHINESE|ENGLISH|CODE)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L24">public class MaritalStatusGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(MaritalStatusGenerator.class);</span>
<span class="nc" id="L27">    private static final Random random = new Random();</span>

    // 婚姻状况枚举
<span class="nc" id="L30">    private enum MaritalStatus {</span>
<span class="nc" id="L31">        SINGLE, // 未婚</span>
<span class="nc" id="L32">        MARRIED, // 已婚</span>
<span class="nc" id="L33">        DIVORCED, // 离异</span>
<span class="nc" id="L34">        WIDOWED // 丧偶</span>
    }

    // 中文输出映射
<span class="nc" id="L38">    private static final Map&lt;MaritalStatus, String&gt; CHINESE_NAMES = new HashMap&lt;&gt;();</span>

    // 英文输出映射
<span class="nc" id="L41">    private static final Map&lt;MaritalStatus, String&gt; ENGLISH_NAMES = new HashMap&lt;&gt;();</span>

    // 代码输出映射
<span class="nc" id="L44">    private static final Map&lt;MaritalStatus, String&gt; CODE_NAMES = new HashMap&lt;&gt;();</span>

    // 现实分布权重（基于中国人口统计）
<span class="nc" id="L47">    private static final Map&lt;MaritalStatus, Double&gt; REALISTIC_WEIGHTS = new HashMap&lt;&gt;();</span>

    // 年龄与婚姻状况的合理性映射
<span class="nc" id="L50">    private static final Map&lt;MaritalStatus, Integer&gt; MIN_AGE_FOR_STATUS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L53">        initializeMappings();</span>
<span class="nc" id="L54">        initializeRealisticWeights();</span>
<span class="nc" id="L55">        initializeAgeMapping();</span>
<span class="nc" id="L56">    }</span>

    private static void initializeMappings() {
        // 中文映射
<span class="nc" id="L60">        CHINESE_NAMES.put(MaritalStatus.SINGLE, &quot;未婚&quot;);</span>
<span class="nc" id="L61">        CHINESE_NAMES.put(MaritalStatus.MARRIED, &quot;已婚&quot;);</span>
<span class="nc" id="L62">        CHINESE_NAMES.put(MaritalStatus.DIVORCED, &quot;离异&quot;);</span>
<span class="nc" id="L63">        CHINESE_NAMES.put(MaritalStatus.WIDOWED, &quot;丧偶&quot;);</span>

        // 英文映射
<span class="nc" id="L66">        ENGLISH_NAMES.put(MaritalStatus.SINGLE, &quot;Single&quot;);</span>
<span class="nc" id="L67">        ENGLISH_NAMES.put(MaritalStatus.MARRIED, &quot;Married&quot;);</span>
<span class="nc" id="L68">        ENGLISH_NAMES.put(MaritalStatus.DIVORCED, &quot;Divorced&quot;);</span>
<span class="nc" id="L69">        ENGLISH_NAMES.put(MaritalStatus.WIDOWED, &quot;Widowed&quot;);</span>

        // 代码映射
<span class="nc" id="L72">        CODE_NAMES.put(MaritalStatus.SINGLE, &quot;S&quot;);</span>
<span class="nc" id="L73">        CODE_NAMES.put(MaritalStatus.MARRIED, &quot;M&quot;);</span>
<span class="nc" id="L74">        CODE_NAMES.put(MaritalStatus.DIVORCED, &quot;D&quot;);</span>
<span class="nc" id="L75">        CODE_NAMES.put(MaritalStatus.WIDOWED, &quot;W&quot;);</span>
<span class="nc" id="L76">    }</span>

    private static void initializeRealisticWeights() {
        // 基于中国成年人口婚姻状况的近似分布
<span class="nc" id="L80">        REALISTIC_WEIGHTS.put(MaritalStatus.SINGLE, 0.25); // 25% 未婚</span>
<span class="nc" id="L81">        REALISTIC_WEIGHTS.put(MaritalStatus.MARRIED, 0.65); // 65% 已婚</span>
<span class="nc" id="L82">        REALISTIC_WEIGHTS.put(MaritalStatus.DIVORCED, 0.08); // 8% 离异</span>
<span class="nc" id="L83">        REALISTIC_WEIGHTS.put(MaritalStatus.WIDOWED, 0.02); // 2% 丧偶</span>
<span class="nc" id="L84">    }</span>

    private static void initializeAgeMapping() {
        // 各婚姻状况的最小合理年龄
<span class="nc" id="L88">        MIN_AGE_FOR_STATUS.put(MaritalStatus.SINGLE, 16); // 16岁以上可以未婚</span>
<span class="nc" id="L89">        MIN_AGE_FOR_STATUS.put(MaritalStatus.MARRIED, 18); // 18岁以上可以结婚</span>
<span class="nc" id="L90">        MIN_AGE_FOR_STATUS.put(MaritalStatus.DIVORCED, 20); // 20岁以上可能离异</span>
<span class="nc" id="L91">        MIN_AGE_FOR_STATUS.put(MaritalStatus.WIDOWED, 25); // 25岁以上可能丧偶</span>
<span class="nc" id="L92">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L96">        return &quot;marital&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L101">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L108">            String status = config.getParam(&quot;status&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L109">            double marriedRatio = Double.parseDouble(config.getParam(&quot;married_ratio&quot;, String.class, &quot;0.65&quot;));</span>
<span class="nc" id="L110">            double divorcedRatio = Double.parseDouble(config.getParam(&quot;divorced_ratio&quot;, String.class, &quot;0.08&quot;));</span>
<span class="nc" id="L111">            double widowedRatio = Double.parseDouble(config.getParam(&quot;widowed_ratio&quot;, String.class, &quot;0.02&quot;));</span>
<span class="nc" id="L112">            boolean ageRelated = Boolean.parseBoolean(config.getParam(&quot;age_related&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L113">            String format = config.getParam(&quot;format&quot;, String.class, &quot;CHINESE&quot;);</span>

            // 获取可选的婚姻状况列表
<span class="nc" id="L116">            List&lt;MaritalStatus&gt; availableStatuses = getAvailableStatuses(status);</span>

            // 如果启用年龄关联，根据年龄过滤婚姻状况
<span class="nc bnc" id="L119" title="All 2 branches missed.">            if (ageRelated) {</span>
<span class="nc" id="L120">                Integer age = context.get(&quot;age&quot;, Integer.class).orElse(null);</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">                if (age != null) {</span>
<span class="nc" id="L122">                    availableStatuses = filterByAge(availableStatuses, age);</span>
                }
            }

            // 根据权重选择婚姻状况
<span class="nc" id="L127">            MaritalStatus maritalStatus = selectMaritalStatus(availableStatuses, marriedRatio, divorcedRatio,</span>
                    widowedRatio);

            // 将婚姻状况信息存入上下文
<span class="nc" id="L131">            context.put(&quot;marital_status&quot;, maritalStatus.name());</span>

            // 格式化输出
<span class="nc" id="L134">            String result = formatMaritalStatus(maritalStatus, format);</span>

<span class="nc" id="L136">            logger.debug(&quot;Generated marital status: {}&quot;, result);</span>
<span class="nc" id="L137">            return result;</span>

<span class="nc" id="L139">        } catch (Exception e) {</span>
<span class="nc" id="L140">            logger.error(&quot;Error generating marital status&quot;, e);</span>
<span class="nc" id="L141">            return &quot;未婚&quot;;</span>
        }
    }

    private List&lt;MaritalStatus&gt; getAvailableStatuses(String status) {
<span class="nc" id="L146">        List&lt;MaritalStatus&gt; statuses = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L148" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(status)) {</span>
<span class="nc" id="L149">            statuses.addAll(Arrays.asList(MaritalStatus.values()));</span>
        } else {
            try {
<span class="nc" id="L152">                MaritalStatus specificStatus = MaritalStatus.valueOf(status);</span>
<span class="nc" id="L153">                statuses.add(specificStatus);</span>
<span class="nc" id="L154">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L155">                logger.warn(&quot;Unknown marital status: {}. Using all statuses.&quot;, status);</span>
<span class="nc" id="L156">                statuses.addAll(Arrays.asList(MaritalStatus.values()));</span>
<span class="nc" id="L157">            }</span>
        }

<span class="nc" id="L160">        return statuses;</span>
    }

    private List&lt;MaritalStatus&gt; filterByAge(List&lt;MaritalStatus&gt; statuses, int age) {
<span class="nc" id="L164">        List&lt;MaritalStatus&gt; filteredStatuses = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L166" title="All 2 branches missed.">        for (MaritalStatus status : statuses) {</span>
<span class="nc" id="L167">            Integer minAge = MIN_AGE_FOR_STATUS.get(status);</span>
<span class="nc bnc" id="L168" title="All 4 branches missed.">            if (minAge != null &amp;&amp; age &gt;= minAge) {</span>
<span class="nc" id="L169">                filteredStatuses.add(status);</span>
            }
<span class="nc" id="L171">        }</span>

        // 如果过滤后没有合适的状态，返回最基本的状态
<span class="nc bnc" id="L174" title="All 2 branches missed.">        if (filteredStatuses.isEmpty()) {</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">            if (age &gt;= 16) {</span>
<span class="nc" id="L176">                filteredStatuses.add(MaritalStatus.SINGLE);</span>
            }
        }

<span class="nc bnc" id="L180" title="All 2 branches missed.">        return filteredStatuses.isEmpty() ? Arrays.asList(MaritalStatus.SINGLE) : filteredStatuses;</span>
    }

    private MaritalStatus selectMaritalStatus(List&lt;MaritalStatus&gt; availableStatuses,
            double marriedRatio, double divorcedRatio, double widowedRatio) {

        // 如果只有一个选项，直接返回
<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (availableStatuses.size() == 1) {</span>
<span class="nc" id="L188">            return availableStatuses.get(0);</span>
        }

        // 构建权重映射
<span class="nc" id="L192">        Map&lt;MaritalStatus, Double&gt; weights = new HashMap&lt;&gt;();</span>
<span class="nc" id="L193">        double singleRatio = 1.0 - marriedRatio - divorcedRatio - widowedRatio;</span>
<span class="nc" id="L194">        singleRatio = Math.max(0.0, singleRatio); // 确保不为负数</span>

<span class="nc bnc" id="L196" title="All 2 branches missed.">        for (MaritalStatus status : availableStatuses) {</span>
<span class="nc bnc" id="L197" title="All 5 branches missed.">            switch (status) {</span>
                case SINGLE:
<span class="nc" id="L199">                    weights.put(status, singleRatio);</span>
<span class="nc" id="L200">                    break;</span>
                case MARRIED:
<span class="nc" id="L202">                    weights.put(status, marriedRatio);</span>
<span class="nc" id="L203">                    break;</span>
                case DIVORCED:
<span class="nc" id="L205">                    weights.put(status, divorcedRatio);</span>
<span class="nc" id="L206">                    break;</span>
                case WIDOWED:
<span class="nc" id="L208">                    weights.put(status, widowedRatio);</span>
                    break;
            }
<span class="nc" id="L211">        }</span>

        // 计算总权重
<span class="nc" id="L214">        double totalWeight = weights.values().stream().mapToDouble(Double::doubleValue).sum();</span>

<span class="nc bnc" id="L216" title="All 2 branches missed.">        if (totalWeight &lt;= 0) {</span>
            // 如果权重为0，使用现实分布
<span class="nc" id="L218">            return selectWithRealisticWeights(availableStatuses);</span>
        }

        // 随机选择
<span class="nc" id="L222">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L223">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L225" title="All 2 branches missed.">        for (MaritalStatus status : availableStatuses) {</span>
<span class="nc" id="L226">            currentWeight += weights.getOrDefault(status, 0.0);</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L228">                return status;</span>
            }
<span class="nc" id="L230">        }</span>

        // 默认返回第一个
<span class="nc" id="L233">        return availableStatuses.get(0);</span>
    }

    private MaritalStatus selectWithRealisticWeights(List&lt;MaritalStatus&gt; availableStatuses) {
        // 计算总权重
<span class="nc" id="L238">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">        for (MaritalStatus status : availableStatuses) {</span>
<span class="nc" id="L240">            totalWeight += REALISTIC_WEIGHTS.getOrDefault(status, 0.0);</span>
<span class="nc" id="L241">        }</span>

        // 随机选择
<span class="nc" id="L244">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L245">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L247" title="All 2 branches missed.">        for (MaritalStatus status : availableStatuses) {</span>
<span class="nc" id="L248">            currentWeight += REALISTIC_WEIGHTS.getOrDefault(status, 0.0);</span>
<span class="nc bnc" id="L249" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L250">                return status;</span>
            }
<span class="nc" id="L252">        }</span>

        // 默认返回第一个
<span class="nc" id="L255">        return availableStatuses.get(0);</span>
    }

    private String formatMaritalStatus(MaritalStatus status, String format) {
<span class="nc bnc" id="L259" title="All 4 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;CHINESE&quot;:
            case &quot;CN&quot;:
<span class="nc" id="L262">                return CHINESE_NAMES.get(status);</span>

            case &quot;ENGLISH&quot;:
            case &quot;EN&quot;:
<span class="nc" id="L266">                return ENGLISH_NAMES.get(status);</span>

            case &quot;CODE&quot;:
<span class="nc" id="L269">                return CODE_NAMES.get(status);</span>

            default:
<span class="nc" id="L272">                logger.warn(&quot;Unknown marital status format: {}. Using CHINESE format.&quot;, format);</span>
<span class="nc" id="L273">                return CHINESE_NAMES.get(status);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>