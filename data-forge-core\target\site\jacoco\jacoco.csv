GRO<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUC<PERSON>ON_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
DataForge Core,com.dataforge.util,DataLoader,345,0,40,0,86,0,31,0,11,0
DataForge Core,com.dataforge.service,DataForgeException,13,0,0,0,6,0,3,0,3,0
DataForge Core,com.dataforge.service,DataForgeService,541,0,46,0,123,0,37,0,14,0
DataForge Core,com.dataforge.core,DataForgeContext,221,23,28,0,45,7,27,2,13,2
DataForge Core,com.dataforge.core,GeneratorFactory,337,0,36,0,78,0,30,0,12,0
DataForge Core,com.dataforge.generators.internal,RandomNumberGenerator.OutputFormat,3,46,0,0,1,9,1,2,1,2
DataForge Core,com.dataforge.generators.internal,WebSocketGenerator.SubProtocol,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,UsccGenerator,499,0,23,0,63,0,23,0,10,0
DataForge Core,com.dataforge.generators.internal,BloodTypeGenerator,593,0,45,0,115,0,36,0,12,0
DataForge Core,com.dataforge.generators.internal,DomainGenerator,1404,0,86,0,136,0,62,0,18,0
DataForge Core,com.dataforge.generators.internal,MaritalStatusGenerator,549,0,43,0,120,0,37,0,13,0
DataForge Core,com.dataforge.generators.internal,VerificationCodeGenerator.CodeType,33,0,0,0,6,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,GeolocationGenerator.Region,3,79,0,0,1,16,1,6,1,6
DataForge Core,com.dataforge.generators.internal,LandlineGenerator,1006,0,58,0,206,0,49,0,19,0
DataForge Core,com.dataforge.generators.internal,MacAddressGenerator,523,0,46,0,107,0,40,0,14,0
DataForge Core,com.dataforge.generators.internal,DeviceIdGenerator,128,487,27,18,24,64,19,17,0,12
DataForge Core,com.dataforge.generators.internal,GeolocationGenerator,331,263,22,7,59,44,18,13,4,11
DataForge Core,com.dataforge.generators.internal,UsernameGenerator,1206,0,124,0,215,0,82,0,17,0
DataForge Core,com.dataforge.generators.internal,ReligionGenerator.ReligionInfo,15,0,0,0,6,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,IdCardGenerator.RegionInfo,15,0,0,0,6,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,LandlineGenerator.LandlineFormat,27,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,NameGenerator,826,0,98,0,141,0,73,0,23,0
DataForge Core,com.dataforge.generators.internal,ProxyGenerator.ProxyConfig,0,69,0,0,0,24,0,12,0,12
DataForge Core,com.dataforge.generators.internal,FaxGenerator.FaxFormat,21,0,0,0,4,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,ZodiacGenerator,491,0,45,0,78,0,35,0,11,0
DataForge Core,com.dataforge.generators.internal,CookieGenerator.OutputFormat,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,MimeTypeGenerator,1329,0,65,0,137,0,55,0,20,0
DataForge Core,com.dataforge.generators.internal,LicensePlateGenerator,910,0,65,0,133,0,54,0,20,0
DataForge Core,com.dataforge.generators.internal,ProxyGenerator,252,966,28,37,41,123,25,31,1,19
DataForge Core,com.dataforge.generators.internal,HttpHeaderGenerator,227,0,9,0,33,0,13,0,8,0
DataForge Core,com.dataforge.generators.internal,CookieGenerator.CookieInfo,0,57,0,0,0,20,0,10,0,10
DataForge Core,com.dataforge.generators.internal,ZodiacGenerator.ZodiacInfo,30,0,0,0,11,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,GenderGenerator.Gender,21,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,FaxGenerator,2832,0,41,0,104,0,36,0,15,0
DataForge Core,com.dataforge.generators.internal,UserAgentGenerator.BrowserType,0,45,0,0,0,2,0,1,0,1
DataForge Core,com.dataforge.generators.internal,OrganizationCodeGenerator,255,0,24,0,56,0,20,0,8,0
DataForge Core,com.dataforge.generators.internal,ReligionGenerator.Religion,63,0,0,0,11,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,CookieGenerator.ValueType,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,CookieGenerator.SameSite,0,21,0,0,0,2,0,1,0,1
DataForge Core,com.dataforge.generators.internal,EmailGenerator,1100,0,89,0,209,0,76,0,30,0
DataForge Core,com.dataforge.generators.internal,BaseGenerator,35,104,14,24,15,28,14,11,0,6
DataForge Core,com.dataforge.generators.internal,BloodTypeGenerator.BloodGroup,27,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,EducationGenerator,561,0,51,0,119,0,40,0,14,0
DataForge Core,com.dataforge.generators.internal,DeviceIdGenerator.DeviceIdType,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,AddressGenerator.AdministrativeDivision,33,0,0,0,12,0,6,0,6,0
DataForge Core,com.dataforge.generators.internal,PortGenerator.ServicePort,12,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,GeolocationGenerator.GeoLocation,0,21,0,0,0,8,0,4,0,4
DataForge Core,com.dataforge.generators.internal,RandomNumberGenerator,214,282,33,18,43,60,29,22,4,16
DataForge Core,com.dataforge.generators.internal,WebSocketGenerator.OutputFormat,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,DecimalGenerator.OutputFormat,3,46,0,0,1,9,1,2,1,2
DataForge Core,com.dataforge.generators.internal,BankCardGenerator.BinInfo,21,0,0,0,8,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,PasswordGenerator,865,0,75,0,142,0,53,0,14,0
DataForge Core,com.dataforge.generators.internal,ProxyGenerator.ReliabilityLevel,3,39,0,0,1,5,1,2,1,2
DataForge Core,com.dataforge.generators.internal,VerificationCodeGenerator.CodeTemplate,12,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,AgeGenerator,324,0,42,0,76,0,33,0,12,0
DataForge Core,com.dataforge.generators.internal,UrlGenerator,1243,0,80,0,141,0,61,0,17,0
DataForge Core,com.dataforge.generators.internal,SessionTokenGenerator,236,0,16,0,53,0,21,0,11,0
DataForge Core,com.dataforge.generators.internal,WebSocketGenerator.Protocol,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,IdCardGenerator,1226,0,114,0,245,0,86,0,28,0
DataForge Core,com.dataforge.generators.internal,IpAddressGenerator,645,0,69,0,117,0,54,0,16,0
DataForge Core,com.dataforge.generators.internal,AddressGenerator,1047,0,82,0,149,0,58,0,17,0
DataForge Core,com.dataforge.generators.internal,DecimalGenerator.DistributionType,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,ReligionGenerator,698,0,56,0,144,0,45,0,14,0
DataForge Core,com.dataforge.generators.internal,PasswordGenerator.ComplexityLevel,27,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,AddressGenerator.DetailLevel,39,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,ApiKeyGenerator,615,0,42,0,101,0,42,0,18,0
DataForge Core,com.dataforge.generators.internal,ProxyGenerator.OutputFormat,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,TimezoneGenerator.OutputFormat,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,UserAgentGenerator,88,620,11,39,27,87,11,32,0,15
DataForge Core,com.dataforge.generators.internal,GenderGenerator,287,0,47,0,80,0,41,0,12,0
DataForge Core,com.dataforge.generators.internal,MimeTypeGenerator.MimeCategory,51,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,RandomNumberGenerator.NumberType,3,46,0,0,1,9,1,2,1,2
DataForge Core,com.dataforge.generators.internal,ProxyGenerator.ProxyType,3,53,0,0,1,10,1,2,1,2
DataForge Core,com.dataforge.generators.internal,DecimalGenerator.DecimalType,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,RandomNumberGenerator.DistributionType,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,FilePathGenerator.OSType,21,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,OccupationGenerator,1535,0,32,0,112,0,25,0,9,0
DataForge Core,com.dataforge.generators.internal,ZodiacGenerator.ZodiacSign,75,0,0,0,13,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,TimezoneGenerator.Region,3,60,0,0,1,11,1,2,1,2
DataForge Core,com.dataforge.generators.internal,GeolocationGenerator.OutputFormat,3,39,0,0,1,8,1,2,1,2
DataForge Core,com.dataforge.generators.internal,FilePathGenerator.PathType,21,0,0,0,4,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,BloodTypeGenerator.RhFactor,15,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,LandlineGenerator.CountryInfo,15,0,0,0,6,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,UuidGenerator,123,0,13,0,33,0,16,0,9,0
DataForge Core,com.dataforge.generators.internal,EthnicityGenerator,883,0,54,0,138,0,41,0,13,0
DataForge Core,com.dataforge.generators.internal,UserAgentGenerator.OSType,0,39,0,0,0,2,0,1,0,1
DataForge Core,com.dataforge.generators.internal,WebSocketGenerator.WebSocketConfig,2,79,1,3,0,24,1,13,0,12
DataForge Core,com.dataforge.generators.internal,FilePathGenerator,1710,0,88,0,180,0,73,0,26,0
DataForge Core,com.dataforge.generators.internal,CookieGenerator,218,510,28,26,49,87,26,20,2,15
DataForge Core,com.dataforge.generators.internal,DecimalGenerator,143,391,24,20,33,88,19,28,1,20
DataForge Core,com.dataforge.generators.internal,EmailGenerator.DomainInfo,3,0,0,0,2,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,WebSocketGenerator,190,727,34,42,38,113,29,33,1,17
DataForge Core,com.dataforge.generators.internal,PortGenerator,1816,0,64,0,121,0,53,0,20,0
DataForge Core,com.dataforge.generators.internal,TimezoneGenerator,82,517,11,14,26,65,12,19,2,15
DataForge Core,com.dataforge.generators.internal,VerificationCodeGenerator,598,0,57,0,113,0,48,0,17,0
DataForge Core,com.dataforge.generators.internal,ProxyGenerator.SpeedLevel,3,39,0,0,1,5,1,2,1,2
DataForge Core,com.dataforge.generators.internal,BankCardGenerator,1254,0,104,0,224,0,92,0,37,0
DataForge Core,com.dataforge.generators.internal,BloodTypeGenerator.BloodType,54,0,10,0,11,0,8,0,3,0
DataForge Core,com.dataforge.generators.internal,PhoneGenerator,677,0,53,0,95,0,46,0,17,0
DataForge Core,com.dataforge.generators.internal,UserAgentGenerator.DeviceType,0,27,0,0,0,2,0,1,0,1
DataForge Core,com.dataforge.generators.internal,MaritalStatusGenerator.MaritalStatus,27,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.internal,DeviceIdGenerator.OutputFormat,3,32,0,0,1,7,1,2,1,2
DataForge Core,com.dataforge.generators.internal,CompanyNameGenerator,1218,0,32,0,90,0,26,0,10,0
DataForge Core,com.dataforge.generators.internal,FaxGenerator.CountryInfo,12,0,0,0,5,0,1,0,1,0
DataForge Core,com.dataforge.generators.spi,DataGenerator,10,0,2,0,2,0,3,0,2,0
DataForge Core,com.dataforge.model,FieldConfig,111,27,5,1,25,9,18,3,15,3
DataForge Core,com.dataforge.config,FieldConfigWrapper,146,0,22,0,41,0,25,0,14,0
DataForge Core,com.dataforge.config,OutputConfig.Format,54,0,2,0,13,0,4,0,3,0
DataForge Core,com.dataforge.config,ForgeConfig,138,0,14,0,31,0,23,0,16,0
DataForge Core,com.dataforge.config,SimpleFieldConfig,16,0,0,0,7,0,3,0,3,0
DataForge Core,com.dataforge.config,OutputConfig,122,0,6,0,36,0,22,0,19,0
DataForge Core,com.dataforge.io,ConsoleOutputStrategy,262,0,22,0,69,0,25,0,14,0
DataForge Core,com.dataforge.io,OutputException,13,0,0,0,6,0,3,0,3,0
DataForge Core,com.dataforge.io,OutputStrategy,34,0,6,0,8,0,8,0,5,0
DataForge Core,com.dataforge.io,SqlOutputStrategy,730,0,80,0,171,0,56,0,16,0
DataForge Core,com.dataforge.io,CsvOutputStrategy,270,0,34,0,67,0,27,0,10,0
DataForge Core,com.dataforge.validation,UsccValidator,582,0,56,0,112,0,43,0,15,0
DataForge Core,com.dataforge.validation,LuhnValidator,192,127,34,14,49,28,28,8,8,4
DataForge Core,com.dataforge.validation,IdCardValidator,725,0,60,0,142,0,45,0,15,0
DataForge Core,com.dataforge.validation,UsccValidator.UsccInfo,59,0,0,0,20,0,14,0,14,0
DataForge Core,com.dataforge.validation,OrganizationCodeValidator,432,0,50,0,89,0,42,0,17,0
DataForge Core,com.dataforge.validation,ValidationResult,214,0,16,0,43,0,24,0,16,0
