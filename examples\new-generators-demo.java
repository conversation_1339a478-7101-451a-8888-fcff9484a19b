import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.internal.*;
import com.dataforge.config.SimpleFieldConfig;

/**
 * 新增生成器演示
 * 
 * 展示WebSocketGenerator、ProxyGenerator、RandomNumberGenerator、DecimalGenerator的功能
 */
public class NewGeneratorsDemo {
    
    public static void main(String[] args) {
        DataForgeContext context = new DataForgeContext();
        
        System.out.println("=== DataForge 新增生成器演示 ===\n");
        
        // 1. WebSocket生成器演示
        demonstrateWebSocketGenerator(context);
        
        // 2. 代理生成器演示
        demonstrateProxyGenerator(context);
        
        // 3. 随机数生成器演示
        demonstrateRandomNumberGenerator(context);
        
        // 4. 小数生成器演示
        demonstrateDecimalGenerator(context);
    }
    
    private static void demonstrateWebSocketGenerator(DataForgeContext context) {
        System.out.println("1. WebSocket连接生成器 (WebSocketGenerator)");
        System.out.println("============================================");
        
        WebSocketGenerator generator = new WebSocketGenerator();
        
        // WebSocket URL
        SimpleFieldConfig urlConfig = new SimpleFieldConfig("websocket", "websocket");
        urlConfig.setParam("format", "URL");
        urlConfig.setParam("protocol", "WS");
        urlConfig.setParam("host", "localhost");
        urlConfig.setParam("port", 8080);
        System.out.println("WebSocket URL: " + generator.generate(urlConfig, context));
        
        // 安全WebSocket
        SimpleFieldConfig secureConfig = new SimpleFieldConfig("websocket", "websocket");
        secureConfig.setParam("format", "URL");
        secureConfig.setParam("protocol", "WSS");
        secureConfig.setParam("port", 443);
        secureConfig.setParam("include_query", true);
        System.out.println("安全WebSocket: " + generator.generate(secureConfig, context));
        
        // JSON配置格式
        SimpleFieldConfig jsonConfig = new SimpleFieldConfig("websocket", "websocket");
        jsonConfig.setParam("format", "JSON");
        jsonConfig.setParam("subprotocol", "CHAT");
        jsonConfig.setParam("include_headers", true);
        System.out.println("JSON配置:");
        System.out.println(generator.generate(jsonConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateProxyGenerator(DataForgeContext context) {
        System.out.println("2. 代理配置生成器 (ProxyGenerator)");
        System.out.println("==================================");
        
        ProxyGenerator generator = new ProxyGenerator();
        
        // HTTP代理
        SimpleFieldConfig httpConfig = new SimpleFieldConfig("proxy", "proxy");
        httpConfig.setParam("format", "URL");
        httpConfig.setParam("type", "HTTP");
        httpConfig.setParam("host", "proxy.example.com");
        httpConfig.setParam("port", 8080);
        System.out.println("HTTP代理: " + generator.generate(httpConfig, context));
        
        // SOCKS5代理
        SimpleFieldConfig socksConfig = new SimpleFieldConfig("proxy", "proxy");
        socksConfig.setParam("format", "URL");
        socksConfig.setParam("type", "SOCKS5");
        socksConfig.setParam("auth_required", true);
        System.out.println("SOCKS5代理: " + generator.generate(socksConfig, context));
        
        // JSON配置
        SimpleFieldConfig jsonConfig = new SimpleFieldConfig("proxy", "proxy");
        jsonConfig.setParam("format", "JSON");
        jsonConfig.setParam("type", "HTTPS");
        jsonConfig.setParam("country", "US");
        jsonConfig.setParam("speed", "FAST");
        jsonConfig.setParam("reliability", "HIGH");
        System.out.println("JSON配置:");
        System.out.println(generator.generate(jsonConfig, context));
        
        // PAC脚本
        SimpleFieldConfig pacConfig = new SimpleFieldConfig("proxy", "proxy");
        pacConfig.setParam("format", "PAC");
        pacConfig.setParam("host", "corporate-proxy.company.com");
        pacConfig.setParam("port", 3128);
        System.out.println("PAC脚本:");
        System.out.println(generator.generate(pacConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateRandomNumberGenerator(DataForgeContext context) {
        System.out.println("3. 随机数生成器 (RandomNumberGenerator)");
        System.out.println("======================================");
        
        RandomNumberGenerator generator = new RandomNumberGenerator();
        
        // 整数
        SimpleFieldConfig intConfig = new SimpleFieldConfig("number", "random_number");
        intConfig.setParam("type", "INT");
        intConfig.setParam("min", 1);
        intConfig.setParam("max", 1000);
        System.out.println("随机整数: " + generator.generate(intConfig, context));
        
        // 十六进制
        SimpleFieldConfig hexConfig = new SimpleFieldConfig("number", "random_number");
        hexConfig.setParam("type", "INT");
        hexConfig.setParam("format", "HEX");
        hexConfig.setParam("min", 16);
        hexConfig.setParam("max", 255);
        System.out.println("十六进制: " + generator.generate(hexConfig, context));
        
        // 二进制
        SimpleFieldConfig binConfig = new SimpleFieldConfig("number", "random_number");
        binConfig.setParam("type", "INT");
        binConfig.setParam("format", "BINARY");
        binConfig.setParam("min", 1);
        binConfig.setParam("max", 31);
        System.out.println("二进制: " + generator.generate(binConfig, context));
        
        // 大整数
        SimpleFieldConfig bigintConfig = new SimpleFieldConfig("number", "random_number");
        bigintConfig.setParam("type", "BIGINT");
        bigintConfig.setParam("precision", 20);
        System.out.println("大整数: " + generator.generate(bigintConfig, context));
        
        // 正态分布
        SimpleFieldConfig normalConfig = new SimpleFieldConfig("number", "random_number");
        normalConfig.setParam("type", "INT");
        normalConfig.setParam("distribution", "NORMAL");
        normalConfig.setParam("mean", 100);
        normalConfig.setParam("stddev", 20);
        normalConfig.setParam("min", 0);
        normalConfig.setParam("max", 200);
        System.out.println("正态分布: " + generator.generate(normalConfig, context));
        
        // 科学计数法
        SimpleFieldConfig scientificConfig = new SimpleFieldConfig("number", "random_number");
        scientificConfig.setParam("type", "LONG");
        scientificConfig.setParam("format", "SCIENTIFIC");
        scientificConfig.setParam("min", 1000000);
        scientificConfig.setParam("max", 999999999);
        System.out.println("科学计数法: " + generator.generate(scientificConfig, context));
        
        System.out.println();
    }
    
    private static void demonstrateDecimalGenerator(DataForgeContext context) {
        System.out.println("4. 小数生成器 (DecimalGenerator)");
        System.out.println("===============================");
        
        DecimalGenerator generator = new DecimalGenerator();
        
        // 普通小数
        SimpleFieldConfig plainConfig = new SimpleFieldConfig("decimal", "decimal");
        plainConfig.setParam("type", "DOUBLE");
        plainConfig.setParam("min", 0.0);
        plainConfig.setParam("max", 100.0);
        plainConfig.setParam("scale", 2);
        System.out.println("普通小数: " + generator.generate(plainConfig, context));
        
        // 货币格式
        SimpleFieldConfig currencyConfig = new SimpleFieldConfig("decimal", "decimal");
        currencyConfig.setParam("type", "DOUBLE");
        currencyConfig.setParam("format", "CURRENCY");
        currencyConfig.setParam("min", 10.0);
        currencyConfig.setParam("max", 10000.0);
        currencyConfig.setParam("currency_code", "USD");
        System.out.println("货币格式: " + generator.generate(currencyConfig, context));
        
        // 百分比格式
        SimpleFieldConfig percentConfig = new SimpleFieldConfig("decimal", "decimal");
        percentConfig.setParam("type", "DOUBLE");
        percentConfig.setParam("format", "PERCENTAGE");
        percentConfig.setParam("min", 0.0);
        percentConfig.setParam("max", 100.0);
        percentConfig.setParam("scale", 1);
        System.out.println("百分比格式: " + generator.generate(percentConfig, context));
        
        // 科学计数法
        SimpleFieldConfig scientificConfig = new SimpleFieldConfig("decimal", "decimal");
        scientificConfig.setParam("type", "DOUBLE");
        scientificConfig.setParam("format", "SCIENTIFIC");
        scientificConfig.setParam("min", 0.001);
        scientificConfig.setParam("max", 999999.999);
        System.out.println("科学计数法: " + generator.generate(scientificConfig, context));
        
        // 自定义格式
        SimpleFieldConfig customConfig = new SimpleFieldConfig("decimal", "decimal");
        customConfig.setParam("type", "DOUBLE");
        customConfig.setParam("format", "CUSTOM");
        customConfig.setParam("pattern", "#,##0.000");
        customConfig.setParam("min", 1000.0);
        customConfig.setParam("max", 999999.0);
        System.out.println("自定义格式: " + generator.generate(customConfig, context));
        
        // 高精度小数
        SimpleFieldConfig bigDecimalConfig = new SimpleFieldConfig("decimal", "decimal");
        bigDecimalConfig.setParam("type", "BIGDECIMAL");
        bigDecimalConfig.setParam("min", 0.0);
        bigDecimalConfig.setParam("max", 1000000.0);
        bigDecimalConfig.setParam("precision", 15);
        bigDecimalConfig.setParam("scale", 6);
        System.out.println("高精度小数: " + generator.generate(bigDecimalConfig, context));
        
        // 正态分布小数
        SimpleFieldConfig normalConfig = new SimpleFieldConfig("decimal", "decimal");
        normalConfig.setParam("type", "DOUBLE");
        normalConfig.setParam("distribution", "NORMAL");
        normalConfig.setParam("mean", 50.0);
        normalConfig.setParam("stddev", 15.0);
        normalConfig.setParam("min", 0.0);
        normalConfig.setParam("max", 100.0);
        normalConfig.setParam("scale", 3);
        System.out.println("正态分布小数: " + generator.generate(normalConfig, context));
        
        System.out.println();
    }
}
