<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ValidationResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_source">ValidationResult.java</span></div><h1>ValidationResult.java</h1><pre class="source lang-java linenums">package com.dataforge.validation;

import java.util.ArrayList;
import java.util.List;

/**
 * 校验结果类。
 * 
 * &lt;p&gt;
 * 封装了数据校验的结果信息，包括是否有效、错误消息等。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class ValidationResult {

    /**
     * 校验是否通过。
     */
    private final boolean valid;

    /**
     * 错误消息列表。
     */
    private final List&lt;String&gt; errorMessages;

    /**
     * 警告消息列表。
     */
    private final List&lt;String&gt; warningMessages;

    /**
     * 构造函数 - 创建有效的校验结果。
     */
<span class="nc" id="L35">    public ValidationResult() {</span>
<span class="nc" id="L36">        this.valid = true;</span>
<span class="nc" id="L37">        this.errorMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L38">        this.warningMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L39">    }</span>

    /**
     * 构造函数 - 创建指定有效性的校验结果。
     * 
     * @param valid 是否有效
     */
<span class="nc" id="L46">    public ValidationResult(boolean valid) {</span>
<span class="nc" id="L47">        this.valid = valid;</span>
<span class="nc" id="L48">        this.errorMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L49">        this.warningMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L50">    }</span>

    /**
     * 构造函数 - 创建无效的校验结果并指定错误消息。
     * 
     * @param errorMessage 错误消息
     */
<span class="nc" id="L57">    public ValidationResult(String errorMessage) {</span>
<span class="nc" id="L58">        this.valid = false;</span>
<span class="nc" id="L59">        this.errorMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L60">        this.warningMessages = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L61">        this.errorMessages.add(errorMessage);</span>
<span class="nc" id="L62">    }</span>

    /**
     * 构造函数 - 创建校验结果并指定所有属性。
     * 
     * @param valid           是否有效
     * @param errorMessages   错误消息列表
     * @param warningMessages 警告消息列表
     */
<span class="nc" id="L71">    public ValidationResult(boolean valid, List&lt;String&gt; errorMessages, List&lt;String&gt; warningMessages) {</span>
<span class="nc" id="L72">        this.valid = valid;</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">        this.errorMessages = errorMessages != null ? new ArrayList&lt;&gt;(errorMessages) : new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">        this.warningMessages = warningMessages != null ? new ArrayList&lt;&gt;(warningMessages) : new ArrayList&lt;&gt;();</span>
<span class="nc" id="L75">    }</span>

    /**
     * 检查校验是否通过。
     * 
     * @return 如果校验通过返回true，否则返回false
     */
    public boolean isValid() {
<span class="nc" id="L83">        return valid;</span>
    }

    /**
     * 获取错误消息列表。
     * 
     * @return 错误消息列表的副本
     */
    public List&lt;String&gt; getErrorMessages() {
<span class="nc" id="L92">        return new ArrayList&lt;&gt;(errorMessages);</span>
    }

    /**
     * 获取警告消息列表。
     * 
     * @return 警告消息列表的副本
     */
    public List&lt;String&gt; getWarningMessages() {
<span class="nc" id="L101">        return new ArrayList&lt;&gt;(warningMessages);</span>
    }

    /**
     * 检查是否有错误消息。
     * 
     * @return 如果有错误消息返回true，否则返回false
     */
    public boolean hasErrors() {
<span class="nc bnc" id="L110" title="All 2 branches missed.">        return !errorMessages.isEmpty();</span>
    }

    /**
     * 检查是否有警告消息。
     * 
     * @return 如果有警告消息返回true，否则返回false
     */
    public boolean hasWarnings() {
<span class="nc bnc" id="L119" title="All 2 branches missed.">        return !warningMessages.isEmpty();</span>
    }

    /**
     * 获取第一个错误消息。
     * 
     * @return 第一个错误消息，如果没有错误消息则返回null
     */
    public String getFirstErrorMessage() {
<span class="nc bnc" id="L128" title="All 2 branches missed.">        return errorMessages.isEmpty() ? null : errorMessages.get(0);</span>
    }

    /**
     * 获取第一个警告消息。
     * 
     * @return 第一个警告消息，如果没有警告消息则返回null
     */
    public String getFirstWarningMessage() {
<span class="nc bnc" id="L137" title="All 2 branches missed.">        return warningMessages.isEmpty() ? null : warningMessages.get(0);</span>
    }

    /**
     * 创建成功的校验结果。
     * 
     * @return 成功的校验结果
     */
    public static ValidationResult success() {
<span class="nc" id="L146">        return new ValidationResult(true);</span>
    }

    /**
     * 创建失败的校验结果。
     * 
     * @param errorMessage 错误消息
     * @return 失败的校验结果
     */
    public static ValidationResult failure(String errorMessage) {
<span class="nc" id="L156">        return new ValidationResult(errorMessage);</span>
    }

    /**
     * 创建带有多个错误消息的失败校验结果。
     * 
     * @param errorMessages 错误消息列表
     * @return 失败的校验结果
     */
    public static ValidationResult failure(List&lt;String&gt; errorMessages) {
<span class="nc" id="L166">        return new ValidationResult(false, errorMessages, null);</span>
    }

    /**
     * 创建带有警告的成功校验结果。
     * 
     * @param warningMessage 警告消息
     * @return 带有警告的成功校验结果
     */
    public static ValidationResult successWithWarning(String warningMessage) {
<span class="nc" id="L176">        List&lt;String&gt; warnings = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L177">        warnings.add(warningMessage);</span>
<span class="nc" id="L178">        return new ValidationResult(true, null, warnings);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L183">        StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L184">        sb.append(&quot;ValidationResult{&quot;);</span>
<span class="nc" id="L185">        sb.append(&quot;valid=&quot;).append(valid);</span>

<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (!errorMessages.isEmpty()) {</span>
<span class="nc" id="L188">            sb.append(&quot;, errors=&quot;).append(errorMessages);</span>
        }

<span class="nc bnc" id="L191" title="All 2 branches missed.">        if (!warningMessages.isEmpty()) {</span>
<span class="nc" id="L192">            sb.append(&quot;, warnings=&quot;).append(warningMessages);</span>
        }

<span class="nc" id="L195">        sb.append('}');</span>
<span class="nc" id="L196">        return sb.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>