<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OccupationGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">OccupationGenerator.java</span></div><h1>OccupationGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 职业/职位生成器
 * 
 * 支持的参数：
 * - industry: 行业类型 (IT|FINANCE|EDUCATION|HEALTHCARE|MANUFACTURING|RETAIL|ANY)
 * - level: 职位层级 (JUNIOR|SENIOR|MANAGER|DIRECTOR|EXECUTIVE|ANY)
 * - file: 自定义职业列表文件路径
 * - weights: 职业权重配置 (如 &quot;软件工程师:10,产品经理:5&quot;)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L23">public class OccupationGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(OccupationGenerator.class);</span>
<span class="nc" id="L26">    private static final Random random = new Random();</span>

    // 按行业分类的职业数据
<span class="nc" id="L29">    private static final Map&lt;String, Map&lt;String, List&lt;String&gt;&gt;&gt; OCCUPATIONS_BY_INDUSTRY = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L32">        initializeOccupations();</span>
<span class="nc" id="L33">    }</span>

    private static void initializeOccupations() {
        // IT行业
<span class="nc" id="L37">        Map&lt;String, List&lt;String&gt;&gt; itOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L38">        itOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;初级软件工程师&quot;, &quot;前端开发工程师&quot;, &quot;后端开发工程师&quot;, &quot;测试工程师&quot;, &quot;运维工程师&quot;,
                &quot;UI设计师&quot;, &quot;产品助理&quot;, &quot;数据分析师&quot;, &quot;技术支持工程师&quot;, &quot;系统管理员&quot;));
<span class="nc" id="L41">        itOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;高级软件工程师&quot;, &quot;架构师&quot;, &quot;高级前端工程师&quot;, &quot;高级后端工程师&quot;, &quot;高级测试工程师&quot;,
                &quot;DevOps工程师&quot;, &quot;高级UI设计师&quot;, &quot;产品经理&quot;, &quot;高级数据分析师&quot;, &quot;技术专家&quot;));
<span class="nc" id="L44">        itOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;技术经理&quot;, &quot;项目经理&quot;, &quot;产品总监&quot;, &quot;研发经理&quot;, &quot;测试经理&quot;,
                &quot;运维经理&quot;, &quot;设计总监&quot;, &quot;数据总监&quot;, &quot;技术总监&quot;, &quot;部门经理&quot;));
<span class="nc" id="L47">        itOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;技术总监&quot;, &quot;研发总监&quot;, &quot;产品副总裁&quot;, &quot;工程总监&quot;, &quot;创新总监&quot;));
<span class="nc" id="L49">        itOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;首席技术官&quot;, &quot;首席产品官&quot;, &quot;首席信息官&quot;, &quot;副总裁&quot;, &quot;总经理&quot;));
<span class="nc" id="L51">        OCCUPATIONS_BY_INDUSTRY.put(&quot;IT&quot;, itOccupations);</span>

        // 金融行业
<span class="nc" id="L54">        Map&lt;String, List&lt;String&gt;&gt; financeOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L55">        financeOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;银行柜员&quot;, &quot;客户经理&quot;, &quot;信贷员&quot;, &quot;风控专员&quot;, &quot;财务分析师&quot;,
                &quot;投资顾问助理&quot;, &quot;保险代理人&quot;, &quot;证券经纪人&quot;, &quot;会计&quot;, &quot;出纳&quot;));
<span class="nc" id="L58">        financeOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;高级客户经理&quot;, &quot;高级信贷经理&quot;, &quot;风控经理&quot;, &quot;高级财务分析师&quot;, &quot;投资顾问&quot;,
                &quot;保险经理&quot;, &quot;证券分析师&quot;, &quot;高级会计师&quot;, &quot;财务经理&quot;, &quot;审计师&quot;));
<span class="nc" id="L61">        financeOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;支行行长&quot;, &quot;信贷部经理&quot;, &quot;风控总监&quot;, &quot;财务总监&quot;, &quot;投资经理&quot;,
                &quot;保险部经理&quot;, &quot;证券部经理&quot;, &quot;会计主管&quot;, &quot;审计经理&quot;, &quot;合规经理&quot;));
<span class="nc" id="L64">        financeOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;分行行长&quot;, &quot;风控总监&quot;, &quot;财务总监&quot;, &quot;投资总监&quot;, &quot;运营总监&quot;));
<span class="nc" id="L66">        financeOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;总行行长&quot;, &quot;首席风险官&quot;, &quot;首席财务官&quot;, &quot;首席投资官&quot;, &quot;副总裁&quot;));
<span class="nc" id="L68">        OCCUPATIONS_BY_INDUSTRY.put(&quot;FINANCE&quot;, financeOccupations);</span>

        // 教育行业
<span class="nc" id="L71">        Map&lt;String, List&lt;String&gt;&gt; educationOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L72">        educationOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;小学教师&quot;, &quot;中学教师&quot;, &quot;幼儿园教师&quot;, &quot;培训师&quot;, &quot;教学助理&quot;,
                &quot;辅导员&quot;, &quot;班主任&quot;, &quot;体育教师&quot;, &quot;音乐教师&quot;, &quot;美术教师&quot;));
<span class="nc" id="L75">        educationOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;高级教师&quot;, &quot;学科带头人&quot;, &quot;教研员&quot;, &quot;高级培训师&quot;, &quot;教务主任&quot;,
                &quot;年级主任&quot;, &quot;教学主管&quot;, &quot;课程设计师&quot;, &quot;教育顾问&quot;, &quot;学术研究员&quot;));
<span class="nc" id="L78">        educationOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;教务处长&quot;, &quot;学生处长&quot;, &quot;系主任&quot;, &quot;培训部经理&quot;, &quot;教学总监&quot;,
                &quot;学术主任&quot;, &quot;研究所所长&quot;, &quot;教育项目经理&quot;, &quot;校区负责人&quot;, &quot;部门主管&quot;));
<span class="nc" id="L81">        educationOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;副校长&quot;, &quot;教学副校长&quot;, &quot;学术副校长&quot;, &quot;教育总监&quot;, &quot;研究院院长&quot;));
<span class="nc" id="L83">        educationOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;校长&quot;, &quot;院长&quot;, &quot;教育集团总裁&quot;, &quot;首席教育官&quot;, &quot;董事长&quot;));
<span class="nc" id="L85">        OCCUPATIONS_BY_INDUSTRY.put(&quot;EDUCATION&quot;, educationOccupations);</span>

        // 医疗行业
<span class="nc" id="L88">        Map&lt;String, List&lt;String&gt;&gt; healthcareOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L89">        healthcareOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;住院医师&quot;, &quot;护士&quot;, &quot;药师&quot;, &quot;医技师&quot;, &quot;康复师&quot;,
                &quot;营养师&quot;, &quot;心理咨询师&quot;, &quot;医学检验师&quot;, &quot;放射技师&quot;, &quot;麻醉师&quot;));
<span class="nc" id="L92">        healthcareOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;主治医师&quot;, &quot;主管护师&quot;, &quot;主管药师&quot;, &quot;高级医技师&quot;, &quot;高级康复师&quot;,
                &quot;高级营养师&quot;, &quot;心理治疗师&quot;, &quot;高级检验师&quot;, &quot;高级放射师&quot;, &quot;主管麻醉师&quot;));
<span class="nc" id="L95">        healthcareOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;科室主任&quot;, &quot;护士长&quot;, &quot;药剂科主任&quot;, &quot;医技科主任&quot;, &quot;康复科主任&quot;,
                &quot;营养科主任&quot;, &quot;心理科主任&quot;, &quot;检验科主任&quot;, &quot;放射科主任&quot;, &quot;麻醉科主任&quot;));
<span class="nc" id="L98">        healthcareOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;医务处长&quot;, &quot;护理部主任&quot;, &quot;医院副院长&quot;, &quot;医疗总监&quot;, &quot;学科带头人&quot;));
<span class="nc" id="L100">        healthcareOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;院长&quot;, &quot;医疗集团总裁&quot;, &quot;首席医疗官&quot;, &quot;医院董事长&quot;, &quot;卫生局长&quot;));
<span class="nc" id="L102">        OCCUPATIONS_BY_INDUSTRY.put(&quot;HEALTHCARE&quot;, healthcareOccupations);</span>

        // 制造业
<span class="nc" id="L105">        Map&lt;String, List&lt;String&gt;&gt; manufacturingOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L106">        manufacturingOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;生产工人&quot;, &quot;质检员&quot;, &quot;设备操作员&quot;, &quot;仓库管理员&quot;, &quot;采购员&quot;,
                &quot;销售代表&quot;, &quot;技术员&quot;, &quot;维修工&quot;, &quot;包装工&quot;, &quot;物流专员&quot;));
<span class="nc" id="L109">        manufacturingOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;生产主管&quot;, &quot;质量工程师&quot;, &quot;设备工程师&quot;, &quot;仓储主管&quot;, &quot;采购主管&quot;,
                &quot;销售经理&quot;, &quot;工艺工程师&quot;, &quot;维修主管&quot;, &quot;物流经理&quot;, &quot;生产计划员&quot;));
<span class="nc" id="L112">        manufacturingOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;生产经理&quot;, &quot;质量经理&quot;, &quot;设备经理&quot;, &quot;仓储经理&quot;, &quot;采购经理&quot;,
                &quot;销售总监&quot;, &quot;技术经理&quot;, &quot;维修经理&quot;, &quot;物流总监&quot;, &quot;车间主任&quot;));
<span class="nc" id="L115">        manufacturingOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;生产总监&quot;, &quot;质量总监&quot;, &quot;技术总监&quot;, &quot;供应链总监&quot;, &quot;运营总监&quot;));
<span class="nc" id="L117">        manufacturingOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;总经理&quot;, &quot;副总裁&quot;, &quot;首席运营官&quot;, &quot;工厂厂长&quot;, &quot;集团总裁&quot;));
<span class="nc" id="L119">        OCCUPATIONS_BY_INDUSTRY.put(&quot;MANUFACTURING&quot;, manufacturingOccupations);</span>

        // 零售行业
<span class="nc" id="L122">        Map&lt;String, List&lt;String&gt;&gt; retailOccupations = new HashMap&lt;&gt;();</span>
<span class="nc" id="L123">        retailOccupations.put(&quot;JUNIOR&quot;, Arrays.asList(</span>
                &quot;销售员&quot;, &quot;收银员&quot;, &quot;导购员&quot;, &quot;客服专员&quot;, &quot;理货员&quot;,
                &quot;店员&quot;, &quot;促销员&quot;, &quot;配送员&quot;, &quot;仓库管理员&quot;, &quot;美工&quot;));
<span class="nc" id="L126">        retailOccupations.put(&quot;SENIOR&quot;, Arrays.asList(</span>
                &quot;销售主管&quot;, &quot;客服主管&quot;, &quot;店长&quot;, &quot;区域销售&quot;, &quot;采购专员&quot;,
                &quot;商品经理&quot;, &quot;营销专员&quot;, &quot;运营专员&quot;, &quot;视觉陈列师&quot;, &quot;培训师&quot;));
<span class="nc" id="L129">        retailOccupations.put(&quot;MANAGER&quot;, Arrays.asList(</span>
                &quot;销售经理&quot;, &quot;客服经理&quot;, &quot;区域经理&quot;, &quot;采购经理&quot;, &quot;商品总监&quot;,
                &quot;营销经理&quot;, &quot;运营经理&quot;, &quot;门店经理&quot;, &quot;品牌经理&quot;, &quot;渠道经理&quot;));
<span class="nc" id="L132">        retailOccupations.put(&quot;DIRECTOR&quot;, Arrays.asList(</span>
                &quot;销售总监&quot;, &quot;运营总监&quot;, &quot;商品总监&quot;, &quot;营销总监&quot;, &quot;品牌总监&quot;));
<span class="nc" id="L134">        retailOccupations.put(&quot;EXECUTIVE&quot;, Arrays.asList(</span>
                &quot;总经理&quot;, &quot;副总裁&quot;, &quot;首席营销官&quot;, &quot;首席运营官&quot;, &quot;董事长&quot;));
<span class="nc" id="L136">        OCCUPATIONS_BY_INDUSTRY.put(&quot;RETAIL&quot;, retailOccupations);</span>
<span class="nc" id="L137">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L141">        return &quot;occupation&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L146">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L153">            String industry = config.getParam(&quot;industry&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L154">            String level = config.getParam(&quot;level&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L155">            String weightsParam = config.getParam(&quot;weights&quot;, String.class, null);</span>

            // 加载职业数据
<span class="nc" id="L158">            List&lt;String&gt; occupations = loadOccupations(config, industry, level);</span>

            // 应用权重选择
            String occupation;
<span class="nc bnc" id="L162" title="All 4 branches missed.">            if (weightsParam != null &amp;&amp; !weightsParam.isEmpty()) {</span>
<span class="nc" id="L163">                occupation = selectWithWeights(occupations, weightsParam);</span>
            } else {
<span class="nc" id="L165">                occupation = occupations.get(random.nextInt(occupations.size()));</span>
            }

<span class="nc" id="L168">            logger.debug(&quot;Generated occupation: {}&quot;, occupation);</span>
<span class="nc" id="L169">            return occupation;</span>

<span class="nc" id="L171">        } catch (Exception e) {</span>
<span class="nc" id="L172">            logger.error(&quot;Error generating occupation&quot;, e);</span>
<span class="nc" id="L173">            return &quot;软件工程师&quot;;</span>
        }
    }

    private List&lt;String&gt; loadOccupations(FieldConfig config, String industry, String level) {
<span class="nc" id="L178">        String customFile = config.getParam(&quot;file&quot;, String.class, null);</span>
<span class="nc bnc" id="L179" title="All 2 branches missed.">        if (customFile != null) {</span>
            try {
<span class="nc" id="L181">                return DataLoader.loadDataFromFile(customFile);</span>
<span class="nc" id="L182">            } catch (Exception e) {</span>
<span class="nc" id="L183">                logger.warn(&quot;Failed to load custom occupation file: {}&quot;, customFile, e);</span>
            }
        }

        // 使用内置职业数据
<span class="nc" id="L188">        List&lt;String&gt; occupations = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L190" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(industry)) {</span>
            // 混合所有行业
<span class="nc bnc" id="L192" title="All 2 branches missed.">            for (Map&lt;String, List&lt;String&gt;&gt; industryOccupations : OCCUPATIONS_BY_INDUSTRY.values()) {</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">                if (&quot;ANY&quot;.equals(level)) {</span>
                    // 混合所有级别
<span class="nc" id="L195">                    industryOccupations.values().forEach(occupations::addAll);</span>
                } else {
                    // 指定级别
<span class="nc" id="L198">                    List&lt;String&gt; levelOccupations = industryOccupations.get(level);</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">                    if (levelOccupations != null) {</span>
<span class="nc" id="L200">                        occupations.addAll(levelOccupations);</span>
                    }
                }
<span class="nc" id="L203">            }</span>
        } else {
            // 指定行业
<span class="nc" id="L206">            Map&lt;String, List&lt;String&gt;&gt; industryOccupations = OCCUPATIONS_BY_INDUSTRY.get(industry);</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">            if (industryOccupations != null) {</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">                if (&quot;ANY&quot;.equals(level)) {</span>
                    // 混合所有级别
<span class="nc" id="L210">                    industryOccupations.values().forEach(occupations::addAll);</span>
                } else {
                    // 指定级别
<span class="nc" id="L213">                    List&lt;String&gt; levelOccupations = industryOccupations.get(level);</span>
<span class="nc bnc" id="L214" title="All 2 branches missed.">                    if (levelOccupations != null) {</span>
<span class="nc" id="L215">                        occupations.addAll(levelOccupations);</span>
                    }
                }
            }
        }

        // 如果没有找到合适的职业，使用默认列表
<span class="nc bnc" id="L222" title="All 2 branches missed.">        if (occupations.isEmpty()) {</span>
<span class="nc" id="L223">            occupations.addAll(OCCUPATIONS_BY_INDUSTRY.get(&quot;IT&quot;).get(&quot;JUNIOR&quot;));</span>
        }

<span class="nc" id="L226">        return occupations;</span>
    }

    private String selectWithWeights(List&lt;String&gt; occupations, String weightsParam) {
<span class="nc" id="L230">        Map&lt;String, Integer&gt; weights = parseWeights(weightsParam);</span>

        // 计算总权重
<span class="nc" id="L233">        int totalWeight = 0;</span>
<span class="nc bnc" id="L234" title="All 2 branches missed.">        for (String occupation : occupations) {</span>
<span class="nc" id="L235">            totalWeight += weights.getOrDefault(occupation, 1);</span>
<span class="nc" id="L236">        }</span>

        // 随机选择
<span class="nc" id="L239">        int randomValue = random.nextInt(totalWeight);</span>
<span class="nc" id="L240">        int currentWeight = 0;</span>

<span class="nc bnc" id="L242" title="All 2 branches missed.">        for (String occupation : occupations) {</span>
<span class="nc" id="L243">            currentWeight += weights.getOrDefault(occupation, 1);</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">            if (randomValue &lt; currentWeight) {</span>
<span class="nc" id="L245">                return occupation;</span>
            }
<span class="nc" id="L247">        }</span>

        // 默认返回第一个
<span class="nc" id="L250">        return occupations.get(0);</span>
    }

    private Map&lt;String, Integer&gt; parseWeights(String weightsParam) {
<span class="nc" id="L254">        Map&lt;String, Integer&gt; weights = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L257">            String[] pairs = weightsParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">            for (String pair : pairs) {</span>
<span class="nc" id="L259">                String[] parts = pair.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L261">                    String occupation = parts[0].trim();</span>
<span class="nc" id="L262">                    int weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L263">                    weights.put(occupation, weight);</span>
                }
            }
<span class="nc" id="L266">        } catch (Exception e) {</span>
<span class="nc" id="L267">            logger.warn(&quot;Failed to parse weights: {}&quot;, weightsParam, e);</span>
<span class="nc" id="L268">        }</span>

<span class="nc" id="L270">        return weights;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>