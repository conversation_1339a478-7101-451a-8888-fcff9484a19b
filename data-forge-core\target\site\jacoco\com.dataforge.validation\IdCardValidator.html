<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IdCardValidator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.validation</a> &gt; <span class="el_class">IdCardValidator</span></div><h1>IdCardValidator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">725 of 725</td><td class="ctr2">0%</td><td class="bar">60 of 60</td><td class="ctr2">0%</td><td class="ctr1">45</td><td class="ctr2">45</td><td class="ctr1">142</td><td class="ctr2">142</td><td class="ctr1">15</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a10"><a href="IdCardValidator.java.html#L35" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="321" alt="321"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h0">44</td><td class="ctr2" id="i0">44</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="IdCardValidator.java.html#L103" class="el_method">validate(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="90" alt="90"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h1">24</td><td class="ctr2" id="i1">24</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="IdCardValidator.java.html#L191" class="el_method">validateBirthDate(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="55" alt="55"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a14"><a href="IdCardValidator.java.html#L160" class="el_method">validateRegionCode(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="49" alt="49"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="IdCardValidator.java.html#L251" class="el_method">calculateCheckCode(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="45" alt="45"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a9"><a href="IdCardValidator.java.html#L339" class="el_method">maskIdCard(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="36" alt="36"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="IdCardValidator.java.html#L231" class="el_method">validateCheckCode(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="34" alt="34"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="IdCardValidator.java.html#L283" class="el_method">extractGender(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="32" alt="32"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="IdCardValidator.java.html#L304" class="el_method">extractBirthDate(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="30" alt="30"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="IdCardValidator.java.html#L325" class="el_method">extractRegionCode(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h9">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="IdCardValidator.java.html#L272" class="el_method">generateValidIdCard(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a8"><a href="IdCardValidator.java.html#L98" class="el_method">isValid(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="IdCardValidator.java.html#L33" class="el_method">IdCardValidator()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="IdCardValidator.java.html#L354" class="el_method">getName()</a></td><td class="bar" id="b13"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a5"><a href="IdCardValidator.java.html#L359" class="el_method">getDescription()</a></td><td class="bar" id="b14"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>