<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AddressGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">AddressGenerator.java</span></div><h1>AddressGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 地址生成器
 * 
 * 支持功能：
 * 1. 基于行政区划数据的层级地址生成
 * 2. 与身份证号的地区信息关联
 * 3. 支持不同详细程度的地址
 * 4. 支持自定义街道和小区名称
 * 
 * 参数配置：
 * - country: 国家代码（默认CN）
 * - province: 指定省份名称或代码
 * - city: 指定城市名称或代码
 * - district: 指定区县名称或代码
 * - detail_level: 详细程度 PROVINCE|CITY|DISTRICT|STREET|COMMUNITY|FULL（默认FULL）
 * - include_zipcode: 是否包含邮编（默认true）
 * - link_idcard: 是否关联身份证号（默认true）
 * 
 * 关联字段：
 * - idcard: 从身份证号中提取地区代码
 * - region_code: 从上下文中获取地区代码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L38">public class AddressGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L40">    private static final Logger log = LoggerFactory.getLogger(AddressGenerator.class);</span>

    private static final String TYPE = &quot;address&quot;;
    private static final String DEFAULT_DETAIL_LEVEL = &quot;FULL&quot;;
    private static final boolean DEFAULT_INCLUDE_ZIPCODE = true;
    private static final boolean DEFAULT_LINK_IDCARD = true;

    // 详细程度枚举
<span class="nc" id="L48">    public enum DetailLevel {</span>
<span class="nc" id="L49">        PROVINCE, CITY, DISTRICT, STREET, COMMUNITY, FULL</span>
    }

    // 上下文键名
    private static final String CONTEXT_ID_CARD = &quot;idcard&quot;;
    private static final String CONTEXT_REGION_CODE = &quot;region_code&quot;;

    // 数据缓存
    private static volatile Map&lt;String, AdministrativeDivision&gt; divisionsCache;
    private static volatile List&lt;String&gt; streetNames;
    private static volatile List&lt;String&gt; communityNames;
    private static volatile List&lt;String&gt; buildingNames;

    // 行政区划数据结构
    public static class AdministrativeDivision {
        private final String code;
        private final String name;
        private final String level;
        private final String parentCode;
        private final String zipCode;

<span class="nc" id="L70">        public AdministrativeDivision(String code, String name, String level, String parentCode, String zipCode) {</span>
<span class="nc" id="L71">            this.code = code;</span>
<span class="nc" id="L72">            this.name = name;</span>
<span class="nc" id="L73">            this.level = level;</span>
<span class="nc" id="L74">            this.parentCode = parentCode;</span>
<span class="nc" id="L75">            this.zipCode = zipCode;</span>
<span class="nc" id="L76">        }</span>

        // Getters
        public String getCode() {
<span class="nc" id="L80">            return code;</span>
        }

        public String getName() {
<span class="nc" id="L84">            return name;</span>
        }

        public String getLevel() {
<span class="nc" id="L88">            return level;</span>
        }

        public String getParentCode() {
<span class="nc" id="L92">            return parentCode;</span>
        }

        public String getZipCode() {
<span class="nc" id="L96">            return zipCode;</span>
        }
    }

    @Override
    public String getType() {
<span class="nc" id="L102">        return TYPE;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L107">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L112">        Map&lt;String, Object&gt; params = config.getParams();</span>

        // 解析配置参数
<span class="nc" id="L115">        String province = getStringParam(params, &quot;province&quot;, null);</span>
<span class="nc" id="L116">        String city = getStringParam(params, &quot;city&quot;, null);</span>
<span class="nc" id="L117">        String district = getStringParam(params, &quot;district&quot;, null);</span>
<span class="nc" id="L118">        String detailLevelStr = getStringParam(params, &quot;detail_level&quot;, DEFAULT_DETAIL_LEVEL);</span>
<span class="nc" id="L119">        boolean includeZipcode = getBooleanParam(params, &quot;include_zipcode&quot;, DEFAULT_INCLUDE_ZIPCODE);</span>
<span class="nc" id="L120">        boolean linkIdCard = getBooleanParam(params, &quot;link_idcard&quot;, DEFAULT_LINK_IDCARD);</span>

        // 解析详细程度
        DetailLevel detailLevel;
        try {
<span class="nc" id="L125">            detailLevel = DetailLevel.valueOf(detailLevelStr.toUpperCase());</span>
<span class="nc" id="L126">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L127">            log.warn(&quot;Invalid detail level: {}. Using FULL.&quot;, detailLevelStr);</span>
<span class="nc" id="L128">            detailLevel = DetailLevel.FULL;</span>
<span class="nc" id="L129">        }</span>

        // 确保数据已加载
<span class="nc" id="L132">        ensureDataLoaded();</span>

        // 确定地区代码
<span class="nc" id="L135">        String regionCode = determineRegionCode(province, city, district, linkIdCard, context);</span>

        // 生成地址
<span class="nc" id="L138">        return generateAddress(regionCode, detailLevel, includeZipcode);</span>
    }

    /**
     * 确保数据已加载
     */
    private void ensureDataLoaded() {
<span class="nc bnc" id="L145" title="All 2 branches missed.">        if (divisionsCache == null) {</span>
<span class="nc" id="L146">            synchronized (AddressGenerator.class) {</span>
<span class="nc bnc" id="L147" title="All 2 branches missed.">                if (divisionsCache == null) {</span>
<span class="nc" id="L148">                    loadAdministrativeDivisions();</span>
<span class="nc" id="L149">                    loadAddressComponents();</span>
                }
<span class="nc" id="L151">            }</span>
        }
<span class="nc" id="L153">    }</span>

    /**
     * 加载行政区划数据
     */
    private void loadAdministrativeDivisions() {
        try {
<span class="nc" id="L160">            List&lt;String&gt; lines = DataLoader.loadDataFromResource(&quot;data/administrative-divisions.txt&quot;);</span>
<span class="nc" id="L161">            Map&lt;String, AdministrativeDivision&gt; divisions = new HashMap&lt;&gt;();</span>

<span class="nc bnc" id="L163" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc bnc" id="L164" title="All 4 branches missed.">                if (line.trim().isEmpty() || line.startsWith(&quot;#&quot;)) {</span>
<span class="nc" id="L165">                    continue;</span>
                }

<span class="nc" id="L168">                String[] parts = line.split(&quot;\\|&quot;);</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">                if (parts.length &gt;= 4) {</span>
<span class="nc" id="L170">                    String code = parts[0].trim();</span>
<span class="nc" id="L171">                    String name = parts[1].trim();</span>
<span class="nc" id="L172">                    String level = parts[2].trim();</span>
<span class="nc" id="L173">                    String parentCode = parts[3].trim();</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">                    String zipCode = parts.length &gt; 4 ? parts[4].trim() : &quot;&quot;;</span>

<span class="nc" id="L176">                    divisions.put(code, new AdministrativeDivision(code, name, level, parentCode, zipCode));</span>
                }
<span class="nc" id="L178">            }</span>

<span class="nc" id="L180">            divisionsCache = divisions;</span>
<span class="nc" id="L181">            log.info(&quot;Administrative division data loaded - Total regions: {}&quot;, divisions.size());</span>

<span class="nc" id="L183">        } catch (Exception e) {</span>
<span class="nc" id="L184">            log.error(&quot;Failed to load administrative division data&quot;, e);</span>
<span class="nc" id="L185">            divisionsCache = new HashMap&lt;&gt;();</span>
<span class="nc" id="L186">        }</span>
<span class="nc" id="L187">    }</span>

    /**
     * 加载地址组件数据
     */
    private void loadAddressComponents() {
        // 加载街道名称
<span class="nc" id="L194">        streetNames = Arrays.asList(</span>
                &quot;人民路&quot;, &quot;解放路&quot;, &quot;中山路&quot;, &quot;建设路&quot;, &quot;胜利路&quot;, &quot;和平路&quot;, &quot;友谊路&quot;, &quot;光明路&quot;,
                &quot;新华路&quot;, &quot;文化路&quot;, &quot;学府路&quot;, &quot;科技路&quot;, &quot;创业路&quot;, &quot;发展路&quot;, &quot;繁荣路&quot;, &quot;幸福路&quot;,
                &quot;安康路&quot;, &quot;健康路&quot;, &quot;长寿路&quot;, &quot;吉祥路&quot;, &quot;如意路&quot;, &quot;顺心路&quot;, &quot;美好路&quot;, &quot;希望路&quot;,
                &quot;未来路&quot;, &quot;梦想路&quot;, &quot;青春路&quot;, &quot;活力路&quot;, &quot;朝阳路&quot;, &quot;向阳路&quot;, &quot;东风路&quot;, &quot;春风路&quot;,
                &quot;南风路&quot;, &quot;西风路&quot;, &quot;北风路&quot;, &quot;海风路&quot;, &quot;山风路&quot;, &quot;清风路&quot;, &quot;和风路&quot;, &quot;暖风路&quot;);

        // 加载小区名称
<span class="nc" id="L202">        communityNames = Arrays.asList(</span>
                &quot;阳光花园&quot;, &quot;绿色家园&quot;, &quot;幸福家园&quot;, &quot;温馨家园&quot;, &quot;和谐家园&quot;, &quot;美好家园&quot;, &quot;舒适家园&quot;, &quot;宁静家园&quot;,
                &quot;春天花园&quot;, &quot;夏日花园&quot;, &quot;秋韵花园&quot;, &quot;冬雪花园&quot;, &quot;四季花园&quot;, &quot;百花园&quot;, &quot;玫瑰园&quot;, &quot;牡丹园&quot;,
                &quot;桂花园&quot;, &quot;梅花园&quot;, &quot;兰花园&quot;, &quot;菊花园&quot;, &quot;荷花园&quot;, &quot;樱花园&quot;, &quot;桃花园&quot;, &quot;杏花园&quot;,
                &quot;金桂小区&quot;, &quot;银桂小区&quot;, &quot;丹桂小区&quot;, &quot;月桂小区&quot;, &quot;桂花小区&quot;, &quot;梧桐小区&quot;, &quot;银杏小区&quot;, &quot;柳树小区&quot;,
                &quot;松柏小区&quot;, &quot;竹林小区&quot;, &quot;梅园小区&quot;, &quot;兰园小区&quot;, &quot;菊园小区&quot;, &quot;荷园小区&quot;, &quot;莲花小区&quot;, &quot;水仙小区&quot;,
                &quot;紫薇小区&quot;, &quot;海棠小区&quot;, &quot;茉莉小区&quot;, &quot;玉兰小区&quot;, &quot;丁香小区&quot;, &quot;薰衣草小区&quot;, &quot;向日葵小区&quot;, &quot;康乃馨小区&quot;);

        // 加载建筑名称
<span class="nc" id="L211">        buildingNames = Arrays.asList(</span>
                &quot;栋&quot;, &quot;号楼&quot;, &quot;座&quot;, &quot;幢&quot;, &quot;单元&quot;, &quot;区&quot;, &quot;院&quot;, &quot;苑&quot;, &quot;轩&quot;, &quot;阁&quot;, &quot;居&quot;, &quot;庭&quot;, &quot;府&quot;, &quot;邸&quot;, &quot;宅&quot;, &quot;舍&quot;);

<span class="nc" id="L214">        log.info(&quot;Address component data loaded - Streets: {}, Communities: {}, Buildings: {}&quot;,</span>
<span class="nc" id="L215">                streetNames.size(), communityNames.size(), buildingNames.size());</span>
<span class="nc" id="L216">    }</span>

    /**
     * 确定地区代码
     */
    private String determineRegionCode(String province, String city, String district,
            boolean linkIdCard, DataForgeContext context) {

        // 1. 如果指定了具体地区参数
<span class="nc bnc" id="L225" title="All 2 branches missed.">        if (district != null) {</span>
<span class="nc" id="L226">            String code = findRegionCode(district);</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">            if (code != null)</span>
<span class="nc" id="L228">                return code;</span>
        }
<span class="nc bnc" id="L230" title="All 2 branches missed.">        if (city != null) {</span>
<span class="nc" id="L231">            String code = findRegionCode(city);</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">            if (code != null)</span>
<span class="nc" id="L233">                return code;</span>
        }
<span class="nc bnc" id="L235" title="All 2 branches missed.">        if (province != null) {</span>
<span class="nc" id="L236">            String code = findRegionCode(province);</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">            if (code != null)</span>
<span class="nc" id="L238">                return code;</span>
        }

        // 2. 尝试从身份证号中提取地区代码
<span class="nc bnc" id="L242" title="All 2 branches missed.">        if (linkIdCard) {</span>
<span class="nc" id="L243">            String regionCodeFromIdCard = getRegionCodeFromIdCard(context);</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">            if (regionCodeFromIdCard != null) {</span>
<span class="nc" id="L245">                log.debug(&quot;Using region code from ID card: {}&quot;, regionCodeFromIdCard);</span>
<span class="nc" id="L246">                return regionCodeFromIdCard;</span>
            }
        }

        // 3. 从上下文中获取地区代码
<span class="nc" id="L251">        String regionCode = context.get(CONTEXT_REGION_CODE, String.class).orElse(null);</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">        if (regionCode != null) {</span>
<span class="nc" id="L253">            return regionCode;</span>
        }

        // 4. 随机选择一个地区
<span class="nc" id="L257">        return getRandomRegionCode();</span>
    }

    /**
     * 查找地区代码
     */
    private String findRegionCode(String nameOrCode) {
<span class="nc bnc" id="L264" title="All 4 branches missed.">        if (nameOrCode == null || nameOrCode.trim().isEmpty()) {</span>
<span class="nc" id="L265">            return null;</span>
        }

        // 如果是6位数字，直接作为代码使用
<span class="nc bnc" id="L269" title="All 2 branches missed.">        if (nameOrCode.matches(&quot;\\d{6}&quot;)) {</span>
<span class="nc" id="L270">            return nameOrCode;</span>
        }

        // 按名称查找
<span class="nc bnc" id="L274" title="All 2 branches missed.">        for (AdministrativeDivision division : divisionsCache.values()) {</span>
<span class="nc bnc" id="L275" title="All 4 branches missed.">            if (division.getName().equals(nameOrCode) || division.getName().contains(nameOrCode)) {</span>
<span class="nc" id="L276">                return division.getCode();</span>
            }
<span class="nc" id="L278">        }</span>

<span class="nc" id="L280">        return null;</span>
    }

    /**
     * 从身份证号中提取地区代码
     */
    private String getRegionCodeFromIdCard(DataForgeContext context) {
<span class="nc" id="L287">        String idCard = context.get(CONTEXT_ID_CARD, String.class).orElse(null);</span>
<span class="nc bnc" id="L288" title="All 4 branches missed.">        if (idCard != null &amp;&amp; idCard.length() &gt;= 6) {</span>
<span class="nc" id="L289">            return idCard.substring(0, 6);</span>
        }
<span class="nc" id="L291">        return null;</span>
    }

    /**
     * 随机选择地区代码
     */
    private String getRandomRegionCode() {
<span class="nc bnc" id="L298" title="All 2 branches missed.">        if (divisionsCache.isEmpty()) {</span>
<span class="nc" id="L299">            return &quot;110101&quot;; // 默认北京东城区</span>
        }

<span class="nc" id="L302">        List&lt;String&gt; codes = new ArrayList&lt;&gt;(divisionsCache.keySet());</span>
<span class="nc" id="L303">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L304">        return codes.get(random.nextInt(codes.size()));</span>
    }

    /**
     * 生成地址
     */
    private String generateAddress(String regionCode, DetailLevel detailLevel, boolean includeZipcode) {
<span class="nc" id="L311">        StringBuilder address = new StringBuilder();</span>
<span class="nc" id="L312">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

        // 构建层级地址
<span class="nc" id="L315">        List&lt;String&gt; addressParts = buildAddressParts(regionCode);</span>

        // 根据详细程度添加地址组件
<span class="nc" id="L318">        int maxLevel = Math.min(detailLevel.ordinal() + 1, addressParts.size());</span>
<span class="nc bnc" id="L319" title="All 2 branches missed.">        for (int i = 0; i &lt; maxLevel; i++) {</span>
<span class="nc bnc" id="L320" title="All 2 branches missed.">            if (i &gt; 0)</span>
<span class="nc" id="L321">                address.append(&quot;&quot;);</span>
<span class="nc" id="L322">            address.append(addressParts.get(i));</span>
        }

        // 添加详细地址组件
<span class="nc bnc" id="L326" title="All 2 branches missed.">        if (detailLevel.ordinal() &gt;= DetailLevel.STREET.ordinal()) {</span>
<span class="nc" id="L327">            String street = streetNames.get(random.nextInt(streetNames.size()));</span>
<span class="nc" id="L328">            int streetNumber = random.nextInt(1, 1000);</span>
<span class="nc" id="L329">            address.append(street).append(streetNumber).append(&quot;号&quot;);</span>
        }

<span class="nc bnc" id="L332" title="All 2 branches missed.">        if (detailLevel.ordinal() &gt;= DetailLevel.COMMUNITY.ordinal()) {</span>
<span class="nc" id="L333">            String community = communityNames.get(random.nextInt(communityNames.size()));</span>
<span class="nc" id="L334">            address.append(community);</span>
        }

<span class="nc bnc" id="L337" title="All 2 branches missed.">        if (detailLevel == DetailLevel.FULL) {</span>
<span class="nc" id="L338">            int building = random.nextInt(1, 50);</span>
<span class="nc" id="L339">            String buildingName = buildingNames.get(random.nextInt(buildingNames.size()));</span>
<span class="nc" id="L340">            int unit = random.nextInt(1, 6);</span>
<span class="nc" id="L341">            int room = random.nextInt(101, 3999);</span>

<span class="nc" id="L343">            address.append(building).append(buildingName)</span>
<span class="nc" id="L344">                    .append(unit).append(&quot;单元&quot;)</span>
<span class="nc" id="L345">                    .append(room).append(&quot;室&quot;);</span>
        }

        // 添加邮编
<span class="nc bnc" id="L349" title="All 2 branches missed.">        if (includeZipcode) {</span>
<span class="nc" id="L350">            String zipCode = getZipCode(regionCode);</span>
<span class="nc bnc" id="L351" title="All 4 branches missed.">            if (zipCode != null &amp;&amp; !zipCode.isEmpty()) {</span>
<span class="nc" id="L352">                address.append(&quot; (&quot;).append(zipCode).append(&quot;)&quot;);</span>
            }
        }

<span class="nc" id="L356">        return address.toString();</span>
    }

    /**
     * 构建地址层级部分
     */
    private List&lt;String&gt; buildAddressParts(String regionCode) {
<span class="nc" id="L363">        List&lt;String&gt; parts = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L365">        AdministrativeDivision current = divisionsCache.get(regionCode);</span>
<span class="nc bnc" id="L366" title="All 2 branches missed.">        if (current == null) {</span>
<span class="nc" id="L367">            parts.add(&quot;未知地区&quot;);</span>
<span class="nc" id="L368">            return parts;</span>
        }

        // 从当前地区向上追溯到省级
<span class="nc" id="L372">        List&lt;AdministrativeDivision&gt; hierarchy = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L373" title="All 2 branches missed.">        while (current != null) {</span>
<span class="nc" id="L374">            hierarchy.add(current);</span>
<span class="nc" id="L375">            current = divisionsCache.get(current.getParentCode());</span>
        }

        // 反转顺序，从省到区县
<span class="nc" id="L379">        Collections.reverse(hierarchy);</span>

<span class="nc bnc" id="L381" title="All 2 branches missed.">        for (AdministrativeDivision division : hierarchy) {</span>
<span class="nc" id="L382">            parts.add(division.getName());</span>
<span class="nc" id="L383">        }</span>

<span class="nc" id="L385">        return parts;</span>
    }

    /**
     * 获取邮编
     */
    private String getZipCode(String regionCode) {
<span class="nc" id="L392">        AdministrativeDivision division = divisionsCache.get(regionCode);</span>
<span class="nc bnc" id="L393" title="All 4 branches missed.">        if (division != null &amp;&amp; !division.getZipCode().isEmpty()) {</span>
<span class="nc" id="L394">            return division.getZipCode();</span>
        }

        // 如果没有具体邮编，生成一个合理的邮编
<span class="nc" id="L398">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L399">        int baseCode = Integer.parseInt(regionCode.substring(0, 2)) * 10000;</span>
<span class="nc" id="L400">        int offset = random.nextInt(1000, 9999);</span>
<span class="nc" id="L401">        return String.valueOf(baseCode + offset);</span>
    }

    // 工具方法
    private String getStringParam(Map&lt;String, Object&gt; params, String key, String defaultValue) {
<span class="nc" id="L406">        Object value = params.get(key);</span>
<span class="nc bnc" id="L407" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    private boolean getBooleanParam(Map&lt;String, Object&gt; params, String key, boolean defaultValue) {
<span class="nc" id="L411">        Object value = params.get(key);</span>
<span class="nc bnc" id="L412" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L413">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L415" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L416">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L418">        return defaultValue;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>