# 中国行政区划代码数据库
# 格式：代码:省份:城市:区县:权重(可选)

# 北京市
110000:北京市:北京市::20
110101:北京市:北京市:东城区:8
110102:北京市:北京市:西城区:8
110105:北京市:北京市:朝阳区:12
110106:北京市:北京市:丰台区:10
110107:北京市:北京市:石景山区:6
110108:北京市:北京市:海淀区:15
110109:北京市:北京市:门头沟区:3
110111:北京市:北京市:房山区:8
110112:北京市:北京市:通州区:10
110113:北京市:北京市:顺义区:8
110114:北京市:北京市:昌平区:12
110115:北京市:北京市:大兴区:10
110116:北京市:北京市:怀柔区:4
110117:北京市:北京市:平谷区:4
110118:北京市:北京市:密云区:4
110119:北京市:北京市:延庆区:3

# 天津市
120000:天津市:天津市::12
120101:天津市:天津市:和平区:5
120102:天津市:天津市:河东区:6
120103:天津市:天津市:河西区:7
120104:天津市:天津市:南开区:8
120105:天津市:天津市:河北区:6
120106:天津市:天津市:红桥区:5
120110:天津市:天津市:东丽区:6
120111:天津市:天津市:西青区:8
120112:天津市:天津市:津南区:7
120113:天津市:天津市:北辰区:8
120114:天津市:天津市:武清区:9
120115:天津市:天津市:宝坻区:7
120116:天津市:天津市:滨海新区:12
120117:天津市:天津市:宁河区:5
120118:天津市:天津市:静海区:6
120119:天津市:天津市:蓟州区:5

# 上海市
310000:上海市:上海市::18
310101:上海市:上海市:黄浦区:8
310104:上海市:上海市:徐汇区:10
310105:上海市:上海市:长宁区:8
310106:上海市:上海市:静安区:7
310107:上海市:上海市:普陀区:9
310109:上海市:上海市:虹口区:6
310110:上海市:上海市:杨浦区:10
310112:上海市:上海市:闵行区:12
310113:上海市:上海市:宝山区:9
310114:上海市:上海市:嘉定区:10
310115:上海市:上海市:浦东新区:20
310116:上海市:上海市:金山区:6
310117:上海市:上海市:松江区:11
310118:上海市:上海市:青浦区:8
310120:上海市:上海市:奉贤区:7
310151:上海市:上海市:崇明区:5

# 重庆市
500000:重庆市:重庆市::15
500101:重庆市:重庆市:万州区:8
500102:重庆市:重庆市:涪陵区:7
500103:重庆市:重庆市:渝中区:6
500104:重庆市:重庆市:大渡口区:5
500105:重庆市:重庆市:江北区:9
500106:重庆市:重庆市:沙坪坝区:10
500107:重庆市:重庆市:九龙坡区:11
500108:重庆市:重庆市:南岸区:8
500109:重庆市:重庆市:北碚区:6
500110:重庆市:重庆市:綦江区:5
500111:重庆市:重庆市:大足区:6
500112:重庆市:重庆市:渝北区:12
500113:重庆市:重庆市:巴南区:8
500114:重庆市:重庆市:黔江区:4
500115:重庆市:重庆市:长寿区:5
500116:重庆市:重庆市:江津区:7
500117:重庆市:重庆市:合川区:8
500118:重庆市:重庆市:永川区:7
500119:重庆市:重庆市:南川区:4

# 河北省
130000:河北省:::12
130100:河北省:石家庄市::10
130101:河北省:石家庄市:长安区:8
130102:河北省:石家庄市:桥西区:7
130104:河北省:石家庄市:新华区:8
130105:河北省:石家庄市:井陉矿区:3
130107:河北省:石家庄市:裕华区:9
130108:河北省:石家庄市:藁城区:6
130109:河北省:石家庄市:鹿泉区:5
130110:河北省:石家庄市:栾城区:4
130121:河北省:石家庄市:井陉县:3
130123:河北省:石家庄市:正定县:5
130125:河北省:石家庄市:行唐县:4
130126:河北省:石家庄市:灵寿县:3
130127:河北省:石家庄市:高邑县:2
130128:河北省:石家庄市:深泽县:2
130129:河北省:石家庄市:赞皇县:2
130130:河北省:石家庄市:无极县:3
130131:河北省:石家庄市:平山县:4
130132:河北省:石家庄市:元氏县:3
130133:河北省:石家庄市:赵县:3

# 山西省
140000:山西省:::8
140100:山西省:太原市::8
140101:山西省:太原市:小店区:6
140105:山西省:太原市:迎泽区:5
140106:山西省:太原市:杏花岭区:4
140107:河北省:太原市:尖草坪区:4
140108:山西省:太原市:万柏林区:6
140109:山西省:太原市:晋源区:5
140110:山西省:太原市:古交市:3
140121:山西省:太原市:清徐县:3
140122:山西省:太原市:阳曲县:2
140123:山西省:太原市:娄烦县:2

# 江苏省
320000:江苏省:::15
320100:江苏省:南京市::12
320101:江苏省:南京市:玄武区:6
320102:江苏省:南京市:秦淮区:7
320104:江苏省:南京市:建邺区:8
320105:江苏省:南京市:鼓楼区:7
320106:江苏省:南京市:浦口区:6
320111:江苏省:南京市:栖霞区:8
320113:江苏省:南京市:六合区:5
320114:江苏省:南京市:溧水区:4
320115:江苏省:南京市:高淳区:3
320200:江苏省:无锡市::10
320205:江苏省:无锡市:锡山区:6
320206:江苏省:无锡市:惠山区:7
320211:江苏省:无锡市:滨湖区:8
320213:江苏省:无锡市:梁溪区:9
320214:江苏省:无锡市:新吴区:10
320281:江苏省:无锡市:江阴市:8
320282:江苏省:无锡市:宜兴市:6

# 浙江省
330000:浙江省:::14
330100:浙江省:杭州市::12
330102:浙江省:杭州市:上城区:8
330105:浙江省:杭州市:拱墅区:7
330106:浙江省:杭州市:西湖区:10
330108:浙江省:杭州市:滨江区:9
330109:浙江省:杭州市:萧山区:11
330110:浙江省:杭州市:余杭区:12
330111:浙江省:杭州市:富阳区:6
330112:浙江省:杭州市:临安区:5
330113:浙江省:杭州市:临平区:8
330114:浙江省:杭州市:钱塘区:7
330122:浙江省:杭州市:桐庐县:3
330127:浙江省:杭州市:淳安县:2
330182:浙江省:杭州市:建德市:3

# 广东省
440000:广东省:::18
440100:广东省:广州市::15
440103:广东省:广州市:荔湾区:6
440104:广东省:广州市:越秀区:7
440105:广东省:广州市:海珠区:8
440106:广东省:广州市:天河区:12
440111:广东省:广州市:白云区:10
440112:广东省:广州市:黄埔区:9
440113:广东省:广州市:番禺区:11
440114:广东省:广州市:花都区:8
440115:广东省:广州市:南沙区:7
440117:广东省:广州市:从化区:4
440118:广东省:广州市:增城区:6
440200:广东省:韶关市::4
440300:广东省:深圳市::18
440303:广东省:深圳市:罗湖区:8
440304:广东省:深圳市:福田区:10
440305:广东省:深圳市:南山区:12
440306:广东省:深圳市:宝安区:15
440307:广东省:深圳市:龙岗区:12
440308:广东省:深圳市:盐田区:4
440309:广东省:深圳市:龙华区:10
440310:广东省:深圳市:坪山区:6
440311:广东省:深圳市:光明区:5

# 四川省
510000:四川省:::12
510100:四川省:成都市::12
510104:四川省:成都市:锦江区:8
510105:四川省:成都市:青羊区:7
510106:四川省:成都市:金牛区:9
510107:四川省:成都市:武侯区:10
510108:四川省:成都市:成华区:8
510112:四川省:成都市:龙泉驿区:7
510113:四川省:成都市:青白江区:4
510114:四川省:成都市:新都区:6
510115:四川省:成都市:温江区:5
510116:四川省:成都市:双流区:8
510117:四川省:成都市:郫都区:6
510118:四川省:成都市:新津区:4
510121:四川省:成都市:金堂县:4
510129:四川省:成都市:大邑县:3
510131:四川省:成都市:蒲江县:2
510132:四川省:成都市:都江堰市:5
510181:四川省:成都市:彭州市:4
510182:四川省:成都市:邛崃市:3
510183:四川省:成都市:崇州市:3
510184:四川省:成都市:简阳市:6