<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.dataforge.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <span class="el_package">com.dataforge.config</span></div><h1>com.dataforge.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">476 of 476</td><td class="ctr2">0%</td><td class="bar">44 of 44</td><td class="ctr2">0%</td><td class="ctr1">77</td><td class="ctr2">77</td><td class="ctr1">128</td><td class="ctr2">128</td><td class="ctr1">55</td><td class="ctr2">55</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a2"><a href="OutputConfig.java.html" class="el_source">OutputConfig.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="176" alt="176"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h0">49</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j0">22</td><td class="ctr2" id="k0">22</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a0"><a href="FieldConfigWrapper.java.html" class="el_source">FieldConfigWrapper.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="99" height="10" title="146" alt="146"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h1">41</td><td class="ctr2" id="i1">41</td><td class="ctr1" id="j2">14</td><td class="ctr2" id="k2">14</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="ForgeConfig.java.html" class="el_source">ForgeConfig.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="138" alt="138"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="14" alt="14"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">23</td><td class="ctr2" id="g2">23</td><td class="ctr1" id="h2">31</td><td class="ctr2" id="i2">31</td><td class="ctr1" id="j1">16</td><td class="ctr2" id="k1">16</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="SimpleFieldConfig.java.html" class="el_source">SimpleFieldConfig.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">3</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>