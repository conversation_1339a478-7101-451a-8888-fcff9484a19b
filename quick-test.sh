#!/bin/bash

# DataForge 快速测试脚本
# 用于验证新实现的生成器功能

set -e

echo "========================================="
echo "DataForge Quick Test"
echo "========================================="

# 确保项目已构建
if [ ! -f "data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar" ]; then
    echo "Building project..."
    mvn clean package -DskipTests -q
fi

# 创建测试输出目录
mkdir -p test-output

echo ""
echo "Testing individual generators..."
echo ""

# 测试UUID生成器
echo "1. Testing UUID Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "id:uuid"

echo ""
echo "2. Testing Name Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "name:name"

echo ""
echo "3. Testing Phone Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "phone:phone"

echo ""
echo "4. Testing Bank Card Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "bankcard:bankcard"

echo ""
echo "5. Testing ID Card Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "idcard:idcard"

echo ""
echo "6. Testing Email Generator:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format console --fields "email:email"

echo ""
echo "7. Testing Data Correlation (Name + ID Card + Email):"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 5 --format console --fields "name:name,idcard:idcard,email:email"

echo ""
echo "8. Testing All Generators Together:"
java -jar data-forge-cli/target/data-forge-cli-1.0.0-SNAPSHOT.jar \
    --count 3 --format csv --output test-output/all-generators.csv \
    --fields "id:uuid,name:name,phone:phone,bankcard:bankcard,idcard:idcard,email:email"

echo ""
echo "========================================="
echo "Quick Test Completed!"
echo "========================================="

echo ""
echo "Generated test file:"
if [ -f "test-output/all-generators.csv" ]; then
    echo "✅ test-output/all-generators.csv"
    echo ""
    echo "Sample content:"
    head -5 test-output/all-generators.csv
else
    echo "❌ Failed to generate test file"
fi

echo ""
echo "To run comprehensive tests:"
echo "./run-example.sh"