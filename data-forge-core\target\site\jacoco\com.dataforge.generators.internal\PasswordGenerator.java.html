<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PasswordGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">PasswordGenerator.java</span></div><h1>PasswordGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 密码生成器
 * 
 * 支持功能：
 * 1. 指定长度和复杂度的密码生成
 * 2. 支持多种复杂度级别
 * 3. 支持常见弱密码生成（用于安全测试）
 * 4. 支持密码强度评估
 * 5. 支持自定义字符集
 * 
 * 参数配置：
 * - length: 密码长度范围 &quot;min,max&quot;（默认&quot;8,16&quot;）
 * - complexity: 复杂度级别 LOW|MEDIUM|HIGH|CUSTOM（默认MEDIUM）
 * - custom_chars: 自定义字符集（当complexity=CUSTOM时使用）
 * - include_weak: 是否包含常见弱密码（默认false）
 * - weak_ratio: 弱密码占比 0.0-1.0（默认0.1）
 * - require_uppercase: 是否必须包含大写字母（默认false）
 * - require_lowercase: 是否必须包含小写字母（默认false）
 * - require_digits: 是否必须包含数字（默认false）
 * - require_special: 是否必须包含特殊字符（默认false）
 * - exclude_ambiguous: 是否排除易混淆字符（默认false）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L37">public class PasswordGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L39">    private static final Logger log = LoggerFactory.getLogger(PasswordGenerator.class);</span>

    private static final String TYPE = &quot;password&quot;;
    private static final String DEFAULT_LENGTH = &quot;8,16&quot;;
    private static final String DEFAULT_COMPLEXITY = &quot;MEDIUM&quot;;
    private static final boolean DEFAULT_INCLUDE_WEAK = false;
    private static final double DEFAULT_WEAK_RATIO = 0.1;

    // 字符集定义
    private static final String LOWERCASE_CHARS = &quot;abcdefghijklmnopqrstuvwxyz&quot;;
    private static final String UPPERCASE_CHARS = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;;
    private static final String DIGIT_CHARS = &quot;0123456789&quot;;
    private static final String SIMPLE_SPECIAL_CHARS = &quot;!@#$%^&amp;*&quot;;

    // 易混淆字符
    private static final String AMBIGUOUS_CHARS = &quot;0O1lI|&quot;;

    // 常见弱密码列表
<span class="nc" id="L57">    private static final List&lt;String&gt; WEAK_PASSWORDS = Arrays.asList(</span>
            &quot;123456&quot;, &quot;password&quot;, &quot;123456789&quot;, &quot;12345678&quot;, &quot;12345&quot;, &quot;1234567&quot;, &quot;1234567890&quot;,
            &quot;qwerty&quot;, &quot;abc123&quot;, &quot;111111&quot;, &quot;123123&quot;, &quot;admin&quot;, &quot;letmein&quot;, &quot;welcome&quot;, &quot;monkey&quot;,
            &quot;dragon&quot;, &quot;pass&quot;, &quot;master&quot;, &quot;hello&quot;, &quot;freedom&quot;, &quot;whatever&quot;, &quot;qazwsx&quot;, &quot;trustno1&quot;,
            &quot;jordan&quot;, &quot;harley&quot;, &quot;1234&quot;, &quot;robert&quot;, &quot;matthew&quot;, &quot;jordan23&quot;, &quot;daniel&quot;, &quot;andrew&quot;,
            &quot;joshua&quot;, &quot;1qaz2wsx&quot;, &quot;shadow&quot;, &quot;hunter&quot;, &quot;michael&quot;, &quot;tigger&quot;, &quot;123qwe&quot;, &quot;football&quot;,
            &quot;password1&quot;, &quot;123456a&quot;, &quot;a123456&quot;, &quot;123abc&quot;, &quot;abc123456&quot;, &quot;password123&quot;, &quot;admin123&quot;,
            &quot;root123&quot;, &quot;test123&quot;, &quot;user123&quot;, &quot;guest123&quot;, &quot;demo123&quot;, &quot;temp123&quot;, &quot;pass123&quot;);

    // 复杂度级别定义
<span class="nc" id="L67">    public enum ComplexityLevel {</span>
<span class="nc" id="L68">        LOW, // 纯数字或纯字母</span>
<span class="nc" id="L69">        MEDIUM, // 数字+字母</span>
<span class="nc" id="L70">        HIGH, // 数字+大小写字母+特殊字符</span>
<span class="nc" id="L71">        CUSTOM // 自定义字符集</span>
    }

    @Override
    public String getType() {
<span class="nc" id="L76">        return TYPE;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L81">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L86">        Map&lt;String, Object&gt; params = config.getParams();</span>

        // 解析配置参数
<span class="nc" id="L89">        String lengthStr = getStringParam(params, &quot;length&quot;, DEFAULT_LENGTH);</span>
<span class="nc" id="L90">        String complexityStr = getStringParam(params, &quot;complexity&quot;, DEFAULT_COMPLEXITY);</span>
<span class="nc" id="L91">        String customChars = getStringParam(params, &quot;custom_chars&quot;, &quot;&quot;);</span>
<span class="nc" id="L92">        boolean includeWeak = getBooleanParam(params, &quot;include_weak&quot;, DEFAULT_INCLUDE_WEAK);</span>
<span class="nc" id="L93">        double weakRatio = getDoubleParam(params, &quot;weak_ratio&quot;, DEFAULT_WEAK_RATIO);</span>
<span class="nc" id="L94">        boolean requireUppercase = getBooleanParam(params, &quot;require_uppercase&quot;, false);</span>
<span class="nc" id="L95">        boolean requireLowercase = getBooleanParam(params, &quot;require_lowercase&quot;, false);</span>
<span class="nc" id="L96">        boolean requireDigits = getBooleanParam(params, &quot;require_digits&quot;, false);</span>
<span class="nc" id="L97">        boolean requireSpecial = getBooleanParam(params, &quot;require_special&quot;, false);</span>
<span class="nc" id="L98">        boolean excludeAmbiguous = getBooleanParam(params, &quot;exclude_ambiguous&quot;, false);</span>

        // 解析长度范围
<span class="nc" id="L101">        int[] lengthRange = parseLengthRange(lengthStr);</span>
<span class="nc" id="L102">        int minLength = lengthRange[0];</span>
<span class="nc" id="L103">        int maxLength = lengthRange[1];</span>

        // 解析复杂度级别
        ComplexityLevel complexity;
        try {
<span class="nc" id="L108">            complexity = ComplexityLevel.valueOf(complexityStr.toUpperCase());</span>
<span class="nc" id="L109">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L110">            log.warn(&quot;Invalid complexity level: {}. Using MEDIUM.&quot;, complexityStr);</span>
<span class="nc" id="L111">            complexity = ComplexityLevel.MEDIUM;</span>
<span class="nc" id="L112">        }</span>

        // 参数校验
<span class="nc" id="L115">        weakRatio = Math.max(0.0, Math.min(1.0, weakRatio));</span>

        // 决定是否生成弱密码
<span class="nc" id="L118">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc bnc" id="L119" title="All 4 branches missed.">        if (includeWeak &amp;&amp; random.nextDouble() &lt; weakRatio) {</span>
<span class="nc" id="L120">            return generateWeakPassword(minLength, maxLength);</span>
        }

        // 生成强密码
<span class="nc" id="L124">        return generateStrongPassword(minLength, maxLength, complexity, customChars,</span>
                requireUppercase, requireLowercase, requireDigits,
                requireSpecial, excludeAmbiguous);
    }

    /**
     * 解析长度范围
     */
    private int[] parseLengthRange(String lengthStr) {
        try {
<span class="nc bnc" id="L134" title="All 2 branches missed.">            if (lengthStr.contains(&quot;,&quot;)) {</span>
<span class="nc" id="L135">                String[] parts = lengthStr.split(&quot;,&quot;);</span>
<span class="nc" id="L136">                int min = Integer.parseInt(parts[0].trim());</span>
<span class="nc" id="L137">                int max = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L138">                return new int[] { Math.max(1, min), Math.max(min, max) };</span>
            } else {
<span class="nc" id="L140">                int length = Integer.parseInt(lengthStr.trim());</span>
<span class="nc" id="L141">                return new int[] { Math.max(1, length), Math.max(1, length) };</span>
            }
<span class="nc" id="L143">        } catch (Exception e) {</span>
<span class="nc" id="L144">            log.warn(&quot;Invalid length parameter: {}. Using default.&quot;, lengthStr);</span>
<span class="nc" id="L145">            return new int[] { 8, 16 };</span>
        }
    }

    /**
     * 生成弱密码
     */
    private String generateWeakPassword(int minLength, int maxLength) {
<span class="nc" id="L153">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>

        // 从弱密码列表中选择合适长度的密码
<span class="nc" id="L156">        List&lt;String&gt; suitablePasswords = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L157" title="All 2 branches missed.">        for (String weakPassword : WEAK_PASSWORDS) {</span>
<span class="nc bnc" id="L158" title="All 4 branches missed.">            if (weakPassword.length() &gt;= minLength &amp;&amp; weakPassword.length() &lt;= maxLength) {</span>
<span class="nc" id="L159">                suitablePasswords.add(weakPassword);</span>
            }
<span class="nc" id="L161">        }</span>

<span class="nc bnc" id="L163" title="All 2 branches missed.">        if (!suitablePasswords.isEmpty()) {</span>
<span class="nc" id="L164">            return suitablePasswords.get(random.nextInt(suitablePasswords.size()));</span>
        }

        // 如果没有合适的弱密码，生成简单的数字密码
<span class="nc" id="L168">        int length = random.nextInt(minLength, maxLength + 1);</span>
<span class="nc" id="L169">        StringBuilder password = new StringBuilder();</span>

        // 生成简单的重复数字或连续数字
<span class="nc bnc" id="L172" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
            // 重复数字
<span class="nc" id="L174">            char digit = (char) ('0' + random.nextInt(10));</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">            for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L176">                password.append(digit);</span>
            }
<span class="nc" id="L178">        } else {</span>
            // 连续数字
<span class="nc" id="L180">            int start = random.nextInt(10 - Math.min(length, 9));</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">            for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L182">                password.append((char) ('0' + (start + i) % 10));</span>
            }
        }

<span class="nc" id="L186">        return password.toString();</span>
    }

    /**
     * 生成强密码
     */
    private String generateStrongPassword(int minLength, int maxLength, ComplexityLevel complexity,
            String customChars, boolean requireUppercase,
            boolean requireLowercase, boolean requireDigits,
            boolean requireSpecial, boolean excludeAmbiguous) {

<span class="nc" id="L197">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L198">        int length = random.nextInt(minLength, maxLength + 1);</span>

        // 构建字符集
<span class="nc" id="L201">        String charSet = buildCharSet(complexity, customChars, excludeAmbiguous);</span>

        // 构建必需字符集合
<span class="nc" id="L204">        List&lt;String&gt; requiredCharSets = buildRequiredCharSets(complexity, requireUppercase,</span>
                requireLowercase, requireDigits,
                requireSpecial, excludeAmbiguous);

        // 生成密码
<span class="nc" id="L209">        StringBuilder password = new StringBuilder();</span>

        // 首先确保包含所有必需的字符类型
<span class="nc bnc" id="L212" title="All 2 branches missed.">        for (String requiredSet : requiredCharSets) {</span>
<span class="nc bnc" id="L213" title="All 4 branches missed.">            if (password.length() &lt; length &amp;&amp; !requiredSet.isEmpty()) {</span>
<span class="nc" id="L214">                password.append(requiredSet.charAt(random.nextInt(requiredSet.length())));</span>
            }
<span class="nc" id="L216">        }</span>

        // 填充剩余长度
<span class="nc bnc" id="L219" title="All 2 branches missed.">        while (password.length() &lt; length) {</span>
<span class="nc" id="L220">            password.append(charSet.charAt(random.nextInt(charSet.length())));</span>
        }

        // 打乱字符顺序
<span class="nc" id="L224">        return shuffleString(password.toString(), random);</span>
    }

    /**
     * 构建字符集
     */
    private String buildCharSet(ComplexityLevel complexity, String customChars, boolean excludeAmbiguous) {
<span class="nc" id="L231">        StringBuilder charSet = new StringBuilder();</span>

<span class="nc bnc" id="L233" title="All 5 branches missed.">        switch (complexity) {</span>
            case LOW:
                // 随机选择纯数字或纯字母
<span class="nc bnc" id="L236" title="All 2 branches missed.">                if (ThreadLocalRandom.current().nextBoolean()) {</span>
<span class="nc" id="L237">                    charSet.append(DIGIT_CHARS);</span>
                } else {
<span class="nc" id="L239">                    charSet.append(LOWERCASE_CHARS);</span>
                }
<span class="nc" id="L241">                break;</span>

            case MEDIUM:
<span class="nc" id="L244">                charSet.append(LOWERCASE_CHARS)</span>
<span class="nc" id="L245">                        .append(UPPERCASE_CHARS)</span>
<span class="nc" id="L246">                        .append(DIGIT_CHARS);</span>
<span class="nc" id="L247">                break;</span>

            case HIGH:
<span class="nc" id="L250">                charSet.append(LOWERCASE_CHARS)</span>
<span class="nc" id="L251">                        .append(UPPERCASE_CHARS)</span>
<span class="nc" id="L252">                        .append(DIGIT_CHARS)</span>
<span class="nc" id="L253">                        .append(SIMPLE_SPECIAL_CHARS);</span>
<span class="nc" id="L254">                break;</span>

            case CUSTOM:
<span class="nc bnc" id="L257" title="All 2 branches missed.">                if (!customChars.isEmpty()) {</span>
<span class="nc" id="L258">                    charSet.append(customChars);</span>
                } else {
                    // 如果没有提供自定义字符集，使用MEDIUM级别
<span class="nc" id="L261">                    charSet.append(LOWERCASE_CHARS)</span>
<span class="nc" id="L262">                            .append(UPPERCASE_CHARS)</span>
<span class="nc" id="L263">                            .append(DIGIT_CHARS);</span>
                }
                break;
        }

        // 排除易混淆字符
<span class="nc bnc" id="L269" title="All 2 branches missed.">        if (excludeAmbiguous) {</span>
<span class="nc" id="L270">            String result = charSet.toString();</span>
<span class="nc bnc" id="L271" title="All 2 branches missed.">            for (char ambiguous : AMBIGUOUS_CHARS.toCharArray()) {</span>
<span class="nc" id="L272">                result = result.replace(String.valueOf(ambiguous), &quot;&quot;);</span>
            }
<span class="nc" id="L274">            return result;</span>
        }

<span class="nc" id="L277">        return charSet.toString();</span>
    }

    /**
     * 构建必需字符集合
     */
    private List&lt;String&gt; buildRequiredCharSets(ComplexityLevel complexity, boolean requireUppercase,
            boolean requireLowercase, boolean requireDigits,
            boolean requireSpecial, boolean excludeAmbiguous) {

<span class="nc" id="L287">        List&lt;String&gt; requiredSets = new ArrayList&lt;&gt;();</span>

        // 根据复杂度级别自动确定要求
<span class="nc bnc" id="L290" title="All 4 branches missed.">        if (complexity == ComplexityLevel.MEDIUM || complexity == ComplexityLevel.HIGH) {</span>
<span class="nc" id="L291">            requireLowercase = true;</span>
<span class="nc" id="L292">            requireUppercase = true;</span>
<span class="nc" id="L293">            requireDigits = true;</span>
        }

<span class="nc bnc" id="L296" title="All 2 branches missed.">        if (complexity == ComplexityLevel.HIGH) {</span>
<span class="nc" id="L297">            requireSpecial = true;</span>
        }

        // 构建必需字符集
<span class="nc bnc" id="L301" title="All 2 branches missed.">        if (requireLowercase) {</span>
<span class="nc bnc" id="L302" title="All 2 branches missed.">            String chars = excludeAmbiguous ? LOWERCASE_CHARS.replaceAll(&quot;[&quot; + AMBIGUOUS_CHARS + &quot;]&quot;, &quot;&quot;)</span>
<span class="nc" id="L303">                    : LOWERCASE_CHARS;</span>
<span class="nc" id="L304">            requiredSets.add(chars);</span>
        }

<span class="nc bnc" id="L307" title="All 2 branches missed.">        if (requireUppercase) {</span>
<span class="nc bnc" id="L308" title="All 2 branches missed.">            String chars = excludeAmbiguous ? UPPERCASE_CHARS.replaceAll(&quot;[&quot; + AMBIGUOUS_CHARS + &quot;]&quot;, &quot;&quot;)</span>
<span class="nc" id="L309">                    : UPPERCASE_CHARS;</span>
<span class="nc" id="L310">            requiredSets.add(chars);</span>
        }

<span class="nc bnc" id="L313" title="All 2 branches missed.">        if (requireDigits) {</span>
<span class="nc bnc" id="L314" title="All 2 branches missed.">            String chars = excludeAmbiguous ? DIGIT_CHARS.replaceAll(&quot;[&quot; + AMBIGUOUS_CHARS + &quot;]&quot;, &quot;&quot;) : DIGIT_CHARS;</span>
<span class="nc" id="L315">            requiredSets.add(chars);</span>
        }

<span class="nc bnc" id="L318" title="All 2 branches missed.">        if (requireSpecial) {</span>
<span class="nc" id="L319">            requiredSets.add(SIMPLE_SPECIAL_CHARS);</span>
        }

<span class="nc" id="L322">        return requiredSets;</span>
    }

    /**
     * 打乱字符串
     */
    private String shuffleString(String input, ThreadLocalRandom random) {
<span class="nc" id="L329">        List&lt;Character&gt; characters = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L330" title="All 2 branches missed.">        for (char c : input.toCharArray()) {</span>
<span class="nc" id="L331">            characters.add(c);</span>
        }

<span class="nc" id="L334">        Collections.shuffle(characters, random);</span>

<span class="nc" id="L336">        StringBuilder result = new StringBuilder();</span>
<span class="nc bnc" id="L337" title="All 2 branches missed.">        for (char c : characters) {</span>
<span class="nc" id="L338">            result.append(c);</span>
<span class="nc" id="L339">        }</span>

<span class="nc" id="L341">        return result.toString();</span>
    }

    // 工具方法
    private String getStringParam(Map&lt;String, Object&gt; params, String key, String defaultValue) {
<span class="nc" id="L346">        Object value = params.get(key);</span>
<span class="nc bnc" id="L347" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    private boolean getBooleanParam(Map&lt;String, Object&gt; params, String key, boolean defaultValue) {
<span class="nc" id="L351">        Object value = params.get(key);</span>
<span class="nc bnc" id="L352" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L353">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L355" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L356">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L358">        return defaultValue;</span>
    }

    private double getDoubleParam(Map&lt;String, Object&gt; params, String key, double defaultValue) {
<span class="nc" id="L362">        Object value = params.get(key);</span>
<span class="nc bnc" id="L363" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L364">            return ((Number) value).doubleValue();</span>
        }
<span class="nc bnc" id="L366" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L368">                return Double.parseDouble((String) value);</span>
<span class="nc" id="L369">            } catch (NumberFormatException e) {</span>
<span class="nc" id="L370">                log.warn(&quot;Invalid double parameter '{}': {}&quot;, key, value);</span>
            }
        }
<span class="nc" id="L373">        return defaultValue;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>