<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConsoleOutputStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.io</a> &gt; <span class="el_source">ConsoleOutputStrategy.java</span></div><h1>ConsoleOutputStrategy.java</h1><pre class="source lang-java linenums">package com.dataforge.io;

import com.dataforge.config.OutputConfig;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 控制台输出策略实现。
 * 
 * &lt;p&gt;
 * 将生成的数据以表格形式输出到控制台（标准输出）。
 * 支持自动列宽调整和美观的表格格式。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L22">public class ConsoleOutputStrategy implements OutputStrategy {</span>

<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(ConsoleOutputStrategy.class);</span>

    /**
     * 字段名称列表。
     */
    private List&lt;String&gt; fieldNames;

    /**
     * 已输出的记录数量。
     */
<span class="nc" id="L34">    private long recordCount = 0;</span>

    /**
     * 是否已输出表头。
     */
<span class="nc" id="L39">    private boolean headerPrinted = false;</span>

    /**
     * 列分隔符。
     */
    private static final String COLUMN_SEPARATOR = &quot; | &quot;;

    /**
     * 默认列宽。
     */
    private static final int DEFAULT_COLUMN_WIDTH = 15;

    @Override
    public OutputConfig.Format getSupportedFormat() {
<span class="nc" id="L53">        return OutputConfig.Format.CONSOLE;</span>
    }

    @Override
    public void initialize(OutputConfig config, List&lt;String&gt; fieldNames) throws OutputException {
<span class="nc bnc" id="L58" title="All 4 branches missed.">        if (fieldNames == null || fieldNames.isEmpty()) {</span>
<span class="nc" id="L59">            throw new OutputException(&quot;Field names cannot be null or empty for console output&quot;);</span>
        }

<span class="nc" id="L62">        this.fieldNames = fieldNames;</span>
<span class="nc" id="L63">        this.recordCount = 0;</span>
<span class="nc" id="L64">        this.headerPrinted = false;</span>

<span class="nc" id="L66">        logger.debug(&quot;Console output strategy initialized with {} fields: {}&quot;, fieldNames.size(), fieldNames);</span>
<span class="nc" id="L67">    }</span>

    @Override
    public void writeRecord(Map&lt;String, Object&gt; record) throws OutputException {
<span class="nc bnc" id="L71" title="All 2 branches missed.">        if (record == null) {</span>
<span class="nc" id="L72">            throw new OutputException(&quot;Record cannot be null&quot;);</span>
        }

        try {
            // 首次输出时打印表头
<span class="nc bnc" id="L77" title="All 2 branches missed.">            if (!headerPrinted) {</span>
<span class="nc" id="L78">                printHeader();</span>
<span class="nc" id="L79">                printSeparator();</span>
<span class="nc" id="L80">                headerPrinted = true;</span>
            }

            // 输出数据行
<span class="nc" id="L84">            printDataRow(record);</span>
<span class="nc" id="L85">            recordCount++;</span>

<span class="nc" id="L87">        } catch (Exception e) {</span>
<span class="nc" id="L88">            throw new OutputException(&quot;Failed to write record to console&quot;, e);</span>
<span class="nc" id="L89">        }</span>
<span class="nc" id="L90">    }</span>

    @Override
    public void finish() throws OutputException {
        try {
<span class="nc bnc" id="L95" title="All 2 branches missed.">            if (headerPrinted) {</span>
<span class="nc" id="L96">                printSeparator();</span>
<span class="nc" id="L97">                System.out.println(&quot;Total records: &quot; + recordCount);</span>
            } else {
<span class="nc" id="L99">                System.out.println(&quot;No records to display.&quot;);</span>
            }

            // 刷新输出流
<span class="nc" id="L103">            System.out.flush();</span>

<span class="nc" id="L105">            logger.info(&quot;Console output completed. Total records: {}&quot;, recordCount);</span>

<span class="nc" id="L107">        } catch (Exception e) {</span>
<span class="nc" id="L108">            throw new OutputException(&quot;Failed to finish console output&quot;, e);</span>
<span class="nc" id="L109">        }</span>
<span class="nc" id="L110">    }</span>

    @Override
    public void flush() throws OutputException {
<span class="nc" id="L114">        System.out.flush();</span>
<span class="nc" id="L115">    }</span>

    @Override
    public long getWrittenRecordCount() {
<span class="nc" id="L119">        return recordCount;</span>
    }

    /**
     * 打印表头。
     */
    private void printHeader() {
<span class="nc" id="L126">        StringJoiner joiner = new StringJoiner(COLUMN_SEPARATOR);</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">        for (String fieldName : fieldNames) {</span>
<span class="nc" id="L128">            joiner.add(formatColumn(fieldName, DEFAULT_COLUMN_WIDTH));</span>
<span class="nc" id="L129">        }</span>
<span class="nc" id="L130">        System.out.println(joiner.toString());</span>
<span class="nc" id="L131">    }</span>

    /**
     * 打印分隔线。
     */
    private void printSeparator() {
<span class="nc" id="L137">        StringJoiner joiner = new StringJoiner(COLUMN_SEPARATOR);</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">        for (String fieldName : fieldNames) {</span>
<span class="nc" id="L139">            joiner.add(&quot;-&quot;.repeat(Math.max(DEFAULT_COLUMN_WIDTH, fieldName.length())));</span>
<span class="nc" id="L140">        }</span>
<span class="nc" id="L141">        System.out.println(joiner.toString());</span>
<span class="nc" id="L142">    }</span>

    /**
     * 打印数据行。
     * 
     * @param record 记录数据
     */
    private void printDataRow(Map&lt;String, Object&gt; record) {
<span class="nc" id="L150">        StringJoiner joiner = new StringJoiner(COLUMN_SEPARATOR);</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">        for (String fieldName : fieldNames) {</span>
<span class="nc" id="L152">            Object value = record.get(fieldName);</span>
<span class="nc" id="L153">            String displayValue = formatValue(value);</span>
<span class="nc" id="L154">            joiner.add(formatColumn(displayValue, DEFAULT_COLUMN_WIDTH));</span>
<span class="nc" id="L155">        }</span>
<span class="nc" id="L156">        System.out.println(joiner.toString());</span>
<span class="nc" id="L157">    }</span>

    /**
     * 格式化列内容，确保固定宽度。
     * 
     * @param content 列内容
     * @param width   列宽
     * @return 格式化后的列内容
     */
    private String formatColumn(String content, int width) {
<span class="nc bnc" id="L167" title="All 2 branches missed.">        if (content == null) {</span>
<span class="nc" id="L168">            content = &quot;&quot;;</span>
        }

        // 如果内容超过列宽，截断并添加省略号
<span class="nc bnc" id="L172" title="All 2 branches missed.">        if (content.length() &gt; width) {</span>
<span class="nc" id="L173">            return content.substring(0, width - 3) + &quot;...&quot;;</span>
        }

        // 左对齐，右侧填充空格
<span class="nc" id="L177">        return String.format(&quot;%-&quot; + width + &quot;s&quot;, content);</span>
    }

    /**
     * 格式化字段值为字符串。
     * 
     * @param value 字段值
     * @return 格式化后的字符串
     */
    private String formatValue(Object value) {
<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L188">            return &quot;null&quot;;</span>
        }

<span class="nc" id="L191">        String str = value.toString();</span>

        // 处理换行符，替换为空格
<span class="nc" id="L194">        str = str.replace(&quot;\n&quot;, &quot; &quot;).replace(&quot;\r&quot;, &quot; &quot;);</span>

        // 处理制表符，替换为空格
<span class="nc" id="L197">        str = str.replace(&quot;\t&quot;, &quot; &quot;);</span>

<span class="nc" id="L199">        return str.trim();</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L204">        return &quot;Console output strategy - displays data in tabular format to standard output&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>