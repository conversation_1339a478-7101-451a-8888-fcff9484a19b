<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GeolocationGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">GeolocationGenerator.java</span></div><h1>GeolocationGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * 地理坐标生成器
 * 
 * &lt;p&gt;
 * 支持生成地理坐标信息，包括经度、纬度、高度等，用于位置服务测试、
 * 地图应用开发、LBS功能测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;format: 输出格式 (DECIMAL|DMS|JSON|WKT) 默认: DECIMAL&lt;/li&gt;
 * &lt;li&gt;latitude_min: 最小纬度 默认: -90.0&lt;/li&gt;
 * &lt;li&gt;latitude_max: 最大纬度 默认: 90.0&lt;/li&gt;
 * &lt;li&gt;longitude_min: 最小经度 默认: -180.0&lt;/li&gt;
 * &lt;li&gt;longitude_max: 最大经度 默认: 180.0&lt;/li&gt;
 * &lt;li&gt;altitude_min: 最小高度（米）默认: -500.0&lt;/li&gt;
 * &lt;li&gt;altitude_max: 最大高度（米）默认: 10000.0&lt;/li&gt;
 * &lt;li&gt;precision: 小数点后位数 默认: 6&lt;/li&gt;
 * &lt;li&gt;include_altitude: 是否包含高度 默认: false&lt;/li&gt;
 * &lt;li&gt;center_lat: 中心纬度（区域生成）&lt;/li&gt;
 * &lt;li&gt;center_lon: 中心经度（区域生成）&lt;/li&gt;
 * &lt;li&gt;radius_km: 半径（公里，区域生成）&lt;/li&gt;
 * &lt;li&gt;region: 预设区域 (CHINA|USA|EUROPE|WORLD) 默认: WORLD&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L42">public class GeolocationGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L44">    private static final Logger logger = LoggerFactory.getLogger(GeolocationGenerator.class);</span>
<span class="fc" id="L45">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 输出格式枚举
<span class="fc" id="L48">    public enum OutputFormat {</span>
<span class="fc" id="L49">        DECIMAL(&quot;小数格式&quot;),</span>
<span class="fc" id="L50">        DMS(&quot;度分秒格式&quot;),</span>
<span class="fc" id="L51">        JSON(&quot;JSON格式&quot;),</span>
<span class="fc" id="L52">        WKT(&quot;WKT格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L56">        OutputFormat(String description) {</span>
<span class="fc" id="L57">            this.description = description;</span>
<span class="fc" id="L58">        }</span>
        
        public String getDescription() {
<span class="nc" id="L61">            return description;</span>
        }
    }
    
    // 预设区域枚举
<span class="fc" id="L66">    public enum Region {</span>
<span class="fc" id="L67">        CHINA(&quot;中国&quot;, 18.0, 54.0, 73.0, 135.0),</span>
<span class="fc" id="L68">        USA(&quot;美国&quot;, 24.0, 49.0, -125.0, -66.0),</span>
<span class="fc" id="L69">        EUROPE(&quot;欧洲&quot;, 35.0, 71.0, -10.0, 40.0),</span>
<span class="fc" id="L70">        WORLD(&quot;全球&quot;, -90.0, 90.0, -180.0, 180.0);</span>
        
        private final String description;
        private final double minLat;
        private final double maxLat;
        private final double minLon;
        private final double maxLon;
        
<span class="fc" id="L78">        Region(String description, double minLat, double maxLat, double minLon, double maxLon) {</span>
<span class="fc" id="L79">            this.description = description;</span>
<span class="fc" id="L80">            this.minLat = minLat;</span>
<span class="fc" id="L81">            this.maxLat = maxLat;</span>
<span class="fc" id="L82">            this.minLon = minLon;</span>
<span class="fc" id="L83">            this.maxLon = maxLon;</span>
<span class="fc" id="L84">        }</span>
        
<span class="nc" id="L86">        public String getDescription() { return description; }</span>
<span class="fc" id="L87">        public double getMinLat() { return minLat; }</span>
<span class="fc" id="L88">        public double getMaxLat() { return maxLat; }</span>
<span class="fc" id="L89">        public double getMinLon() { return minLon; }</span>
<span class="fc" id="L90">        public double getMaxLon() { return maxLon; }</span>
    }
    
    // 地理坐标信息类
    public static class GeoLocation {
        private final double latitude;
        private final double longitude;
        private final Double altitude;
        
<span class="fc" id="L99">        public GeoLocation(double latitude, double longitude, Double altitude) {</span>
<span class="fc" id="L100">            this.latitude = latitude;</span>
<span class="fc" id="L101">            this.longitude = longitude;</span>
<span class="fc" id="L102">            this.altitude = altitude;</span>
<span class="fc" id="L103">        }</span>
        
<span class="fc" id="L105">        public double getLatitude() { return latitude; }</span>
<span class="fc" id="L106">        public double getLongitude() { return longitude; }</span>
<span class="fc" id="L107">        public Double getAltitude() { return altitude; }</span>
    }

    @Override
    public String getType() {
<span class="fc" id="L112">        return &quot;geolocation&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L117">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取输出格式
<span class="fc" id="L124">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;DECIMAL&quot;);</span>
<span class="fc" id="L125">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 获取精度
<span class="fc" id="L128">            int precision = getIntParam(config, &quot;precision&quot;, 6);</span>
            
            // 获取是否包含高度
<span class="fc" id="L131">            boolean includeAltitude = getBooleanParam(config, &quot;include_altitude&quot;, false);</span>
            
            // 生成地理坐标
<span class="fc" id="L134">            GeoLocation location = generateGeoLocation(config, includeAltitude);</span>
            
            // 格式化输出
<span class="fc" id="L137">            return formatGeoLocation(location, format, precision);</span>
            
<span class="nc" id="L139">        } catch (Exception e) {</span>
<span class="nc" id="L140">            logger.error(&quot;Failed to generate geolocation&quot;, e);</span>
            // 返回一个默认坐标作为fallback（北京天安门）
<span class="nc" id="L142">            return &quot;39.908722,116.397499&quot;;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L151">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L152">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L153">            logger.warn(&quot;Invalid output format: {}, using DECIMAL as default&quot;, formatStr);</span>
<span class="nc" id="L154">            return OutputFormat.DECIMAL;</span>
        }
    }

    /**
     * 生成地理坐标
     */
    private GeoLocation generateGeoLocation(FieldConfig config, boolean includeAltitude) {
        double latitude, longitude;
<span class="fc" id="L163">        Double altitude = null;</span>
        
        // 检查是否指定了中心点和半径（区域生成）
<span class="fc" id="L166">        String centerLatStr = getStringParam(config, &quot;center_lat&quot;, null);</span>
<span class="fc" id="L167">        String centerLonStr = getStringParam(config, &quot;center_lon&quot;, null);</span>
<span class="fc" id="L168">        String radiusStr = getStringParam(config, &quot;radius_km&quot;, null);</span>
        
<span class="pc bpc" id="L170" title="5 of 6 branches missed.">        if (centerLatStr != null &amp;&amp; centerLonStr != null &amp;&amp; radiusStr != null) {</span>
            // 区域生成模式
<span class="nc" id="L172">            double centerLat = Double.parseDouble(centerLatStr);</span>
<span class="nc" id="L173">            double centerLon = Double.parseDouble(centerLonStr);</span>
<span class="nc" id="L174">            double radiusKm = Double.parseDouble(radiusStr);</span>
            
<span class="nc" id="L176">            double[] coords = generateWithinRadius(centerLat, centerLon, radiusKm);</span>
<span class="nc" id="L177">            latitude = coords[0];</span>
<span class="nc" id="L178">            longitude = coords[1];</span>
<span class="nc" id="L179">        } else {</span>
            // 范围生成模式
<span class="fc" id="L181">            double[] bounds = getBounds(config);</span>
<span class="fc" id="L182">            latitude = bounds[0] + random.nextDouble() * (bounds[1] - bounds[0]);</span>
<span class="fc" id="L183">            longitude = bounds[2] + random.nextDouble() * (bounds[3] - bounds[2]);</span>
        }
        
        // 生成高度
<span class="fc bfc" id="L187" title="All 2 branches covered.">        if (includeAltitude) {</span>
<span class="fc" id="L188">            double altitudeMin = getDoubleParam(config, &quot;altitude_min&quot;, -500.0);</span>
<span class="fc" id="L189">            double altitudeMax = getDoubleParam(config, &quot;altitude_max&quot;, 10000.0);</span>
<span class="fc" id="L190">            altitude = altitudeMin + random.nextDouble() * (altitudeMax - altitudeMin);</span>
        }
        
<span class="fc" id="L193">        return new GeoLocation(latitude, longitude, altitude);</span>
    }

    /**
     * 获取坐标边界
     */
    private double[] getBounds(FieldConfig config) {
        // 检查是否指定了预设区域
<span class="fc" id="L201">        String regionStr = getStringParam(config, &quot;region&quot;, &quot;WORLD&quot;);</span>
        try {
<span class="fc" id="L203">            Region region = Region.valueOf(regionStr.toUpperCase());</span>
<span class="fc" id="L204">            return new double[]{region.getMinLat(), region.getMaxLat(), region.getMinLon(), region.getMaxLon()};</span>
<span class="nc" id="L205">        } catch (IllegalArgumentException e) {</span>
            // 使用自定义边界
<span class="nc" id="L207">            double latMin = getDoubleParam(config, &quot;latitude_min&quot;, -90.0);</span>
<span class="nc" id="L208">            double latMax = getDoubleParam(config, &quot;latitude_max&quot;, 90.0);</span>
<span class="nc" id="L209">            double lonMin = getDoubleParam(config, &quot;longitude_min&quot;, -180.0);</span>
<span class="nc" id="L210">            double lonMax = getDoubleParam(config, &quot;longitude_max&quot;, 180.0);</span>
            
<span class="nc" id="L212">            return new double[]{latMin, latMax, lonMin, lonMax};</span>
        }
    }

    /**
     * 在指定半径内生成坐标
     */
    private double[] generateWithinRadius(double centerLat, double centerLon, double radiusKm) {
        // 将半径转换为度数（近似）
<span class="nc" id="L221">        double radiusDegrees = radiusKm / 111.0; // 1度约等于111公里</span>
        
        // 生成随机角度和距离
<span class="nc" id="L224">        double angle = random.nextDouble() * 2 * Math.PI;</span>
<span class="nc" id="L225">        double distance = random.nextDouble() * radiusDegrees;</span>
        
        // 计算新坐标
<span class="nc" id="L228">        double deltaLat = distance * Math.cos(angle);</span>
<span class="nc" id="L229">        double deltaLon = distance * Math.sin(angle) / Math.cos(Math.toRadians(centerLat));</span>
        
<span class="nc" id="L231">        double newLat = centerLat + deltaLat;</span>
<span class="nc" id="L232">        double newLon = centerLon + deltaLon;</span>
        
        // 确保坐标在有效范围内
<span class="nc" id="L235">        newLat = Math.max(-90.0, Math.min(90.0, newLat));</span>
<span class="nc" id="L236">        newLon = Math.max(-180.0, Math.min(180.0, newLon));</span>
        
<span class="nc" id="L238">        return new double[]{newLat, newLon};</span>
    }

    /**
     * 格式化地理坐标
     */
    private String formatGeoLocation(GeoLocation location, OutputFormat format, int precision) {
<span class="pc bpc" id="L245" title="3 of 5 branches missed.">        switch (format) {</span>
            case DECIMAL:
<span class="fc" id="L247">                return formatDecimal(location, precision);</span>
            case DMS:
<span class="nc" id="L249">                return formatDMS(location, precision);</span>
            case JSON:
<span class="fc" id="L251">                return formatJSON(location, precision);</span>
            case WKT:
<span class="nc" id="L253">                return formatWKT(location, precision);</span>
            default:
<span class="nc" id="L255">                return formatDecimal(location, precision);</span>
        }
    }

    /**
     * 格式化为小数格式
     */
    private String formatDecimal(GeoLocation location, int precision) {
<span class="fc" id="L263">        DecimalFormat df = new DecimalFormat(&quot;#.&quot; + &quot;0&quot;.repeat(precision));</span>
        
<span class="fc" id="L265">        String result = df.format(location.getLatitude()) + &quot;,&quot; + df.format(location.getLongitude());</span>
        
<span class="pc bpc" id="L267" title="1 of 2 branches missed.">        if (location.getAltitude() != null) {</span>
<span class="nc" id="L268">            result += &quot;,&quot; + df.format(location.getAltitude());</span>
        }
        
<span class="fc" id="L271">        return result;</span>
    }

    /**
     * 格式化为度分秒格式
     */
    private String formatDMS(GeoLocation location, int precision) {
<span class="nc" id="L278">        String latDMS = convertToDMS(location.getLatitude(), true);</span>
<span class="nc" id="L279">        String lonDMS = convertToDMS(location.getLongitude(), false);</span>
        
<span class="nc" id="L281">        String result = latDMS + &quot; &quot; + lonDMS;</span>
        
<span class="nc bnc" id="L283" title="All 2 branches missed.">        if (location.getAltitude() != null) {</span>
<span class="nc" id="L284">            DecimalFormat df = new DecimalFormat(&quot;#.&quot; + &quot;0&quot;.repeat(precision));</span>
<span class="nc" id="L285">            result += &quot; &quot; + df.format(location.getAltitude()) + &quot;m&quot;;</span>
        }
        
<span class="nc" id="L288">        return result;</span>
    }

    /**
     * 格式化为JSON格式
     */
    private String formatJSON(GeoLocation location, int precision) {
<span class="fc" id="L295">        DecimalFormat df = new DecimalFormat(&quot;#.&quot; + &quot;0&quot;.repeat(precision));</span>
        
<span class="fc" id="L297">        StringBuilder json = new StringBuilder();</span>
<span class="fc" id="L298">        json.append(&quot;{&quot;);</span>
<span class="fc" id="L299">        json.append(&quot;\&quot;latitude\&quot;:&quot;).append(df.format(location.getLatitude())).append(&quot;,&quot;);</span>
<span class="fc" id="L300">        json.append(&quot;\&quot;longitude\&quot;:&quot;).append(df.format(location.getLongitude()));</span>
        
<span class="pc bpc" id="L302" title="1 of 2 branches missed.">        if (location.getAltitude() != null) {</span>
<span class="fc" id="L303">            json.append(&quot;,\&quot;altitude\&quot;:&quot;).append(df.format(location.getAltitude()));</span>
        }
        
<span class="fc" id="L306">        json.append(&quot;}&quot;);</span>
<span class="fc" id="L307">        return json.toString();</span>
    }

    /**
     * 格式化为WKT格式
     */
    private String formatWKT(GeoLocation location, int precision) {
<span class="nc" id="L314">        DecimalFormat df = new DecimalFormat(&quot;#.&quot; + &quot;0&quot;.repeat(precision));</span>
        
<span class="nc bnc" id="L316" title="All 2 branches missed.">        if (location.getAltitude() != null) {</span>
<span class="nc" id="L317">            return String.format(&quot;POINT Z (%s %s %s)&quot;, </span>
<span class="nc" id="L318">                df.format(location.getLongitude()),</span>
<span class="nc" id="L319">                df.format(location.getLatitude()),</span>
<span class="nc" id="L320">                df.format(location.getAltitude()));</span>
        } else {
<span class="nc" id="L322">            return String.format(&quot;POINT (%s %s)&quot;, </span>
<span class="nc" id="L323">                df.format(location.getLongitude()),</span>
<span class="nc" id="L324">                df.format(location.getLatitude()));</span>
        }
    }

    /**
     * 将小数度数转换为度分秒格式
     */
    private String convertToDMS(double decimal, boolean isLatitude) {
<span class="nc bnc" id="L332" title="All 2 branches missed.">        boolean isNegative = decimal &lt; 0;</span>
<span class="nc" id="L333">        decimal = Math.abs(decimal);</span>
        
<span class="nc" id="L335">        int degrees = (int) decimal;</span>
<span class="nc" id="L336">        double minutesDecimal = (decimal - degrees) * 60;</span>
<span class="nc" id="L337">        int minutes = (int) minutesDecimal;</span>
<span class="nc" id="L338">        double seconds = (minutesDecimal - minutes) * 60;</span>
        
        String direction;
<span class="nc bnc" id="L341" title="All 2 branches missed.">        if (isLatitude) {</span>
<span class="nc bnc" id="L342" title="All 2 branches missed.">            direction = isNegative ? &quot;S&quot; : &quot;N&quot;;</span>
        } else {
<span class="nc bnc" id="L344" title="All 2 branches missed.">            direction = isNegative ? &quot;W&quot; : &quot;E&quot;;</span>
        }
        
<span class="nc" id="L347">        return String.format(&quot;%d°%d'%.2f\&quot;%s&quot;, degrees, minutes, seconds, direction);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>