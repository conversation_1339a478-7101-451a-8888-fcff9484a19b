<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OutputConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.config</a> &gt; <span class="el_source">OutputConfig.java</span></div><h1>OutputConfig.java</h1><pre class="source lang-java linenums">package com.dataforge.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 输出配置类。
 * 
 * &lt;p&gt;
 * 定义数据输出的格式、目标和相关参数。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class OutputConfig {

    /**
     * 输出格式枚举。
     */
<span class="nc" id="L20">    public enum Format {</span>
        /** 控制台输出 */
<span class="nc" id="L22">        CONSOLE,</span>
        /** CSV格式 */
<span class="nc" id="L24">        CSV,</span>
        /** JSON格式 */
<span class="nc" id="L26">        JSON,</span>
        /** XML格式 */
<span class="nc" id="L28">        XML,</span>
        /** SQL INSERT语句 */
<span class="nc" id="L30">        SQL;</span>

        @JsonCreator
        public static Format fromString(String value) {
<span class="nc bnc" id="L34" title="All 2 branches missed.">            if (value == null) {</span>
<span class="nc" id="L35">                return null;</span>
            }
            try {
<span class="nc" id="L38">                return Format.valueOf(value.toUpperCase());</span>
<span class="nc" id="L39">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L40">                throw new IllegalArgumentException(&quot;Invalid format: &quot; + value +</span>
<span class="nc" id="L41">                        &quot;. Valid values are: &quot; + java.util.Arrays.toString(Format.values()));</span>
            }
        }

        @JsonValue
        public String toValue() {
<span class="nc" id="L47">            return this.name().toLowerCase();</span>
        }
    }

    /**
     * 输出格式，默认为CONSOLE。
     */
<span class="nc" id="L54">    private Format format = Format.CONSOLE;</span>

    /**
     * 输出文件路径，当format不为CONSOLE时使用。
     */
    private String file;

    /**
     * 是否追加到文件末尾，默认为false（覆盖）。
     */
<span class="nc" id="L64">    private boolean append = false;</span>

    /**
     * 字符编码，默认为UTF-8。
     */
<span class="nc" id="L69">    private String encoding = &quot;UTF-8&quot;;</span>

    /**
     * CSV分隔符，默认为逗号。
     */
<span class="nc" id="L74">    private String csvDelimiter = &quot;,&quot;;</span>

    /**
     * CSV是否包含标题行，默认为true。
     */
<span class="nc" id="L79">    private boolean csvIncludeHeader = true;</span>

    /**
     * JSON是否格式化输出，默认为true。
     */
<span class="nc" id="L84">    private boolean jsonPrettyPrint = true;</span>

    /**
     * SQL表名，当format为SQL时使用。
     */
<span class="nc" id="L89">    private String sqlTableName = &quot;test_data&quot;;</span>

    /**
     * 默认构造函数。
     */
<span class="nc" id="L94">    public OutputConfig() {</span>
<span class="nc" id="L95">    }</span>

    /**
     * 获取输出格式。
     * 
     * @return 输出格式
     */
    public Format getFormat() {
<span class="nc" id="L103">        return format;</span>
    }

    /**
     * 设置输出格式。
     * 
     * @param format 输出格式
     */
    public void setFormat(Format format) {
<span class="nc" id="L112">        this.format = format;</span>
<span class="nc" id="L113">    }</span>

    /**
     * 获取输出文件路径。
     * 
     * @return 文件路径
     */
    public String getFile() {
<span class="nc" id="L121">        return file;</span>
    }

    /**
     * 设置输出文件路径。
     * 
     * @param file 文件路径
     */
    public void setFile(String file) {
<span class="nc" id="L130">        this.file = file;</span>
<span class="nc" id="L131">    }</span>

    /**
     * 检查是否追加到文件末尾。
     * 
     * @return 如果追加返回true，否则返回false
     */
    public boolean isAppend() {
<span class="nc" id="L139">        return append;</span>
    }

    /**
     * 设置是否追加到文件末尾。
     * 
     * @param append 是否追加
     */
    public void setAppend(boolean append) {
<span class="nc" id="L148">        this.append = append;</span>
<span class="nc" id="L149">    }</span>

    /**
     * 获取字符编码。
     * 
     * @return 字符编码
     */
    public String getEncoding() {
<span class="nc" id="L157">        return encoding;</span>
    }

    /**
     * 设置字符编码。
     * 
     * @param encoding 字符编码
     */
    public void setEncoding(String encoding) {
<span class="nc" id="L166">        this.encoding = encoding;</span>
<span class="nc" id="L167">    }</span>

    /**
     * 获取CSV分隔符。
     * 
     * @return CSV分隔符
     */
    public String getCsvDelimiter() {
<span class="nc" id="L175">        return csvDelimiter;</span>
    }

    /**
     * 设置CSV分隔符。
     * 
     * @param csvDelimiter CSV分隔符
     */
    public void setCsvDelimiter(String csvDelimiter) {
<span class="nc" id="L184">        this.csvDelimiter = csvDelimiter;</span>
<span class="nc" id="L185">    }</span>

    /**
     * 检查CSV是否包含标题行。
     * 
     * @return 如果包含标题行返回true，否则返回false
     */
    public boolean isCsvIncludeHeader() {
<span class="nc" id="L193">        return csvIncludeHeader;</span>
    }

    /**
     * 设置CSV是否包含标题行。
     * 
     * @param csvIncludeHeader 是否包含标题行
     */
    public void setCsvIncludeHeader(boolean csvIncludeHeader) {
<span class="nc" id="L202">        this.csvIncludeHeader = csvIncludeHeader;</span>
<span class="nc" id="L203">    }</span>

    /**
     * 检查JSON是否格式化输出。
     * 
     * @return 如果格式化输出返回true，否则返回false
     */
    public boolean isJsonPrettyPrint() {
<span class="nc" id="L211">        return jsonPrettyPrint;</span>
    }

    /**
     * 设置JSON是否格式化输出。
     * 
     * @param jsonPrettyPrint 是否格式化输出
     */
    public void setJsonPrettyPrint(boolean jsonPrettyPrint) {
<span class="nc" id="L220">        this.jsonPrettyPrint = jsonPrettyPrint;</span>
<span class="nc" id="L221">    }</span>

    /**
     * 获取SQL表名。
     * 
     * @return SQL表名
     */
    public String getSqlTableName() {
<span class="nc" id="L229">        return sqlTableName;</span>
    }

    /**
     * 设置SQL表名。
     * 
     * @param sqlTableName SQL表名
     */
    public void setSqlTableName(String sqlTableName) {
<span class="nc" id="L238">        this.sqlTableName = sqlTableName;</span>
<span class="nc" id="L239">    }</span>

    /**
     * 检查是否需要输出到文件。
     * 
     * @return 如果需要输出到文件返回true，否则返回false
     */
    public boolean isFileOutput() {
<span class="nc bnc" id="L247" title="All 6 branches missed.">        return format != Format.CONSOLE &amp;&amp; file != null &amp;&amp; !file.trim().isEmpty();</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L252">        return String.format(&quot;OutputConfig{format=%s, file='%s', append=%s, encoding='%s'}&quot;,</span>
<span class="nc" id="L253">                format, file, append, encoding);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>