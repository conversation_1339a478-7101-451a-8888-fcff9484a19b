<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenderGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">GenderGenerator.java</span></div><h1>GenderGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 性别生成器
 * 
 * 支持功能：
 * 1. 基于权重的性别选择
 * 2. 与身份证号的性别信息关联
 * 3. 与姓名生成器关联
 * 4. 支持多种性别表示格式
 * 
 * 参数配置：
 * - type: 性别类型 MALE|FEMALE|OTHER|ANY（默认ANY）
 * - format: 输出格式 CHINESE|ENGLISH|NUMBER|SYMBOL（默认CHINESE）
 * - male_ratio: 男性占比 0.0-1.0（默认0.5）
 * - other_ratio: 其他性别占比 0.0-1.0（默认0.01）
 * - link_idcard: 是否关联身份证号（默认true）
 * 
 * 关联字段：
 * - idcard: 从身份证号中提取性别信息
 * - name: 向上下文输出性别信息供姓名生成器使用
 * 
 * 输出格式：
 * - CHINESE: 男/女/其他
 * - ENGLISH: Male/Female/Other
 * - NUMBER: 1/0/2 (1=男性, 0=女性, 2=其他)
 * - SYMBOL: M/F/O
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L41">public class GenderGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L43">    private static final Logger log = LoggerFactory.getLogger(GenderGenerator.class);</span>

    private static final String TYPE = &quot;gender&quot;;
    private static final String DEFAULT_TYPE = &quot;ANY&quot;;
    private static final String DEFAULT_FORMAT = &quot;CHINESE&quot;;
    private static final double DEFAULT_MALE_RATIO = 0.5;
    private static final double DEFAULT_OTHER_RATIO = 0.01;
    private static final boolean DEFAULT_LINK_IDCARD = true;

    // 性别枚举
<span class="nc" id="L53">    public enum Gender {</span>
<span class="nc" id="L54">        MALE, FEMALE, OTHER</span>
    }

    // 上下文键名
    private static final String CONTEXT_ID_CARD = &quot;idcard&quot;;
    private static final String CONTEXT_GENDER = &quot;gender&quot;;

    @Override
    public String getType() {
<span class="nc" id="L63">        return TYPE;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L68">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L73">        Map&lt;String, Object&gt; params = config.getParams();</span>

        // 解析配置参数
<span class="nc" id="L76">        String genderType = getStringParam(params, &quot;type&quot;, DEFAULT_TYPE);</span>
<span class="nc" id="L77">        String format = getStringParam(params, &quot;format&quot;, DEFAULT_FORMAT);</span>
<span class="nc" id="L78">        double maleRatio = getDoubleParam(params, &quot;male_ratio&quot;, DEFAULT_MALE_RATIO);</span>
<span class="nc" id="L79">        double otherRatio = getDoubleParam(params, &quot;other_ratio&quot;, DEFAULT_OTHER_RATIO);</span>
<span class="nc" id="L80">        boolean linkIdCard = getBooleanParam(params, &quot;link_idcard&quot;, DEFAULT_LINK_IDCARD);</span>

        // 参数校验
<span class="nc" id="L83">        maleRatio = Math.max(0.0, Math.min(1.0, maleRatio));</span>
<span class="nc" id="L84">        otherRatio = Math.max(0.0, Math.min(1.0, otherRatio));</span>

        // 确定性别
<span class="nc" id="L87">        Gender gender = determineGender(genderType, maleRatio, otherRatio, linkIdCard, context);</span>

        // 将性别信息存入上下文
<span class="nc" id="L90">        context.put(CONTEXT_GENDER, gender.name());</span>

        // 格式化输出
<span class="nc" id="L93">        return formatGender(gender, format);</span>
    }

    /**
     * 确定性别
     */
    private Gender determineGender(String genderType, double maleRatio, double otherRatio,
            boolean linkIdCard, DataForgeContext context) {

        // 1. 如果指定了具体性别类型
<span class="nc bnc" id="L103" title="All 2 branches missed.">        if (!&quot;ANY&quot;.equalsIgnoreCase(genderType)) {</span>
            try {
<span class="nc" id="L105">                return Gender.valueOf(genderType.toUpperCase());</span>
<span class="nc" id="L106">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L107">                log.warn(&quot;Invalid gender type: {}. Using random generation.&quot;, genderType);</span>
            }
        }

        // 2. 尝试从身份证号中提取性别
<span class="nc bnc" id="L112" title="All 2 branches missed.">        if (linkIdCard) {</span>
<span class="nc" id="L113">            Gender genderFromIdCard = getGenderFromIdCard(context);</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">            if (genderFromIdCard != null) {</span>
<span class="nc" id="L115">                log.debug(&quot;Using gender from ID card: {}&quot;, genderFromIdCard);</span>
<span class="nc" id="L116">                return genderFromIdCard;</span>
            }
        }

        // 3. 基于权重随机生成
<span class="nc" id="L121">        return generateRandomGender(maleRatio, otherRatio);</span>
    }

    /**
     * 从身份证号中提取性别
     */
    private Gender getGenderFromIdCard(DataForgeContext context) {
<span class="nc" id="L128">        String idCard = context.get(CONTEXT_ID_CARD, String.class).orElse(null);</span>
<span class="nc bnc" id="L129" title="All 4 branches missed.">        if (idCard != null &amp;&amp; idCard.length() &gt;= 17) {</span>
            try {
                // 身份证号第17位（倒数第2位）表示性别：奇数为男性，偶数为女性
<span class="nc" id="L132">                char genderChar = idCard.charAt(16);</span>
<span class="nc" id="L133">                int genderDigit = Character.getNumericValue(genderChar);</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">                return (genderDigit % 2 == 1) ? Gender.MALE : Gender.FEMALE;</span>
<span class="nc" id="L135">            } catch (Exception e) {</span>
<span class="nc" id="L136">                log.debug(&quot;Failed to extract gender from ID card: {}&quot;, idCard, e);</span>
            }
        }
<span class="nc" id="L139">        return null;</span>
    }

    /**
     * 基于权重随机生成性别
     */
    private Gender generateRandomGender(double maleRatio, double otherRatio) {
<span class="nc" id="L146">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L147">        double randomValue = random.nextDouble();</span>

<span class="nc bnc" id="L149" title="All 2 branches missed.">        if (randomValue &lt; otherRatio) {</span>
<span class="nc" id="L150">            return Gender.OTHER;</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">        } else if (randomValue &lt; otherRatio + maleRatio * (1 - otherRatio)) {</span>
<span class="nc" id="L152">            return Gender.MALE;</span>
        } else {
<span class="nc" id="L154">            return Gender.FEMALE;</span>
        }
    }

    /**
     * 格式化性别输出
     */
    private String formatGender(Gender gender, String format) {
<span class="nc bnc" id="L162" title="All 5 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;CHINESE&quot;:
            case &quot;CN&quot;:
<span class="nc bnc" id="L165" title="All 4 branches missed.">                switch (gender) {</span>
                    case MALE:
<span class="nc" id="L167">                        return &quot;男&quot;;</span>
                    case FEMALE:
<span class="nc" id="L169">                        return &quot;女&quot;;</span>
                    case OTHER:
<span class="nc" id="L171">                        return &quot;其他&quot;;</span>
                }
<span class="nc" id="L173">                break;</span>

            case &quot;ENGLISH&quot;:
<span class="nc bnc" id="L176" title="All 4 branches missed.">                switch (gender) {</span>
                    case MALE:
<span class="nc" id="L178">                        return &quot;Male&quot;;</span>
                    case FEMALE:
<span class="nc" id="L180">                        return &quot;Female&quot;;</span>
                    case OTHER:
<span class="nc" id="L182">                        return &quot;Other&quot;;</span>
                }
<span class="nc" id="L184">                break;</span>

            case &quot;NUMBER&quot;:
<span class="nc bnc" id="L187" title="All 4 branches missed.">                switch (gender) {</span>
                    case MALE:
<span class="nc" id="L189">                        return &quot;1&quot;;</span>
                    case FEMALE:
<span class="nc" id="L191">                        return &quot;0&quot;;</span>
                    case OTHER:
<span class="nc" id="L193">                        return &quot;2&quot;;</span>
                }
<span class="nc" id="L195">                break;</span>

            case &quot;SYMBOL&quot;:
<span class="nc bnc" id="L198" title="All 4 branches missed.">                switch (gender) {</span>
                    case MALE:
<span class="nc" id="L200">                        return &quot;M&quot;;</span>
                    case FEMALE:
<span class="nc" id="L202">                        return &quot;F&quot;;</span>
                    case OTHER:
<span class="nc" id="L204">                        return &quot;O&quot;;</span>
                }
<span class="nc" id="L206">                break;</span>

            default:
<span class="nc" id="L209">                log.warn(&quot;Unknown gender format: {}. Using CHINESE format.&quot;, format);</span>
<span class="nc" id="L210">                return formatGender(gender, &quot;CHINESE&quot;);</span>
        }

<span class="nc" id="L213">        return gender.name(); // fallback</span>
    }

    // 工具方法
    private String getStringParam(Map&lt;String, Object&gt; params, String key, String defaultValue) {
<span class="nc" id="L218">        Object value = params.get(key);</span>
<span class="nc bnc" id="L219" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    private double getDoubleParam(Map&lt;String, Object&gt; params, String key, double defaultValue) {
<span class="nc" id="L223">        Object value = params.get(key);</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">        if (value instanceof Number) {</span>
<span class="nc" id="L225">            return ((Number) value).doubleValue();</span>
        }
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (value instanceof String) {</span>
            try {
<span class="nc" id="L229">                return Double.parseDouble((String) value);</span>
<span class="nc" id="L230">            } catch (NumberFormatException e) {</span>
<span class="nc" id="L231">                log.warn(&quot;Invalid double parameter '{}': {}&quot;, key, value);</span>
            }
        }
<span class="nc" id="L234">        return defaultValue;</span>
    }

    private boolean getBooleanParam(Map&lt;String, Object&gt; params, String key, boolean defaultValue) {
<span class="nc" id="L238">        Object value = params.get(key);</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L240">            return (Boolean) value;</span>
        }
<span class="nc bnc" id="L242" title="All 2 branches missed.">        if (value instanceof String) {</span>
<span class="nc" id="L243">            return Boolean.parseBoolean((String) value);</span>
        }
<span class="nc" id="L245">        return defaultValue;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>