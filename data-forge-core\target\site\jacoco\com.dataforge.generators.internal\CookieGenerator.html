<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CookieGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">CookieGenerator</span></div><h1>CookieGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">218 of 728</td><td class="ctr2">70%</td><td class="bar">28 of 54</td><td class="ctr2">48%</td><td class="ctr1">26</td><td class="ctr2">46</td><td class="ctr1">49</td><td class="ctr2">136</td><td class="ctr1">2</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a8"><a href="CookieGenerator.java.html#L315" class="el_method">generateJsonValue()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="108" alt="108"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="CookieGenerator.java.html#L301" class="el_method">generateToken()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="36" alt="36"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="CookieGenerator.java.html#L363" class="el_method">formatAsHeader(CookieGenerator.CookieInfo)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="61" height="10" title="73" alt="73"/></td><td class="ctr2" id="c8">82%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="7" alt="7"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="CookieGenerator.java.html#L187" class="el_method">generateCookieInfo(FieldConfig)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="108" height="10" title="128" alt="128"/></td><td class="ctr2" id="c7">90%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="7" alt="7"/></td><td class="ctr2" id="e2">58%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="CookieGenerator.java.html#L404" class="el_method">formatAsJson(CookieGenerator.CookieInfo)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="81" height="10" title="97" alt="97"/></td><td class="ctr2" id="c6">93%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="CookieGenerator.java.html#L154" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="19" alt="19"/></td><td class="ctr2" id="c10">73%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="CookieGenerator.java.html#L175" class="el_method">parseOutputFormat(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">36%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a15"><a href="CookieGenerator.java.html#L245" class="el_method">parseValueType(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">36%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="CookieGenerator.java.html#L257" class="el_method">parseSameSite(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">36%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i11">4</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a7"><a href="CookieGenerator.java.html#L268" class="el_method">generateCookieValue(CookieGenerator.ValueType, FieldConfig)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="16" alt="16"/></td><td class="ctr2" id="c11">72%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="CookieGenerator.java.html#L347" class="el_method">formatCookie(CookieGenerator.CookieInfo, CookieGenerator.OutputFormat)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="15" alt="15"/></td><td class="ctr2" id="c9">78%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a16"><a href="CookieGenerator.java.html#L45" class="el_method">static {...}</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="94" height="10" title="112" alt="112"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="CookieGenerator.java.html#L286" class="el_method">generateRandomString(int)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="25" alt="25"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="CookieGenerator.java.html#L439" class="el_method">formatAsSimple(CookieGenerator.CookieInfo)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a0"><a href="CookieGenerator.java.html#L43" class="el_method">CookieGenerator()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a12"><a href="CookieGenerator.java.html#L142" class="el_method">getType()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a11"><a href="CookieGenerator.java.html#L147" class="el_method">getConfigClass()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>