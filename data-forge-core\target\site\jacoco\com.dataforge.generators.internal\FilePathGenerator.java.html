<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FilePathGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">FilePathGenerator.java</span></div><h1>FilePathGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 文件路径生成器
 * 
 * 支持的参数：
 * - os: 操作系统 (WINDOWS|UNIX|MAC|ANY)
 * - type: 路径类型 (ABSOLUTE|RELATIVE|UNC)
 * - depth: 目录深度 (1-10)
 * - include_filename: 是否包含文件名 (true|false)
 * - extension: 文件扩展名 (如 &quot;txt&quot;, &quot;jpg&quot;, &quot;ANY&quot;)
 * - include_spaces: 是否包含空格 (true|false)
 * - include_special_chars: 是否包含特殊字符 (true|false)
 * - drive_letter: Windows驱动器盘符 (A-Z)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L26">public class FilePathGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L28">    private static final Logger logger = LoggerFactory.getLogger(FilePathGenerator.class);</span>
<span class="nc" id="L29">    private static final Random random = new Random();</span>

    // 操作系统类型枚举
<span class="nc" id="L32">    private enum OSType {</span>
<span class="nc" id="L33">        WINDOWS, UNIX, MAC</span>
    }

    // 路径类型枚举
<span class="nc" id="L37">    private enum PathType {</span>
<span class="nc" id="L38">        ABSOLUTE, // 绝对路径</span>
<span class="nc" id="L39">        RELATIVE, // 相对路径</span>
<span class="nc" id="L40">        UNC // Windows UNC路径</span>
    }

    // 常见目录名
<span class="nc" id="L44">    private static final List&lt;String&gt; COMMON_DIRECTORIES = Arrays.asList(</span>
            &quot;home&quot;, &quot;user&quot;, &quot;users&quot;, &quot;documents&quot;, &quot;downloads&quot;, &quot;desktop&quot;, &quot;pictures&quot;, &quot;music&quot;, &quot;videos&quot;,
            &quot;projects&quot;, &quot;workspace&quot;, &quot;src&quot;, &quot;source&quot;, &quot;code&quot;, &quot;dev&quot;, &quot;development&quot;, &quot;test&quot;, &quot;tests&quot;,
            &quot;data&quot;, &quot;files&quot;, &quot;temp&quot;, &quot;tmp&quot;, &quot;cache&quot;, &quot;logs&quot;, &quot;log&quot;, &quot;config&quot;, &quot;configuration&quot;,
            &quot;bin&quot;, &quot;lib&quot;, &quot;libs&quot;, &quot;include&quot;, &quot;share&quot;, &quot;var&quot;, &quot;opt&quot;, &quot;usr&quot;, &quot;etc&quot;, &quot;boot&quot;, &quot;root&quot;,
            &quot;program files&quot;, &quot;program files (x86)&quot;, &quot;windows&quot;, &quot;system32&quot;, &quot;appdata&quot;, &quot;local&quot;,
            &quot;roaming&quot;, &quot;public&quot;, &quot;all users&quot;, &quot;default&quot;, &quot;profiles&quot;, &quot;application data&quot;);

    // 常见文件名
<span class="nc" id="L53">    private static final List&lt;String&gt; COMMON_FILENAMES = Arrays.asList(</span>
            &quot;index&quot;, &quot;main&quot;, &quot;app&quot;, &quot;application&quot;, &quot;program&quot;, &quot;readme&quot;, &quot;license&quot;, &quot;changelog&quot;,
            &quot;config&quot;, &quot;configuration&quot;, &quot;settings&quot;, &quot;preferences&quot;, &quot;options&quot;, &quot;data&quot;, &quot;database&quot;,
            &quot;log&quot;, &quot;error&quot;, &quot;debug&quot;, &quot;info&quot;, &quot;test&quot;, &quot;example&quot;, &quot;sample&quot;, &quot;demo&quot;, &quot;template&quot;,
            &quot;backup&quot;, &quot;archive&quot;, &quot;export&quot;, &quot;import&quot;, &quot;report&quot;, &quot;document&quot;, &quot;file&quot;, &quot;image&quot;,
            &quot;photo&quot;, &quot;picture&quot;, &quot;video&quot;, &quot;audio&quot;, &quot;music&quot;, &quot;sound&quot;, &quot;text&quot;, &quot;note&quot;, &quot;memo&quot;);

    // 常见文件扩展名
<span class="nc" id="L61">    private static final Map&lt;String, List&lt;String&gt;&gt; FILE_EXTENSIONS = new HashMap&lt;&gt;();</span>

    // Windows驱动器盘符
<span class="nc" id="L64">    private static final List&lt;String&gt; DRIVE_LETTERS = Arrays.asList(</span>
            &quot;C&quot;, &quot;D&quot;, &quot;E&quot;, &quot;F&quot;, &quot;G&quot;, &quot;H&quot;, &quot;I&quot;, &quot;J&quot;, &quot;K&quot;, &quot;L&quot;, &quot;M&quot;, &quot;N&quot;, &quot;O&quot;, &quot;P&quot;, &quot;Q&quot;, &quot;R&quot;, &quot;S&quot;, &quot;T&quot;, &quot;U&quot;, &quot;V&quot;, &quot;W&quot;,
            &quot;X&quot;, &quot;Y&quot;, &quot;Z&quot;);

    // 特殊字符（用于测试）
    private static final String SPECIAL_CHARS = &quot;!@#$%^&amp;()_+-=[]{}|;':\&quot;,./&lt;&gt;?`~&quot;;

    static {
<span class="nc" id="L72">        initializeFileExtensions();</span>
<span class="nc" id="L73">    }</span>

    private static void initializeFileExtensions() {
<span class="nc" id="L76">        FILE_EXTENSIONS.put(&quot;DOCUMENT&quot;, Arrays.asList(&quot;txt&quot;, &quot;doc&quot;, &quot;docx&quot;, &quot;pdf&quot;, &quot;rtf&quot;, &quot;odt&quot;, &quot;pages&quot;));</span>
<span class="nc" id="L77">        FILE_EXTENSIONS.put(&quot;IMAGE&quot;, Arrays.asList(&quot;jpg&quot;, &quot;jpeg&quot;, &quot;png&quot;, &quot;gif&quot;, &quot;bmp&quot;, &quot;tiff&quot;, &quot;svg&quot;, &quot;webp&quot;));</span>
<span class="nc" id="L78">        FILE_EXTENSIONS.put(&quot;VIDEO&quot;, Arrays.asList(&quot;mp4&quot;, &quot;avi&quot;, &quot;mkv&quot;, &quot;mov&quot;, &quot;wmv&quot;, &quot;flv&quot;, &quot;webm&quot;, &quot;m4v&quot;));</span>
<span class="nc" id="L79">        FILE_EXTENSIONS.put(&quot;AUDIO&quot;, Arrays.asList(&quot;mp3&quot;, &quot;wav&quot;, &quot;flac&quot;, &quot;aac&quot;, &quot;ogg&quot;, &quot;wma&quot;, &quot;m4a&quot;));</span>
<span class="nc" id="L80">        FILE_EXTENSIONS.put(&quot;ARCHIVE&quot;, Arrays.asList(&quot;zip&quot;, &quot;rar&quot;, &quot;7z&quot;, &quot;tar&quot;, &quot;gz&quot;, &quot;bz2&quot;, &quot;xz&quot;));</span>
<span class="nc" id="L81">        FILE_EXTENSIONS.put(&quot;CODE&quot;,</span>
<span class="nc" id="L82">                Arrays.asList(&quot;java&quot;, &quot;py&quot;, &quot;js&quot;, &quot;html&quot;, &quot;css&quot;, &quot;cpp&quot;, &quot;c&quot;, &quot;h&quot;, &quot;php&quot;, &quot;rb&quot;, &quot;go&quot;, &quot;rs&quot;));</span>
<span class="nc" id="L83">        FILE_EXTENSIONS.put(&quot;DATA&quot;, Arrays.asList(&quot;json&quot;, &quot;xml&quot;, &quot;csv&quot;, &quot;sql&quot;, &quot;db&quot;, &quot;sqlite&quot;, &quot;xlsx&quot;, &quot;xls&quot;));</span>
<span class="nc" id="L84">        FILE_EXTENSIONS.put(&quot;CONFIG&quot;, Arrays.asList(&quot;conf&quot;, &quot;cfg&quot;, &quot;ini&quot;, &quot;properties&quot;, &quot;yaml&quot;, &quot;yml&quot;, &quot;toml&quot;));</span>
<span class="nc" id="L85">        FILE_EXTENSIONS.put(&quot;EXECUTABLE&quot;, Arrays.asList(&quot;exe&quot;, &quot;msi&quot;, &quot;dmg&quot;, &quot;pkg&quot;, &quot;deb&quot;, &quot;rpm&quot;, &quot;appimage&quot;));</span>
<span class="nc" id="L86">        FILE_EXTENSIONS.put(&quot;WEB&quot;, Arrays.asList(&quot;html&quot;, &quot;htm&quot;, &quot;css&quot;, &quot;js&quot;, &quot;php&quot;, &quot;asp&quot;, &quot;jsp&quot;, &quot;xml&quot;));</span>
<span class="nc" id="L87">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L91">        return &quot;filepath&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L96">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L103">            String os = config.getParam(&quot;os&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L104">            String type = config.getParam(&quot;type&quot;, String.class, &quot;ABSOLUTE&quot;);</span>
<span class="nc" id="L105">            int depth = Integer.parseInt(config.getParam(&quot;depth&quot;, String.class, &quot;3&quot;));</span>
<span class="nc" id="L106">            boolean includeFilename = Boolean.parseBoolean(config.getParam(&quot;include_filename&quot;, String.class, &quot;true&quot;));</span>
<span class="nc" id="L107">            String extension = config.getParam(&quot;extension&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L108">            boolean includeSpaces = Boolean.parseBoolean(config.getParam(&quot;include_spaces&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L109">            boolean includeSpecialChars = Boolean</span>
<span class="nc" id="L110">                    .parseBoolean(config.getParam(&quot;include_special_chars&quot;, String.class, &quot;false&quot;));</span>
<span class="nc" id="L111">            String driveLetter = config.getParam(&quot;drive_letter&quot;, String.class, null);</span>

            // 生成文件路径
<span class="nc" id="L114">            String filePath = generateFilePath(os, type, depth, includeFilename, extension,</span>
                    includeSpaces, includeSpecialChars, driveLetter);

            // 将文件路径信息存入上下文
<span class="nc" id="L118">            context.put(&quot;file_path&quot;, filePath);</span>
<span class="nc" id="L119">            context.put(&quot;file_os&quot;, determineOS(os).name());</span>
<span class="nc" id="L120">            context.put(&quot;file_extension&quot;, extractExtension(filePath));</span>
<span class="nc" id="L121">            context.put(&quot;file_directory&quot;, extractDirectory(filePath));</span>
<span class="nc" id="L122">            context.put(&quot;file_name&quot;, extractFilename(filePath));</span>

<span class="nc" id="L124">            logger.debug(&quot;Generated file path: {}&quot;, filePath);</span>
<span class="nc" id="L125">            return filePath;</span>

<span class="nc" id="L127">        } catch (Exception e) {</span>
<span class="nc" id="L128">            logger.error(&quot;Error generating file path&quot;, e);</span>
<span class="nc" id="L129">            return &quot;/home/<USER>/documents/file.txt&quot;;</span>
        }
    }

    private String generateFilePath(String os, String type, int depth, boolean includeFilename,
            String extension, boolean includeSpaces, boolean includeSpecialChars,
            String driveLetter) {

<span class="nc" id="L137">        OSType osType = determineOS(os);</span>
<span class="nc" id="L138">        PathType pathType = determinePathType(type);</span>

<span class="nc" id="L140">        StringBuilder path = new StringBuilder();</span>

        // 生成路径前缀
<span class="nc" id="L143">        generatePathPrefix(path, osType, pathType, driveLetter);</span>

        // 生成目录结构
<span class="nc" id="L146">        generateDirectoryStructure(path, osType, depth, includeSpaces, includeSpecialChars);</span>

        // 生成文件名
<span class="nc bnc" id="L149" title="All 2 branches missed.">        if (includeFilename) {</span>
<span class="nc" id="L150">            generateFilename(path, osType, extension, includeSpaces, includeSpecialChars);</span>
        }

<span class="nc" id="L153">        return path.toString();</span>
    }

    private OSType determineOS(String os) {
<span class="nc bnc" id="L157" title="All 4 branches missed.">        switch (os.toUpperCase()) {</span>
            case &quot;WINDOWS&quot;:
<span class="nc" id="L159">                return OSType.WINDOWS;</span>
            case &quot;UNIX&quot;:
            case &quot;LINUX&quot;:
<span class="nc" id="L162">                return OSType.UNIX;</span>
            case &quot;MAC&quot;:
            case &quot;MACOS&quot;:
<span class="nc" id="L165">                return OSType.MAC;</span>
            case &quot;ANY&quot;:
            default:
<span class="nc" id="L168">                OSType[] types = OSType.values();</span>
<span class="nc" id="L169">                return types[random.nextInt(types.length)];</span>
        }
    }

    private PathType determinePathType(String type) {
        try {
<span class="nc" id="L175">            return PathType.valueOf(type.toUpperCase());</span>
<span class="nc" id="L176">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L177">            logger.warn(&quot;Unknown path type: {}. Using ABSOLUTE.&quot;, type);</span>
<span class="nc" id="L178">            return PathType.ABSOLUTE;</span>
        }
    }

    private void generatePathPrefix(StringBuilder path, OSType osType, PathType pathType, String driveLetter) {
<span class="nc bnc" id="L183" title="All 4 branches missed.">        switch (pathType) {</span>
            case ABSOLUTE:
<span class="nc" id="L185">                generateAbsolutePrefix(path, osType, driveLetter);</span>
<span class="nc" id="L186">                break;</span>

            case RELATIVE:
<span class="nc" id="L189">                generateRelativePrefix(path, osType);</span>
<span class="nc" id="L190">                break;</span>

            case UNC:
<span class="nc bnc" id="L193" title="All 2 branches missed.">                if (osType == OSType.WINDOWS) {</span>
<span class="nc" id="L194">                    generateUNCPrefix(path);</span>
                } else {
<span class="nc" id="L196">                    generateAbsolutePrefix(path, osType, driveLetter);</span>
                }
                break;
        }
<span class="nc" id="L200">    }</span>

    private void generateAbsolutePrefix(StringBuilder path, OSType osType, String driveLetter) {
<span class="nc bnc" id="L203" title="All 4 branches missed.">        switch (osType) {</span>
            case WINDOWS:
<span class="nc bnc" id="L205" title="All 2 branches missed.">                String drive = driveLetter != null ? driveLetter</span>
<span class="nc" id="L206">                        : DRIVE_LETTERS.get(random.nextInt(DRIVE_LETTERS.size()));</span>
<span class="nc" id="L207">                path.append(drive).append(&quot;:\\&quot;);</span>
<span class="nc" id="L208">                break;</span>

            case UNIX:
<span class="nc" id="L211">                path.append(&quot;/&quot;);</span>
<span class="nc" id="L212">                break;</span>

            case MAC:
<span class="nc" id="L215">                path.append(&quot;/&quot;);</span>
                break;
        }
<span class="nc" id="L218">    }</span>

    private void generateRelativePrefix(StringBuilder path, OSType osType) {
        // 相对路径可能以 ./ 或 ../ 开始
<span class="nc bnc" id="L222" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc bnc" id="L223" title="All 2 branches missed.">            if (random.nextBoolean()) {</span>
<span class="nc" id="L224">                path.append(&quot;./&quot;);</span>
            } else {
<span class="nc" id="L226">                path.append(&quot;../&quot;);</span>
            }
        }
<span class="nc" id="L229">    }</span>

    private void generateUNCPrefix(StringBuilder path) {
        // UNC路径格式：\\server\share
<span class="nc" id="L233">        path.append(&quot;\\\\&quot;);</span>
<span class="nc" id="L234">        path.append(generateServerName());</span>
<span class="nc" id="L235">        path.append(&quot;\\&quot;);</span>
<span class="nc" id="L236">        path.append(generateShareName());</span>
<span class="nc" id="L237">        path.append(&quot;\\&quot;);</span>
<span class="nc" id="L238">    }</span>

    private String generateServerName() {
<span class="nc" id="L241">        String[] serverPrefixes = { &quot;srv&quot;, &quot;server&quot;, &quot;fs&quot;, &quot;file&quot;, &quot;nas&quot;, &quot;storage&quot; };</span>
<span class="nc" id="L242">        String prefix = serverPrefixes[random.nextInt(serverPrefixes.length)];</span>
<span class="nc" id="L243">        return prefix + (random.nextInt(99) + 1);</span>
    }

    private String generateShareName() {
<span class="nc" id="L247">        String[] shareNames = { &quot;shared&quot;, &quot;public&quot;, &quot;data&quot;, &quot;files&quot;, &quot;documents&quot;, &quot;projects&quot;, &quot;backup&quot; };</span>
<span class="nc" id="L248">        return shareNames[random.nextInt(shareNames.length)];</span>
    }

    private void generateDirectoryStructure(StringBuilder path, OSType osType, int depth,
            boolean includeSpaces, boolean includeSpecialChars) {

<span class="nc" id="L254">        String separator = getSeparator(osType);</span>

<span class="nc bnc" id="L256" title="All 2 branches missed.">        for (int i = 0; i &lt; depth; i++) {</span>
<span class="nc" id="L257">            String dirName = generateDirectoryName(includeSpaces, includeSpecialChars);</span>
<span class="nc" id="L258">            path.append(dirName);</span>

<span class="nc bnc" id="L260" title="All 4 branches missed.">            if (i &lt; depth - 1 || path.charAt(path.length() - 1) != separator.charAt(0)) {</span>
<span class="nc" id="L261">                path.append(separator);</span>
            }
        }
<span class="nc" id="L264">    }</span>

    private String generateDirectoryName(boolean includeSpaces, boolean includeSpecialChars) {
<span class="nc" id="L267">        String baseName = COMMON_DIRECTORIES.get(random.nextInt(COMMON_DIRECTORIES.size()));</span>

        // 30%概率添加数字后缀
<span class="nc bnc" id="L270" title="All 2 branches missed.">        if (random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L271">            baseName += random.nextInt(100);</span>
        }

        // 处理空格
<span class="nc bnc" id="L275" title="All 4 branches missed.">        if (includeSpaces &amp;&amp; random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L276">            baseName = baseName.replace(&quot; &quot;, &quot; &quot;);</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">            if (!baseName.contains(&quot; &quot;)) {</span>
<span class="nc" id="L278">                baseName += &quot; &quot; + (random.nextInt(10) + 1);</span>
            }
        } else {
<span class="nc" id="L281">            baseName = baseName.replace(&quot; &quot;, &quot;_&quot;);</span>
        }

        // 处理特殊字符
<span class="nc bnc" id="L285" title="All 4 branches missed.">        if (includeSpecialChars &amp;&amp; random.nextDouble() &lt; 0.2) {</span>
<span class="nc" id="L286">            char specialChar = SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length()));</span>
<span class="nc" id="L287">            baseName += specialChar;</span>
        }

<span class="nc" id="L290">        return baseName;</span>
    }

    private void generateFilename(StringBuilder path, OSType osType, String extension,
            boolean includeSpaces, boolean includeSpecialChars) {

<span class="nc" id="L296">        String separator = getSeparator(osType);</span>

        // 确保路径以分隔符结尾
<span class="nc bnc" id="L299" title="All 4 branches missed.">        if (path.length() &gt; 0 &amp;&amp; path.charAt(path.length() - 1) != separator.charAt(0)) {</span>
<span class="nc" id="L300">            path.append(separator);</span>
        }

        // 生成文件名
<span class="nc" id="L304">        String filename = generateBaseFilename(includeSpaces, includeSpecialChars);</span>

        // 添加扩展名
<span class="nc" id="L307">        String fileExtension = determineFileExtension(extension);</span>
<span class="nc bnc" id="L308" title="All 4 branches missed.">        if (fileExtension != null &amp;&amp; !fileExtension.isEmpty()) {</span>
<span class="nc" id="L309">            filename += &quot;.&quot; + fileExtension;</span>
        }

<span class="nc" id="L312">        path.append(filename);</span>
<span class="nc" id="L313">    }</span>

    private String generateBaseFilename(boolean includeSpaces, boolean includeSpecialChars) {
<span class="nc" id="L316">        String baseName = COMMON_FILENAMES.get(random.nextInt(COMMON_FILENAMES.size()));</span>

        // 50%概率添加数字或日期后缀
<span class="nc bnc" id="L319" title="All 2 branches missed.">        if (random.nextDouble() &lt; 0.5) {</span>
<span class="nc bnc" id="L320" title="All 2 branches missed.">            if (random.nextBoolean()) {</span>
<span class="nc" id="L321">                baseName += &quot;_&quot; + random.nextInt(1000);</span>
            } else {
<span class="nc" id="L323">                baseName += &quot;_&quot; + String.format(&quot;%04d%02d%02d&quot;,</span>
<span class="nc" id="L324">                        2020 + random.nextInt(5),</span>
<span class="nc" id="L325">                        1 + random.nextInt(12),</span>
<span class="nc" id="L326">                        1 + random.nextInt(28));</span>
            }
        }

        // 处理空格
<span class="nc bnc" id="L331" title="All 4 branches missed.">        if (includeSpaces &amp;&amp; random.nextDouble() &lt; 0.3) {</span>
<span class="nc" id="L332">            baseName = baseName.replace(&quot;_&quot;, &quot; &quot;);</span>
        }

        // 处理特殊字符
<span class="nc bnc" id="L336" title="All 4 branches missed.">        if (includeSpecialChars &amp;&amp; random.nextDouble() &lt; 0.2) {</span>
<span class="nc" id="L337">            char specialChar = SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length()));</span>
<span class="nc" id="L338">            baseName += specialChar;</span>
        }

<span class="nc" id="L341">        return baseName;</span>
    }

    private String determineFileExtension(String extension) {
<span class="nc bnc" id="L345" title="All 6 branches missed.">        if (extension == null || extension.isEmpty() || &quot;NONE&quot;.equalsIgnoreCase(extension)) {</span>
<span class="nc" id="L346">            return null;</span>
        }

<span class="nc bnc" id="L349" title="All 2 branches missed.">        if (&quot;ANY&quot;.equalsIgnoreCase(extension)) {</span>
            // 随机选择一个类别
<span class="nc" id="L351">            List&lt;String&gt; categories = new ArrayList&lt;&gt;(FILE_EXTENSIONS.keySet());</span>
<span class="nc" id="L352">            String category = categories.get(random.nextInt(categories.size()));</span>
<span class="nc" id="L353">            List&lt;String&gt; extensions = FILE_EXTENSIONS.get(category);</span>
<span class="nc" id="L354">            return extensions.get(random.nextInt(extensions.size()));</span>
        }

        // 检查是否是预定义类别
<span class="nc bnc" id="L358" title="All 2 branches missed.">        if (FILE_EXTENSIONS.containsKey(extension.toUpperCase())) {</span>
<span class="nc" id="L359">            List&lt;String&gt; extensions = FILE_EXTENSIONS.get(extension.toUpperCase());</span>
<span class="nc" id="L360">            return extensions.get(random.nextInt(extensions.size()));</span>
        }

        // 直接使用指定的扩展名
<span class="nc" id="L364">        return extension.toLowerCase();</span>
    }

    private String getSeparator(OSType osType) {
<span class="nc bnc" id="L368" title="All 2 branches missed.">        switch (osType) {</span>
            case WINDOWS:
<span class="nc" id="L370">                return &quot;\\&quot;;</span>
            case UNIX:
            case MAC:
            default:
<span class="nc" id="L374">                return &quot;/&quot;;</span>
        }
    }

    private String extractExtension(String filePath) {
<span class="nc" id="L379">        int lastDot = filePath.lastIndexOf('.');</span>
<span class="nc" id="L380">        int lastSeparator = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));</span>

<span class="nc bnc" id="L382" title="All 4 branches missed.">        if (lastDot &gt; lastSeparator &amp;&amp; lastDot &lt; filePath.length() - 1) {</span>
<span class="nc" id="L383">            return filePath.substring(lastDot + 1);</span>
        }

<span class="nc" id="L386">        return &quot;&quot;;</span>
    }

    private String extractDirectory(String filePath) {
<span class="nc" id="L390">        int lastSeparator = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));</span>

<span class="nc bnc" id="L392" title="All 2 branches missed.">        if (lastSeparator &gt; 0) {</span>
<span class="nc" id="L393">            return filePath.substring(0, lastSeparator);</span>
        }

<span class="nc" id="L396">        return &quot;&quot;;</span>
    }

    private String extractFilename(String filePath) {
<span class="nc" id="L400">        int lastSeparator = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));</span>

<span class="nc bnc" id="L402" title="All 4 branches missed.">        if (lastSeparator &gt;= 0 &amp;&amp; lastSeparator &lt; filePath.length() - 1) {</span>
<span class="nc" id="L403">            return filePath.substring(lastSeparator + 1);</span>
        }

<span class="nc" id="L406">        return filePath;</span>
    }

    /**
     * 生成路径穿越攻击payload
     */
    public String generatePathTraversalPayload(OSType osType, int depth) {
<span class="nc" id="L413">        StringBuilder payload = new StringBuilder();</span>
<span class="nc" id="L414">        String separator = getSeparator(osType);</span>

<span class="nc bnc" id="L416" title="All 2 branches missed.">        for (int i = 0; i &lt; depth; i++) {</span>
<span class="nc" id="L417">            payload.append(&quot;..&quot;).append(separator);</span>
        }

        // 添加目标文件
<span class="nc bnc" id="L421" title="All 2 branches missed.">        if (osType == OSType.WINDOWS) {</span>
<span class="nc" id="L422">            payload.append(&quot;windows&quot;).append(separator).append(&quot;win.ini&quot;);</span>
        } else {
<span class="nc" id="L424">            payload.append(&quot;etc&quot;).append(separator).append(&quot;passwd&quot;);</span>
        }

<span class="nc" id="L427">        return payload.toString();</span>
    }

    /**
     * 生成超长路径（用于测试路径长度限制）
     */
    public String generateLongPath(OSType osType, int targetLength) {
<span class="nc" id="L434">        StringBuilder longPath = new StringBuilder();</span>
<span class="nc" id="L435">        String separator = getSeparator(osType);</span>

        // 添加前缀
<span class="nc" id="L438">        generateAbsolutePrefix(longPath, osType, null);</span>

        // 生成长目录名
<span class="nc" id="L441">        String longDirName = &quot;a&quot;.repeat(Math.min(255, targetLength / 10));</span>

<span class="nc bnc" id="L443" title="All 2 branches missed.">        while (longPath.length() &lt; targetLength - 100) {</span>
<span class="nc" id="L444">            longPath.append(longDirName).append(separator);</span>
        }

        // 添加文件名
<span class="nc" id="L448">        longPath.append(&quot;file.txt&quot;);</span>

<span class="nc" id="L450">        return longPath.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>