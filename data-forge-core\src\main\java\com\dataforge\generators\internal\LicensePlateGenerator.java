package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 车牌号生成器。
 * 
 * <p>
 * 生成符合中国大陆车牌号规则的车牌号码。
 * 支持燃油车牌和新能源车牌，可指定省份和城市。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class LicensePlateGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger logger = LoggerFactory.getLogger(LicensePlateGenerator.class);

    /**
     * 省份简称映射。
     */
    private static final Map<String, String> PROVINCE_CODES = new HashMap<>();

    static {
        PROVINCE_CODES.put("北京", "京");
        PROVINCE_CODES.put("天津", "津");
        PROVINCE_CODES.put("河北", "冀");
        PROVINCE_CODES.put("山西", "晋");
        PROVINCE_CODES.put("内蒙古", "蒙");
        PROVINCE_CODES.put("辽宁", "辽");
        PROVINCE_CODES.put("吉林", "吉");
        PROVINCE_CODES.put("黑龙江", "黑");
        PROVINCE_CODES.put("上海", "沪");
        PROVINCE_CODES.put("江苏", "苏");
        PROVINCE_CODES.put("浙江", "浙");
        PROVINCE_CODES.put("安徽", "皖");
        PROVINCE_CODES.put("福建", "闽");
        PROVINCE_CODES.put("江西", "赣");
        PROVINCE_CODES.put("山东", "鲁");
        PROVINCE_CODES.put("河南", "豫");
        PROVINCE_CODES.put("湖北", "鄂");
        PROVINCE_CODES.put("湖南", "湘");
        PROVINCE_CODES.put("广东", "粤");
        PROVINCE_CODES.put("广西", "桂");
        PROVINCE_CODES.put("海南", "琼");
        PROVINCE_CODES.put("重庆", "渝");
        PROVINCE_CODES.put("四川", "川");
        PROVINCE_CODES.put("贵州", "贵");
        PROVINCE_CODES.put("云南", "云");
        PROVINCE_CODES.put("西藏", "藏");
        PROVINCE_CODES.put("陕西", "陕");
        PROVINCE_CODES.put("甘肃", "甘");
        PROVINCE_CODES.put("青海", "青");
        PROVINCE_CODES.put("宁夏", "宁");
        PROVINCE_CODES.put("新疆", "新");
        PROVINCE_CODES.put("香港", "港");
        PROVINCE_CODES.put("澳门", "澳");
        PROVINCE_CODES.put("台湾", "台");
    }

    /**
     * 常用省份简称。
     */
    private static final List<String> COMMON_PROVINCES = Arrays.asList(
            "京", "津", "沪", "渝", "冀", "豫", "云", "辽", "黑", "湘",
            "皖", "鲁", "新", "苏", "浙", "赣", "鄂", "桂", "甘", "晋",
            "蒙", "陕", "吉", "闽", "贵", "粤", "青", "藏", "川", "宁",
            "琼", "港", "澳", "台");

    /**
     * 城市代码字母。
     */
    private static final List<String> CITY_CODES = Arrays.asList(
            "A", "B", "C", "D", "E", "F", "G", "H", "J", "K",
            "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z");

    /**
     * 车牌字符集（不包含I和O）。
     */
    private static final String PLATE_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ0123456789";

    /**
     * 新能源车牌专用字符。
     */
    private static final List<String> NEW_ENERGY_PREFIXES = Arrays.asList("D", "F");

    private final Random random = new Random();

    @Override
    public String getType() {
        return "licenseplate";
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 从参数中获取车牌类型
            String plateType = getStringParam(config, "type", "BOTH");

            // 从参数中获取省份
            String province = getStringParam(config, "province", null);

            // 从参数中获取城市代码
            String city = getStringParam(config, "city", null);

            // 从参数中获取是否包含I和O
            boolean includeIO = getBooleanParam(config, "include_io", false);

            // 从参数中获取是否生成有效车牌
            boolean valid = getBooleanParam(config, "valid", true);

            if (!valid) {
                return generateInvalidLicensePlate();
            }

            return generateValidLicensePlate(plateType, province, city, includeIO);

        } catch (Exception e) {
            logger.error("Failed to generate license plate", e);
            // 返回一个默认车牌号作为fallback
            return "京A12345";
        }
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    /**
     * 生成有效的车牌号。
     * 
     * @param plateType 车牌类型
     * @param province  省份
     * @param city      城市代码
     * @param includeIO 是否包含I和O
     * @return 有效的车牌号
     */
    private String generateValidLicensePlate(String plateType, String province, String city, boolean includeIO) {
        // 确定车牌类型
        boolean isNewEnergy = determineNewEnergyType(plateType);

        // 生成省份简称
        String provinceCode = selectProvinceCode(province);

        // 生成城市代码
        String cityCode = selectCityCode(city);

        // 生成车牌号码部分
        String numberPart = generateNumberPart(isNewEnergy, includeIO);

        return provinceCode + cityCode + numberPart;
    }

    /**
     * 确定是否为新能源车牌。
     * 
     * @param plateType 车牌类型参数
     * @return 是否为新能源车牌
     */
    private boolean determineNewEnergyType(String plateType) {
        return switch (plateType.toUpperCase()) {
            case "NEW_ENERGY", "ELECTRIC", "GREEN" -> true;
            case "FUEL", "TRADITIONAL", "BLUE" -> false;
            default -> random.nextBoolean(); // BOTH或其他情况随机选择
        };
    }

    /**
     * 选择省份代码。
     * 
     * @param province 指定的省份
     * @return 省份简称
     */
    private String selectProvinceCode(String province) {
        if (province != null && !province.trim().isEmpty()) {
            String cleanProvince = province.trim();

            // 如果直接是省份简称
            if (cleanProvince.length() == 1 && COMMON_PROVINCES.contains(cleanProvince)) {
                return cleanProvince;
            }

            // 如果是省份全名，查找对应简称
            String code = PROVINCE_CODES.get(cleanProvince);
            if (code != null) {
                return code;
            }

            // 如果是省份全名的一部分，模糊匹配
            for (Map.Entry<String, String> entry : PROVINCE_CODES.entrySet()) {
                if (entry.getKey().contains(cleanProvince) || cleanProvince.contains(entry.getKey())) {
                    return entry.getValue();
                }
            }

            logger.warn("Unknown province: {}, using random province", province);
        }

        // 随机选择一个常用省份
        return COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size()));
    }

    /**
     * 选择城市代码。
     * 
     * @param city 指定的城市代码
     * @return 城市代码字母
     */
    private String selectCityCode(String city) {
        if (city != null && !city.trim().isEmpty()) {
            String cleanCity = city.trim().toUpperCase();

            // 如果是单个字母且在有效范围内
            if (cleanCity.length() == 1 && CITY_CODES.contains(cleanCity)) {
                return cleanCity;
            }

            logger.warn("Invalid city code: {}, using random city code", city);
        }

        // 随机选择一个城市代码
        return CITY_CODES.get(random.nextInt(CITY_CODES.size()));
    }

    /**
     * 生成车牌号码部分。
     * 
     * @param isNewEnergy 是否为新能源车牌
     * @param includeIO   是否包含I和O
     * @return 号码部分
     */
    private String generateNumberPart(boolean isNewEnergy, boolean includeIO) {
        if (isNewEnergy) {
            return generateNewEnergyNumberPart(includeIO);
        } else {
            return generateTraditionalNumberPart(includeIO);
        }
    }

    /**
     * 生成新能源车牌号码部分（6位）。
     * 
     * @param includeIO 是否包含I和O
     * @return 新能源车牌号码部分
     */
    private String generateNewEnergyNumberPart(boolean includeIO) {
        StringBuilder numberPart = new StringBuilder();
        String charSet = includeIO ? PLATE_CHARS + "IO" : PLATE_CHARS;

        // 新能源车牌：D或F开头，后面5位字母数字
        String prefix = NEW_ENERGY_PREFIXES.get(random.nextInt(NEW_ENERGY_PREFIXES.size()));
        numberPart.append(prefix);

        // 生成后5位
        for (int i = 0; i < 5; i++) {
            numberPart.append(charSet.charAt(random.nextInt(charSet.length())));
        }

        return numberPart.toString();
    }

    /**
     * 生成传统车牌号码部分（5位）。
     * 
     * @param includeIO 是否包含I和O
     * @return 传统车牌号码部分
     */
    private String generateTraditionalNumberPart(boolean includeIO) {
        StringBuilder numberPart = new StringBuilder();
        String charSet = includeIO ? PLATE_CHARS + "IO" : PLATE_CHARS;

        // 传统车牌：5位字母数字组合
        for (int i = 0; i < 5; i++) {
            numberPart.append(charSet.charAt(random.nextInt(charSet.length())));
        }

        return numberPart.toString();
    }

    /**
     * 生成无效的车牌号。
     * 
     * @return 无效的车牌号
     */
    private String generateInvalidLicensePlate() {
        int type = random.nextInt(4);

        return switch (type) {
            case 0 -> generateWrongLengthPlate();
            case 1 -> generateWrongFormatPlate();
            case 2 -> generateInvalidCharsPlate();
            default -> generateOtherInvalidPlate();
        };
    }

    /**
     * 生成长度错误的车牌号。
     * 
     * @return 长度错误的车牌号
     */
    private String generateWrongLengthPlate() {
        // 生成过短或过长的车牌号
        int length = random.nextBoolean() ? random.nextInt(4) + 2 : // 2-5位
                random.nextInt(5) + 9; // 9-13位

        StringBuilder plate = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (i == 0) {
                // 第一位用省份简称
                plate.append(COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size())));
            } else {
                plate.append(PLATE_CHARS.charAt(random.nextInt(PLATE_CHARS.length())));
            }
        }

        return plate.toString();
    }

    /**
     * 生成格式错误的车牌号。
     * 
     * @return 格式错误的车牌号
     */
    private String generateWrongFormatPlate() {
        // 生成不符合省份+城市+号码格式的车牌
        return "1A12345"; // 第一位不是汉字
    }

    /**
     * 生成包含非法字符的车牌号。
     * 
     * @return 包含非法字符的车牌号
     */
    private String generateInvalidCharsPlate() {
        String province = COMMON_PROVINCES.get(random.nextInt(COMMON_PROVINCES.size()));
        String city = CITY_CODES.get(random.nextInt(CITY_CODES.size()));

        // 在号码部分包含非法字符
        String[] invalidChars = { "I", "O", "!", "@", "#", "$", "%" };
        String invalidChar = invalidChars[random.nextInt(invalidChars.length)];

        return province + city + "123" + invalidChar + "5";
    }

    /**
     * 生成其他类型的无效车牌号。
     * 
     * @return 其他无效车牌号
     */
    private String generateOtherInvalidPlate() {
        // 生成全数字或全字母的车牌号
        if (random.nextBoolean()) {
            return "1234567"; // 全数字
        } else {
            return "ABCDEFG"; // 全字母
        }
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
        if (config == null || config.getParams() == null) {
            return defaultValue;
        }

        Object value = config.getParams().get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
        if (config == null || config.getParams() == null) {
            return defaultValue;
        }

        Object value = config.getParams().get(key);
        if (value == null) {
            return defaultValue;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        return Boolean.parseBoolean(value.toString());
    }

    @Override
    public String getDescription() {
        return "License plate generator - generates Chinese license plate numbers with fuel/electric vehicle support";
    }
}