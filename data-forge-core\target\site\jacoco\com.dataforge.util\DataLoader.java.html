<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataLoader.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.util</a> &gt; <span class="el_source">DataLoader.java</span></div><h1>DataLoader.java</h1><pre class="source lang-java linenums">package com.dataforge.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据加载工具类。
 * 
 * &lt;p&gt;
 * 用于从资源文件或外部文件加载数据，支持缓存和权重解析。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="nc" id="L25">public class DataLoader {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(DataLoader.class);</span>

    /**
     * 数据缓存，避免重复加载。
     */
<span class="nc" id="L32">    private static final Map&lt;String, List&lt;String&gt;&gt; DATA_CACHE = new ConcurrentHashMap&lt;&gt;();</span>

    /**
     * 权重数据缓存。
     */
<span class="nc" id="L37">    private static final Map&lt;String, Map&lt;String, Integer&gt;&gt; WEIGHT_CACHE = new ConcurrentHashMap&lt;&gt;();</span>

    /**
     * 从资源文件加载数据列表。
     * 
     * @param resourcePath 资源文件路径
     * @return 数据列表
     */
    public static List&lt;String&gt; loadDataFromResource(String resourcePath) {
<span class="nc" id="L46">        return DATA_CACHE.computeIfAbsent(resourcePath, path -&gt; {</span>
<span class="nc" id="L47">            List&lt;String&gt; data = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L49">            try (InputStream inputStream = DataLoader.class.getClassLoader().getResourceAsStream(path);</span>
<span class="nc" id="L50">                    BufferedReader reader = new BufferedReader(</span>
                            new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

<span class="nc bnc" id="L53" title="All 2 branches missed.">                if (inputStream == null) {</span>
<span class="nc" id="L54">                    logger.warn(&quot;Resource not found: {}&quot;, path);</span>
<span class="nc" id="L55">                    return data;</span>
                }

                String line;
<span class="nc bnc" id="L59" title="All 2 branches missed.">                while ((line = reader.readLine()) != null) {</span>
<span class="nc" id="L60">                    line = line.trim();</span>

                    // 跳过空行和注释行
<span class="nc bnc" id="L63" title="All 4 branches missed.">                    if (line.isEmpty() || line.startsWith(&quot;#&quot;)) {</span>
<span class="nc" id="L64">                        continue;</span>
                    }

                    // 解析权重格式：item:weight 或 item
<span class="nc" id="L68">                    String item = parseItem(line);</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">                    if (!item.isEmpty()) {</span>
<span class="nc" id="L70">                        data.add(item);</span>
                    }
<span class="nc" id="L72">                }</span>

<span class="nc" id="L74">                logger.debug(&quot;Loaded {} items from resource: {}&quot;, data.size(), path);</span>

<span class="nc bnc" id="L76" title="All 2 branches missed.">            } catch (IOException e) {</span>
<span class="nc" id="L77">                logger.error(&quot;Failed to load data from resource: {}&quot;, path, e);</span>
<span class="nc" id="L78">            }</span>

<span class="nc" id="L80">            return data;</span>
        });
    }

    /**
     * 从资源文件加载带权重的数据。
     * 
     * @param resourcePath 资源文件路径
     * @return 数据权重映射
     */
    public static Map&lt;String, Integer&gt; loadWeightedDataFromResource(String resourcePath) {
<span class="nc" id="L91">        return WEIGHT_CACHE.computeIfAbsent(resourcePath, path -&gt; {</span>
<span class="nc" id="L92">            Map&lt;String, Integer&gt; weightedData = new HashMap&lt;&gt;();</span>

<span class="nc" id="L94">            try (InputStream inputStream = DataLoader.class.getClassLoader().getResourceAsStream(path);</span>
<span class="nc" id="L95">                    BufferedReader reader = new BufferedReader(</span>
                            new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

<span class="nc bnc" id="L98" title="All 2 branches missed.">                if (inputStream == null) {</span>
<span class="nc" id="L99">                    logger.warn(&quot;Resource not found: {}&quot;, path);</span>
<span class="nc" id="L100">                    return weightedData;</span>
                }

                String line;
<span class="nc bnc" id="L104" title="All 2 branches missed.">                while ((line = reader.readLine()) != null) {</span>
<span class="nc" id="L105">                    line = line.trim();</span>

                    // 跳过空行和注释行
<span class="nc bnc" id="L108" title="All 4 branches missed.">                    if (line.isEmpty() || line.startsWith(&quot;#&quot;)) {</span>
<span class="nc" id="L109">                        continue;</span>
                    }

                    // 解析权重格式：item:weight 或 item
<span class="nc" id="L113">                    String[] parts = line.split(&quot;:&quot;);</span>
<span class="nc" id="L114">                    String item = parts[0].trim();</span>
<span class="nc" id="L115">                    int weight = 1; // 默认权重</span>

<span class="nc bnc" id="L117" title="All 2 branches missed.">                    if (parts.length &gt; 1) {</span>
                        try {
<span class="nc" id="L119">                            weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L120">                        } catch (NumberFormatException e) {</span>
<span class="nc" id="L121">                            logger.warn(&quot;Invalid weight format in line: {}, using default weight 1&quot;, line);</span>
<span class="nc" id="L122">                        }</span>
                    }

<span class="nc bnc" id="L125" title="All 2 branches missed.">                    if (!item.isEmpty()) {</span>
<span class="nc" id="L126">                        weightedData.put(item, weight);</span>
                    }
<span class="nc" id="L128">                }</span>

<span class="nc" id="L130">                logger.debug(&quot;Loaded {} weighted items from resource: {}&quot;, weightedData.size(), path);</span>

<span class="nc bnc" id="L132" title="All 2 branches missed.">            } catch (IOException e) {</span>
<span class="nc" id="L133">                logger.error(&quot;Failed to load weighted data from resource: {}&quot;, path, e);</span>
<span class="nc" id="L134">            }</span>

<span class="nc" id="L136">            return weightedData;</span>
        });
    }

    /**
     * 根据权重随机选择数据项。
     * 
     * @param weightedData 权重数据映射
     * @param random       随机数生成器
     * @return 选中的数据项
     */
    public static String selectByWeight(Map&lt;String, Integer&gt; weightedData, java.util.Random random) {
<span class="nc bnc" id="L148" title="All 2 branches missed.">        if (weightedData.isEmpty()) {</span>
<span class="nc" id="L149">            return null;</span>
        }

        // 计算总权重
<span class="nc" id="L153">        int totalWeight = weightedData.values().stream().mapToInt(Integer::intValue).sum();</span>

        // 生成随机数
<span class="nc" id="L156">        int randomValue = random.nextInt(totalWeight);</span>

        // 根据权重选择
<span class="nc" id="L159">        int currentWeight = 0;</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">        for (Map.Entry&lt;String, Integer&gt; entry : weightedData.entrySet()) {</span>
<span class="nc" id="L161">            currentWeight += entry.getValue();</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">            if (randomValue &lt; currentWeight) {</span>
<span class="nc" id="L163">                return entry.getKey();</span>
            }
<span class="nc" id="L165">        }</span>

        // 理论上不应该到达这里，返回第一个元素作为fallback
<span class="nc" id="L168">        return weightedData.keySet().iterator().next();</span>
    }

    /**
     * 从外部文件加载数据列表。
     * 
     * @param filePath 文件路径
     * @return 数据列表
     */
    public static List&lt;String&gt; loadDataFromFile(String filePath) {
<span class="nc" id="L178">        List&lt;String&gt; data = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L180">        try (BufferedReader reader = java.nio.file.Files.newBufferedReader(</span>
<span class="nc" id="L181">                java.nio.file.Paths.get(filePath), StandardCharsets.UTF_8)) {</span>

            String line;
<span class="nc bnc" id="L184" title="All 2 branches missed.">            while ((line = reader.readLine()) != null) {</span>
<span class="nc" id="L185">                line = line.trim();</span>

                // 跳过空行和注释行
<span class="nc bnc" id="L188" title="All 4 branches missed.">                if (line.isEmpty() || line.startsWith(&quot;#&quot;)) {</span>
<span class="nc" id="L189">                    continue;</span>
                }

<span class="nc" id="L192">                String item = parseItem(line);</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">                if (!item.isEmpty()) {</span>
<span class="nc" id="L194">                    data.add(item);</span>
                }
<span class="nc" id="L196">            }</span>

<span class="nc" id="L198">            logger.debug(&quot;Loaded {} items from file: {}&quot;, data.size(), filePath);</span>

<span class="nc" id="L200">        } catch (IOException e) {</span>
<span class="nc" id="L201">            logger.error(&quot;Failed to load data from file: {}&quot;, filePath, e);</span>
<span class="nc" id="L202">        }</span>

<span class="nc" id="L204">        return data;</span>
    }

    /**
     * 解析数据项，去除权重部分。
     * 
     * @param line 原始行
     * @return 数据项
     */
    private static String parseItem(String line) {
<span class="nc" id="L214">        String[] parts = line.split(&quot;:&quot;);</span>
<span class="nc" id="L215">        return parts[0].trim();</span>
    }

    /**
     * 清除缓存。
     */
    public static void clearCache() {
<span class="nc" id="L222">        DATA_CACHE.clear();</span>
<span class="nc" id="L223">        WEIGHT_CACHE.clear();</span>
<span class="nc" id="L224">        logger.debug(&quot;Data cache cleared&quot;);</span>
<span class="nc" id="L225">    }</span>

    /**
     * 获取缓存统计信息。
     * 
     * @return 缓存统计信息
     */
    public static String getCacheStats() {
<span class="nc" id="L233">        return String.format(&quot;Data cache: %d entries, Weight cache: %d entries&quot;,</span>
<span class="nc" id="L234">                DATA_CACHE.size(), WEIGHT_CACHE.size());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>