<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RandomNumberGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">RandomNumberGenerator.java</span></div><h1>RandomNumberGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.List;

/**
 * 随机数生成器
 * 
 * &lt;p&gt;
 * 支持生成各种类型的随机数，包括整数、长整数、大整数等，
 * 用于数值测试、性能测试、统计分析等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;type: 数值类型 (INT|LONG|BIGINT|BYTE|SHORT) 默认: INT&lt;/li&gt;
 * &lt;li&gt;min: 最小值 默认: 0&lt;/li&gt;
 * &lt;li&gt;max: 最大值 默认: 100&lt;/li&gt;
 * &lt;li&gt;distribution: 分布类型 (UNIFORM|NORMAL|EXPONENTIAL|POISSON) 默认: UNIFORM&lt;/li&gt;
 * &lt;li&gt;mean: 正态分布的均值（仅对NORMAL分布有效）默认: 50&lt;/li&gt;
 * &lt;li&gt;stddev: 正态分布的标准差（仅对NORMAL分布有效）默认: 15&lt;/li&gt;
 * &lt;li&gt;lambda: 指数分布/泊松分布的参数 默认: 1.0&lt;/li&gt;
 * &lt;li&gt;positive_only: 是否只生成正数 默认: false&lt;/li&gt;
 * &lt;li&gt;exclude_zero: 是否排除零 默认: false&lt;/li&gt;
 * &lt;li&gt;format: 输出格式 (DECIMAL|HEX|OCTAL|BINARY|SCIENTIFIC) 默认: DECIMAL&lt;/li&gt;
 * &lt;li&gt;precision: 精度位数（用于大数）默认: 10&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L40">public class RandomNumberGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L42">    private static final Logger logger = LoggerFactory.getLogger(RandomNumberGenerator.class);</span>
<span class="fc" id="L43">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 数值类型枚举
<span class="fc" id="L46">    public enum NumberType {</span>
<span class="fc" id="L47">        INT(&quot;32位整数&quot;),</span>
<span class="fc" id="L48">        LONG(&quot;64位长整数&quot;),</span>
<span class="fc" id="L49">        BIGINT(&quot;大整数&quot;),</span>
<span class="fc" id="L50">        BYTE(&quot;8位字节&quot;),</span>
<span class="fc" id="L51">        SHORT(&quot;16位短整数&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L55">        NumberType(String description) {</span>
<span class="fc" id="L56">            this.description = description;</span>
<span class="fc" id="L57">        }</span>
        
        public String getDescription() {
<span class="nc" id="L60">            return description;</span>
        }
    }
    
    // 分布类型枚举
<span class="fc" id="L65">    public enum DistributionType {</span>
<span class="fc" id="L66">        UNIFORM(&quot;均匀分布&quot;),</span>
<span class="fc" id="L67">        NORMAL(&quot;正态分布&quot;),</span>
<span class="fc" id="L68">        EXPONENTIAL(&quot;指数分布&quot;),</span>
<span class="fc" id="L69">        POISSON(&quot;泊松分布&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L73">        DistributionType(String description) {</span>
<span class="fc" id="L74">            this.description = description;</span>
<span class="fc" id="L75">        }</span>
        
        public String getDescription() {
<span class="nc" id="L78">            return description;</span>
        }
    }
    
    // 输出格式枚举
<span class="fc" id="L83">    public enum OutputFormat {</span>
<span class="fc" id="L84">        DECIMAL(&quot;十进制&quot;),</span>
<span class="fc" id="L85">        HEX(&quot;十六进制&quot;),</span>
<span class="fc" id="L86">        OCTAL(&quot;八进制&quot;),</span>
<span class="fc" id="L87">        BINARY(&quot;二进制&quot;),</span>
<span class="fc" id="L88">        SCIENTIFIC(&quot;科学计数法&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L92">        OutputFormat(String description) {</span>
<span class="fc" id="L93">            this.description = description;</span>
<span class="fc" id="L94">        }</span>
        
        public String getDescription() {
<span class="nc" id="L97">            return description;</span>
        }
    }

    @Override
    public String getType() {
<span class="fc" id="L103">        return &quot;random_number&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L108">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取数值类型
<span class="fc" id="L115">            String typeStr = getStringParam(config, &quot;type&quot;, &quot;INT&quot;);</span>
<span class="fc" id="L116">            NumberType numberType = parseNumberType(typeStr);</span>
            
            // 获取分布类型
<span class="fc" id="L119">            String distributionStr = getStringParam(config, &quot;distribution&quot;, &quot;UNIFORM&quot;);</span>
<span class="fc" id="L120">            DistributionType distribution = parseDistributionType(distributionStr);</span>
            
            // 获取输出格式
<span class="fc" id="L123">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;DECIMAL&quot;);</span>
<span class="fc" id="L124">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 生成随机数
<span class="fc" id="L127">            Number number = generateRandomNumber(numberType, distribution, config);</span>
            
            // 格式化输出
<span class="fc" id="L130">            return formatNumber(number, format, numberType);</span>
            
<span class="nc" id="L132">        } catch (Exception e) {</span>
<span class="nc" id="L133">            logger.error(&quot;Failed to generate random number&quot;, e);</span>
            // 返回一个默认的随机数作为fallback
<span class="nc" id="L135">            return String.valueOf(random.nextInt(100));</span>
        }
    }

    /**
     * 解析数值类型
     */
    private NumberType parseNumberType(String typeStr) {
        try {
<span class="fc" id="L144">            return NumberType.valueOf(typeStr.toUpperCase());</span>
<span class="fc" id="L145">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L146">            logger.warn(&quot;Invalid number type: {}, using INT as default&quot;, typeStr);</span>
<span class="fc" id="L147">            return NumberType.INT;</span>
        }
    }

    /**
     * 解析分布类型
     */
    private DistributionType parseDistributionType(String distributionStr) {
        try {
<span class="fc" id="L156">            return DistributionType.valueOf(distributionStr.toUpperCase());</span>
<span class="fc" id="L157">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L158">            logger.warn(&quot;Invalid distribution type: {}, using UNIFORM as default&quot;, distributionStr);</span>
<span class="fc" id="L159">            return DistributionType.UNIFORM;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L168">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L169">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L170">            logger.warn(&quot;Invalid output format: {}, using DECIMAL as default&quot;, formatStr);</span>
<span class="nc" id="L171">            return OutputFormat.DECIMAL;</span>
        }
    }

    /**
     * 生成随机数
     */
    private Number generateRandomNumber(NumberType numberType, DistributionType distribution, FieldConfig config) {
        // 获取范围参数
<span class="fc" id="L180">        long min = getLongParam(config, &quot;min&quot;, 0L);</span>
<span class="fc" id="L181">        long max = getLongParam(config, &quot;max&quot;, 100L);</span>
        
        // 获取其他参数
<span class="fc" id="L184">        boolean positiveOnly = getBooleanParam(config, &quot;positive_only&quot;, false);</span>
<span class="fc" id="L185">        boolean excludeZero = getBooleanParam(config, &quot;exclude_zero&quot;, false);</span>
        
        // 调整范围
<span class="pc bpc" id="L188" title="3 of 4 branches missed.">        if (positiveOnly &amp;&amp; min &lt;= 0) {</span>
<span class="nc" id="L189">            min = 1;</span>
        }
        
<span class="pc bpc" id="L192" title="5 of 6 branches missed.">        if (excludeZero &amp;&amp; min &lt;= 0 &amp;&amp; max &gt;= 0) {</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">            if (min == 0) min = 1;</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">            if (max == 0) max = 1;</span>
        }
        
        // 根据分布类型生成数值
        double value;
<span class="pc bpc" id="L199" title="3 of 5 branches missed.">        switch (distribution) {</span>
            case UNIFORM:
<span class="fc" id="L201">                value = generateUniform(min, max);</span>
<span class="fc" id="L202">                break;</span>
            case NORMAL:
<span class="fc" id="L204">                value = generateNormal(config, min, max);</span>
<span class="fc" id="L205">                break;</span>
            case EXPONENTIAL:
<span class="nc" id="L207">                value = generateExponential(config, min, max);</span>
<span class="nc" id="L208">                break;</span>
            case POISSON:
<span class="nc" id="L210">                value = generatePoisson(config, min, max);</span>
<span class="nc" id="L211">                break;</span>
            default:
<span class="nc" id="L213">                value = generateUniform(min, max);</span>
                break;
        }
        
        // 转换为指定的数值类型
<span class="fc" id="L218">        return convertToNumberType(value, numberType, config);</span>
    }

    /**
     * 生成均匀分布随机数
     */
    private double generateUniform(long min, long max) {
<span class="pc bpc" id="L225" title="1 of 2 branches missed.">        if (min &gt;= max) {</span>
<span class="nc" id="L226">            return min;</span>
        }
<span class="fc" id="L228">        return min + random.nextDouble() * (max - min);</span>
    }

    /**
     * 生成正态分布随机数
     */
    private double generateNormal(FieldConfig config, long min, long max) {
<span class="fc" id="L235">        double mean = getDoubleParam(config, &quot;mean&quot;, (min + max) / 2.0);</span>
<span class="fc" id="L236">        double stddev = getDoubleParam(config, &quot;stddev&quot;, (max - min) / 6.0);</span>
        
        double value;
        do {
<span class="fc" id="L240">            value = random.nextGaussian() * stddev + mean;</span>
<span class="pc bpc" id="L241" title="2 of 4 branches missed.">        } while (value &lt; min || value &gt; max);</span>
        
<span class="fc" id="L243">        return value;</span>
    }

    /**
     * 生成指数分布随机数
     */
    private double generateExponential(FieldConfig config, long min, long max) {
<span class="nc" id="L250">        double lambda = getDoubleParam(config, &quot;lambda&quot;, 1.0);</span>
        
        double value;
        do {
<span class="nc" id="L254">            value = -Math.log(1 - random.nextDouble()) / lambda + min;</span>
<span class="nc bnc" id="L255" title="All 2 branches missed.">        } while (value &gt; max);</span>
        
<span class="nc" id="L257">        return value;</span>
    }

    /**
     * 生成泊松分布随机数
     */
    private double generatePoisson(FieldConfig config, long min, long max) {
<span class="nc" id="L264">        double lambda = getDoubleParam(config, &quot;lambda&quot;, (max - min) / 2.0);</span>
        
        // 使用Knuth算法生成泊松分布
<span class="nc" id="L267">        double L = Math.exp(-lambda);</span>
<span class="nc" id="L268">        double p = 1.0;</span>
<span class="nc" id="L269">        int k = 0;</span>
        
        do {
<span class="nc" id="L272">            k++;</span>
<span class="nc" id="L273">            p *= random.nextDouble();</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">        } while (p &gt; L);</span>
        
<span class="nc" id="L276">        double value = k - 1 + min;</span>
<span class="nc" id="L277">        return Math.min(value, max);</span>
    }

    /**
     * 转换为指定的数值类型
     */
    private Number convertToNumberType(double value, NumberType numberType, FieldConfig config) {
<span class="pc bpc" id="L284" title="3 of 6 branches missed.">        switch (numberType) {</span>
            case BYTE:
<span class="nc" id="L286">                return (byte) Math.max(Byte.MIN_VALUE, Math.min(Byte.MAX_VALUE, Math.round(value)));</span>
            case SHORT:
<span class="nc" id="L288">                return (short) Math.max(Short.MIN_VALUE, Math.min(Short.MAX_VALUE, Math.round(value)));</span>
            case INT:
<span class="fc" id="L290">                return (int) Math.max(Integer.MIN_VALUE, Math.min(Integer.MAX_VALUE, Math.round(value)));</span>
            case LONG:
<span class="fc" id="L292">                return Math.round(value);</span>
            case BIGINT:
<span class="fc" id="L294">                int precision = getIntParam(config, &quot;precision&quot;, 10);</span>
<span class="fc" id="L295">                return generateBigInteger(value, precision);</span>
            default:
<span class="nc" id="L297">                return Math.round(value);</span>
        }
    }

    /**
     * 生成大整数
     */
    private BigInteger generateBigInteger(double baseValue, int precision) {
        // 基于baseValue生成大整数
<span class="fc" id="L306">        StringBuilder sb = new StringBuilder();</span>
        
        // 添加符号
<span class="pc bpc" id="L309" title="1 of 2 branches missed.">        if (baseValue &lt; 0) {</span>
<span class="nc" id="L310">            sb.append(&quot;-&quot;);</span>
<span class="nc" id="L311">            baseValue = Math.abs(baseValue);</span>
        }
        
        // 生成第一位（非零）
<span class="fc" id="L315">        sb.append(1 + random.nextInt(9));</span>
        
        // 生成剩余位数
<span class="fc bfc" id="L318" title="All 2 branches covered.">        for (int i = 1; i &lt; precision; i++) {</span>
<span class="fc" id="L319">            sb.append(random.nextInt(10));</span>
        }
        
<span class="fc" id="L322">        return new BigInteger(sb.toString());</span>
    }

    /**
     * 格式化数值
     */
    private String formatNumber(Number number, OutputFormat format, NumberType numberType) {
<span class="pc bpc" id="L329" title="3 of 6 branches missed.">        switch (format) {</span>
            case DECIMAL:
<span class="fc" id="L331">                return number.toString();</span>
            case HEX:
<span class="fc" id="L333">                return formatAsHex(number);</span>
            case OCTAL:
<span class="nc" id="L335">                return formatAsOctal(number);</span>
            case BINARY:
<span class="fc" id="L337">                return formatAsBinary(number);</span>
            case SCIENTIFIC:
<span class="nc" id="L339">                return formatAsScientific(number);</span>
            default:
<span class="nc" id="L341">                return number.toString();</span>
        }
    }

    /**
     * 格式化为十六进制
     */
    private String formatAsHex(Number number) {
<span class="pc bpc" id="L349" title="1 of 2 branches missed.">        if (number instanceof BigInteger) {</span>
<span class="nc" id="L350">            return &quot;0x&quot; + ((BigInteger) number).toString(16).toUpperCase();</span>
        } else {
<span class="fc" id="L352">            return &quot;0x&quot; + Long.toHexString(number.longValue()).toUpperCase();</span>
        }
    }

    /**
     * 格式化为八进制
     */
    private String formatAsOctal(Number number) {
<span class="nc bnc" id="L360" title="All 2 branches missed.">        if (number instanceof BigInteger) {</span>
<span class="nc" id="L361">            return &quot;0&quot; + ((BigInteger) number).toString(8);</span>
        } else {
<span class="nc" id="L363">            return &quot;0&quot; + Long.toOctalString(number.longValue());</span>
        }
    }

    /**
     * 格式化为二进制
     */
    private String formatAsBinary(Number number) {
<span class="pc bpc" id="L371" title="1 of 2 branches missed.">        if (number instanceof BigInteger) {</span>
<span class="nc" id="L372">            return &quot;0b&quot; + ((BigInteger) number).toString(2);</span>
        } else {
<span class="fc" id="L374">            return &quot;0b&quot; + Long.toBinaryString(number.longValue());</span>
        }
    }

    /**
     * 格式化为科学计数法
     */
    private String formatAsScientific(Number number) {
<span class="nc" id="L382">        double value = number.doubleValue();</span>
<span class="nc" id="L383">        return String.format(&quot;%.3E&quot;, value);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>