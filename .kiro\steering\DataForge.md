<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
------------------------------------------------------------------------------------->
### **DataForge 项目全面设计方案 (Java 版) - 修订版**

---

## 一、项目目标 (Revised)

`DataForge`旨在成为一款高效、灵活且高度可配置的测试数据生成工具，专注于为软件测试团队提供高质量、真实且多样化的测试数据。

- **核心用户价值：** 显著提升自动化测试和功能测试的数据准备效率，降低测试数据维护成本，提高测试覆盖率和数据真实性。

- **自动化测试赋能：** 提供强大的命令行接口（CLI）和可编程API，无缝集成到自动化测试框架和CI/CD流程中，支持大规模、高并发的数据生成和注入。

- **功能测试辅助：** 通过易于理解的CLI配置，快速生成特定场景、边界条件和异常数据，满足功能测试对数据多样性和精准性的需求。

- **数据多样性与真实性：** 能够根据预设规则或配置，自动批量生成各类测试数据，尤其在**中国特定数据类型（如身份证、银行卡、统一社会信用代码等）的真实性、校验规则和数据关联性方面达到业界领先水平**。

- **可配置性与扩展性：** 支持用户通过CLI参数和配置文件灵活自定义数据生成规则，并提供清晰的接口和插件机制，易于添加新的数据类型生成器或集成外部数据源。

- **高性能与可伸缩性：** 能够快速生成大量数据，并支持未来数据量和数据类型的增长，确保在大规模测试场景下的高效运行。

---

## 二、核心功能模块 (Revised)

`DataForge`的核心功能模块围绕数据生成、配置、输出和扩展展开，旨在为测试人员提供端到端的数据准备解决方案。

1. **数据类型定义与生成引擎模块：**

    - **职责：** 定义每种数据类型的生成规则，并根据规则调用相应的生成算法，生成单个或批量数据。

    - **核心能力：**

        - **内置丰富数据类型：** 提供涵盖基础信息、标识、联系通信、网络设备、文本、结构化、数值、时间、安全注入、媒体文件、枚举状态码及特殊场景的全面数据类型生成器。

        - **复杂数据关联性支持：** 允许用户定义多个数据字段之间的逻辑关联（例如，身份证号与年龄、出生日期、性别、地址的关联），确保生成的数据集逻辑自洽。

        - **多种生成模式：** 支持随机生成（默认）、顺序生成、基于模板生成、特定分布生成等。

        - **数据校验器集成：** 内置或可配置的数据校验逻辑，确保生成的数据符合其自身的格式和业务规则（例如，身份证号校验位、银行卡Luhn算法）。

    - **CLI体现：** 通过命令行参数指定数据类型、数量、范围、关联字段等。

2. **配置管理模块：**

    - **职责：** 允许用户通过配置文件或命令行参数定义数据生成规则和输出选项，支持保存和加载常用配置。

    - **核心能力：**

        - **CLI参数解析：** 使用Apache Commons CLI等库高效解析命令行输入。

        - **配置文件支持：** 支持YAML/JSON格式的配置文件，方便用户定义复杂的数据结构、嵌套关系和批量生成任务。

        - **配置模板化：** 允许用户将常用的数据生成配置保存为模板，实现“开箱即用”的快速生成。

    - **CLI体现：** `--config <path/to/config.yaml>` 加载配置，或直接通过 `--type <type> --param <value>` 进行参数化。

3. **数据输出模块：**

    - **职责：** 将生成的数据以多种格式输出到指定目标。

    - **核心能力：**

        - **多格式输出：** 支持CSV、JSON、XML、SQL `INSERT`语句等常见格式。

        - **多目标输出：** 支持输出到文件、标准输出（stdout）、或直接写入数据库。

        - **大文件处理：** 考虑流式写入，避免内存溢出。

    - **CLI体现：** `--output.format <CSV/JSON/SQL>`、`--output.file <path>`、`--output.db <connection_string>`等参数。

4. **扩展机制模块：**

    - **职责：** 提供清晰的接口和插件式架构，允许用户或开发者轻松添加新的数据类型生成器或集成外部数据源。

    - **核心能力：**

        - **`DataGenerator` 接口/抽象类：** 定义统一的生成器规范。

        - **`GeneratorFactory`：** 负责根据请求的数据类型返回对应的生成器实例。

        - **Java ServiceLoader机制：** 支持通过SPI（Service Provider Interface）机制动态加载自定义生成器，实现“深度定制”和生态扩展。

        - **外部数据源集成：** 允许用户指定外部文件（如自定义姓氏库、地址库）作为生成器的数据输入。

    - **CLI体现：** 用户可以通过配置指定自定义生成器的类路径或插件目录。

5. **数据校验模块 (推荐集成)：**

    - **职责：** 对生成的数据进行基本格式和逻辑校验，确保生成的数据是“有效”的。

    - **核心能力：**

        - **内置校验规则：** 针对身份证号、银行卡号等具有明确校验算法的数据类型，自动进行校验。

        - **可配置校验：** 允许用户定义额外的校验规则（如正则表达式）。

        - **校验结果反馈：** 在CLI输出中明确提示哪些数据生成成功，哪些失败及失败原因。

    - **CLI体现：** `--validate <true/false>` 参数控制是否启用校验，并在输出中提供校验报告。

---

### **三、数据类型生成功能**

#### **1. 基础信息类 (Basic Information)**

##### **1.1 姓名 (Name)**

- **核心生成逻辑/规则：**

  - **中文姓名：** 从预设的**公安部/国家统计局公开的姓氏库（含频率）**和常用名字库中，根据频率加权随机组合。名字库可由常用汉字随机组合或从常用名字列表中选择。

  - **英文姓名：** 从预设的英文名（First Name）和英文姓（Last Name）库中随机组合。

- **CLI配置参数建议：**

  - `--name.type <CN|EN|BOTH>`: 指定生成中文姓名、英文姓名或两者混合（默认`BOTH`）。

  - `--name.gender <MALE|FEMALE|ANY>`: 指定生成男性、女性或任意性别的姓名。对于中文名，可根据性别倾向性调整名字组合（默认`ANY`）。

  - `--name.cn.surname_file <path>`: 指定自定义中文姓氏库文件路径（CSV/TXT，含频率）。

  - `--name.cn.givenname_file <path>`: 指定自定义中文名字库文件路径（CSV/TXT）。

  - `--name.en.firstname_file <path>`: 指定自定义英文名库文件路径。

  - `--name.en.lastname_file <path>`: 指定自定义英文姓库文件路径。

- **高级定制/关联性考虑：**

  - **与性别关联：** 如果在同一记录中生成了性别字段，姓名生成器应能根据该性别生成对应名称。

  - **自定义姓名库：** 允许用户通过文件导入特定业务场景下的姓名列表。

  - **姓名拼音生成：** 可选参数，根据生成的中文姓名生成对应的拼音（全拼/首字母），用于后续关联邮箱或账号名。

- **典型用例/场景：**

  - **自动化测试：** 批量生成用户注册数据，包含姓名、性别、邮箱等。

  - **功能测试：** 生成特定姓氏或名字的测试数据，验证系统对姓名特殊字符或长度的兼容性。

- **校验与异常处理建议：**

  - **校验：** 简单字符串，无复杂格式校验。

  - **异常：** 确保编码正确性（UTF-8），避免乱码。

##### **1.2 手机号码 (Phone Number)**

- **核心生成逻辑/规则：**

  - 基于中国大陆运营商号段（如13x, 14x, 15x, 16x, 17x, 18x, 19x）随机选择前缀。

  - 后续数字随机生成，确保总长度为11位。

  - 可支持生成虚拟运营商号段或特定省市号段（需外部数据源支持）。

- **CLI配置参数建议：**

  - `--phone.region <CN>`: 指定国家/地区手机号格式（当前仅中国大陆）。

  - `--phone.prefix <prefix1,prefix2,...>`: 指定生成手机号的前缀列表（如`139,188,170`），不指定则随机选择常用号段。

  - `--phone.valid <true|false>`: 是否保证生成的手机号符合基本号段规则（默认`true`）。设置为`false`可生成非法手机号用于异常测试。

  - `--phone.operator_file <path>`: 指定自定义运营商号段文件路径。

- **高级定制/关联性考虑：**

  - **归属地匹配（未来可扩展）：** 如果有地址字段，可根据地址的省市信息，生成对应归属地的手机号（需要维护庞大的号段-归属地数据）。

  - **连续手机号：** 允许生成一个序列的手机号，用于测试批次处理。

- **典型用例/场景：**

  - **自动化测试：** 批量用户注册、短信验证码接口测试。

  - **功能测试：** 测试手机号输入框的校验（合法/非法格式）、黑名单/白名单功能。

- **校验与异常处理建议：**

  - **校验：** 长度11位，全数字，符合号段规则。

  - **异常：** 生成非11位、包含非数字字符的手机号用于异常测试。

##### **1.3 银行卡号/信用卡号 (Bank Card / Credit Card Number)**

- **核心生成逻辑/规则：**

  - 基于Luhn算法（Mod 10算法）生成有效卡号，确保校验位正确。

  - 通过预设的BIN码（Bank Identification Number）库，模拟不同银行和卡组织（Visa, MasterCard, UnionPay, JCB等）。

  - 根据BIN码确定卡号长度（信用卡16位，部分借记卡13-19位）。

- **CLI配置参数建议：**

  - `--bankcard.type <DEBIT|CREDIT|BOTH>`: 指定生成借记卡、信用卡或两者混合（默认`BOTH`）。

  - `--bankcard.issuer <VISA|MC|UNIONPAY|JCB|ANY>`: 指定卡组织（默认`ANY`）。

  - `--bankcard.bank <bank_code1,bank_code2,...>`: 指定银行（需要预设银行BIN码表，如`ICBC,CCB,CMB`），不指定则随机。

  - `--bankcard.length <int>`: 指定卡号长度（如`16`），若不指定则根据BIN码自动匹配。

  - `--bankcard.valid <true|false>`: 是否保证符合Luhn算法（默认`true`）。设置为`false`可生成无效卡号用于异常测试。

  - `--bankcard.bin_file <path>`: 指定自定义BIN码文件路径。

- **高级定制/关联性考虑：**

  - **卡有效期/CVV/持卡人姓名：** 可作为关联字段一并生成，但需注意CVV是随机数，有效期是日期。

  - **特定卡段生成：** 允许用户指定一个卡号前缀，生成该卡段内的有效卡号。

- **典型用例/场景：**

  - **自动化测试：** 支付接口测试、银行卡绑定流程。

  - **功能测试：** 测试银行卡输入框的校验（合法/非法卡号）、不同卡组织/银行的兼容性。

- **校验与异常处理建议：**

  - **校验：** 长度、数字组成，最重要的是Luhn算法校验通过。

  - **异常：** 生成不符合Luhn算法、长度不正确、包含非数字字符的卡号用于异常测试。

##### **1.4 身份证号码 (ID Card Number)**

- **核心生成逻辑/规则：**

  - 基于中国大陆居民身份证号码18位结构：地区代码(6位) + 出生日期(8位) + 顺序码(3位) + 校验位(1位)。

  - **地区代码：** 从预设的**最新行政区划代码库**中随机选择（可精确到区县级）。

  - **出生日期：** 在指定范围内随机生成，并精确考虑闰年、月份天数。

  - **顺序码：** 随机生成，倒数第二位决定性别（奇数男性，偶数女性）。

  - **校验位：** 根据前17位数字和特定加权算法计算得出。

- **CLI配置参数建议：**

  - `--idcard.region <province_code|city_code|district_code>`: 指定地区代码前缀（如`110000`北京，`330100`杭州，`330106`西湖区），不指定则随机。

  - `--idcard.birth_date_range <YYYY-MM-DD,YYYY-MM-DD>`: 指定出生日期范围（如`1980-01-01,2000-12-31`）。

  - `--idcard.gender <MALE|FEMALE|ANY>`: 指定身份证号中隐含的性别（默认`ANY`）。

  - `--idcard.valid <true|false>`: 是否保证生成有效身份证号（默认`true`）。设置为`false`可生成校验位错误、日期非法等身份证号用于异常测试。

  - `--idcard.region_file <path>`: 指定自定义行政区划代码文件路径。

- **高级定制/关联性考虑：**

  - **与年龄关联：** `-idcard.birth_date_range`参数直接控制年龄范围。

  - **与地址关联：** 如果有详细地址字段，可将身份证号的地区代码与地址中的省市县进行匹配。

  - **少数民族身份证号（未来扩展）：** 身份证号本身不直接关联民族，但可以通过生成特定地区（少数民族聚居区）的身份证号来间接模拟。

- **典型用例/场景：**

  - **自动化测试：** 实名认证接口测试、用户身份信息验证。

  - **功能测试：** 测试身份证输入框的校验（合法/非法格式、不同地区、不同出生日期、不同性别）、年龄限制功能。

- **校验与异常处理建议：**

  - **校验：** 长度18位，地区代码有效，出生日期有效，校验位正确。

  - **异常：** 生成校验位错误、地区代码不存在、出生日期非法（如2月30日）、长度不符、包含非数字字符的身份证号用于异常测试。

##### **1.5 车牌号 (License Plate Number)**

- **核心生成逻辑/规则：**

  - 基于中国大陆车牌号规则：省份简称(1位汉字) + 城市代码(1位字母) + 字母数字组合(5位)。

  - **新能源车牌：** 绿牌，6位字母数字组合，D或F开头。

  - **规避字符：** 默认规避字母I和O（避免与数字1和0混淆），但可配置是否包含。

- **CLI配置参数建议：**

  - `--licenseplate.type <FUEL|NEW_ENERGY|BOTH>`: 指定燃油车牌、新能源车牌或两者混合（默认`BOTH`）。

  - `--licenseplate.province <省份简称代码>`: 指定省份简称（如`京`、`沪`、`粤`），不指定则随机。

  - `--licenseplate.city <城市字母代码>`: 指定城市字母代码（如`A`、`B`），不指定则随机。

  - `--licenseplate.include_io <true|false>`: 是否包含字母I和O（默认`false`）。

  - `--licenseplate.valid <true|false>`: 是否保证符合基本格式规则（默认`true`）。

- **高级定制/关联性考虑：**

  - **特殊车牌：** 可扩展支持教练车、警车、使馆车等特殊车牌前缀。

  - **连续车牌：** 允许生成一个序列的连续车牌号，用于测试库存或批次管理。

- **典型用例/场景：**

  - **自动化测试：** 车辆管理系统接口测试、停车缴费系统。

  - **功能测试：** 测试车牌输入框的校验（合法/非法格式、燃油/新能源车牌）、车牌识别功能。

- **校验与异常处理建议：**

  - **校验：** 长度7位/8位（新能源），格式符合“省份简称+城市代码+字母数字”规则。

  - **异常：** 生成长度不符、格式错误、包含非法字符的车牌号用于异常测试。

##### **1.6 地址 (Address)**

- **核心生成逻辑/规则：**

  - 基于预设的**最新行政区划数据（国家-省-市-区/县-街道）**进行层级关联生成。

  - 街道、小区、详细地址（门牌号）可随机生成或从预设词库中组合。

  - 邮编与区/县进行关联。

- **CLI配置参数建议：**

  - `--address.country <CN>`: 指定国家（当前仅中国大陆）。

  - `--address.province <省份名称|代码>`: 指定省份，不指定则随机。

  - `--address.city <城市名称|代码>`: 指定城市，不指定则随机，但会受省份限制。

  - `--address.district <区县名称|代码>`: 指定区县，不指定则随机，但会受省市限制。

  - `--address.detail_level <STREET|COMMUNITY|FULL>`: 生成到街道、小区还是包含门牌号的完整详细地址（默认`FULL`）。

  - `--address.zipcode <true|false>`: 是否同时生成邮编（默认`true`）。

  - `--address.street_names_file <path>`: 指定自定义街道名称词库。

  - `--address.community_names_file <path>`: 指定自定义小区名称词库。

- **高级定制/关联性考虑：**

  - **地理坐标关联（未来可扩展）：** 与经纬度字段关联，确保地址和坐标的逻辑一致性。

  - **特定区域密集地址：** 允许生成某个城市或区域内的密集地址，用于测试区域服务、物流配送路径。

  - **地址库更新：** 需提供机制更新行政区划数据，确保地址的真实性和时效性。

- **典型用例/场景：**

  - **自动化测试：** 用户收货地址管理、企业注册地址、物流配送系统。

  - **功能测试：** 测试地址输入框的校验（合法/非法地址、不同层级）、地址解析、区域限制功能。

- **校验与异常处理建议：**

  - **校验：** 确保省市县层级关联正确，地址字符串非空。

  - **异常：** 生成层级不匹配（如不存在的区县在存在的市下）、地址过长、包含特殊字符的地址用于异常测试。

##### **1.7 企业名称 (Company Name)**

- **核心生成逻辑/规则：**

  - 从预设的行业关键词、公司类型（有限公司、集团、科技、咨询等）和后缀中随机组合。

  - 可加入随机的地区前缀（如“北京”、“上海”）。

- **CLI配置参数建议：**

  - `--company.industry <IT|FINANCE|RETAIL|ANY>`: 指定行业类型，影响公司名称关键词（默认`ANY`）。

  - `--company.type <CO_LTD|GROUP|INSTITUTE|ANY>`: 指定公司类型后缀（默认`ANY`）。

  - `--company.prefix_region <true|false>`: 是否在名称前加上随机地区前缀（如“北京XXX公司”，默认`true`）。

  - `--company.keywords_file <path>`: 指定自定义行业关键词文件路径。

  - `--company.suffix_file <path>`: 指定自定义公司类型后缀文件路径。

- **高级定制/关联性考虑：**

  - **与统一社会信用代码关联：** 在生成`企业信息`整体数据时，确保USCC与企业名称同时生成并逻辑一致。

  - **指定公司名称前缀/后缀：** 允许用户指定部分公司名称，然后随机补全。

- **典型用例/场景：**

  - **自动化测试：** B端客户管理系统、供应商管理系统、企业认证接口。

  - **功能测试：** 测试企业名称输入框的校验（长度、特殊字符）、企业搜索功能。

- **校验与异常处理建议：**

  - **校验：** 简单字符串，无复杂校验。

  - **异常：** 生成过长、包含非法字符的企业名称用于异常测试。

##### **1.8 统一社会信用代码 (Unified Social Credit Code - USCC)**

- **核心生成逻辑/规则：**

  - 基于中国大陆统一社会信用代码的18位结构：登记管理部门码(1位) + 机构类别码(1位) + 行政区划码(6位) + 主体标识码(9位) + 校验码(1位)。

  - **实现其复杂的校验算法（GB32100-2015标准）**，确保生成的代码完全有效。

- **CLI配置参数建议：**

  - `--uscc.region <行政区划码>`: 指定行政区划码（与身份证号的地区代码相似，默认随机）。

  - `--uscc.valid <true|false>`: 是否保证生成的代码有效（默认`true`）。设置为`false`可生成校验位错误等非法代码用于异常测试。

- **高级定制/关联性考虑：**

  - **与企业名称关联：** 在生成企业信息时，确保USCC与企业名称同时生成并逻辑一致。

- **典型用例/场景：**

  - **自动化测试：** 企业注册、企业认证接口测试。

  - **功能测试：** 测试统一社会信用代码输入框的校验（合法/非法格式）、企业信息查询。

- **校验与异常处理建议：**

  - **校验：** 长度18位，符合编码规则和校验算法（GB32100-2015）。

  - **异常：** 生成校验位错误、长度不符、包含非数字字符的USCC用于异常测试。

##### **1.9 组织机构代码 (Organization Code - 历史数据支持)**

- **核心生成逻辑/规则：**

  - 基于中国大陆组织机构代码的9位结构：8位主体代码 + 1位校验码。

  - **实现其校验算法（GB 11714-1997）**。

- **CLI配置参数建议：**

  - `--orgcode.valid <true|false>`: 是否保证生成的代码有效（默认`true`）。设置为`false`可生成无效代码用于异常测试。

- **高级定制/关联性考虑：**

  - 主要用于模拟历史数据或兼容老系统，不推荐作为新数据生成。

- **典型用例/场景：**

  - **自动化测试：** 兼容老系统数据迁移测试、历史数据查询。

  - **功能测试：** 验证系统对旧版组织机构代码的兼容性。

- **校验与异常处理建议：**

  - **校验：** 长度9位，符合编码规则和校验算法（GB 11714-1997）。

  - **异常：** 生成校验位错误、长度不符、包含非数字字符的组织机构代码用于异常测试。

##### **1.10 LEI码 (Legal Entity Identifier)**

- **核心生成逻辑/规则：**

  - 基于LEI码的20位字母数字组合结构：4位LOU ID + 2位保留位 + 12位实体特定代码 + 2位校验位。

  - **实现其复杂的校验算法（ISO 17442标准）**。

- **CLI配置参数建议：**

  - `--lei.valid <true|false>`: 是否保证生成的代码有效（默认`true`）。

- **高级定制/关联性考虑：**

  - 主要用于国际金融、跨境贸易相关系统。

- **典型用例/场景：**

  - **自动化测试：** 国际金融机构合规系统、跨境交易系统数据。

  - **功能测试：** 验证系统对LEI码的识别和处理。

- **校验与异常处理建议：**

  - **校验：** 长度20位，符合ISO 17442标准和校验算法。

  - **异常：** 生成不符合ISO 17442标准、长度不符、包含非法字符的LEI码用于异常测试。

##### **1.11 年龄 (Age)**

- **核心生成逻辑/规则：**

  - 在指定范围内随机生成整数。

  - 可支持均匀分布或正态分布。

- **CLI配置参数建议：**

  - `--age.min <int>`: 最小年龄（默认18）。

  - `--age.max <int>`: 最大年龄（默认60）。

  - `--age.distribution <UNIFORM|NORMAL>`: 指定年龄分布（均匀分布或正态分布，默认`UNIFORM`）。

  - `--age.precision <int>`: 年龄精度（如`1`表示整数，`0.5`表示可以有半岁，默认`1`）。

- **高级定制/关联性考虑：**

  - **与出生日期关联：** 最常见的关联。如果同时生成出生日期，则年龄应与出生日期精确匹配。

    - **实现方式：** 优先根据出生日期计算年龄；如果只指定年龄，则反推出一个随机的出生日期。

  - **与学历/职业关联：** 高级定制可考虑：如年龄<18，学历不能是本科及以上；年龄>60，职业多为退休等。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据、权限管理（如未成年人限制）。

  - **功能测试：** 测试年龄输入框的校验（合法/非法年龄、边界值）、年龄段筛选功能。

- **校验与异常处理建议：**

  - **校验：** 确保在指定整数范围内，非负。

  - **异常：** 生成负数、超出最大年龄限制的年龄用于异常测试。

##### **1.12 邮箱 (Email)**

- **核心生成逻辑/规则：**

  - **用户名：** 随机字母数字组合，或基于姓名拼音生成。

  - **域名：** 从常用公共域名库（如`qq.com,163.com,gmail.com`）中选择或自定义。

- **CLI配置参数建议：**

  - `--email.domains <domain1,domain2,...>`: 指定生成邮箱的域名列表（默认常用公共域名）。

  - `--email.username_length <min_len,max_len>`: 指定用户名的长度范围（默认`8,12`）。

  - `--email.prefix_name <true|false>`: 是否尝试使用姓名拼音作为用户名（需要姓名数据作为输入，默认`false`）。

  - `--email.valid <true|false>`: 是否保证符合邮箱基本格式（默认`true`）。

- **高级定制/关联性考虑：**

  - **与姓名关联：** 用户名部分可基于生成的姓名拼音（全拼、首字母缩写）加上随机数字组成，增加真实性。

  - **指定邮箱前缀：** 允许用户提供一个前缀列表，在其中随机选择或拼接。

  - **特定邮箱类型：** 如生成带`+`别名的邮箱（`<EMAIL>`）用于测试邮箱过滤。

- **典型用例/场景：**

  - **自动化测试：** 用户注册、邮件通知接口测试。

  - **功能测试：** 测试邮箱输入框的校验（合法/非法格式、长度）、邮箱唯一性校验。

- **校验与异常处理建议：**

  - **校验：** 符合邮箱基本格式（`<EMAIL>`）。

  - **异常：** 生成不含`@`、`@`前或后为空、域名格式错误、过长、包含非法字符的邮箱用于异常测试。

##### **1.13 账号名 (Account Name / Username)**

- **核心生成逻辑/规则：**

  - 随机字母数字组合，可包含下划线等特殊字符。

  - 遵守命名规则（长度、起始字符、禁止字符）。

- **CLI配置参数建议：**

  - `--accountname.length <min_len,max_len>`: 指定账号名长度范围（如`6,16`）。

  - `--accountname.chars <ALPHANUMERIC|ALPHANUMERIC_SPECIAL|CUSTOM>`: 字符集范围（仅字母数字、包含特殊字符或自定义，默认`ALPHANUMERIC`）。

  - `--accountname.custom_chars <string>`: 当`chars`为`CUSTOM`时，指定包含的字符集。

  - `--accountname.prefix <string>`: 可选的账号名前缀。

  - `--accountname.suffix <string>`: 可选的账号名后缀。

  - `--accountname.unique <true|false>`: 是否在生成批次中保证唯一性（默认`true`，需内部去重或使用UUID生成）。

  - `--accountname.regex <regex_pattern>`: 允许用户提供正则表达式来定义更复杂的命名规范。

- **高级定制/关联性考虑：**

  - **与姓名拼音关联：** 类似邮箱，可基于姓名拼音生成账号名。

  - **黑名单/敏感词：** 可配置一个黑名单词库，生成时避免包含这些词。

- **典型用例/场景：**

  - **自动化测试：** 用户注册、系统内部账号创建。

  - **功能测试：** 测试账号名输入框的校验（长度、字符集、唯一性、敏感词）。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、唯一性。

  - **异常：** 生成过长/过短、包含非法字符、与黑名单冲突的账号名用于异常测试。

##### **1.14 密码 (Password)**

- **核心生成逻辑/规则：**

  - 根据指定长度和复杂度要求（数字、大小写字母、特殊字符组合）随机生成。

- **CLI配置参数建议：**

  - `--password.length <min_len,max_len>`: 指定密码长度范围（默认`8,16`）。

  - `--password.complexity <LOW|MEDIUM|HIGH|CUSTOM>`:

    - `LOW`: 纯数字或纯字母。

    - `MEDIUM`: 数字+大小写字母。

    - `HIGH`: 数字+大小写字母+特殊字符。

    - `CUSTOM`: 允许通过`--password.custom_chars`参数指定字符集。

  - `--password.custom_chars <string>`: 当`complexity`为`CUSTOM`时，指定包含的字符集。

  - `--password.include_common_weak <true|false>`: 是否包含常见弱密码（如`123456`, `password`等），用于安全测试（默认`false`）。

- **高级定制/关联性考虑：**

  - **弱密码/强密码：** 可生成常见弱密码用于安全测试，或生成符合强密码策略的密码。

  - **密码加密（不生成加密结果）：** 提示用户，实际应用中密码应加密存储，这里仅生成明文。

- **典型用例/场景：**

  - **自动化测试：** 用户注册、登录接口测试。

  - **功能测试：** 测试密码输入框的校验（长度、复杂度）、弱密码/强密码场景。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集多样性。

  - **异常：** 生成不符合复杂度要求、长度不符的密码用于异常测试。

##### **1.15 性别 (Gender)**

- **核心生成逻辑/规则：**

  - 从预设选项（男/女/其他）中随机选择，可设置不同选项的权重。

- **CLI配置参数建议：**

  - `--gender.type <MALE|FEMALE|OTHER|ANY>`: 指定生成男性、女性、其他或任意性别（默认`ANY`）。

  - `--gender.male_ratio <float>`: 男性占比（如`0.5`），用于调整不同性别的分布比例。

  - `--gender.other_ratio <float>`: 其他性别占比（如`0.01`）。

- **高级定制/关联性考虑：**

  - **与姓名/身份证号关联：** 确保性别与生成的姓名、身份证号中的性别信息一致。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据、注册信息。

  - **功能测试：** 测试性别选择、性别相关的业务逻辑（如女性用户专属优惠）。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值，确保在预设选项内。

  - **异常：** 无。

##### **1.16 职业/职位 (Occupation / Position)**

- **核心生成逻辑/规则：**

  - 从预设的职业/职位库（可按行业分类）中随机选择。

- **CLI配置参数建议：**

  - `--occupation.industry <IT|FINANCE|EDUCATION|ANY>`: 指定行业，影响职业选择范围（默认`ANY`）。

  - `--occupation.level <JUNIOR|SENIOR|MANAGER|DIRECTOR|ANY>`: 指定职位层级（默认`ANY`）。

  - `--occupation.file <path>`: 指定自定义职业/职位列表文件路径。

- **高级定制/关联性考虑：**

  - **与年龄/学历关联：** 例如，年龄太小或学历太低的不太可能是高级经理。

  - **职业/职位库扩展：** 允许用户导入自定义的职业/职位列表。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据、招聘系统数据。

  - **功能测试：** 测试职业/职位选择、基于职业的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 简单字符串，无复杂校验。

  - **异常：** 无。

##### **1.17 学历 (Education Level)**

- **核心生成逻辑/规则：**

  - 从预设的学历层次（小学、初中、高中、大专、本科、硕士、博士等）中随机选择，可设置权重。

- **CLI配置参数建议：**

  - `--education.levels <PRIMARY|JUNIOR_HIGH|HIGH_SCHOOL|COLLEGE|BACHELOR|MASTER|PHD|ANY>`: 指定生成学历的范围，默认`ANY`。

  - `--education.distribution <UNIFORM|WEIGHTED>`: 指定学历分布（均匀分布或加权分布，默认`UNIFORM`）。

  - `--education.weights <level1:weight1,level2:weight2,...>`: 当`distribution`为`WEIGHTED`时，指定各学历的权重。

- **高级定制/关联性考虑：**

  - **与年龄关联：** 确保学历与年龄逻辑匹配（如年龄过小不能是博士）。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据、简历信息。

  - **功能测试：** 测试学历选择、学历相关的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

##### **1.18 婚姻状况 (Marital Status)**

- **核心生成逻辑/规则：**

  - 从预设选项（未婚/已婚/离异/丧偶）中随机选择，可设置权重。

- **CLI配置参数建议：**

  - `--marital.status <SINGLE|MARRIED|DIVORCED|WIDOWED|ANY>`: 指定状态（默认`ANY`）。

  - `--marital.married_ratio <float>`: 已婚占比（如`0.6`）。

  - `--marital.divorced_ratio <float>`: 离异占比（如`0.1`）。

- **高级定制/关联性考虑：**

  - **与年龄关联：** 例如，年龄过小的不能是“已婚”或“离异”。

- **典型用例/场景：**

  - **自动化测试：** 用户信息、金融服务（贷款、保险）数据。

  - **功能测试：** 测试婚姻状况选择、婚姻状况相关的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

##### **1.19 血型 (Blood Type)**

- **核心生成逻辑/规则：**

  - 从预设选项（A/B/AB/O型，Rh阳/阴性）中随机选择，可设置权重。

- **CLI配置参数建议：**

  - `--bloodtype.group <A|B|AB|O|ANY>`: 指定血型组别（默认`ANY`）。

  - `--bloodtype.rh <POSITIVE|NEGATIVE|ANY>`: 指定Rh因子（默认`ANY`）。

  - `--bloodtype.distribution <UNIFORM|WEIGHTED>`: 指定血型分布（均匀分布或加权分布）。

- **高级定制/关联性考虑：** 无。

- **典型用例/场景：**

  - **自动化测试：** 医疗系统、健康档案数据。

  - **功能测试：** 测试血型选择。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

##### **1.20 星座 (Zodiac Sign)**

- **核心生成逻辑/规则：**

  - 从十二星座中随机选择。

- **CLI配置参数建议：**

  - `--zodiac.sign <ARIES|TAURUS|...|PISCES|ANY>`: 指定星座，默认`ANY`。

- **高级定制/关联性考虑：**

  - **与出生日期关联：** 如果有出生日期字段，则星座应与出生日期精确匹配。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据、社交应用。

  - **功能测试：** 测试星座选择。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

##### **1.21 民族 (Ethnicity)**

- **核心生成逻辑/规则：**

  - 从中国56个民族中随机选择，可设置汉族的权重更高。

- **CLI配置参数建议：**

  - `--ethnicity.type <HAN|MINOR|ANY>`: 指定汉族、少数民族或任意（默认`ANY`）。

  - `--ethnicity.han_ratio <float>`: 汉族占比（如`0.9`）。

  - `--ethnicity.file <path>`: 指定自定义民族列表文件路径。

- **高级定制/关联性考虑：**

  - **与地区关联（未来扩展）：** 某些少数民族在特定省份聚居，可根据地区生成对应民族。

- **典型用例/场景：**

  - **自动化测试：** 用户信息、人口统计数据。

  - **功能测试：** 测试民族选择。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

##### **1.22 宗教信仰 (Religion)**

- **核心生成逻辑/规则：**

  - 从预设的宗教类型中随机选择，可设置无信仰的权重更高。

- **CLI配置参数建议：**

  - `--religion.type <BUDDHISM|CHRISTIANITY|ISLAM|TAOISM|NONE|ANY>`: 指定宗教类型（默认`ANY`）。

  - `--religion.none_ratio <float>`: 无信仰者占比（如`0.7`）。

  - `--religion.file <path>`: 指定自定义宗教类型列表文件路径。

- **高级定制/关联性考虑：** 无。

- **典型用例/场景：**

  - **自动化测试：** 用户画像数据。

  - **功能测试：** 测试宗教信仰选择。

- **校验与异常处理建议：**

  - **校验：** 简单枚举值。

  - **异常：** 无。

### **标识类 (Identifiers)**

#### **2.1 全局唯一 ID (Global Unique ID)**

- **核心生成逻辑/规则：**

  - **UUID/GUID：** 生成符合RFC 4122标准的UUID（版本1、版本4）。

  - **ULID：** 生成ULID（Universally Unique Lexicographically Sortable Identifier），结合了时间戳和随机性，可排序。

  - **Snowflake ID：** 模拟Twitter Snowflake算法生成ID，包含时间戳、机器ID、数据中心ID和序列号，保证趋势递增和唯一性。

- **CLI配置参数建议：**

  - `--uuid.type <UUID1|UUID4|ULID|SNOWFLAKE>`: 指定生成ID的类型（默认`UUID4`）。

  - `--uuid.snowflake.worker_id <int>`: (仅对Snowflake) 指定工作机器ID (0-1023)。

  - `--uuid.snowflake.datacenter_id <int>`: (仅对Snowflake) 指定数据中心ID (0-1023)。

  - `--uuid.snowflake.epoch <timestamp>`: (仅对Snowflake) 指定起始时间戳（Unix毫秒时间戳），用于控制ID的起始时间。

- **高级定制/关联性考虑：**

  - **Snowflake ID的分布式模拟：** 允许用户配置多个worker_id和datacenter_id，模拟分布式环境下ID的生成，用于测试ID冲突或排序问题。

  - **趋势递增ID：** 对于需要ID趋势递增的场景，推荐使用ULID或Snowflake ID。

- **典型用例/场景：**

  - **自动化测试：** 数据库主键、消息队列消息ID、日志追踪ID、分布式系统中的唯一标识。

  - **功能测试：** 测试系统对长ID、特定格式ID的兼容性。

- **校验与异常处理建议：**

  - **校验：** 格式校验（如UUID的`-`分隔符），唯一性（在生成批次内）。

  - **异常：** 无。

#### **2.2 业务单据号 (Business Document Number)**

- **核心生成逻辑/规则：**

  - **订单号 (Order ID)、发票号、合同号、出库入库单号等：** 通常由前缀（如业务类型缩写）、日期/时间戳、序列号（递增或随机）、校验位（可选）组合而成。

  - 支持自定义前缀、日期格式、序列号长度和起始值。

- **CLI配置参数建议：**

  - `--docnum.type <ORDER|INVOICE|CONTRACT|CUSTOM>`: 指定单据类型，提供预设规则（默认`CUSTOM`）。

  - `--docnum.prefix <string>`: 自定义单据号前缀（如`ORD-`，`INV-`）。

  - `--docnum.date_format <format_string>`: 日期部分格式（如`yyyyMMdd`，`yyMM`）。

  - `--docnum.sequence_length <int>`: 序列号长度（如`6`位）。

  - `--docnum.sequence_start <int>`: 序列号起始值（默认`1`）。

  - `--docnum.sequence_mode <INCREMENTAL|RANDOM>`: 序列号模式（递增或随机，默认`INCREMENTAL`）。

  - `--docnum.checksum <true|false>`: 是否添加校验位（需用户提供校验算法或简单Luhn）。

  - `--docnum.template <format_string>`: 允许用户通过模板字符串定义复杂格式（如`ORD-{YYYYMMDD}-{SEQ:6}-{RAND:4}`）。

- **高级定制/关联性考虑：**

  - **与日期关联：** 确保单据号中的日期与实际生成日期或指定日期范围匹配。

  - **并发场景模拟：** 在高并发生成时，模拟序列号跳号或冲突（如果`sequence_mode`为`INCREMENTAL`且未加锁）。

- **典型用例/场景：**

  - **自动化测试：** 订单创建、发票生成、库存管理等业务流程测试。

  - **功能测试：** 测试单据号的唯一性、格式校验、搜索功能。

- **校验与异常处理建议：**

  - **校验：** 长度、格式、唯一性（在生成批次内）。

  - **异常：** 生成格式不符、长度不符、序列号重复（如果要求唯一）的单据号。

#### **2.3 产品编码 (Product Code)**

- **核心生成逻辑/规则：**

  - **SKU (Stock Keeping Unit)：** 通常是字母数字组合，可包含产品类别、颜色、尺寸等编码。

  - **GTIN（EAN/UPC）：** 生成符合EAN-13或UPC-A标准的条形码数字，包含校验位。

  - **ISBN (International Standard Book Number)：** 生成ISBN-10或ISBN-13，包含校验位。

  - **ISSN (International Standard Serial Number)：** 生成ISSN，包含校验位。

- **CLI配置参数建议：**

  - `--productcode.type <SKU|EAN13|UPCA|ISBN13|ISSN|CUSTOM>`: 指定编码类型（默认`SKU`）。

  - `--productcode.length <int>`: (仅对SKU/自定义) 指定编码长度。

  - `--productcode.chars <ALPHANUMERIC|ALPHANUMERIC_SPECIAL|NUMERIC>`: (仅对SKU/自定义) 字符集。

  - `--productcode.prefix <string>`: (仅对SKU/自定义) 自定义前缀。

  - `--productcode.valid <true|false>`: (对GTIN/ISBN/ISSN) 是否保证符合校验规则（默认`true`）。

  - `--productcode.template <format_string>`: 允许用户通过模板字符串定义SKU等复杂格式。

- **高级定制/关联性考虑：**

  - **与产品属性关联：** SKU可根据产品类别、颜色等字段进行编码组合。

  - **批量生成序列号：** 对于需要连续SKU或条码的场景。

- **典型用例/场景：**

  - **自动化测试：** 商品管理系统、库存系统、销售点(POS)系统。

  - **功能测试：** 测试产品编码的唯一性、格式校验、条码扫描功能。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、特定编码（GTIN/ISBN/ISSN）的校验位。

  - **异常：** 生成不符合格式、校验位错误、长度不符的产品编码。

#### **2.4 社保／医保号 (Social Security / Medical Insurance Number)**

- **核心生成逻辑/规则：**

  - **中国社保/医保号：** 通常与身份证号关联，或基于地区代码和序列号生成。

  - **其他国家/地区：** 如果有需求，需研究对应国家/地区的社保/医保号生成规则和校验算法。

- **CLI配置参数建议：**

  - `--social_id.region <CN>`: 指定国家/地区（当前仅中国大陆）。

  - `--social_id.link_idcard <true|false>`: 是否与已生成的身份证号关联（默认`true`）。如果关联，则直接使用身份证号。

  - `--social_id.prefix <string>`: (不关联身份证时) 自定义前缀。

  - `--social_id.length <int>`: (不关联身份证时) 指定长度。

  - `--social_id.valid <true|false>`: 是否保证符合校验规则（如果存在）。

- **高级定制/关联性考虑：**

  - **与身份证号强关联：** 在中国，社保/医保号通常就是身份证号，或基于身份证号衍生。

- **典型用例/场景：**

  - **自动化测试：** 社保系统、医保系统、人力资源系统。

  - **功能测试：** 测试社保/医保号的输入校验。

- **校验与异常处理建议：**

  - **校验：** 长度、数字组成，以及与身份证号的关联性。

  - **异常：** 生成不符合规则、长度不符的社保/医保号。

#### **2.5 护照号、签证号、驾驶证号 (Passport Number, Visa Number, Driver's License Number)**

- **核心生成逻辑/规则：**

  - **护照号：** 通常是字母和数字的组合，不同国家有不同格式和长度。

  - **签证号：** 字母数字组合，格式多样。

  - **驾驶证号：** 中国驾驶证号通常为身份证号。其他国家有各自规则。

  - 需要为每个国家/地区维护其格式规则和校验算法。

- **CLI配置参数建议：**

  - `--doc_id.type <PASSPORT|VISA|DRIVER_LICENSE>`: 指定证件类型。

  - `--doc_id.country <string>`: 指定国家代码（如`CN`, `US`, `GB`）。

  - `--doc_id.valid <true|false>`: 是否保证符合该国家/证件的校验规则（默认`true`）。

  - `--doc_id.length <int>`: (自定义) 指定长度。

  - `--doc_id.template <format_string>`: (自定义) 允许用户通过模板字符串定义格式。

- **高级定制/关联性考虑：**

  - **与个人信息关联：** 护照号和驾驶证号通常与个人姓名、出生日期等信息绑定。

- **典型用例/场景：**

  - **自动化测试：** 出入境管理系统、租车系统、身份验证系统。

  - **功能测试：** 测试证件号输入框的校验、不同国家证件格式的兼容性。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、特定国家/证件的格式和校验位。

  - **异常：** 生成不符合格式、长度不符、包含非法字符的证件号。

#### **2.6 优惠券码/促销码 (Coupon Code / Promo Code)**

- **核心生成逻辑/规则：**

  - 随机字母数字组合，可包含特殊字符。

  - 支持自定义前缀、后缀、长度。

  - 可生成不同状态（有效、已使用、过期）。

- **CLI配置参数建议：**

  - `--coupon.length <int>`: 指定优惠券码长度（默认`8`）。

  - `--coupon.chars <ALPHANUMERIC|ALPHANUMERIC_SPECIAL|CUSTOM>`: 字符集范围（默认`ALPHANUMERIC`）。

  - `--coupon.custom_chars <string>`: 自定义字符集。

  - `--coupon.prefix <string>`: 自定义前缀。

  - `--coupon.suffix <string>`: 自定义后缀。

  - `--coupon.status <VALID|USED|EXPIRED|ANY>`: 指定优惠券状态（默认`VALID`）。

  - `--coupon.batch_id <string>`: 可选的批次ID，用于生成特定批次的优惠券。

  - `--coupon.unique <true|false>`: 是否在生成批次中保证唯一性（默认`true`）。

- **高级定制/关联性考虑：**

  - **与有效期关联：** 如果生成`EXPIRED`状态的优惠券，可同时生成一个过去的`expiration_date`。

  - **与用户/订单关联：** 在生成测试数据时，可将优惠券与特定用户或订单关联。

- **典型用例/场景：**

  - **自动化测试：** 优惠券核销、促销活动测试、订单结算。

  - **功能测试：** 测试优惠券码的输入校验、不同状态优惠券的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、唯一性。

  - **异常：** 生成不符合长度、包含非法字符的优惠券码。

#### **2.7 物流单号/运单号 (Tracking Number / Waybill Number)**

- **核心生成逻辑/规则：**

  - 通常由物流公司代码、日期、序列号等组合而成。

  - 不同物流公司有不同的格式和校验规则。

  - 支持自定义前缀、长度。

- **CLI配置参数建议：**

  - `--tracking.carrier <SF|JD|YTO|CUSTOM>`: 指定物流公司（默认`CUSTOM`）。

  - `--tracking.prefix <string>`: 自定义前缀。

  - `--tracking.length <int>`: 指定长度。

  - `--tracking.chars <ALPHANUMERIC|NUMERIC>`: 字符集。

  - `--tracking.valid <true|false>`: 是否保证符合特定物流公司的校验规则（如果已知）。

- **高级定制/关联性考虑：**

  - **与订单关联：** 在生成订单数据时，可同时生成对应的物流单号。

  - **物流状态模拟：** 结合日期和时间，模拟不同物流状态（揽收、运输中、派送中、已签收）。

- **典型用例/场景：**

  - **自动化测试：** 物流追踪系统、订单管理系统。

  - **功能测试：** 测试物流单号的输入校验、物流状态查询。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集。

  - **异常：** 生成不符合格式、长度不符的物流单号。

### **联系／通信类 (Contact / Communication)**

#### **3.1 电子邮件验证码 (Email Verification Code)**

- **核心生成逻辑/规则：**

  - 生成指定长度（通常为6-8位）的纯数字或字母+数字混合字符串。

  - 可配置是否包含字母，以及字母是否区分大小写。

- **CLI配置参数建议：**

  - `--email_code.length <int>`: 指定验证码长度（默认`6`）。

  - `--email_code.chars <NUMERIC|ALPHANUMERIC|ALPHANUMERIC_UPPER|ALPHANUMERIC_LOWER>`: 指定字符集（纯数字，或字母数字混合，字母大小写，默认`NUMERIC`）。

- **高级定制/关联性考虑：**

  - **与邮箱关联：** 在生成用户注册/找回密码流程数据时，可同时生成对应的邮箱和验证码。

  - **有效期模拟：** 可生成一个时间戳，模拟验证码的生成时间，用于测试验证码过期逻辑。

- **典型用例/场景：**

  - **自动化测试：** 用户注册、密码找回、邮箱验证等接口测试。

  - **功能测试：** 测试验证码输入框的校验（长度、字符集）、验证码有效性。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集。

  - **异常：** 生成长度不符、包含非法字符的验证码。

#### **3.2 短信验证码 (SMS Verification Code)**

- **核心生成逻辑/规则：**

  - 生成指定长度（通常为4-6位）的纯数字字符串。

  - 可配置是否允许包含字母（较少见，但可作为定制选项）。

- **CLI配置参数建议：**

  - `--sms_code.length <int>`: 指定验证码长度（默认`6`）。

  - `--sms_code.chars <NUMERIC|ALPHANUMERIC>`: 指定字符集（纯数字，或字母数字混合，默认`NUMERIC`）。

- **高级定制/关联性考虑：**

  - **与手机号关联：** 在生成用户注册/登录流程数据时，可同时生成对应的手机号和验证码。

  - **有效期模拟：** 可生成一个时间戳，模拟验证码的生成时间，用于测试验证码过期逻辑。

- **典型用例/场景：**

  - **自动化测试：** 用户注册、登录、手机号绑定等接口测试。

  - **功能测试：** 测试验证码输入框的校验、验证码有效性。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集。

  - **异常：** 生成长度不符、包含非法字符的验证码。

#### **3.3 传真号码 (Fax Number)**

- **核心生成逻辑/规则：**

  - 通常由区号和本地号码组成，格式与固定电话类似。

  - 可支持国际传真号码格式。

- **CLI配置参数建议：**

  - `--fax.region <CN|US|ANY>`: 指定国家/地区格式（默认`CN`）。

  - `--fax.prefix <string>`: 指定区号或国际区号（如`+86-010`）。

  - `--fax.length <int>`: 指定本地号码长度。

  - `--fax.valid <true|false>`: 是否保证符合基本格式（默认`true`）。

- **高级定制/关联性考虑：**

  - **与地址关联：** 可根据地址的省市生成对应区号的传真号码。

- **典型用例/场景：**

  - **自动化测试：** 企业联系方式管理、传统通信系统。

  - **功能测试：** 测试传真号码输入框的校验。

- **校验与异常处理建议：**

  - **校验：** 格式（区号-号码）、长度、数字组成。

  - **异常：** 生成格式不符、长度不符、包含非法字符的传真号码。

#### **3.4 坐机固话 (Landline Phone Number)**

- **核心生成逻辑/规则：**

  - 由区号 + 号码 + 分机号（可选）组成。

  - 区号从预设的中国大陆区号库中随机选择。

  - 号码部分随机生成，分机号随机生成。

- **CLI配置参数建议：**

  - `--landline.region <CN>`: 指定国家/地区格式（当前仅中国大陆）。

  - `--landline.area_code <code1,code2,...>`: 指定区号列表（如`010,021,0755`），不指定则随机。

  - `--landline.number_length <int>`: 指定号码长度（默认`7`或`8`）。

  - `--landline.extension_length <int>`: 指定分机号长度（默认`0`，即不生成分机号）。

  - `--landline.valid <true|false>`: 是否保证符合基本格式（默认`true`）。

- **高级定制/关联性考虑：**

  - **与地址关联：** 可根据地址的省市生成对应区号的固话号码。

- **典型用例/场景：**

  - **自动化测试：** 企业联系方式管理、客服系统。

  - **功能测试：** 测试固话号码输入框的校验。

- **校验与异常处理建议：**

  - **校验：** 格式（区号-号码-分机）、长度、数字组成。

  - **异常：** 生成格式不符、长度不符、包含非法字符的固话号码。

#### **3.5 URL／URI (Uniform Resource Locator / Uniform Resource Identifier)**

- **核心生成逻辑/规则：**

  - 生成符合URL/URI规范的字符串，包含协议、域名、路径、查询参数、锚点。

  - 可配置协议类型、域名后缀、路径深度、参数数量和值。

- **CLI配置参数建议：**

  - `--url.protocol <HTTP|HTTPS|FTP|ANY>`: 指定协议（默认`HTTPS`）。

  - `--url.domain_suffix <.com|.org|.cn|ANY>`: 指定域名后缀（默认`ANY`）。

  - `--url.path_depth <int>`: 指定路径深度（如`/a/b/c`为3，默认`2`）。

  - `--url.param_count <int>`: 指定查询参数数量（默认`2`）。

  - `--url.include_anchor <true|false>`: 是否包含锚点（默认`false`）。

  - `--url.valid <true|false>`: 是否保证生成有效URL（默认`true`）。设置为`false`可生成非法URL用于异常测试。

- **高级定制/关联性考虑：**

  - **特定域名生成：** 允许用户指定一个或多个域名。

  - **特定参数生成：** 允许用户指定参数名和参数值范围。

  - **长URL/URI：** 可生成超长URL用于测试系统对URL长度的限制。

- **典型用例/场景：**

  - **自动化测试：** 链接有效性测试、API接口URL测试、URL参数解析。

  - **功能测试：** 测试URL输入框的校验、页面跳转、深层链接。

- **校验与异常处理建议：**

  - **校验：** 符合URL/URI规范。

  - **异常：** 生成协议错误、域名非法、路径包含非法字符、参数过多、过长的URL用于异常测试。

#### **3.6 文件路径 (File Path)**

- **核心生成逻辑/规则：**

  - 生成Windows或Unix风格的文件路径，可包含相对/绝对路径、文件名、扩展名。

  - 可包含空格、特殊字符、多级目录。

- **CLI配置参数建议：**

  - `--filepath.os <WINDOWS|UNIX|ANY>`: 指定操作系统风格（默认`ANY`）。

  - `--filepath.type <ABSOLUTE|RELATIVE|ANY>`: 指定绝对路径或相对路径（默认`ANY`）。

  - `--filepath.depth <int>`: 指定目录深度（默认`3`）。

  - `--filepath.filename_length <int>`: 指定文件名长度。

  - `--filepath.extension <.txt|.jpg|.pdf|ANY>`: 指定文件扩展名。

  - `--filepath.include_spaces <true|false>`: 是否在路径中包含空格（默认`false`）。

  - `--filepath.include_special_chars <true|false>`: 是否在路径中包含特殊字符（如`!@#$`，默认`false`）。

  - `--filepath.valid <true|false>`: 是否保证生成有效路径（默认`true`）。

- **高级定制/关联性考虑：**

  - **路径穿越攻击模拟：** 可生成`../../`等路径，用于安全测试。

  - **超长路径：** 生成超出操作系统路径长度限制的路径。

- **典型用例/场景：**

  - **自动化测试：** 文件上传/下载路径测试、文件系统操作、日志路径配置。

  - **功能测试：** 测试文件路径输入框的校验、文件选择对话框。

- **校验与异常处理建议：**

  - **校验：** 格式符合指定操作系统风格。

  - **异常：** 生成非法字符、过长、路径穿越等路径用于异常测试。

#### **3.7 MIME Type (Multipurpose Internet Mail Extensions Type)**

- **核心生成逻辑/规则：**

  - 从常用MIME类型库中随机选择（如`text/html`, `application/json`, `image/png`, `application/octet-stream`）。

  - 可支持自定义MIME类型。

- **CLI配置参数建议：**

  - `--mimetype.category <TEXT|IMAGE|APPLICATION|AUDIO|VIDEO|ANY>`: 指定MIME类型类别（默认`ANY`）。

  - `--mimetype.file <path>`: 指定自定义MIME类型列表文件路径。

  - `--mimetype.valid <true|false>`: 是否保证生成有效MIME类型（默认`true`）。

- **高级定制/关联性考虑：**

  - **与文件扩展名关联：** 如果同时生成文件名和扩展名，MIME类型应与扩展名匹配。

- **典型用例/场景：**

  - **自动化测试：** 文件上传/下载接口（`Content-Type`头）、HTTP请求头测试。

  - **功能测试：** 测试文件类型识别、文件上传限制。

- **校验与异常处理建议：**

  - **校验：** 格式符合`type/subtype`。

  - **异常：** 生成非法格式的MIME类型。

### **网络／设备类 (Network / Device)**

#### **4.1 IP 地址 (IP Address)**

- **核心生成逻辑/规则：**

  - **IPv4：** 生成符合IPv4格式的地址（`xxx.xxx.xxx.xxx`），每个部分在0-255之间。

  - **IPv6：** 生成符合IPv6格式的地址（`xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx`），可包含缩写形式。

  - 可配置生成公网IP、私网IP或特定网段IP。

- **CLI配置参数建议：**

  - `--ip.version <IPV4|IPV6|ANY>`: 指定IP地址版本（默认`ANY`）。

  - `--ip.type <PUBLIC|PRIVATE|ANY>`: 指定公网IP、私网IP或任意类型（默认`ANY`）。

    - 私网IP范围：IPv4 (10.0.0.0/8, **********/12, ***********/16)。

  - `--ip.cidr <cidr_block>`: 指定IP地址所在的CIDR网段（如`***********/24`），在此网段内随机生成。

  - `--ip.valid <true|false>`: 是否保证生成有效IP地址（默认`true`）。设置为`false`可生成非法IP用于异常测试。

- **高级定制/关联性考虑：**

  - **连续IP：** 允许生成一个IP地址序列，用于测试IP池管理。

  - **特定国家/地区IP（未来扩展）：** 需要维护IP归属地数据库。

- **典型用例/场景：**

  - **自动化测试：** 网络访问控制、日志分析、安全策略测试、用户地域分析。

  - **功能测试：** 测试IP地址输入框的校验（合法/非法格式）、黑名单/白名单功能。

- **校验与异常处理建议：**

  - **校验：** 格式符合IPv4/IPv6规范，各部分数值范围正确。

  - **异常：** 生成格式错误（如`256.0.0.1`）、包含非法字符、过长的IP地址用于异常测试。

#### **4.2 域名 (Domain Name)**

- **核心生成逻辑/规则：**

  - 生成符合域名规范的字符串，由二级域名、顶级域名（TLD）组成。

  - 可配置顶级域名后缀（`.com`, `.org`, `.cn`等）。

  - 二级域名可随机生成或基于关键词组合。

- **CLI配置参数建议：**

  - `--domain.tld <.com|.org|.cn|ANY>`: 指定顶级域名后缀（默认`ANY`）。

  - `--domain.length <min_len,max_len>`: 指定二级域名长度范围（默认`6,12`）。

  - `--domain.prefix <string>`: 可选的二级域名前缀。

  - `--domain.valid <true|false>`: 是否保证生成有效域名（默认`true`）。设置为`false`可生成非法域名用于异常测试。

  - `--domain.tld_file <path>`: 指定自定义顶级域名列表文件路径。

- **高级定制/关联性考虑：**

  - **与URL关联：** 在生成URL时，可作为URL的域名部分。

  - **恶意域名：** 可配置生成包含常见恶意关键词的域名，用于安全测试。

- **典型用例/场景：**

  - **自动化测试：** DNS解析、网站访问、安全策略（域名黑白名单）。

  - **功能测试：** 测试域名输入框的校验、网站跳转。

- **校验与异常处理建议：**

  - **校验：** 符合域名命名规范（字符集、长度、点分隔）。

  - **异常：** 生成格式错误、包含非法字符、过长、不含TLD的域名用于异常测试。

#### **4.3 MAC 地址 (MAC Address)**

- **核心生成逻辑/规则：**

  - 生成符合MAC地址格式的字符串（`XX:XX:XX:XX:XX:XX`或`XX-XX-XX-XX-XX-XX`）。

  - 每个`XX`为两位十六进制数。

  - 可配置OUI（Organizationally Unique Identifier）前缀，模拟特定厂商设备。

- **CLI配置参数建议：**

  - `--mac.format <COLON|HYPHEN|ANY>`: 指定分隔符格式（冒号或连字符，默认`COLON`）。

  - `--mac.oui <oui1,oui2,...>`: 指定MAC地址的OUI前缀（如`00:1A:2B`），不指定则随机。

  - `--mac.valid <true|false>`: 是否保证生成有效MAC地址（默认`true`）。

- **高级定制/关联性考虑：**

  - **特定厂商设备模拟：** 通过指定OUI，模拟思科、华为、英特尔等厂商的设备。

  - **虚拟MAC地址：** 可生成以`02`开头的MAC地址，用于模拟虚拟化环境。

- **典型用例/场景：**

  - **自动化测试：** 网络设备管理、设备识别、网络安全策略。

  - **功能测试：** 测试MAC地址输入框的校验。

- **校验与异常处理建议：**

  - **校验：** 长度17位，符合十六进制字符和分隔符规则。

  - **异常：** 生成格式错误、包含非法字符、长度不符的MAC地址。

#### **4.4 端口号 (Port Number)**

- **核心生成逻辑/规则：**

  - 生成0-65535之间的整数。

  - 可配置生成常用服务端口（如80, 443, 22, 3306等）或随机端口。

- **CLI配置参数建议：**

  - `--port.min <int>`: 最小端口号（默认`0`）。

  - `--port.max <int>`: 最大端口号（默认`65535`）。

  - `--port.type <WELL_KNOWN|REGISTERED|DYNAMIC|ANY>`: 指定端口类型（知名端口0-1023，注册端口1024-49151，动态/私有端口49152-65535，默认`ANY`）。

  - `--port.common <true|false>`: 是否优先生成常用服务端口（默认`false`）。

- **高级定制/关联性考虑：**

  - **端口冲突模拟：** 可生成重复端口号，用于测试端口冲突处理。

- **典型用例/场景：**

  - **自动化测试：** 网络服务配置、防火墙规则测试、端口扫描模拟。

  - **功能测试：** 测试端口号输入框的校验（合法/非法范围）。

- **校验与异常处理建议：**

  - **校验：** 整数，在0-65535范围内。

  - **异常：** 生成负数、超出65535的端口号。

#### **4.5 HTTP 头 (HTTP Header)**

- **核心生成逻辑/规则：**

  - 生成常见的HTTP请求头（如`User-Agent`, `Cookie`, `Referer`, `Authorization`, `Accept-Language`等）及其值。

  - 可配置生成合法或非法/恶意头（如超长头、注入攻击头）。

- **CLI配置参数建议：**

  - `--http_header.name <header_name>`: 指定要生成的HTTP头名称（如`User-Agent`，`Authorization`）。

  - `--http_header.value_type <RANDOM|PRESET|ATTACK>`: 指定值类型（随机生成、从预设库选择、或生成攻击payload）。

    - `RANDOM`: 随机生成符合格式的值。

    - `PRESET`: 从内置的常见User-Agent、Accept-Language等列表中选择。

    - `ATTACK`: 生成SQL注入、XSS等攻击payload作为头值（需与安全注入模块联动）。

  - `--http_header.value_length <min_len,max_len>`: 指定头值长度范围。

  - `--http_header.count <int>`: (针对一组数据) 指定生成多少个不同的HTTP头。

- **高级定制/关联性考虑：**

  - **与Session ID/Token关联：** 如果生成Cookie或Authorization头，其值可与Session ID或Token关联。

  - **多语言User-Agent：** 可生成不同语言环境的User-Agent。

  - **自定义头：** 允许用户指定自定义头名称和生成规则。

- **典型用例/场景：**

  - **自动化测试：** Web应用安全测试、API接口测试、浏览器兼容性测试。

  - **功能测试：** 验证系统对不同HTTP头的处理。

- **校验与异常处理建议：**

  - **校验：** 格式符合HTTP头规范（`Name: Value`）。

  - **异常：** 生成格式错误、超长、包含非法字符的HTTP头用于异常测试。

#### **4.6 Session ID／Token (Session ID / Token)**

- **核心生成逻辑/规则：**

  - 生成随机的、指定长度和字符集的字符串。

  - 可模拟JWT (JSON Web Token) 结构（但仅生成结构，不进行签名校验）。

  - 可模拟OAuth Access Token、CSRF Token等。

- **CLI配置参数建议：**

  - `--token.type <RANDOM_STRING|JWT|OAUTH|CSRF>`: 指定Token类型（默认`RANDOM_STRING`）。

  - `--token.length <int>`: (仅对`RANDOM_STRING`) 指定长度。

  - `--token.chars <ALPHANUMERIC|ALPHANUMERIC_SPECIAL|BASE64_URL_SAFE>`: 字符集（默认`BASE64_URL_SAFE`）。

  - `--token.jwt.header <json_string>`: (仅对JWT) 指定JWT Header的JSON字符串。

  - `--token.jwt.payload <json_string>`: (仅对JWT) 指定JWT Payload的JSON字符串。

  - `--token.valid <true|false>`: 是否保证符合基本格式（默认`true`）。设置为`false`可生成非法Token用于异常测试。

- **高级定制/关联性考虑：**

  - **JWT内容定制：** 允许用户完全控制JWT的Header和Payload内容，用于测试特定权限或用户身份。

  - **过期/无效Token：** 可生成过期时间在过去或签名错误的JWT（仅模拟格式，不进行实际加密）。

- **典型用例/场景：**

  - **自动化测试：** 认证授权接口测试、会话管理、API安全测试。

  - **功能测试：** 测试Token的有效性、过期处理。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、基本格式（如JWT的`xxx.yyy.zzz`结构）。

  - **异常：** 生成格式错误、长度不符、包含非法字符的Token。

#### **4.7 设备 ID／IMEI／IMSI (Device ID / IMEI / IMSI)**

- **核心生成逻辑/规则：**

  - **设备ID：** 随机生成UUID或自定义格式的字符串。

  - **IMEI (International Mobile Equipment Identity)：** 15位数字，包含TAC（Type Allocation Code）、FAC（Final Assembly Code）、序列号和校验位。需实现Luhn算法校验。

  - **IMSI (International Mobile Subscriber Identity)：** 15位数字，包含MCC（Mobile Country Code）、MNC（Mobile Network Code）和MSIN（Mobile Subscription Identification Number）。

- **CLI配置参数建议：**

  - `--device_id.type <UUID|IMEI|IMSI|CUSTOM>`: 指定设备ID类型（默认`UUID`）。

  - `--device_id.length <int>`: (仅对`CUSTOM`) 指定长度。

  - `--device_id.imei.tac <string>`: (仅对IMEI) 指定TAC码前缀。

  - `--device_id.imsi.mcc <string>`: (仅对IMSI) 指定MCC（如`460`中国）。

  - `--device_id.imsi.mnc <string>`: (仅对IMSI) 指定MNC（如`00`移动，`01`联通）。

  - `--device_id.valid <true|false>`: 是否保证生成有效ID（默认`true`）。

- **高级定制/关联性考虑：**

  - **模拟特定设备：** 通过指定IMEI的TAC码，模拟特定品牌或型号的设备。

  - **模拟特定运营商：** 通过指定IMSI的MCC和MNC，模拟特定国家和运营商的用户。

- **典型用例/场景：**

  - **自动化测试：** 移动应用测试、设备识别、用户行为追踪、运营商服务测试。

  - **功能测试：** 测试设备ID/IMEI/IMSI输入框的校验。

- **校验与异常处理建议：**

  - **校验：** 长度、数字组成，IMEI需通过Luhn算法校验。

  - **异常：** 生成格式错误、长度不符、校验位错误的设备ID。

#### **4.8 地理坐标 (Geographical Coordinates)**

- **核心生成逻辑/规则：**

  - **经度 (Longitude)：** -180到+180之间的浮点数。

  - **纬度 (Latitude)：** -90到+90之间的浮点数。

  - **高度 (Altitude)：** 随机浮点数。

  - 可配置坐标系（GPS、GCJ-02、WGS-84，但生成时仅生成数值，不进行坐标系转换）。

  - 可配置生成特定区域内的坐标。

- **CLI配置参数建议：**

  - `--geo.latitude.min <float>`: 最小纬度（默认`-90.0`）。

  - `--geo.latitude.max <float>`: 最大纬度（默认`90.0`）。

  - `--geo.longitude.min <float>`: 最小经度（默认`-180.0`）。

  - `--geo.longitude.max <float>`: 最大经度（默认`180.0`）。

  - `--geo.altitude.min <float>`: 最小高度（默认`-500.0`）。

  - `--geo.altitude.max <float>`: 最大高度（默认`10000.0`）。

  - `--geo.precision <int>`: 浮点数精度（小数点后位数，默认`6`）。

  - `--geo.center_lat <float>`: (区域生成) 中心纬度。

  - `--geo.center_lon <float>`: (区域生成) 中心经度。

  - `--geo.radius_km <float>`: (区域生成) 半径（公里），在此半径内随机生成坐标。

- **高级定制/关联性考虑：**

  - **与地址关联：** 如果有地址字段，可尝试根据地址生成大致匹配的地理坐标（需要外部地理编码服务或大型地址-坐标数据库，复杂）。

  - **路径模拟：** 可生成一系列连续的坐标点，模拟用户移动轨迹。

- **典型用例/场景：**

  - **自动化测试：** 定位服务、地图应用、LBS（基于位置服务）功能、地理围栏测试。

  - **功能测试：** 测试地理坐标输入框的校验、地图显示。

- **校验与异常处理建议：**

  - **校验：** 数值范围正确，浮点数格式。

  - **异常：** 生成超出范围的经纬度。

#### **4.9 时区标识 (Time Zone Identifier)**

- **核心生成逻辑/规则：**

  - 从IANA时区数据库（如`Asia/Shanghai`, `America/New_York`）中随机选择。

  - 可生成UTC偏移量（如`+08:00`, `-05:00`）。

- **CLI配置参数建议：**

  - `--timezone.type <IANA|UTC_OFFSET|ANY>`: 指定时区类型（默认`IANA`）。

  - `--timezone.region <ASIA|AMERICA|EUROPE|ANY>`: (仅对IANA) 指定时区所属大洲（默认`ANY`）。

  - `--timezone.offset_min <int>`: (仅对UTC_OFFSET) 最小UTC偏移量（分钟，如`-720`）。

  - `--timezone.offset_max <int>`: (仅对UTC_OFFSET) 最大UTC偏移量（分钟，如`+840`）。

  - `--timezone.file <path>`: 指定自定义时区列表文件路径。

- **高级定制/关联性考虑：**

  - **与日期时间关联：** 如果生成日期时间，可同时生成对应时区的日期时间。

  - **与地理位置关联：** 可根据地理坐标或地址，生成对应的时区标识（需要外部数据源支持，复杂）。

- **典型用例/场景：**

  - **自动化测试：** 全球化应用、时间敏感系统、日志记录。

  - **功能测试：** 测试时区设置、时间显示。

- **校验与异常处理建议：**

  - **校验：** 格式符合IANA或UTC偏移量规范。

  - **异常：** 生成非法格式的时区标识。

### **文本／多语言类 (Text / Multilingual)**

#### **5.1 长文本 (Long Text)**

- **核心生成逻辑/规则：**

  - 生成指定长度的随机文本，可选择中文、英文或混合文本。

  - 可配置是否包含标点符号、换行符。

  - 可从预设的文本片段（如新闻、文章摘要）中随机抽取或组合。

- **CLI配置参数建议：**

  - `--longtext.language <CN|EN|BOTH>`: 指定文本语言（默认`BOTH`）。

  - `--longtext.length <min_len,max_len>`: 指定文本长度范围（字符数，默认`100,500`）。

  - `--longtext.include_punctuation <true|false>`: 是否包含标点符号（默认`true`）。

  - `--longtext.include_newlines <true|false>`: 是否包含换行符（默认`true`）。

  - `--longtext.source_file <path>`: 指定自定义文本片段文件路径，从其中随机抽取或组合。

  - `--longtext.paragraph_count <min_count,max_count>`: (可选) 指定生成段落数量范围。

- **高级定制/关联性考虑：**

  - **特定主题文本：** 允许用户提供特定主题的文本语料库，生成相关内容的文本。

  - **超长文本：** 生成远超常规长度的文本，用于测试字段长度限制、性能（如数据库BLOB/TEXT字段）。

- **典型用例/场景：**

  - **自动化测试：** 用户评论、文章内容、日志信息、产品描述、邮件正文。

  - **功能测试：** 测试文本输入框的长度限制、多行文本显示、文本搜索功能。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集编码（UTF-8）。

  - **异常：** 生成超长文本、包含特殊编码字符的文本用于异常测试。

#### **5.2 富文本／HTML、Markdown 片段 (Rich Text / HTML, Markdown Snippet)**

- **核心生成逻辑/规则：**

  - 生成包含HTML标签或Markdown语法的文本片段。

  - 可配置包含的标签类型（`<b>`, `<i>`, `<a>`, `<img>`等）或Markdown元素（标题、列表、链接、图片）。

  - 可生成合法或包含恶意脚本（XSS）的片段。

- **CLI配置参数建议：**

  - `--richtext.format <HTML|MARKDOWN>`: 指定富文本格式（默认`HTML`）。

  - `--richtext.length <min_len,max_len>`: 指定片段长度范围（默认`50,200`）。

  - `--richtext.include_tags <tag1,tag2,...>`: (仅对HTML) 指定包含的HTML标签（如`p,b,i,a,img`）。

  - `--richtext.include_elements <heading,list,link,image>`: (仅对Markdown) 指定包含的Markdown元素。

  - `--richtext.xss_payload <true|false>`: 是否包含XSS攻击脚本（默认`false`，**谨慎使用**）。

  - `--richtext.valid <true|false>`: 是否保证生成合法格式（默认`true`）。

- **高级定制/关联性考虑：**

  - **嵌套标签/复杂结构：** 生成多层嵌套的HTML标签，测试解析器健壮性。

  - **图片/链接URL关联：** 如果生成图片或链接，其URL可与URL生成器关联。

- **典型用例/场景：**

  - **自动化测试：** 富文本编辑器输入、内容渲染、XSS漏洞测试。

  - **功能测试：** 测试富文本内容显示、安全过滤。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合HTML/Markdown规范（如果`--richtext.valid`为`true`）。

  - **异常：** 生成格式错误、标签未闭合、包含非法属性、恶意脚本的片段用于异常测试。

#### **5.3 Unicode 边界字符 (Unicode Boundary Characters)**

- **核心生成逻辑/规则：**

  - 生成包含Unicode字符集边界情况的文本，如：

    - 高低位代理对（Surrogate Pairs）。

    - 控制字符（Control Characters，如`\u0000`空字符，`\u0007`响铃符）。

    - Emoji表情符号。

    - 组合字符（Combining Characters）。

    - 零宽字符（Zero-width characters）。

  - 可配置生成特定类型的边界字符。

- **CLI配置参数建议：**

  - `--unicode.type <SURROGATE_PAIRS|CONTROL_CHARS|EMOJI|COMBINING_CHARS|ZERO_WIDTH|ALL>`: 指定Unicode字符类型（默认`ALL`）。

  - `--unicode.length <int>`: 指定生成字符串的长度。

  - `--unicode.frequency <float>`: 指定特殊字符在生成文本中的出现频率（0-1，默认`0.1`）。

- **高级定制/关联性考虑：**

  - **与长文本/短文本结合：** 将这些边界字符插入到普通文本中，测试系统对混合字符的处理。

- **典型用例/场景：**

  - **自动化测试：** 数据库存储、字符串处理、文本编码/解码、跨平台兼容性、安全漏洞（如零宽字符绕过）。

  - **功能测试：** 测试文本输入框、显示区域对特殊Unicode字符的兼容性。

- **校验与异常处理建议：**

  - **校验：** 确保字符编码正确。

  - **异常：** 无。

#### **5.4 多语言示例 (Multilingual Examples)**

- **核心生成逻辑/规则：**

  - 生成包含指定语言（中文、英文、阿拉伯文、希腊文、日文假名、韩文等）的文本片段。

  - 可配置生成单语文本或多语言混排文本。

- **CLI配置参数建议：**

  - `--multilang.languages <CN|EN|AR|EL|JA|KO|ANY>`: 指定要包含的语言列表（默认`ANY`，即随机选择多种）。

  - `--multilang.length <min_len,max_len>`: 指定文本长度范围。

  - `--multilang.mix_ratio <float>`: (仅对多语言) 指定不同语言混排的比例（0-1，如`0.5`表示各占一半）。

  - `--multilang.source_file.<lang_code> <path>`: 为特定语言指定自定义文本语料库。

- **高级定制/关联性考虑：**

  - **特定脚本方向：** 生成从右到左（RTL）语言（如阿拉伯文、希伯来文）的文本，测试UI布局。

- **典型用例/场景：**

  - **自动化测试：** 国际化(i18n)/本地化(l10n)测试、多语言搜索、编码兼容性。

  - **功能测试：** 测试多语言输入框、显示区域、字体兼容性。

- **校验与异常处理建议：**

  - **校验：** 确保字符编码正确。

  - **异常：** 无。

#### **5.5 特殊字符 (Special Characters)**

- **核心生成逻辑/规则：**

  - 生成包含各种特殊字符的文本，如：

    - 空格（全角／半角）、Tab、换行、回车。

    - 常见符号（`!@#$%^&*()_+-=[]{}|;:'",.<>/?`）。

    - 转义字符。

  - 可配置生成特定类型的特殊字符。

- **CLI配置参数建议：**

  - `--specialchars.type <WHITESPACE|COMMON_SYMBOLS|ESCAPED_CHARS|ALL>`: 指定特殊字符类型（默认`ALL`）。

  - `--specialchars.length <int>`: 指定生成字符串的长度。

  - `--specialchars.frequency <float>`: 指定特殊字符在生成文本中的出现频率（0-1，默认`0.2`）。

  - `--specialchars.include_zero_width <true|false>`: 是否包含零宽字符（默认`false`）。

- **高级定制/关联性考虑：**

  - **与长文本/短文本结合：** 将这些特殊字符插入到普通文本中，测试系统对混合字符的处理。

  - **SQL注入/XSS等攻击：** 结合安全注入模块，生成包含特殊字符的攻击payload。

- **典型用例/场景：**

  - **自动化测试：** 文本输入处理、字符串解析、数据库存储、安全过滤。

  - **功能测试：** 测试输入框对特殊字符的兼容性、搜索功能。

- **校验与异常处理建议：**

  - **校验：** 确保字符编码正确。

  - **异常：** 无。

#### **5.6 用户行为数据 (User Behavior Data)**

- **核心生成逻辑/规则：**

  - **点击流数据：** 模拟用户在网站或应用中的点击路径、访问页面、停留时间等。需要定义页面/事件列表和跳转规则。

  - **搜索关键词：** 从预设关键词库中随机选择或组合。

  - **购物车内容：** 模拟用户购物车中的商品ID及数量，需与产品编码关联。

- **CLI配置参数建议：**

  - `--user_behavior.type <CLICKSTREAM|SEARCH_KEYWORDS|CART_CONTENT>`: 指定行为数据类型。

  - `--user_behavior.clickstream.pages <page1,page2,...>`: (仅对点击流) 指定页面列表。

  - `--user_behavior.clickstream.min_steps <int>`: (仅对点击流) 最小点击步数。

  - `--user_behavior.clickstream.max_steps <int>`: (仅对点击流) 最大点击步数。

  - `--user_behavior.search.keywords_file <path>`: (仅对搜索) 指定搜索关键词库文件路径。

  - `--user_behavior.cart.product_ids <id1,id2,...>`: (仅对购物车) 指定可选商品ID列表。

  - `--user_behavior.cart.min_items <int>`: (仅对购物车) 购物车最小商品数量。

  - `--user_behavior.cart.max_items <int>`: (仅对购物车) 购物车最大商品数量。

- **高级定制/关联性考虑：**

  - **与用户ID关联：** 生成的用户行为数据应与特定用户ID关联。

  - **时间序列模拟：** 为点击流数据生成时间戳，模拟用户行为的时间序列。

  - **复杂行为路径：** 可定义用户行为的马尔可夫链模型，生成更真实的跳转路径。

- **典型用例/场景：**

  - **自动化测试：** 用户行为分析系统、推荐系统、A/B测试数据、日志系统。

  - **功能测试：** 模拟用户操作路径，验证数据上报和分析结果。

- **校验与异常处理建议：**

  - **校验：** 数据格式、关联性。

  - **异常：** 生成不符合业务逻辑的异常行为数据。

### **结构化／半结构化数据 (Structured / Semi-structured Data)**

#### **6.1 JSON 对象／数组 (JSON Object / Array)**

- **核心生成逻辑/规则：**

  - 根据用户定义的JSON Schema（或简化配置），生成符合结构的JSON对象或数组。

  - 支持嵌套结构、多种数据类型（字符串、数字、布尔、null、数组、对象）混用。

  - 可配置字段的必填/可选、数据类型、值范围、数组长度等。

  - 支持引用其他已定义的数据类型生成器来填充字段值。

- **CLI配置参数建议：**

  - `--json.schema_file <path>`: 指定JSON Schema文件路径，`DataForge`将根据此Schema生成数据。

  - `--json.template_file <path>`: 指定JSON模板文件路径，模板中可使用占位符，由`DataForge`填充。

  - `--json.pretty_print <true|false>`: 是否格式化输出JSON（默认`true`）。

  - `--json.min_depth <int>`: (仅对随机生成) 最小嵌套深度。

  - `--json.max_depth <int>`: (仅对随机生成) 最大嵌套深度。

  - `--json.array_min_items <int>`: (仅对数组) 数组最小元素数量。

  - `--json.array_max_items <int>`: (仅对数组) 数组最大元素数量。

  - `--json.include_nulls <true|false>`: 是否包含null值（默认`false`）。

  - `--json.include_missing_fields <true|false>`: 是否随机缺失可选字段（默认`false`）。

  - `--json.valid <true|false>`: 是否保证符合Schema（默认`true`）。设置为`false`可生成不符合Schema的JSON用于异常测试。

- **高级定制/关联性考虑：**

  - **数据关联性：** 允许在Schema或模板中定义字段间的引用关系，例如一个JSON对象中的`userId`字段引用另一个JSON对象中的`id`字段。

  - **复杂数据类型嵌套：** 深度嵌套的对象和数组，用于测试解析器的递归处理能力。

  - **超大JSON文件：** 生成MB/GB级别的JSON文件，用于测试内存和传输性能。

- **典型用例/场景：**

  - **自动化测试：** RESTful API接口的请求/响应体、NoSQL数据库文档、消息队列数据。

  - **功能测试：** 测试JSON数据的解析、显示、存储和处理（如字段缺失、数据类型不匹配）。

- **校验与异常处理建议：**

  - **校验：** 格式是否为合法JSON，是否符合提供的JSON Schema。

  - **异常：** 生成非JSON格式、Schema不匹配、嵌套过深、字段缺失、数据类型错误、包含非法Unicode字符的JSON。

#### **6.2 XML 文档 (XML Document)**

- **核心生成逻辑/规则：**

  - 根据用户定义的XML Schema (XSD) 或简化配置，生成符合结构的XML文档。

  - 支持命名空间、DTD、CDATA、属性、节点嵌套等。

  - 可配置节点名称、属性值、文本内容、嵌套深度、元素数量等。

  - 支持引用其他已定义的数据类型生成器来填充元素/属性值。

- **CLI配置参数建议：**

  - `--xml.schema_file <path>`: 指定XML Schema (XSD) 文件路径，`DataForge`将根据此Schema生成数据。

  - `--xml.template_file <path>`: 指定XML模板文件路径，模板中可使用占位符。

  - `--xml.pretty_print <true|false>`: 是否格式化输出XML（默认`true`）。

  - `--xml.min_depth <int>`: (仅对随机生成) 最小嵌套深度。

  - `--xml.max_depth <int>`: (仅对随机生成) 最大嵌套深度。

  - `--xml.include_cdata <true|false>`: 是否包含CDATA节（默认`false`）。

  - `--xml.include_namespaces <true|false>`: 是否包含命名空间（默认`false`）。

  - `--xml.valid <true|false>`: 是否保证符合Schema（默认`true`）。设置为`false`可生成不符合Schema的XML用于异常测试。

- **高级定制/关联性考虑：**

  - **数据关联性：** 允许在Schema或模板中定义元素间的引用关系。

  - **复杂结构：** 深度嵌套的元素和属性，用于测试解析器健壮性。

  - **超大XML文件：** 生成MB/GB级别的XML文件，用于测试内存和传输性能。

- **典型用例/场景：**

  - **自动化测试：** SOAP Web Service请求/响应、旧系统数据交换、XML配置文件。

  - **功能测试：** 测试XML数据的解析、显示、存储和处理。

- **校验与异常处理建议：**

  - **校验：** 格式是否为合法XML，是否符合提供的XML Schema。

  - **异常：** 生成非XML格式、Schema不匹配、嵌套过深、标签未闭合、属性非法、包含非法Unicode字符的XML。

#### **6.3 YAML (YAML Ain't Markup Language)**

- **核心生成逻辑/规则：**

  - 根据用户定义的YAML Schema（或简化配置），生成符合结构的YAML文档。

  - 支持缩进、列表、映射、引用（锚点和别名）、合并标签等YAML特性。

  - 可配置字段的必填/可选、数据类型、值范围、列表长度等。

  - 支持引用其他已定义的数据类型生成器来填充字段值。

- **CLI配置参数建议：**

  - `--yaml.schema_file <path>`: 指定YAML Schema文件路径，`DataForge`将根据此Schema生成数据。

  - `--yaml.template_file <path>`: 指定YAML模板文件路径，模板中可使用占位符。

  - `--yaml.min_depth <int>`: (仅对随机生成) 最小嵌套深度。

  - `--yaml.max_depth <int>`: (仅对随机生成) 最大嵌套深度。

  - `--yaml.array_min_items <int>`: (仅对列表) 列表最小元素数量。

  - `--yaml.array_max_items <int>`: (仅对列表) 列表最大元素数量。

  - `--yaml.include_anchors <true|false>`: 是否包含锚点和别名（默认`false`）。

  - `--yaml.valid <true|false>`: 是否保证符合Schema（默认`true`）。设置为`false`可生成不符合Schema的YAML用于异常测试。

- **高级定制/关联性考虑：**

  - **数据关联性：** 允许在Schema或模板中定义字段间的引用关系。

  - **复杂结构：** 深度嵌套的映射和列表，用于测试解析器健壮性。

- **典型用例/场景：**

  - **自动化测试：** 配置文件生成、自动化脚本参数、Kubernetes配置。

  - **功能测试：** 测试YAML数据的解析、显示。

- **校验与异常处理建议：**

  - **校验：** 格式是否为合法YAML，是否符合提供的Schema。

  - **异常：** 生成缩进错误、格式错误、Schema不匹配、字段缺失、数据类型错误、包含非法Unicode字符的YAML。

#### **6.4 CSV／TSV (Comma Separated Values / Tab Separated Values)**

- **核心生成逻辑/规则：**

  - 生成由指定分隔符（逗号、制表符或其他）分隔的文本行。

  - 可配置是否包含标题行、是否包含引号、换行符处理。

  - 支持引用其他已定义的数据类型生成器来填充列值。

- **CLI配置参数建议：**

  - `--csv.delimiter <string>`: 指定分隔符（如`,`, `\t`，默认`,`）。

  - `--csv.include_header <true|false>`: 是否包含标题行（默认`true`）。

  - `--csv.quote_mode <ALL|NON_NUMERIC|NONE>`: 字段引用模式（所有字段加引号、非数字字段加引号、不加引号，默认`NON_NUMERIC`）。

  - `--csv.columns <column1_type,column2_type,...>`: 定义列及其数据类型（如`name:string,age:int,email:email`）。

  - `--csv.column.<column_name>.param <value>`: 对特定列的数据生成器进行参数配置（如`--csv.column.age.min 18`）。

  - `--csv.valid <true|false>`: 是否保证生成有效CSV（如引号匹配，默认`true`）。

- **高级定制/关联性考虑：**

  - **数据关联性：** 允许在列定义中指定字段间的引用关系，确保行内数据的逻辑一致性。

  - **大文件生成：** 流式写入，支持生成GB级别的大型CSV文件，用于性能测试和数据导入。

  - **特定编码：** 可配置生成GBK、UTF-8等不同编码的CSV文件。

- **典型用例/场景：**

  - **自动化测试：** 数据导入/导出功能、数据分析、性能测试（如JMeter的CSV Data Set Config）。

  - **功能测试：** 测试CSV文件的解析、导入、导出。

- **校验与异常处理建议：**

  - **校验：** 行数、列数、分隔符、引号匹配。

  - **异常：** 生成分隔符不匹配、引号不闭合、包含换行符但未加引号、列数不一致的CSV。

#### **6.5 表单数据 (Form Data)**

- **核心生成逻辑/规则：**

  - 生成符合`application/x-www-form-urlencoded`或`multipart/form-data`格式的键值对数据。

  - 支持URL编码、文件上传模拟（仅文件名和MIME Type）。

  - 可配置字段名称、值类型、值范围。

- **CLI配置参数建议：**

  - `--form.content_type <URL_ENCODED|MULTIPART>`: 指定表单类型（默认`URL_ENCODED`）。

  - `--form.fields <field1_name:type,field2_name:type,...>`: 定义表单字段及其数据类型。

  - `--form.field.<field_name>.param <value>`: 对特定字段的数据生成器进行参数配置。

  - `--form.multipart.boundary <string>`: (仅对`MULTIPART`) 指定自定义边界字符串。

  - `--form.multipart.file_field <field_name:filename:mimetype>`: (仅对`MULTIPART`) 模拟文件上传字段。

- **高级定制/关联性考虑：**

  - **与文件路径/MIME Type关联：** 在模拟`multipart/form-data`时，文件字段的`filename`和`Content-Type`可与文件路径和MIME Type生成器关联。

  - **超长字段值：** 生成超长字段值，用于测试表单提交的长度限制。

- **典型用例/场景：**

  - **自动化测试：** Web表单提交、API接口测试（POST请求体）。

  - **功能测试：** 测试表单提交的校验、文件上传功能。

- **校验与异常处理建议：**

  - **校验：** 格式符合`application/x-www-form-urlencoded`或`multipart/form-data`规范。

  - **异常：** 生成格式错误、字段值过长、包含非法字符的表单数据。

### **数值／计量类 (Numeric / Measurement)**

#### **7.1 可定义长度的随机数 (Random Number with Definable Length)**

- **核心生成逻辑/规则：**

  - 生成指定位数（长度）的随机整数。

  - 可配置是否包含前导零。

- **CLI配置参数建议：**

  - `--random_num.length <int>`: 指定数字的位数（如`6`表示生成6位数字）。

  - `--random_num.leading_zeros <true|false>`: 是否允许包含前导零（默认`false`）。

  - `--random_num.min <int>`: (可选) 最小值（例如，如果长度为6，但希望最小值是100000）。

  - `--random_num.max <int>`: (可选) 最大值。

- **高级定制/关联性考虑：**

  - **与业务单据号关联：** 可作为业务单据号中的序列号部分。

  - **特定分布：** 可扩展支持生成符合正态分布、泊松分布等特定统计分布的随机数。

- **典型用例/场景：**

  - **自动化测试：** 各种业务ID、验证码、批次号等。

  - **功能测试：** 测试输入框对特定长度数字的校验。

- **校验与异常处理建议：**

  - **校验：** 确保生成的数字位数正确。

  - **异常：** 生成位数不符、包含非数字字符的“数字”。

#### **7.2 小数 (Decimal)**

- **核心生成逻辑/规则：**

  - 生成指定范围、指定小数位数的浮点数。

  - 可配置正负数、是否包含零。

- **CLI配置参数建议：**

  - `--decimal.min <float>`: 最小值（默认`0.0`）。

  - `--decimal.max <float>`: 最大值（默认`100.0`）。

  - `--decimal.precision <int>`: 小数点后位数（默认`2`）。

  - `--decimal.include_negative <true|false>`: 是否包含负数（默认`true`，如果`min`为负）。

  - `--decimal.include_zero <true|false>`: 是否包含零（默认`true`）。

- **高级定制/关联性考虑：**

  - **金融精度：** 可配置高精度小数，用于金融计算测试。

  - **特定分布：** 允许生成符合特定统计分布的小数。

- **典型用例/场景：**

  - **自动化测试：** 金额、价格、百分比、测量数据。

  - **功能测试：** 测试小数输入框的校验（合法/非法小数位数、范围）、精度处理。

- **校验与异常处理建议：**

  - **校验：** 确保在指定范围和精度内。

  - **异常：** 生成超出范围、精度不符、包含非数字字符的小数。

#### **7.3 整数 (Integer)**

- **核心生成逻辑/规则：**

  - 生成指定范围的整数。

  - 可配置正负数、是否包含零。

- **CLI配置参数建议：**

  - `--integer.min <int>`: 最小值（默认`0`）。

  - `--integer.max <int>`: 最大值（默认`100`）。

  - `--integer.include_negative <true|false>`: 是否包含负数（默认`true`，如果`min`为负）。

  - `--integer.include_zero <true|false>`: 是否包含零（默认`true`）。

- **高级定制/关联性考虑：**

  - **边界值：** 可用于生成系统支持的最大/最小值（如`Integer.MAX_VALUE`）。

  - **特定步长：** 允许按特定步长生成整数序列。

- **典型用例/场景：**

  - **自动化测试：** 数量、年龄、计数器、ID。

  - **功能测试：** 测试整数输入框的校验（合法/非法范围）、边界值。

- **校验与异常处理建议：**

  - **校验：** 确保在指定整数范围内。

  - **异常：** 生成负数、超出范围、包含非数字字符的整数。

#### **7.4 负数 (Negative Number)**

- **核心生成逻辑/规则：**

  - 生成指定范围的负整数或负小数。

- **CLI配置参数建议：**

  - `--negative.type <INTEGER|DECIMAL>`: 指定生成负整数或负小数（默认`INTEGER`）。

  - `--negative.min <float>`: 最小值（负数，如`-100.0`）。

  - `--negative.max <float>`: 最大值（负数，如`-1.0`）。

  - `--negative.precision <int>`: (仅对小数) 小数点后位数。

- **高级定制/关联性考虑：**

  - **与金额关联：** 模拟退款、负库存等场景。

- **典型用例/场景：**

  - **自动化测试：** 财务系统（退款、亏损）、库存管理（负库存）、异常数据处理。

  - **功能测试：** 测试系统对负数的处理、输入校验。

- **校验与异常处理建议：**

  - **校验：** 确保为负数且在指定范围内。

  - **异常：** 生成非负数、超出范围的数字。

#### **7.5 币种 (Currency)**

- **核心生成逻辑/规则：**

  - 从ISO 4217标准中定义的货币代码（如`USD`, `CNY`, `EUR`）中随机选择。

  - 可配置生成常用货币或所有货币。

- **CLI配置参数建议：**

  - `--currency.type <ISO_CODE|SYMBOL|NAME>`: 指定生成货币代码（USD）、符号（$）或名称（美元）（默认`ISO_CODE`）。

  - `--currency.common_only <true|false>`: 是否只生成常用货币（默认`true`）。

  - `--currency.file <path>`: 指定自定义货币列表文件路径。

- **高级定制/关联性考虑：**

  - **与金额关联：** 在生成金额数据时，可同时生成对应的货币代码。

- **典型用例/场景：**

  - **自动化测试：** 国际化电商、金融交易系统、多币种支付。

  - **功能测试：** 测试币种选择、多币种显示。

- **校验与异常处理建议：**

  - **校验：** 符合ISO 4217规范或预设列表。

  - **异常：** 生成非法货币代码。

#### **7.6 科学计数法 (Scientific Notation)**

- **核心生成逻辑/规则：**

  - 生成符合科学计数法格式的字符串（如`1.23e+10`, `5.67E-4`）。

  - 可配置尾数范围、指数范围。

- **CLI配置参数建议：**

  - `--scientific.mantissa_min <float>`: 尾数最小值（默认`1.0`）。

  - `--scientific.mantissa_max <float>`: 尾数最大值（默认`9.99`）。

  - `--scientific.exponent_min <int>`: 指数最小值（默认`-10`）。

  - `--scientific.exponent_max <int>`: 指数最大值（默认`10`）。

  - `--scientific.precision <int>`: 尾数小数位精度（默认`2`）。

  - `--scientific.e_case <UPPER|LOWER|ANY>`: 指数符号`e`的大小写（默认`ANY`）。

- **高级定制/关联性考虑：**

  - **极端值：** 生成极大或极小的数，用于测试数据类型溢出、精度丢失等问题。

- **典型用例/场景：**

  - **自动化测试：** 科学计算、大数据处理、金融系统（高精度计算）、传感器数据。

  - **功能测试：** 测试数字输入框对科学计数法的兼容性、显示精度。

- **校验与异常处理建议：**

  - **校验：** 格式符合科学计数法规范。

  - **异常：** 生成格式错误、指数或尾数超出范围的科学计数法字符串。

#### **7.7 高精度小数 (High-Precision Decimal)**

- **核心生成逻辑/规则：**

  - 生成具有指定小数位数（通常大于2位）的浮点数，用于模拟精确的货币计算、税率等。

  - 可配置整数部分和小数部分的长度及范围。

- **CLI配置参数建议：**

  - `--high_precision.integer_length <int>`: 整数部分最大长度（默认`10`）。

  - `--high_precision.decimal_length <int>`: 小数部分长度（默认`4`，可设为`8`甚至更高）。

  - `--high_precision.min <float>`: 最小值。

  - `--high_precision.max <float>`: 最大值。

  - `--high_precision.include_negative <true|false>`: 是否包含负数。

- **高级定制/关联性考虑：**

  - **特定业务场景：** 模拟金融交易、税务计算（如金税三期）、科学测量等对精度要求高的场景。

- **典型用例/场景：**

  - **自动化测试：** 金融系统、税务系统、计量系统。

  - **功能测试：** 测试系统对高精度小数的存储、计算和显示。

- **校验与异常处理建议：**

  - **校验：** 确保在指定范围和精度内。

  - **异常：** 生成超出范围、精度不符的数字。

#### **7.8 度量单位组合 (Measurement Unit Combination)**

- **核心生成逻辑/规则：**

  - 生成数值和单位的组合（如`10 kg`, `5.5 m`, `25 °C`）。

  - 可配置度量类型（长度、重量、体积、温度、速度等），并从预设的单位列表中随机选择。

  - 数值部分可关联`decimal`或`integer`生成器。

- **CLI配置参数建议：**

  - `--measure.type <LENGTH|WEIGHT|VOLUME|TEMPERATURE|SPEED|ANY>`: 指定度量类型（默认`ANY`）。

  - `--measure.value_min <float>`: 数值最小值。

  - `--measure.value_max <float>`: 数值最大值。

  - `--measure.value_precision <int>`: 数值小数位精度。

  - `--measure.units <unit1,unit2,...>`: 指定单位列表（如`m,km,ft`）。

  - `--measure.unit_file <path>`: 指定自定义单位列表文件路径。

- **高级定制/关联性考虑：**

  - **单位转换：** 可生成一组数值，并附带不同单位，测试单位转换功能。

- **典型用例/场景：**

  - **自动化测试：** 物联网(IoT)数据、传感器数据、库存管理（按体积/重量）、物流系统。

  - **功能测试：** 测试度量单位的输入、显示和转换。

- **校验与异常处理建议：**

  - **校验：** 格式符合“数值+单位”，单位在预设列表中。

  - **异常：** 生成非法单位、数值格式错误的数据。

#### **7.9 百分比、利率、汇率 (Percentage, Interest Rate, Exchange Rate)**

- **核心生成逻辑/规则：**

  - 生成浮点数，可配置是否包含百分号、千分位分隔符。

  - 可配置数值范围和精度。

- **CLI配置参数建议：**

  - `--rate.type <PERCENTAGE|INTEREST_RATE|EXCHANGE_RATE>`: 指定类型。

  - `--rate.min <float>`: 最小值（默认`0.0`）。

  - `--rate.max <float>`: 最大值（默认`1.0`，对应100%）。

  - `--rate.precision <int>`: 小数点后位数（默认`4`）。

  - `--rate.include_symbol <true|false>`: 是否包含`%`符号（仅对百分比，默认`true`）。

  - `--rate.include_thousands_separator <true|false>`: 是否包含千分位分隔符（默认`false`）。

- **高级定制/关联性考虑：**

  - **与币种关联：** 汇率可与两种币种关联（如`USD/CNY`）。

- **典型用例/场景：**

  - **自动化测试：** 金融系统、报表生成、营销活动（折扣）。

  - **功能测试：** 测试百分比/利率/汇率的输入、显示和计算。

- **校验与异常处理建议：**

  - **校验：** 数值范围、精度、格式（符号）。

  - **异常：** 生成超出范围、格式错误（如`100%%`）的数据。

#### **7.10 坐标／矩阵数据 (Coordinate / Matrix Data)**

- **核心生成逻辑/规则：**

  - **向量：** 生成一维数组的数值（如`[x, y, z]`）。

  - **矩阵：** 生成二维数组的数值（如`[[a,b],[c,d]]`）。

  - **张量：** 生成多维数组的数值（复杂，可作为未来扩展）。

  - 可配置维度、数值范围、精度。

- **CLI配置参数建议：**

  - `--matrix.type <VECTOR|MATRIX>`: 指定数据类型（默认`VECTOR`）。

  - `--matrix.rows <int>`: (仅对矩阵) 行数。

  - `--matrix.cols <int>`: (仅对矩阵) 列数。

  - `--matrix.dimension <int>`: (仅对向量) 向量维度（如`2`表示`[x,y]`，`3`表示`[x,y,z]`）。

  - `--matrix.value_min <float>`: 矩阵/向量中数值的最小值。

  - `--matrix.value_max <float>`: 矩阵/向量中数值的最大值。

  - `--matrix.value_precision <int>`: 数值小数位精度。

- **高级定制/关联性考虑：**

  - **稀疏矩阵：** 可配置生成包含大量零的稀疏矩阵。

  - **特定矩阵类型：** 如单位矩阵、对角矩阵。

- **典型用例/场景：**

  - **自动化测试：** 图像处理、机器学习模型输入、科学计算、游戏开发（变换矩阵）。

  - **功能测试：** 测试系统对复杂数值结构的处理。

- **校验与异常处理建议：**

  - **校验：** 维度、数值范围。

  - **异常：** 生成维度不符、数值格式错误的数据。

#### **7.11 颜色值 (Color Value)**

- **核心生成逻辑/规则：**

  - 生成RGB、HEX、HSL等格式的颜色值。

  - 可配置透明度（Alpha值）。

- **CLI配置参数建议：H

  - `--color.format <RGB|HEX|HSL|ANY>`: 指定颜色格式（默认`HEX`）。

  - `--color.include_alpha <true|false>`: 是否包含Alpha透明度（默认`false`）。

  - `--color.valid <true|false>`: 是否保证生成有效颜色值（默认`true`）。

- **高级定制/关联性考虑：**

  - **特定色系：** 可配置生成暖色系、冷色系或特定范围的颜色。

- **典型用例/场景：**

  - **自动化测试：** UI自动化测试（验证颜色）、图表生成、设计工具。

  - **功能测试：** 测试颜色选择器、颜色显示。

- **校验与异常处理建议：**

  - **校验：** 格式符合RGB/HEX/HSL规范，数值范围正确。

  - **异常：** 生成格式错误、数值超出范围的颜色值。

### **时间／日历类 (Time / Calendar)**

#### **8.1 日期 (Date)**

- **核心生成逻辑/规则：**

  - 在指定日期范围内随机生成日期。

  - 支持多种日期格式（如`YYYY-MM-DD`、`MM/DD/YYYY`、`YYYYMMDD`）。

  - 考虑闰年、月份天数等日历规则。

- **CLI配置参数建议：**

  - `--date.format <format_string>`: 指定日期格式（如`yyyy-MM-dd`，`MM/dd/yyyy`，默认`yyyy-MM-dd`）。

  - `--date.start <YYYY-MM-DD>`: 指定日期范围的开始日期（默认当前日期前1年）。

  - `--date.end <YYYY-MM-DD>`: 指定日期范围的结束日期（默认当前日期）。

  - `--date.valid <true|false>`: 是否保证生成有效日期（默认`true`）。设置为`false`可生成非法日期用于异常测试（如`2023-02-30`）。

- **高级定制/关联性考虑：**

  - **与年龄关联：** 可以通过指定日期范围来间接控制年龄范围。

  - **特定日期类型：** 如工作日、周末、节假日（需外部节假日数据源）。

  - **连续日期：** 生成一个日期序列，用于测试时间序列数据。

- **典型用例/场景：**

  - **自动化测试：** 订单日期、注册日期、合同生效/截止日期、报表日期筛选。

  - **功能测试：** 测试日期输入框的校验（合法/非法日期、边界值）、日期选择器、日期格式显示。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合，日期是否有效（如2月30日是无效日期）。

  - **异常：** 生成格式错误、日期不存在、超出范围的日期。

#### **8.2 时间 (Time)**

- **核心生成逻辑/规则：**

  - 在指定时间范围内随机生成时间。

  - 支持多种时间格式（如`HH:mm:ss`、`HHmmss`）。

  - 可配置是否包含毫秒。

- **CLI配置参数建议：**

  - `--time.format <format_string>`: 指定时间格式（如`HH:mm:ss`，`HH:mm:ss.SSS`，默认`HH:mm:ss`）。

  - `--time.start <HH:mm:ss>`: 指定时间范围的开始时间（默认`00:00:00`）。

  - `--time.end <HH:mm:ss>`: 指定时间范围的结束时间（默认`23:59:59`）。

  - `--time.include_milliseconds <true|false>`: 是否包含毫秒（默认`false`）。

  - `--time.valid <true|false>`: 是否保证生成有效时间（默认`true`）。设置为`false`可生成非法时间用于异常测试（如`25:00:00`）。

- **高级定制/关联性考虑：**

  - **与日期关联：** 结合日期生成完整的时间点。

  - **特定时间段：** 模拟工作时间、高峰期时间等。

- **典型用例/场景：**

  - **自动化测试：** 交易时间、日志时间、事件发生时间。

  - **功能测试：** 测试时间输入框的校验、时间选择器、时间格式显示。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合，时间是否有效（如小时0-23，分钟0-59）。

  - **异常：** 生成格式错误、时间超出范围的日期。

#### **8.3 时间戳 (Timestamp)**

- **核心生成逻辑/规则：**

  - 生成Unix时间戳（秒或毫秒），表示从Unix纪元（1970-01-01 00:00:00 UTC）到指定时间的秒数或毫秒数。

  - 可在指定日期/时间范围内生成对应的时间戳。

- **CLI配置参数建议：**

  - `--timestamp.unit <SECONDS|MILLISECONDS>`: 指定时间戳单位（秒或毫秒，默认`MILLISECONDS`）。

  - `--timestamp.start_date <YYYY-MM-DD HH:mm:ss>`: 指定时间戳范围的开始时间点（默认`1970-01-01 00:00:00`）。

  - `--timestamp.end_date <YYYY-MM-DD HH:mm:ss>`: 指定时间戳范围的结束时间点（默认当前时间）。

  - `--timestamp.valid <true|false>`: 是否保证生成有效时间戳（默认`true`）。

- **高级定制/关联性考虑：**

  - **未来/过去时间戳：** 可用于测试过期逻辑或历史数据处理。

  - **时间戳精度：** 可配置纳秒级时间戳（如果系统支持）。

- **典型用例/场景：**

  - **自动化测试：** 数据库时间字段、API接口参数、日志记录、缓存过期时间。

  - **功能测试：** 测试系统对时间戳的存储、转换和显示。

- **校验与异常处理建议：**

  - **校验：** 确保为数字，且在合理的时间戳范围内。

  - **异常：** 生成超出`long`类型范围、负数的时间戳。

#### **8.4 ISO 8601 周日期 (ISO 8601 Week Date)**

- **核心生成逻辑/规则：**

  - 生成符合ISO 8601周日期格式的字符串（`YYYY-Www-D`，如`2025-W29-1`表示2025年第29周的星期一）。

  - 可配置年份范围和周数范围。

- **CLI配置参数建议：**

  - `--iso_week.year_start <int>`: 开始年份（默认`2000`）。

  - `--iso_week.year_end <int>`: 结束年份（默认当前年份）。

  - `--iso_week.week_start <int>`: 周数最小值（默认`1`）。

  - `--iso_week.week_end <int>`: 周数最大值（默认`53`）。

  - `--iso_week.day_of_week <1-7|ANY>`: 指定星期几（1为周一，7为周日，默认`ANY`）。

  - `--iso_week.valid <true|false>`: 是否保证生成有效周日期（默认`true`）。

- **高级定制/关联性考虑：**

  - **与日期关联：** 可根据生成的周日期反推出具体的日期。

- **典型用例/场景：**

  - **自动化测试：** 报表周期、项目计划、周报系统。

  - **功能测试：** 测试周日期输入、显示和计算。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合`YYYY-Www-D`，周数和天数是否有效。

  - **异常：** 生成格式错误、周数或天数超出范围的周日期。

#### **8.5 年份区间 (Year Range)**

- **核心生成逻辑/规则：**

  - 生成表示年份区间的字符串（如`2020/2025`、`2022-2024`）。

  - 可配置年份范围、分隔符、区间长度。

- **CLI配置参数建议：**

  - `--year_range.format <SLASH|HYPHEN|CUSTOM>`: 指定分隔符（`/`或`-`，默认`SLASH`）。

  - `--year_range.start_year_min <int>`: 开始年份最小值（默认`1900`）。

  - `--year_range.start_year_max <int>`: 开始年份最大值（默认当前年份）。

  - `--year_range.duration_min <int>`: 区间最小持续年数（默认`1`）。

  - `--year_range.duration_max <int>`: 区间最大持续年数（默认`5`）。

  - `--year_range.valid <true|false>`: 是否保证开始年份小于等于结束年份（默认`true`）。

- **高级定制/关联性考虑：**

  - **与学历/工作经验关联：** 模拟教育或工作经历的时间段。

- **典型用例/场景：**

  - **自动化测试：** 简历系统、教育背景、项目周期管理。

  - **功能测试：** 测试年份区间输入框的校验、范围筛选。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合，开始年份小于等于结束年份。

  - **异常：** 生成格式错误、开始年份大于结束年份的区间。

#### **8.6 时段／持续时间 (Period / Duration)**

- **核心生成逻辑/规则：**

  - 生成符合ISO 8601持续时间格式的字符串（`P1Y2M10DT2H30M`表示1年2月10天2小时30分钟）。

  - 可配置包含的单位（年、月、日、小时、分钟、秒）。

  - 可配置每个单位的数值范围。

- **CLI配置参数建议：**

  - `--duration.units <Y|M|D|H|MIN|S|ANY>`: 指定包含的单位列表（默认`ANY`）。

  - `--duration.min_value <int>`: 每个单位的最小值（默认`0`）。

  - `--duration.max_value <int>`: 每个单位的最大值（默认`60`，对年/月/日可更大）。

  - `--duration.valid <true|false>`: 是否保证生成有效持续时间（默认`true`）。

- **高级定制/关联性考虑：**

  - **与日期/时间计算：** 可用于计算某个日期/时间点加上或减去一个持续时间后的结果。

- **典型用例/场景：**

  - **自动化测试：** 任务持续时间、项目工期、服务有效期。

  - **功能测试：** 测试持续时间输入、显示和计算。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合ISO 8601持续时间规范。

  - **异常：** 生成格式错误、数值超出范围的持续时间。

#### **8.7 金融交易日历 (Financial Trading Calendar)**

- **核心生成逻辑/规则：**

  - 生成指定市场（如A股、美股）的交易日或非交易日（周末、节假日）。

  - 需要维护金融市场的节假日数据。

- **CLI配置参数建议：**

  - `--trading_calendar.market <A_SHARE|US_STOCK|CUSTOM>`: 指定金融市场（默认`A_SHARE`）。

  - `--trading_calendar.type <TRADING_DAY|NON_TRADING_DAY>`: 指定生成交易日还是非交易日。

  - `--trading_calendar.start_date <YYYY-MM-DD>`: 开始日期。

  - `--trading_calendar.end_date <YYYY-MM-DD>`: 结束日期。

  - `--trading_calendar.holiday_file <path>`: 指定自定义节假日文件路径。

- **高级定制/关联性考虑：**

  - **与交易时间关联：** 可结合时间生成交易日内的特定交易时间点。

- **典型用例/场景：**

  - **自动化测试：** 金融交易系统、清算结算系统、报表生成（按交易日）。

  - **功能测试：** 测试系统对交易日/非交易日的识别和处理。

- **校验与异常处理建议：**

  - **校验：** 确保日期有效且符合市场规则。

  - **异常：** 无。

#### **8.8 Cron 表达式 (Cron Expression)**

- **核心生成逻辑/规则：**

  - 生成符合Cron表达式规范的字符串，用于定义定时任务的执行计划。

  - 支持秒、分、时、日、月、周的组合，以及特殊字符（`*`, `?`, `-`, `,`, `/`, `L`, `W`, `#`）。

- **CLI配置参数建议：**

  - `--cron.type <SIMPLE|COMPLEX|ANY>`: 指定生成简单（如每小时）或复杂（如每月最后一个周五）的表达式（默认`ANY`）。

  - `--cron.minute <string>`: 分钟字段（如`*/5`每5分钟，默认`*`）。

  - `--cron.hour <string>`: 小时字段（如`9-17`工作时间，默认`*`）。

  - `--cron.day_of_month <string>`: 月份中的日期字段（默认`*`）。

  - `--cron.month <string>`: 月份字段（默认`*`）。

  - `--cron.day_of_week <string>`: 周中的日期字段（如`MON-FRI`，默认`?`）。

  - `--cron.include_seconds <true|false>`: 是否包含秒字段（默认`false`）。

  - `--cron.valid <true|false>`: 是否保证生成有效Cron表达式（默认`true`）。

- **高级定制/关联性考虑：**

  - **特定业务场景：** 模拟报表生成、数据同步、系统维护等定时任务。

- **典型用例/场景：**

  - **自动化测试：** 任务调度系统、定时任务配置。

  - **功能测试：** 测试Cron表达式的输入、解析和执行。

- **校验与异常处理建议：**

  - **校验：** 格式是否符合Cron表达式规范，各字段数值范围是否有效。

  - **异常：** 生成格式错误、数值超出范围、逻辑冲突（如同时指定`day_of_month`和`day_of_week`为非`?`）的Cron表达式。

### **安全／注入测试数据 (Security / Injection Test Data)**

#### **9.1 SQL 注入 payload (SQL Injection Payload)**

- **核心生成逻辑/规则：**

  - 生成常见的SQL注入攻击字符串，包括：

    - 基于错误的注入（Error-based）：`' OR 1=1 --`

    - 联合查询注入（Union-based）：`' UNION SELECT null, null, null --`

    - 盲注（Blind SQLi）：`' AND 1=1 --`，`' AND 1=2 --`

    - 时间盲注（Time-based blind）：`' AND SLEEP(5) --`

    - 堆叠查询注入（Stacked queries）：`; DROP TABLE users; --`

    - 带注释的注入：`' OR 1=1 /*`

    - 宽字节注入（针对特定编码）：`%df%27`

  - 可配置生成不同数据库类型（MySQL, PostgreSQL, SQL Server, Oracle）的特定语法。

- **CLI配置参数建议：**

  - `--sql_inject.type <ERROR|UNION|BLIND|TIME_BLIND|STACKED|COMMENT|WIDE_BYTE|ALL>`: 指定注入类型（默认`ALL`）。

  - `--sql_inject.db_type <MYSQL|PGSQL|MSSQL|ORACLE|ANY>`: 指定数据库类型，影响语法（默认`ANY`）。

  - `--sql_inject.param_name <string>`: (可选) 模拟注入的参数名（如`username`），可用于生成更完整的请求体。

  - `--sql_inject.encode <URL|BASE64|NONE>`: 是否对payload进行URL编码或Base64编码（默认`NONE`）。

  - `--sql_inject.length <min_len,max_len>`: 指定payload长度范围。

- **高级定制/关联性考虑：**

  - **与HTTP头/表单数据关联：** 可将生成的SQL注入payload作为HTTP请求头（如User-Agent）或表单字段的值。

  - **自定义注入点：** 允许用户提供一个基础SQL语句，然后将payload插入到指定位置。

- **典型用例/场景：**

  - **自动化测试：** Web应用安全扫描、API接口安全测试（SQL注入漏洞）。

  - **功能测试：** 测试输入框、查询参数、URL路径等对SQL注入的防御能力。

- **校验与异常处理建议：**

  - **校验：** 仅校验基本格式，不校验其是否实际有效（这取决于目标系统）。

  - **异常：** 无。

#### **9.2 XSS 攻击脚本 (XSS Attack Script)**

- **核心生成逻辑/规则：**

  - 生成常见的跨站脚本攻击（XSS）payload，包括：

    - 反射型XSS：`<script>alert(1)</script>`

    - 存储型XSS：`<img src=x onerror=alert(1)>`

    - DOM型XSS：`#<script>alert(1)</script>`

    - 编码绕过：`%3cscript%3ealert(1)%3c/script%3e`

    - HTML实体编码：`&lt;script&gt;alert(1)&lt;/script&gt;`

    - 事件处理函数：`<body onload=alert(1)>`

  - 可配置生成不同编码方式的payload。

- **CLI配置参数建议：**

  - `--xss_script.type <REFLECTED|STORED|DOM|ENCODED|EVENT_HANDLER|ALL>`: 指定XSS类型（默认`ALL`）。

  - `--xss_script.tag <script|img|body|any>`: 指定用于XSS的HTML标签（默认`script`）。

  - `--xss_script.event <onload|onerror|onclick|any>`: 指定事件处理函数（默认`onload`）。

  - `--xss_script.payload_content <string>`: 自定义XSS执行内容（如`alert(document.cookie)`，默认`alert(1)`）。

  - `--xss_script.encode <URL|HTML_ENTITY|BASE64|NONE>`: 是否对payload进行编码（默认`NONE`）。

  - `--xss_script.length <min_len,max_len>`: 指定payload长度范围。

- **高级定制/关联性考虑：**

  - **与富文本/长文本关联：** 可将XSS payload插入到富文本编辑器内容或用户评论中。

  - **DOM XSS模拟：** 结合URL生成器，将XSS payload作为URL参数或锚点。

- **典型用例/场景：**

  - **自动化测试：** Web应用安全扫描、输入验证、内容渲染安全。

  - **功能测试：** 测试用户输入、评论区、富文本编辑器、URL参数等对XSS的防御能力。

- **校验与异常处理建议：**

  - **校验：** 仅校验基本格式，不校验其是否实际有效。

  - **异常：** 无。

#### **9.3 命令注入 (Command Injection)**

- **核心生成逻辑/规则：**

  - 生成常见的命令注入payload，用于测试系统是否未对用户输入进行充分过滤，导致执行恶意系统命令。

  - 包括：`; rm -rf /`, `| ls`, `$(cat /etc/passwd)`, `&& whoami`。

  - 可配置针对不同操作系统（Linux/Unix, Windows）的命令分隔符和命令。

- **CLI配置参数建议：**

  - `--cmd_inject.os <LINUX|WINDOWS|ANY>`: 指定目标操作系统（默认`ANY`）。

  - `--cmd_inject.delimiter <SEMICOLON|PIPE|AMPERSAND|BACKTICK|ANY>`: 指定命令分隔符（`;`, `|`, `&&`, `` ` ``，默认`ANY`）。

  - `--cmd_inject.command <string>`: 自定义要注入的命令（如`cat /etc/passwd`，`ipconfig`）。

  - `--cmd_inject.encode <URL|BASE64|NONE>`: 是否对payload进行编码（默认`NONE`）。

  - `--cmd_inject.length <min_len,max_len>`: 指定payload长度范围。

- **高级定制/关联性考虑：**

  - **与文件路径/URL关联：** 可将命令注入payload作为文件名、URL参数等。

- **典型用例/场景：**

  - **自动化测试：** 后端服务接口、文件上传处理、系统命令执行功能。

  - **功能测试：** 测试系统对用户输入进行系统命令调用的防御能力。

- **校验与异常处理建议：**

  - **校验：** 仅校验基本格式。

  - **异常：** 无。

#### **9.4 路径穿越 (Path Traversal)**

- **核心生成逻辑/规则：**

  - 生成常见的路径穿越payload，用于测试系统是否未对用户输入的文件路径进行充分过滤，导致访问任意文件或目录。

  - 包括：`../../etc/passwd`, `..\..\windows\win.ini`, `%2e%2e%2f` (URL编码)。

  - 可配置不同操作系统风格和编码方式。

- **CLI配置参数建议：**

  - `--path_traversal.os <LINUX|WINDOWS|ANY>`: 指定目标操作系统（默认`ANY`）。

  - `--path_traversal.depth <int>`: 指定穿越深度（如`2`表示`../../`，默认`3`）。

  - `--path_traversal.target_file <string>`: 指定目标文件（如`etc/passwd`, `windows/win.ini`）。

  - `--path_traversal.encode <URL|NONE>`: 是否对payload进行URL编码（默认`NONE`）。

  - `--path_traversal.length <min_len,max_len>`: 指定payload长度范围。

- **高级定制/关联性考虑：**

  - **与文件路径/URL关联：** 可将路径穿越payload作为文件下载路径、文件上传路径、URL参数等。

- **典型用例/场景：**

  - **自动化测试：** 文件下载、文件上传、图片加载、日志文件访问等功能。

  - **功能测试：** 测试系统对文件路径输入的防御能力。

- **校验与异常处理建议：**

  - **校验：** 仅校验基本格式。

  - **异常：** 无。

#### **9.5 二进制／Base64 编码数据 (Binary / Base64 Encoded Data)**

- **核心生成逻辑/规则：**

  - 生成随机的二进制数据，并将其Base64编码。

  - 可配置原始二进制数据的长度和字符集。

  - 可生成合法或包含非法Base64字符的编码数据。

- **CLI配置参数建议：**

  - `--binary_data.length <min_len,max_len>`: 指定原始二进制数据长度范围（默认`10,100`）。

  - `--binary_data.chars <ASCII|UTF8|RANDOM_BYTE>`: 原始数据字符集或字节类型（默认`RANDOM_BYTE`）。

  - `--binary_data.encode <BASE64|NONE>`: 是否进行Base64编码（默认`BASE64`）。

  - `--binary_data.valid <true|false>`: 是否保证生成有效Base64编码（默认`true`）。设置为`false`可生成包含非法字符的Base64数据。

- **高级定制/关联性考虑：**

  - **大文件模拟：** 生成超长的Base64数据，模拟大文件传输。

  - **特定文件头模拟：** 可生成特定文件头（如PNG魔数）的Base64编码，模拟文件类型伪装。

- **典型用例/场景：**

  - **自动化测试：** 文件上传（Base64编码）、图片/文件内容传输、API接口（二进制数据）。

  - **功能测试：** 测试系统对Base64编码数据的解析、处理和存储。

- **校验与异常处理建议：**

  - **校验：** Base64编码是否有效（字符集、填充符）。

  - **异常：** 生成包含非法Base64字符、长度不符的编码数据。

### **媒体／二进制及文件相关类 (Media / Binary and File Related)**

#### **10.1 图像文件头 (Image File Header)**

- **核心生成逻辑/规则：**

  - 生成特定图像文件格式（如PNG, JPEG, GIF）的“魔数”（Magic Number）或文件头字节序列。

  - 这些文件头通常是文件类型识别的关键。

  - 可生成合法文件头或被篡改的文件头（用于测试文件类型识别的健壮性）。

- **CLI配置参数建议：**

  - `--image_header.type <PNG|JPEG|GIF|BMP|WEBP|ANY>`: 指定图像文件类型（默认`ANY`）。

  - `--image_header.valid <true|false>`: 是否生成合法的文件头（默认`true`）。设置为`false`可生成错误的文件头用于异常测试。

  - `--image_header.length <int>`: 指定生成的字节序列长度（默认`8`，足够包含常见魔数）。

- **高级定制/关联性考虑：**

  - **与文件内容关联：** 可作为模拟文件内容的前缀，用于测试文件上传时的类型校验。

  - **文件类型伪装：** 生成一个文件头，但实际内容是另一种类型，测试服务器端的文件类型识别和安全扫描。

- **典型用例/场景：**

  - **自动化测试：** 文件上传接口（验证文件类型）、图片处理服务、内容安全扫描。

  - **功能测试：** 测试图片上传功能对不同文件类型、合法性校验。

- **校验与异常处理建议：**

  - **校验：** 确保生成的字节序列符合指定图像格式的魔数。

  - **异常：** 生成不符合魔数规则的字节序列。

#### **10.2 音频片段 (Audio Snippet)**

- **核心生成逻辑/规则：**

  - 生成模拟的音频文件片段，通常是其文件头或少量模拟的二进制数据。

  - 支持常见的音频格式（如MP3, WAV, AAC）。

  - 不生成实际可播放的音频内容，仅模拟其二进制结构。

- **CLI配置参数建议：**

  - `--audio.format <MP3|WAV|AAC|FLAC|ANY>`: 指定音频格式（默认`ANY`）。

  - `--audio.length <int>`: 指定模拟二进制数据的长度（字节数，默认`1024`）。

  - `--audio.valid <true|false>`: 是否生成符合格式的有效片段（默认`true`）。

- **高级定制/关联性考虑：**

  - **与文件上传关联：** 模拟上传音频文件。

  - **大/小文件模拟：** 通过`length`参数控制模拟文件的大小。

- **典型用例/场景：**

  - **自动化测试：** 音频上传、转码服务、媒体处理管道。

  - **功能测试：** 测试音频文件上传、播放器兼容性（仅模拟文件存在）。

- **校验与异常处理建议：**

  - **校验：** 确保生成的二进制数据符合基本格式特征（如文件头）。

  - **异常：** 生成长度不符、包含非法字节的片段。

#### **10.3 视频片段 (Video Snippet)**

- **核心生成逻辑/规则：**

  - 生成模拟的视频文件片段，通常是其文件头或少量模拟的二进制数据。

  - 支持常见的视频格式（如MP4, AVI, MKV）。

  - 不生成实际可播放的视频内容，仅模拟其二进制结构。

- **CLI配置参数建议：**

  - `--video.format <MP4|AVI|MKV|MOV|ANY>`: 指定视频格式（默认`ANY`）。

  - `--video.length <int>`: 指定模拟二进制数据的长度（字节数，默认`2048`）。

  - `--video.valid <true|false>`: 是否生成符合格式的有效片段（默认`true`）。

- **高级定制/关联性考虑：**

  - **与文件上传关联：** 模拟上传视频文件。

  - **大/小文件模拟：** 通过`length`参数控制模拟文件的大小。

- **典型用例/场景：**

  - **自动化测试：** 视频上传、转码服务、媒体处理管道。

  - **功能测试：** 测试视频文件上传、播放器兼容性（仅模拟文件存在）。

- **校验与异常处理建议：**

  - **校验：** 确保生成的二进制数据符合基本格式特征（如文件头）。

  - **异常：** 生成长度不符、包含非法字节的片段。

#### **10.4 压缩包 (Archive File)**

- **核心生成逻辑/规则：**

  - 生成模拟的压缩文件（如ZIP, TAR, 7z），仅模拟其文件头和少量结构，不包含实际压缩内容。

  - 可模拟损坏的压缩包或边界大小的压缩包。

- **CLI配置参数建议：**

  - `--archive.format <ZIP|TAR|SEVEN_ZIP|RAR|ANY>`: 指定压缩格式（默认`ANY`）。

  - `--archive.length <int>`: 指定模拟二进制数据的长度（字节数，默认`512`）。

  - `--archive.corrupted <true|false>`: 是否生成损坏的压缩包（默认`false`）。

  - `--archive.valid <true|false>`: 是否生成符合格式的有效片段（默认`true`）。

- **高级定制/关联性考虑：**

  - **嵌套压缩包：** 可生成包含模拟压缩包的压缩包，测试解压工具的递归处理。

  - **超大压缩包（模拟）：** 通过`length`参数生成非常大的模拟文件，测试文件传输和存储。

- **典型用例/场景：**

  - **自动化测试：** 文件上传、解压缩服务、文件扫描。

  - **功能测试：** 测试压缩包上传、解压功能对合法/非法/损坏文件的处理。

- **校验与异常处理建议：**

  - **校验：** 确保生成的二进制数据符合基本格式特征。

  - **异常：** 生成长度不符、包含非法字节、模拟损坏的压缩包。

#### **10.5 PDF／Office 文档 (PDF / Office Document)**

- **核心生成逻辑/规则：**

  - 生成模拟的PDF或Office文档（如DOCX, XLSX, PPTX）的二进制片段。

  - 仅模拟文件头和基本结构，不包含实际可读内容。

  - 可模拟包含宏脚本或反射式XSS的“恶意”文档（仅模拟特征，不实际生成可执行宏）。

- **CLI配置参数建议：**

  - `--document.format <PDF|DOCX|XLSX|PPTX|ANY>`: 指定文档格式（默认`ANY`）。

  - `--document.length <int>`: 指定模拟二进制数据的长度（字节数，默认`1024`）。

  - `--document.include_macro_signature <true|false>`: 是否模拟包含宏脚本特征（默认`false`）。

  - `--document.include_xss_signature <true|false>`: 是否模拟包含反射式XSS特征（默认`false`）。

  - `--document.valid <true|false>`: 是否生成符合格式的有效片段（默认`true`）。

- **高级定制/关联性考虑：**

  - **与文件上传关联：** 模拟上传各种文档。

  - **大/小文件模拟：** 通过`length`参数控制模拟文件的大小。

- **典型用例/场景：**

  - **自动化测试：** 文档上传、预览服务、文件内容扫描（防病毒/DLP）。

  - **功能测试：** 测试文档上传、预览、下载功能对不同格式、合法/非法文档的处理。

- **校验与异常处理建议：**

  - **校验：** 确保生成的二进制数据符合基本格式特征。

  - **异常：** 生成长度不符、包含非法字节、模拟恶意特征的文档。

#### **10.6 图片/视频文件（模拟） (Simulated Image/Video File)**

- **核心生成逻辑/规则：**

  - 不生成实际文件内容，而是生成图片/视频文件的**占位符路径**或**模拟其元数据**（大小、格式、分辨率、时长等）。

  - 这对于不实际处理文件内容的业务逻辑测试非常有用。

- **CLI配置参数建议：**

  - `--media_file.type <IMAGE|VIDEO|ANY>`: 指定媒体文件类型（默认`ANY`）。

  - `--media_file.format <JPG|PNG|MP4|AVI|ANY>`: 指定媒体文件格式（默认`ANY`）。

  - `--media_file.path_only <true|false>`: 是否仅生成文件路径，不生成元数据（默认`false`）。

  - `--media_file.size_kb <min_kb,max_kb>`: 文件大小范围（KB，默认`100,1024`）。

  - `--media_file.width <min_px,max_px>`: (仅对图片) 宽度像素范围。

  - `--media_file.height <min_px,max_px>`: (仅对图片) 高度像素范围。

  - `--media_file.duration_sec <min_sec,max_sec>`: (仅对视频) 视频时长范围（秒）。

  - `--media_file.include_metadata <true|false>`: 是否包含元数据（默认`true`）。

- **高级定制/关联性考虑：**

  - **与URL关联：** 生成的路径可以是URL，模拟网络图片/视频资源。

  - **与文件路径生成器关联：** 路径本身可由文件路径生成器生成。

- **典型用例/场景：**

  - **自动化测试：** 媒体库管理、内容发布、CDN服务、相册功能。

  - **功能测试：** 测试图片/视频列表显示、文件大小限制、分辨率/时长显示。

- **校验与异常处理建议：**

  - **校验：** 元数据数值范围合理。

  - **异常：** 生成不合理的文件大小、分辨率、时长。

#### **10.7 文件扩展名 (File Extension)**

- **核心生成逻辑/规则：**

  - 从常用文件扩展名库中随机选择（如`.jpg`, `.pdf`, `.xlsx`, `.exe`）。

  - 可配置生成合法或非法/危险扩展名。

- **CLI配置参数建议：**

  - `--extension.type <IMAGE|DOCUMENT|ARCHIVE|EXECUTABLE|ANY>`: 指定扩展名类别（默认`ANY`）。

  - `--extension.valid <true|false>`: 是否生成合法/常用扩展名（默认`true`）。设置为`false`可生成非法或危险扩展名（如`.php`, `.asp`）用于安全测试。

  - `--extension.file <path>`: 指定自定义扩展名列表文件路径。

- **高级定制/关联性考虑：**

  - **与文件名关联：** 可与文件名生成器结合，生成完整的文件名。

  - **与MIME Type关联：** 确保扩展名与MIME Type匹配。

- **典型用例/场景：**

  - **自动化测试：** 文件上传/下载、文件类型校验、安全过滤。

  - **功能测试：** 测试文件上传限制（按扩展名）、文件类型识别。

- **校验与异常处理建议：**

  - **校验：** 格式是否为`.xxx`。

  - **异常：** 生成不含点、过长、非法字符的扩展名。

#### **10.8 MIME 类型 (MIME Type)**

- **说明：** 此数据类型已在**“3.7 联系／通信类”**中详细细化，此处不再重复。

#### **10.9 文件大小 (File Size)**

- **核心生成逻辑/规则：**

  - 生成指定范围内的文件大小数值，可配置单位（KB, MB, GB）。

- **CLI配置参数建议：**

  - `--filesize.min <float>`: 最小值（默认`0.0`）。

  - `--filesize.max <float>`: 最大值（默认`10.0`）。

  - `--filesize.unit <KB|MB|GB|BYTES>`: 指定单位（默认`MB`）。

  - `--filesize.precision <int>`: 小数点后位数（默认`2`）。

- **高级定制/关联性考虑：**

  - **边界值：** 生成0字节文件、系统最大文件大小限制的边界值。

  - **与文件内容关联：** 可作为模拟文件内容生成时的长度参数。

- **典型用例/场景：**

  - **自动化测试：** 文件上传/下载、存储容量限制、带宽测试。

  - **功能测试：** 测试文件大小显示、上传/下载限制。

- **校验与异常处理建议：**

  - **校验：** 数值范围、单位。

  - **异常：** 生成负数、超出系统最大限制的数值。

#### **10.10 文件路径 (File Path)**

- **说明：** 此数据类型已在**“3.6 联系／通信类”**中详细细化，此处不再重复。

#### **10.11 图片尺寸 (Image Dimensions)**

- **核心生成逻辑/规则：**

  - 生成图片宽度和高度的像素值。

  - 可配置宽度和高度的范围，以及是否保持宽高比。

- **CLI配置参数建议：**

  - `--image_dims.width_min <int>`: 最小宽度（默认`100`）。

  - `--image_dims.width_max <int>`: 最大宽度（默认`1920`）。

  - `--image_dims.height_min <int>`: 最小高度（默认`100`）。

  - `--image_dims.height_max <int>`: 最大高度（默认`1080`）。

  - `--image_dims.aspect_ratio <float>`: (可选) 指定宽高比（如`1.77`表示16:9）。如果指定，则高度会根据宽度和宽高比计算。

- **高级定制/关联性考虑：**

  - **与图片文件（模拟）关联：** 可作为模拟图片文件的元数据。

  - **边界值：** 生成0x0、1x1、极大尺寸的图片尺寸。

- **典型用例/场景：**

  - **自动化测试：** 图片处理服务（缩放、裁剪）、图片库管理、响应式设计。

  - **功能测试：** 测试图片尺寸显示、图片上传的尺寸限制。

- **校验与异常处理建议：**

  - **校验：** 宽度和高度为正整数。

  - **异常：** 生成负数、零、过大的尺寸。

### **枚举／状态码 (Enum / Status Code)**

#### **11.1 HTTP 状态码 (HTTP Status Code)**

- **核心生成逻辑/规则：**

  - 从HTTP标准状态码（1xx信息、2xx成功、3xx重定向、4xx客户端错误、5xx服务器错误）中随机选择。

  - 可配置生成特定范围或特定类别的状态码。

- **CLI配置参数建议：**

  - `--http_status.category <1XX|2XX|3XX|4XX|5XX|ALL>`: 指定状态码类别（默认`ALL`）。

  - `--http_status.code <code1,code2,...>`: 指定具体的HTTP状态码列表（如`200,404,500`），不指定则在类别内随机。

  - `--http_status.valid <true|false>`: 是否保证生成标准HTTP状态码（默认`true`）。设置为`false`可生成非标准状态码用于异常测试。

- **高级定制/关联性考虑：**

  - **与URL/API响应关联：** 在模拟API响应数据时，可生成对应的HTTP状态码。

  - **错误场景模拟：** 集中生成4xx和5xx状态码，用于测试错误处理和监控。

- **典型用例/场景：**

  - **自动化测试：** API接口测试（验证响应状态码）、错误处理机制、日志分析。

  - **功能测试：** 测试页面跳转、错误提示、权限控制（如403）。

- **校验与异常处理建议：**

  - **校验：** 确保为整数，且在100-599的HTTP状态码范围内。

  - **异常：** 生成超出范围、非整数的状态码。

#### **11.2 自定义业务状态码 (Custom Business Status Code)**

- **核心生成逻辑/规则：**

  - 生成用户自定义的、符合特定范围或格式的业务状态码。

  - 通常为整数或字符串。

  - 可配置状态码的范围、前缀、长度。

- **CLI配置参数建议：**

  - `--biz_status.type <INTEGER|STRING>`: 指定状态码类型（默认`INTEGER`）。

  - `--biz_status.min <int>`: (仅对整数) 最小值（如`1000`）。

  - `--biz_status.max <int>`: (仅对整数) 最大值（如`1999`）。

  - `--biz_status.prefix <string>`: (仅对字符串) 自定义前缀（如`ERR_`）。

  - `--biz_status.length <int>`: (仅对字符串) 长度。

  - `--biz_status.codes <code1,code2,...>`: 指定具体的自定义状态码列表。

  - `--biz_status.valid <true|false>`: 是否保证生成在预设列表或范围内（默认`true`）。

- **高级定制/关联性考虑：**

  - **与业务场景关联：** 模拟特定业务流程中的成功、失败、处理中等状态。

  - **错误码分类：** 可配置生成不同错误类别（如用户级错误、系统级错误）的状态码。

- **典型用例/场景：**

  - **自动化测试：** 业务逻辑测试、错误码处理、消息通知。

  - **功能测试：** 测试系统对不同业务状态的响应和显示。

- **校验与异常处理建议：**

  - **校验：** 确保在指定范围或预设列表中。

  - **异常：** 生成超出范围、格式不符的状态码。

#### **11.3 布尔型 (Boolean)**

- **核心生成逻辑/规则：**

  - 生成`true`/`false`、`0`/`1`、`Y`/`N`等布尔表示。

  - 可配置不同值的生成比例。

- **CLI配置参数建议：**

  - `--boolean.format <TRUE_FALSE|ZERO_ONE|Y_N|CUSTOM>`: 指定布尔值格式（默认`TRUE_FALSE`）。

  - `--boolean.true_ratio <float>`: `true`或`1`或`Y`的生成比例（0-1，默认`0.5`）。

  - `--boolean.custom_true <string>`: (仅对`CUSTOM`) 自定义真值字符串。

  - `--boolean.custom_false <string>`: (仅对`CUSTOM`) 自定义假值字符串。

- **高级定制/关联性考虑：**

  - **与业务逻辑关联：** 模拟用户是否同意协议、是否启用某个功能等。

- **典型用例/场景：**

  - **自动化测试：** 配置项、开关功能、权限控制。

  - **功能测试：** 测试复选框、开关等UI元素的行为。

- **校验与异常处理建议：**

  - **校验：** 确保在预设的布尔值表示中。

  - **异常：** 无。

#### **11.4 枚举 (Enum)**

- **核心生成逻辑/规则：**

  - 从用户提供的枚举值列表中随机选择一个值。

  - 可配置不同枚举值的生成权重。

- **CLI配置参数建议：**

  - `--enum.values <value1,value2,value3,...>`: 指定枚举值列表（如`PENDING,PROCESSING,COMPLETED`）。

  - `--enum.weights <value1:weight1,value2:weight2,...>`: (可选) 指定各枚举值的权重。

  - `--enum.file <path>`: 指定自定义枚举值列表文件路径。

  - `--enum.valid <true|false>`: 是否保证生成在预设列表中（默认`true`）。设置为`false`可生成列表外的枚举值用于异常测试。

- **高级定制/关联性考虑：**

  - **与业务状态关联：** 模拟订单状态、会员等级、商品品类等。

- **典型用例/场景：**

  - **自动化测试：** 业务流程状态流转、枚举字段校验。

  - **功能测试：** 测试下拉框、单选按钮等UI元素的选择、基于枚举值的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 确保生成的枚举值在预设列表中。

  - **异常：** 生成不在预设列表中的枚举值。

#### **11.5 优先级 (Priority)**

- **核心生成逻辑/规则：**

  - 从预设的优先级列表（如`高/中/低`、`P0/P1/P2`）或数值化优先级（1-5）中随机选择。

  - 可配置不同优先级的生成权重。

- **CLI配置参数建议：**

  - `--priority.type <TEXT|NUMERIC|CUSTOM>`: 指定优先级类型（文本、数值或自定义，默认`TEXT`）。

  - `--priority.values <value1,value2,...>`: (仅对文本/自定义) 指定优先级列表（如`HIGH,MEDIUM,LOW`）。

  - `--priority.min <int>`: (仅对数值) 最小值（默认`1`）。

  - `--priority.max <int>`: (仅对数值) 最大值（默认`5`）。

  - `--priority.weights <value1:weight1,value2:weight2,...>`: (可选) 指定各优先级的权重。

- **高级定制/关联性考虑：**

  - **与任务/缺陷关联：** 模拟任务或缺陷的优先级。

- **典型用例/场景：**

  - **自动化测试：** 任务管理系统、缺陷管理系统、工单系统。

  - **功能测试：** 测试优先级选择、基于优先级的排序和处理逻辑。

- **校验与异常处理建议：**

  - **校验：** 确保在预设列表或数值范围内。

  - **异常：** 生成不在预设列表或范围内的优先级。

### **特殊场景数据（通用） (Special Scenario Data - General)**

#### **12.1 空值/Null值 (Empty / Null Values)**

- **核心生成逻辑/规则：**

  - 针对任何可生成的数据类型，生成其对应的“空”表示：

    - **空字符串：** `""`

    - **Null值：** `null` (Java对象引用、JSON中的`null`)

    - **Undefined值：** (仅对特定输出格式，如JavaScript/JSON中模拟)

    - **空数组：** `[]`

    - **空对象：** `{}`

  - 可配置生成这些空值的频率。

- **CLI配置参数建议：**

  - `--empty.type <STRING|NULL|UNDEFINED|ARRAY|OBJECT|ANY>`: 指定空值类型（默认`ANY`）。

  - `--empty.frequency <float>`: 生成空值的频率（0-1，默认`0.1`）。

  - `--empty.for_field <field_name>`: (可选) 仅对指定字段生成空值。

  - `--empty.include_null_string <true|false>`: 是否生成字符串`"null"`（默认`false`）。

- **高级定制/关联性考虑：**

  - **与必填/可选字段关联：** 针对非必填字段，可提高生成空值的频率。

  - **数据库Null值：** 在生成SQL `INSERT`语句时，将对应的字段值设置为`NULL`。

- **典型用例/场景：**

  - **自动化测试：** 数据库字段的Nullability测试、API接口参数校验（必填/可选）、表单提交（空值处理）。

  - **功能测试：** 测试输入框为空时的提示信息、系统对空值的默认处理、报表数据缺失情况。

- **校验与异常处理建议：**

  - **校验：** 确保生成的值确实是其“空”表示。

  - **异常：** 无。

#### **12.2 边界值/极端值 (Boundary / Extreme Values)**

- **核心生成逻辑/规则：**

  - 针对数值型数据：生成最小值、最大值、接近最小值/最大值的值、零、负零。

  - 针对字符串型数据：生成空字符串、单字符、最大长度字符串、接近最大长度字符串。

  - 针对日期时间型数据：生成闰年边界、世纪末、时区边界、未来/过去极端日期。

  - 可配置生成这些边界值的频率。

- **CLI配置参数建议：**

  - `--boundary.type <NUMERIC|STRING|DATETIME|ALL>`: 指定边界值类型（默认`ALL`）。

  - `--boundary.frequency <float>`: 生成边界值的频率（0-1，默认`0.05`）。

  - `--boundary.for_field <field_name>`: (可选) 仅对指定字段生成边界值。

  - `--boundary.numeric.near_boundary_offset <float>`: (仅对数值) 接近边界值的偏移量（如`1`或`0.01`）。

  - `--boundary.string.max_length <int>`: (仅对字符串) 指定最大长度。

  - `--boundary.datetime.extreme_past <YYYY-MM-DD>`: (仅对日期时间) 极端过去日期。

  - `--boundary.datetime.extreme_future <YYYY-MM-DD>`: (仅对日期时间) 极端未来日期。

- **高级定制/关联性考虑：**

  - **与数据类型生成器结合：** 当生成特定数据类型（如`integer`、`decimal`、`longtext`、`date`）时，可根据此模块的配置，在生成范围内优先选择或插入边界值。

- **典型用例/场景：**

  - **自动化测试：** 系统容量测试、数据类型溢出测试、数据库字段长度限制、日期时间计算的边界条件。

  - **功能测试：** 测试输入框的限制、错误提示、边界条件下的业务逻辑。

- **校验与异常处理建议：**

  - **校验：** 确保生成的值确实是边界值或其附近的值。

  - **异常：** 无。

#### **12.3 非法/异常数据 (Invalid / Exception Data)**

- **核心生成逻辑/规则：**

  - **格式错误：** 例如，手机号位数不对、邮箱格式错误、身份证号校验位错误。

  - **超出范围：** 例如，年龄为负数、日期超出有效范围（如2月30日）。

  - **长文本/超长文本：** 模拟用户输入超长文本的情况，测试字段长度限制和性能。

  - **非法字符：** 包含控制字符、不可打印字符、特殊编码字符。

  - **类型不匹配：** 字段值与预期数据类型不符（如数字字段传入字符串）。

  - 可配置生成这些非法数据的频率。

- **CLI配置参数建议：**

  - `--invalid.type <FORMAT_ERROR|OUT_OF_RANGE|OVERLENGTH|ILLEGAL_CHARS|TYPE_MISMATCH|ALL>`: 指定非法数据类型（默认`ALL`）。

  - `--invalid.frequency <float>`: 生成非法数据的频率（0-1，默认`0.05`）。

  - `--invalid.for_field <field_name>`: (可选) 仅对指定字段生成非法数据。

  - `--invalid.overlength_multiplier <float>`: (仅对超长文本) 超过最大长度的倍数（如`1.2`表示最大长度的1.2倍）。

  - `--invalid.illegal_chars_set <string>`: (仅对非法字符) 指定要插入的非法字符集。

- **高级定制/关联性考虑：**

  - **与数据类型生成器结合：** 当生成特定数据类型时，可根据此模块的配置，故意生成不符合其校验规则的数据。

- **典型用例/场景：**

  - **自动化测试：** 输入校验、错误处理、异常日志记录、系统崩溃测试。

  - **功能测试：** 测试输入框的错误提示、系统对异常输入的容错性。

- **校验与异常处理建议：**

  - **校验：** 确保生成的值确实是“非法”的，即不符合其预期格式或范围。

  - **异常：** 无。

#### **12.4 可自定义长度格式的业务编号 (Customizable Length Business ID)**

- **核心生成逻辑/规则：**

  - 生成由字母、数字、特殊字符组成的自定义格式业务编号。

  - 用户可定义编号的固定长度或长度范围，以及字符集。

  - 可配置前缀、后缀、分隔符。

- **CLI配置参数建议：**

  - `--custom_id.length <int>`: 指定固定长度。

  - `--custom_id.length_range <min_len,max_len>`: 指定长度范围（与`length`二选一）。

  - `--custom_id.chars <ALPHANUMERIC|ALPHANUMERIC_SPECIAL|NUMERIC|CUSTOM>`: 字符集。

  - `--custom_id.custom_chars <string>`: 自定义字符集。

  - `--custom_id.prefix <string>`: 自定义前缀。

  - `--custom_id.suffix <string>`: 自定义后缀。

  - `--custom_id.delimiter <string>`: (可选) 插入分隔符（如`-`）。

  - `--custom_id.delimiter_interval <int>`: (可选) 分隔符插入间隔。

  - `--custom_id.unique <true|false>`: 是否在生成批次中保证唯一性（默认`true`）。

- **高级定制/关联性考虑：**

  - **与业务场景关联：** 用于模拟各种自定义的业务ID，如流水号、批次号、会员ID等。

- **典型用例/场景：**

  - **自动化测试：** 各种业务ID的生成、唯一性校验。

  - **功能测试：** 测试自定义编号的输入、显示、唯一性。

- **校验与异常处理建议：**

  - **校验：** 长度、字符集、唯一性。

  - **异常：** 生成不符合长度、包含非法字符的编号。

#### **12.5 重复数据 (Duplicate Data)**

- **核心生成逻辑/规则：**

  - 在生成大量数据时，故意生成指定字段或整条记录的重复数据。

  - 可配置重复的频率或重复的次数。

- **CLI配置参数建议：**

  - `--duplicate.frequency <float>`: 生成重复数据的频率（0-1，默认`0.05`）。

  - `--duplicate.times <int>`: (可选) 每条数据重复的次数（默认`2`）。

  - `--duplicate.for_field <field_name>`: (可选) 仅对指定字段生成重复值。

  - `--duplicate.record_level <true|false>`: 是否重复整条记录（默认`false`，即只重复指定字段）。

- **高级定制/关联性考虑：**

  - **与唯一性约束测试：** 专门用于测试数据库的唯一索引、系统唯一性校验功能。

  - **数据去重测试：** 模拟数据清洗、ETL流程中的去重场景。

- **典型用例/场景：**

  - **自动化测试：** 数据库唯一性约束、用户注册（重复用户名/手机号）、数据导入去重。

  - **功能测试：** 测试系统对重复数据的处理（提示、拒绝、覆盖）。

- **校验与异常处理建议：**

  - **校验：** 确保生成了指定频率的重复数据。

  - **异常：** 无。

#### **12.6 排序数据 (Sorted Data)**

- **核心生成逻辑/规则：**

  - 生成已排序（升序/降序）或逆序的数据。

  - 可针对数值型、日期时间型、字符串型字段进行排序。

- **CLI配置参数建议：**

  - `--sort.order <ASC|DESC|REVERSE>`: 指定排序顺序（升序、降序、逆序，默认`ASC`）。

  - `--sort.for_field <field_name>`: 指定要排序的字段名称。

  - `--sort.type <NUMERIC|DATETIME|STRING>`: 指定字段的数据类型，以便正确排序。

- **高级定制/关联性考虑：**

  - **与分页/排序功能测试：** 专门用于测试系统对排序功能的支持。

  - **性能测试：** 评估排序算法在已排序数据、逆序数据等极端情况下的性能。

- **典型用例/场景：**

  - **自动化测试：** 数据导入、列表排序、查询优化。

  - **功能测试：** 测试列表排序功能、分页显示。

- **校验与异常处理建议：**

  - **校验：** 确保生成的数据按照指定字段和顺序进行了排序。

  - **异常：** 无。

#### **12.7 并发/竞争数据 (Concurrent / Contention Data)**

- **核心生成逻辑/规则：**

  - 模拟多用户同时操作时可能产生的数据，例如在分布式系统中，可能需要模拟时间戳的微小差异、ID的竞争生成。

  - 这通常不是直接生成某个“值”，而是通过控制多个生成器实例的协作来模拟。

- **CLI配置参数建议：**

  - `--concurrent.threads <int>`: 模拟并发生成数据的线程数（默认`1`）。

  - `--concurrent.delay_ms <int>`: 每个生成操作之间的随机延迟（毫秒，默认`0`）。

  - `--concurrent.id_offset <int>`: (仅对ID生成) 模拟ID生成时的微小偏移量或跳号。

  - `--concurrent.timestamp_jitter_ms <int>`: (仅对时间戳) 模拟时间戳的微小抖动（毫秒）。

- **高级定制/关联性考虑：**

  - **与全局唯一ID关联：** 模拟Snowflake ID在分布式环境下的生成，测试ID冲突或乱序。

  - **与业务单据号关联：** 模拟高并发下的订单号生成，测试序列号的唯一性和连续性。

- **典型用例/场景：**

  - **自动化测试：** 分布式系统测试、高并发场景下的数据一致性、锁机制、事务处理。

  - **功能测试：** 模拟多用户同时操作，观察系统行为。

- **校验与异常处理建议：**

  - **校验：** 确保生成的数据符合并发场景的特征（如时间戳的微小差异）。

  - **异常：** 无。
