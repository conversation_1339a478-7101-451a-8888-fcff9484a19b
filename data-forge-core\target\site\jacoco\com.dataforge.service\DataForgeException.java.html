<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataForgeException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.service</a> &gt; <span class="el_source">DataForgeException.java</span></div><h1>DataForgeException.java</h1><pre class="source lang-java linenums">package com.dataforge.service;

/**
 * DataForge服务异常类。
 * 
 * &lt;p&gt;
 * 用于表示DataForge服务层发生的异常情况。
 * 继承自RuntimeException，使得调用方可以选择是否捕获处理。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class DataForgeException extends RuntimeException {

    /**
     * 序列化版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数。
     * 
     * @param message 异常消息
     */
    public DataForgeException(String message) {
<span class="nc" id="L26">        super(message);</span>
<span class="nc" id="L27">    }</span>

    /**
     * 构造函数。
     * 
     * @param message 异常消息
     * @param cause   原因异常
     */
    public DataForgeException(String message, Throwable cause) {
<span class="nc" id="L36">        super(message, cause);</span>
<span class="nc" id="L37">    }</span>

    /**
     * 构造函数。
     * 
     * @param cause 原因异常
     */
    public DataForgeException(Throwable cause) {
<span class="nc" id="L45">        super(cause);</span>
<span class="nc" id="L46">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.************</span></div></body></html>