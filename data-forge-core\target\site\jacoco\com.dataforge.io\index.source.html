<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.dataforge.io</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <span class="el_package">com.dataforge.io</span></div><h1>com.dataforge.io</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,309 of 1,309</td><td class="ctr2">0%</td><td class="bar">142 of 142</td><td class="ctr2">0%</td><td class="ctr1">119</td><td class="ctr2">119</td><td class="ctr1">321</td><td class="ctr2">321</td><td class="ctr1">48</td><td class="ctr2">48</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a4"><a href="SqlOutputStrategy.java.html" class="el_source">SqlOutputStrategy.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="730" alt="730"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="80" alt="80"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">56</td><td class="ctr2" id="g0">56</td><td class="ctr1" id="h0">171</td><td class="ctr2" id="i0">171</td><td class="ctr1" id="j0">16</td><td class="ctr2" id="k0">16</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CsvOutputStrategy.java.html" class="el_source">CsvOutputStrategy.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="270" alt="270"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="34" alt="34"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">27</td><td class="ctr2" id="g1">27</td><td class="ctr1" id="h2">67</td><td class="ctr2" id="i2">67</td><td class="ctr1" id="j2">10</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="ConsoleOutputStrategy.java.html" class="el_source">ConsoleOutputStrategy.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="262" alt="262"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">25</td><td class="ctr2" id="g2">25</td><td class="ctr1" id="h1">69</td><td class="ctr2" id="i1">69</td><td class="ctr1" id="j1">14</td><td class="ctr2" id="k1">14</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="OutputStrategy.java.html" class="el_source">OutputStrategy.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="34" alt="34"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">5</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a2"><a href="OutputException.java.html" class="el_source">OutputException.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j4">3</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>