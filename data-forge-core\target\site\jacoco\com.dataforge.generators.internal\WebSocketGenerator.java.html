<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebSocketGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">WebSocketGenerator.java</span></div><h1>WebSocketGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * WebSocket连接生成器
 * 
 * &lt;p&gt;
 * 支持生成WebSocket连接相关的配置信息，包括WebSocket URL、
 * 连接参数、协议版本、扩展配置等，用于WebSocket应用测试、
 * 实时通信功能验证、性能测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;format: 输出格式 (URL|JSON|CONFIG) 默认: URL&lt;/li&gt;
 * &lt;li&gt;protocol: 协议类型 (WS|WSS|RANDOM) 默认: WS&lt;/li&gt;
 * &lt;li&gt;host: 主机地址（如果不指定则随机生成）&lt;/li&gt;
 * &lt;li&gt;port: 端口号 默认: 8080&lt;/li&gt;
 * &lt;li&gt;path: 路径 默认: /websocket&lt;/li&gt;
 * &lt;li&gt;subprotocol: 子协议 (NONE|CHAT|ECHO|CUSTOM) 默认: NONE&lt;/li&gt;
 * &lt;li&gt;version: WebSocket版本 默认: 13&lt;/li&gt;
 * &lt;li&gt;include_query: 是否包含查询参数 默认: false&lt;/li&gt;
 * &lt;li&gt;include_headers: 是否包含连接头信息 默认: false&lt;/li&gt;
 * &lt;li&gt;timeout: 连接超时时间（秒）默认: 30&lt;/li&gt;
 * &lt;li&gt;heartbeat: 心跳间隔（秒）默认: 60&lt;/li&gt;
 * &lt;li&gt;compression: 是否启用压缩 默认: false&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L40">public class WebSocketGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L42">    private static final Logger logger = LoggerFactory.getLogger(WebSocketGenerator.class);</span>
<span class="fc" id="L43">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 输出格式枚举
<span class="fc" id="L46">    public enum OutputFormat {</span>
<span class="fc" id="L47">        URL(&quot;WebSocket URL格式&quot;),</span>
<span class="fc" id="L48">        JSON(&quot;JSON配置格式&quot;),</span>
<span class="fc" id="L49">        CONFIG(&quot;配置对象格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L53">        OutputFormat(String description) {</span>
<span class="fc" id="L54">            this.description = description;</span>
<span class="fc" id="L55">        }</span>
        
        public String getDescription() {
<span class="nc" id="L58">            return description;</span>
        }
    }
    
    // 协议类型枚举
<span class="fc" id="L63">    public enum Protocol {</span>
<span class="fc" id="L64">        WS(&quot;WebSocket协议&quot;),</span>
<span class="fc" id="L65">        WSS(&quot;安全WebSocket协议&quot;),</span>
<span class="fc" id="L66">        RANDOM(&quot;随机协议&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L70">        Protocol(String description) {</span>
<span class="fc" id="L71">            this.description = description;</span>
<span class="fc" id="L72">        }</span>
        
        public String getDescription() {
<span class="nc" id="L75">            return description;</span>
        }
    }
    
    // 子协议枚举
<span class="fc" id="L80">    public enum SubProtocol {</span>
<span class="fc" id="L81">        NONE(&quot;无子协议&quot;),</span>
<span class="fc" id="L82">        CHAT(&quot;聊天协议&quot;),</span>
<span class="fc" id="L83">        ECHO(&quot;回声协议&quot;),</span>
<span class="fc" id="L84">        CUSTOM(&quot;自定义协议&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L88">        SubProtocol(String description) {</span>
<span class="fc" id="L89">            this.description = description;</span>
<span class="fc" id="L90">        }</span>
        
        public String getDescription() {
<span class="nc" id="L93">            return description;</span>
        }
    }
    
    // 常见主机地址
<span class="fc" id="L98">    private static final List&lt;String&gt; COMMON_HOSTS = Arrays.asList(</span>
        &quot;localhost&quot;, &quot;127.0.0.1&quot;, &quot;0.0.0.0&quot;,
        &quot;ws.example.com&quot;, &quot;api.example.com&quot;, &quot;chat.example.com&quot;,
        &quot;realtime.example.com&quot;, &quot;socket.example.com&quot;
    );
    
    // 常见路径
<span class="fc" id="L105">    private static final List&lt;String&gt; COMMON_PATHS = Arrays.asList(</span>
        &quot;/websocket&quot;, &quot;/ws&quot;, &quot;/socket&quot;, &quot;/chat&quot;, &quot;/realtime&quot;,
        &quot;/api/ws&quot;, &quot;/v1/websocket&quot;, &quot;/stream&quot;, &quot;/events&quot;, &quot;/notifications&quot;
    );
    
    // 常见查询参数
<span class="fc" id="L111">    private static final List&lt;String&gt; COMMON_QUERY_PARAMS = Arrays.asList(</span>
        &quot;token&quot;, &quot;userId&quot;, &quot;sessionId&quot;, &quot;roomId&quot;, &quot;channelId&quot;,
        &quot;version&quot;, &quot;format&quot;, &quot;compression&quot;, &quot;heartbeat&quot;
    );
    
    // WebSocket配置信息类
    public static class WebSocketConfig {
        private final String protocol;
        private final String host;
        private final int port;
        private final String path;
        private final String subprotocol;
        private final int version;
        private final Map&lt;String, String&gt; queryParams;
        private final Map&lt;String, String&gt; headers;
        private final int timeout;
        private final int heartbeat;
        private final boolean compression;
        
        public WebSocketConfig(String protocol, String host, int port, String path,
                             String subprotocol, int version, Map&lt;String, String&gt; queryParams,
                             Map&lt;String, String&gt; headers, int timeout, int heartbeat,
<span class="fc" id="L133">                             boolean compression) {</span>
<span class="fc" id="L134">            this.protocol = protocol;</span>
<span class="fc" id="L135">            this.host = host;</span>
<span class="fc" id="L136">            this.port = port;</span>
<span class="fc" id="L137">            this.path = path;</span>
<span class="fc" id="L138">            this.subprotocol = subprotocol;</span>
<span class="fc" id="L139">            this.version = version;</span>
<span class="fc bfc" id="L140" title="All 2 branches covered.">            this.queryParams = queryParams != null ? queryParams : new HashMap&lt;&gt;();</span>
<span class="pc bpc" id="L141" title="1 of 2 branches missed.">            this.headers = headers != null ? headers : new HashMap&lt;&gt;();</span>
<span class="fc" id="L142">            this.timeout = timeout;</span>
<span class="fc" id="L143">            this.heartbeat = heartbeat;</span>
<span class="fc" id="L144">            this.compression = compression;</span>
<span class="fc" id="L145">        }</span>
        
        // Getters
<span class="fc" id="L148">        public String getProtocol() { return protocol; }</span>
<span class="fc" id="L149">        public String getHost() { return host; }</span>
<span class="fc" id="L150">        public int getPort() { return port; }</span>
<span class="fc" id="L151">        public String getPath() { return path; }</span>
<span class="fc" id="L152">        public String getSubprotocol() { return subprotocol; }</span>
<span class="fc" id="L153">        public int getVersion() { return version; }</span>
<span class="fc" id="L154">        public Map&lt;String, String&gt; getQueryParams() { return queryParams; }</span>
<span class="fc" id="L155">        public Map&lt;String, String&gt; getHeaders() { return headers; }</span>
<span class="fc" id="L156">        public int getTimeout() { return timeout; }</span>
<span class="fc" id="L157">        public int getHeartbeat() { return heartbeat; }</span>
<span class="fc" id="L158">        public boolean isCompression() { return compression; }</span>
    }

    @Override
    public String getType() {
<span class="fc" id="L163">        return &quot;websocket&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L168">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取输出格式
<span class="fc" id="L175">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;URL&quot;);</span>
<span class="fc" id="L176">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 生成WebSocket配置
<span class="fc" id="L179">            WebSocketConfig wsConfig = generateWebSocketConfig(config);</span>
            
            // 格式化输出
<span class="fc" id="L182">            return formatWebSocketConfig(wsConfig, format);</span>
            
<span class="nc" id="L184">        } catch (Exception e) {</span>
<span class="nc" id="L185">            logger.error(&quot;Failed to generate WebSocket configuration&quot;, e);</span>
            // 返回一个默认的WebSocket URL作为fallback
<span class="nc" id="L187">            return &quot;ws://localhost:8080/websocket&quot;;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L196">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="fc" id="L197">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L198">            logger.warn(&quot;Invalid output format: {}, using URL as default&quot;, formatStr);</span>
<span class="fc" id="L199">            return OutputFormat.URL;</span>
        }
    }

    /**
     * 生成WebSocket配置
     */
    private WebSocketConfig generateWebSocketConfig(FieldConfig config) {
        // 获取协议
<span class="fc" id="L208">        String protocolStr = getStringParam(config, &quot;protocol&quot;, &quot;WS&quot;);</span>
<span class="fc" id="L209">        Protocol protocolEnum = parseProtocol(protocolStr);</span>
<span class="pc bpc" id="L210" title="1 of 2 branches missed.">        String protocol = (protocolEnum == Protocol.RANDOM) ? </span>
<span class="pc bnc" id="L211" title="All 2 branches missed.">            (random.nextBoolean() ? &quot;ws&quot; : &quot;wss&quot;) : protocolEnum.name().toLowerCase();</span>
        
        // 获取主机地址
<span class="fc" id="L214">        String host = getStringParam(config, &quot;host&quot;, null);</span>
<span class="fc bfc" id="L215" title="All 2 branches covered.">        if (host == null) {</span>
<span class="fc" id="L216">            host = COMMON_HOSTS.get(random.nextInt(COMMON_HOSTS.size()));</span>
        }
        
        // 获取端口
<span class="fc bfc" id="L220" title="All 2 branches covered.">        int port = getIntParam(config, &quot;port&quot;, protocol.equals(&quot;wss&quot;) ? 443 : 8080);</span>
        
        // 获取路径
<span class="fc" id="L223">        String path = getStringParam(config, &quot;path&quot;, null);</span>
<span class="pc bpc" id="L224" title="1 of 2 branches missed.">        if (path == null) {</span>
<span class="fc" id="L225">            path = COMMON_PATHS.get(random.nextInt(COMMON_PATHS.size()));</span>
        }
        
        // 获取子协议
<span class="fc" id="L229">        String subprotocolStr = getStringParam(config, &quot;subprotocol&quot;, &quot;NONE&quot;);</span>
<span class="fc" id="L230">        SubProtocol subProtocolEnum = parseSubProtocol(subprotocolStr);</span>
<span class="fc" id="L231">        String subprotocol = generateSubProtocol(subProtocolEnum);</span>
        
        // 获取版本
<span class="fc" id="L234">        int version = getIntParam(config, &quot;version&quot;, 13);</span>
        
        // 生成查询参数
<span class="fc" id="L237">        Map&lt;String, String&gt; queryParams = null;</span>
<span class="fc" id="L238">        boolean includeQuery = getBooleanParam(config, &quot;include_query&quot;, false);</span>
<span class="fc bfc" id="L239" title="All 2 branches covered.">        if (includeQuery) {</span>
<span class="fc" id="L240">            queryParams = generateQueryParams();</span>
        }
        
        // 生成连接头信息
<span class="fc" id="L244">        Map&lt;String, String&gt; headers = null;</span>
<span class="fc" id="L245">        boolean includeHeaders = getBooleanParam(config, &quot;include_headers&quot;, false);</span>
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">        if (includeHeaders) {</span>
<span class="nc" id="L247">            headers = generateHeaders();</span>
        }
        
        // 获取其他配置
<span class="fc" id="L251">        int timeout = getIntParam(config, &quot;timeout&quot;, 30);</span>
<span class="fc" id="L252">        int heartbeat = getIntParam(config, &quot;heartbeat&quot;, 60);</span>
<span class="fc" id="L253">        boolean compression = getBooleanParam(config, &quot;compression&quot;, false);</span>
        
<span class="fc" id="L255">        return new WebSocketConfig(protocol, host, port, path, subprotocol, version,</span>
                                 queryParams, headers, timeout, heartbeat, compression);
    }

    /**
     * 解析协议类型
     */
    private Protocol parseProtocol(String protocolStr) {
        try {
<span class="fc" id="L264">            return Protocol.valueOf(protocolStr.toUpperCase());</span>
<span class="nc" id="L265">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L266">            logger.warn(&quot;Invalid protocol: {}, using WS as default&quot;, protocolStr);</span>
<span class="nc" id="L267">            return Protocol.WS;</span>
        }
    }

    /**
     * 解析子协议
     */
    private SubProtocol parseSubProtocol(String subprotocolStr) {
        try {
<span class="fc" id="L276">            return SubProtocol.valueOf(subprotocolStr.toUpperCase());</span>
<span class="nc" id="L277">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L278">            logger.warn(&quot;Invalid subprotocol: {}, using NONE as default&quot;, subprotocolStr);</span>
<span class="nc" id="L279">            return SubProtocol.NONE;</span>
        }
    }

    /**
     * 生成子协议
     */
    private String generateSubProtocol(SubProtocol subProtocol) {
<span class="pc bpc" id="L287" title="3 of 4 branches missed.">        switch (subProtocol) {</span>
            case CHAT:
<span class="nc" id="L289">                return &quot;chat&quot;;</span>
            case ECHO:
<span class="nc" id="L291">                return &quot;echo&quot;;</span>
            case CUSTOM:
<span class="nc" id="L293">                return &quot;custom-protocol-v&quot; + (1 + random.nextInt(5));</span>
            case NONE:
            default:
<span class="fc" id="L296">                return null;</span>
        }
    }

    /**
     * 生成查询参数
     */
    private Map&lt;String, String&gt; generateQueryParams() {
<span class="fc" id="L304">        Map&lt;String, String&gt; params = new HashMap&lt;&gt;();</span>
<span class="fc" id="L305">        int paramCount = 1 + random.nextInt(4); // 1-4个参数</span>
        
<span class="fc" id="L307">        List&lt;String&gt; availableParams = new ArrayList&lt;&gt;(COMMON_QUERY_PARAMS);</span>
<span class="fc" id="L308">        Collections.shuffle(availableParams);</span>
        
<span class="fc bfc" id="L310" title="All 2 branches covered.">        for (int i = 0; i &lt; Math.min(paramCount, availableParams.size()); i++) {</span>
<span class="fc" id="L311">            String key = availableParams.get(i);</span>
<span class="fc" id="L312">            String value = generateQueryParamValue(key);</span>
<span class="fc" id="L313">            params.put(key, value);</span>
        }
        
<span class="fc" id="L316">        return params;</span>
    }

    /**
     * 生成查询参数值
     */
    private String generateQueryParamValue(String key) {
<span class="pc bpc" id="L323" title="6 of 10 branches missed.">        switch (key) {</span>
            case &quot;token&quot;:
<span class="fc" id="L325">                return &quot;eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.&quot; + generateRandomString(32);</span>
            case &quot;userId&quot;:
<span class="nc" id="L327">                return String.valueOf(1000 + random.nextInt(9000));</span>
            case &quot;sessionId&quot;:
<span class="fc" id="L329">                return UUID.randomUUID().toString();</span>
            case &quot;roomId&quot;:
<span class="fc" id="L331">                return &quot;room_&quot; + (1 + random.nextInt(100));</span>
            case &quot;channelId&quot;:
<span class="nc" id="L333">                return &quot;channel_&quot; + generateRandomString(8);</span>
            case &quot;version&quot;:
<span class="nc" id="L335">                return &quot;v&quot; + (1 + random.nextInt(3));</span>
            case &quot;format&quot;:
<span class="pc bpc" id="L337" title="1 of 2 branches missed.">                return random.nextBoolean() ? &quot;json&quot; : &quot;binary&quot;;</span>
            case &quot;compression&quot;:
<span class="nc bnc" id="L339" title="All 2 branches missed.">                return random.nextBoolean() ? &quot;gzip&quot; : &quot;none&quot;;</span>
            case &quot;heartbeat&quot;:
<span class="nc" id="L341">                return String.valueOf(30 + random.nextInt(120));</span>
            default:
<span class="nc" id="L343">                return generateRandomString(8);</span>
        }
    }

    /**
     * 生成连接头信息
     */
    private Map&lt;String, String&gt; generateHeaders() {
<span class="nc" id="L351">        Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();</span>
        
<span class="nc" id="L353">        headers.put(&quot;Origin&quot;, &quot;https://example.com&quot;);</span>
<span class="nc" id="L354">        headers.put(&quot;User-Agent&quot;, &quot;DataForge-WebSocket-Client/1.0&quot;);</span>
        
<span class="nc bnc" id="L356" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L357">            headers.put(&quot;Authorization&quot;, &quot;Bearer &quot; + generateRandomString(32));</span>
        }
        
<span class="nc bnc" id="L360" title="All 2 branches missed.">        if (random.nextBoolean()) {</span>
<span class="nc" id="L361">            headers.put(&quot;X-Client-Version&quot;, &quot;1.&quot; + random.nextInt(10) + &quot;.&quot; + random.nextInt(10));</span>
        }
        
<span class="nc" id="L364">        return headers;</span>
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
<span class="fc" id="L371">        String chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;;</span>
<span class="fc" id="L372">        StringBuilder sb = new StringBuilder();</span>
        
<span class="fc bfc" id="L374" title="All 2 branches covered.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="fc" id="L375">            sb.append(chars.charAt(random.nextInt(chars.length())));</span>
        }
        
<span class="fc" id="L378">        return sb.toString();</span>
    }

    /**
     * 格式化WebSocket配置
     */
    private String formatWebSocketConfig(WebSocketConfig config, OutputFormat format) {
<span class="pc bpc" id="L385" title="1 of 4 branches missed.">        switch (format) {</span>
            case URL:
<span class="fc" id="L387">                return formatAsUrl(config);</span>
            case JSON:
<span class="fc" id="L389">                return formatAsJson(config);</span>
            case CONFIG:
<span class="fc" id="L391">                return formatAsConfig(config);</span>
            default:
<span class="nc" id="L393">                return formatAsUrl(config);</span>
        }
    }

    /**
     * 格式化为URL格式
     */
    private String formatAsUrl(WebSocketConfig config) {
<span class="fc" id="L401">        StringBuilder url = new StringBuilder();</span>
<span class="fc" id="L402">        url.append(config.getProtocol()).append(&quot;://&quot;);</span>
<span class="fc" id="L403">        url.append(config.getHost());</span>
        
        // 只有在非默认端口时才添加端口号
<span class="pc bpc" id="L406" title="1 of 4 branches missed.">        boolean isDefaultPort = (config.getProtocol().equals(&quot;ws&quot;) &amp;&amp; config.getPort() == 80) ||</span>
<span class="pc bpc" id="L407" title="1 of 4 branches missed.">                               (config.getProtocol().equals(&quot;wss&quot;) &amp;&amp; config.getPort() == 443);</span>
<span class="fc bfc" id="L408" title="All 2 branches covered.">        if (!isDefaultPort) {</span>
<span class="fc" id="L409">            url.append(&quot;:&quot;).append(config.getPort());</span>
        }
        
<span class="fc" id="L412">        url.append(config.getPath());</span>
        
        // 添加查询参数
<span class="pc bpc" id="L415" title="1 of 4 branches missed.">        if (config.getQueryParams() != null &amp;&amp; !config.getQueryParams().isEmpty()) {</span>
<span class="fc" id="L416">            url.append(&quot;?&quot;);</span>
<span class="fc" id="L417">            boolean first = true;</span>
<span class="fc bfc" id="L418" title="All 2 branches covered.">            for (Map.Entry&lt;String, String&gt; entry : config.getQueryParams().entrySet()) {</span>
<span class="fc bfc" id="L419" title="All 2 branches covered.">                if (!first) url.append(&quot;&amp;&quot;);</span>
<span class="fc" id="L420">                url.append(entry.getKey()).append(&quot;=&quot;).append(entry.getValue());</span>
<span class="fc" id="L421">                first = false;</span>
<span class="fc" id="L422">            }</span>
        }
        
<span class="fc" id="L425">        return url.toString();</span>
    }

    /**
     * 格式化为JSON格式
     */
    private String formatAsJson(WebSocketConfig config) {
<span class="fc" id="L432">        StringBuilder json = new StringBuilder(&quot;{&quot;);</span>
<span class="fc" id="L433">        json.append(&quot;\&quot;url\&quot;:\&quot;&quot;).append(formatAsUrl(config)).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L434">        json.append(&quot;\&quot;protocol\&quot;:\&quot;&quot;).append(config.getProtocol()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L435">        json.append(&quot;\&quot;host\&quot;:\&quot;&quot;).append(config.getHost()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L436">        json.append(&quot;\&quot;port\&quot;:&quot;).append(config.getPort()).append(&quot;,&quot;);</span>
<span class="fc" id="L437">        json.append(&quot;\&quot;path\&quot;:\&quot;&quot;).append(config.getPath()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L438">        json.append(&quot;\&quot;version\&quot;:&quot;).append(config.getVersion()).append(&quot;,&quot;);</span>
<span class="fc" id="L439">        json.append(&quot;\&quot;timeout\&quot;:&quot;).append(config.getTimeout()).append(&quot;,&quot;);</span>
<span class="fc" id="L440">        json.append(&quot;\&quot;heartbeat\&quot;:&quot;).append(config.getHeartbeat()).append(&quot;,&quot;);</span>
<span class="fc" id="L441">        json.append(&quot;\&quot;compression\&quot;:&quot;).append(config.isCompression());</span>
        
<span class="pc bpc" id="L443" title="1 of 2 branches missed.">        if (config.getSubprotocol() != null) {</span>
<span class="nc" id="L444">            json.append(&quot;,\&quot;subprotocol\&quot;:\&quot;&quot;).append(config.getSubprotocol()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L447" title="2 of 4 branches missed.">        if (config.getHeaders() != null &amp;&amp; !config.getHeaders().isEmpty()) {</span>
<span class="nc" id="L448">            json.append(&quot;,\&quot;headers\&quot;:{&quot;);</span>
<span class="nc" id="L449">            boolean first = true;</span>
<span class="nc bnc" id="L450" title="All 2 branches missed.">            for (Map.Entry&lt;String, String&gt; entry : config.getHeaders().entrySet()) {</span>
<span class="nc bnc" id="L451" title="All 2 branches missed.">                if (!first) json.append(&quot;,&quot;);</span>
<span class="nc" id="L452">                json.append(&quot;\&quot;&quot;).append(entry.getKey()).append(&quot;\&quot;:\&quot;&quot;).append(entry.getValue()).append(&quot;\&quot;&quot;);</span>
<span class="nc" id="L453">                first = false;</span>
<span class="nc" id="L454">            }</span>
<span class="nc" id="L455">            json.append(&quot;}&quot;);</span>
        }
        
<span class="fc" id="L458">        json.append(&quot;}&quot;);</span>
<span class="fc" id="L459">        return json.toString();</span>
    }

    /**
     * 格式化为配置格式
     */
    private String formatAsConfig(WebSocketConfig config) {
<span class="fc" id="L466">        StringBuilder configStr = new StringBuilder();</span>
<span class="fc" id="L467">        configStr.append(&quot;WebSocket Configuration:\n&quot;);</span>
<span class="fc" id="L468">        configStr.append(&quot;  URL: &quot;).append(formatAsUrl(config)).append(&quot;\n&quot;);</span>
<span class="fc" id="L469">        configStr.append(&quot;  Protocol: &quot;).append(config.getProtocol().toUpperCase()).append(&quot;\n&quot;);</span>
<span class="fc" id="L470">        configStr.append(&quot;  Host: &quot;).append(config.getHost()).append(&quot;\n&quot;);</span>
<span class="fc" id="L471">        configStr.append(&quot;  Port: &quot;).append(config.getPort()).append(&quot;\n&quot;);</span>
<span class="fc" id="L472">        configStr.append(&quot;  Path: &quot;).append(config.getPath()).append(&quot;\n&quot;);</span>
<span class="fc" id="L473">        configStr.append(&quot;  Version: &quot;).append(config.getVersion()).append(&quot;\n&quot;);</span>
<span class="fc" id="L474">        configStr.append(&quot;  Timeout: &quot;).append(config.getTimeout()).append(&quot;s\n&quot;);</span>
<span class="fc" id="L475">        configStr.append(&quot;  Heartbeat: &quot;).append(config.getHeartbeat()).append(&quot;s\n&quot;);</span>
<span class="pc bpc" id="L476" title="1 of 2 branches missed.">        configStr.append(&quot;  Compression: &quot;).append(config.isCompression() ? &quot;Enabled&quot; : &quot;Disabled&quot;);</span>
        
<span class="pc bpc" id="L478" title="1 of 2 branches missed.">        if (config.getSubprotocol() != null) {</span>
<span class="nc" id="L479">            configStr.append(&quot;\n  Subprotocol: &quot;).append(config.getSubprotocol());</span>
        }
        
<span class="fc" id="L482">        return configStr.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>