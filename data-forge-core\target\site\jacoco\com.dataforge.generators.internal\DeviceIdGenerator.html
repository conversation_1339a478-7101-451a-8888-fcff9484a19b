<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DeviceIdGenerator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_class">DeviceIdGenerator</span></div><h1>DeviceIdGenerator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">128 of 615</td><td class="ctr2">79%</td><td class="bar">27 of 45</td><td class="ctr2">40%</td><td class="ctr1">19</td><td class="ctr2">36</td><td class="ctr1">24</td><td class="ctr2">88</td><td class="ctr1">0</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a0"><a href="DeviceIdGenerator.java.html#L291" class="el_method">applyOutputFormat(String, DeviceIdGenerator.OutputFormat, DeviceIdGenerator.DeviceIdType)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="51" alt="51"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c11">8%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">6%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h0">8</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="DeviceIdGenerator.java.html#L229" class="el_method">generateImsi(FieldConfig, boolean)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="38" alt="38"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="77" alt="77"/></td><td class="ctr2" id="c8">66%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="9" alt="9"/></td><td class="ctr2" id="e2">56%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="DeviceIdGenerator.java.html#L124" class="el_method">generate(FieldConfig, DataForgeContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="56" alt="56"/></td><td class="ctr2" id="c7">80%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">80%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="DeviceIdGenerator.java.html#L200" class="el_method">generateImei(FieldConfig, boolean)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="46" alt="46"/></td><td class="ctr2" id="c6">80%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">33%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="DeviceIdGenerator.java.html#L169" class="el_method">parseDeviceIdType(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">36%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="DeviceIdGenerator.java.html#L181" class="el_method">parseOutputFormat(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">36%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a11"><a href="DeviceIdGenerator.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="251" alt="251"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="DeviceIdGenerator.java.html#L275" class="el_method">generateCustomDeviceId(FieldConfig)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="31" alt="31"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="DeviceIdGenerator.java.html#L192" class="el_method">generateUuidDeviceId()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="DeviceIdGenerator.java.html#L40" class="el_method">DeviceIdGenerator()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="DeviceIdGenerator.java.html#L112" class="el_method">getType()</a></td><td class="bar" id="b10"/><td class="ctr2" id="c4">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a7"><a href="DeviceIdGenerator.java.html#L117" class="el_method">getConfigClass()</a></td><td class="bar" id="b11"/><td class="ctr2" id="c5">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>