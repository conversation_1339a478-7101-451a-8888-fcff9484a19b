{"groups": [{"name": "dataforge", "type": "com.dataforge.config.ForgeConfig", "sourceType": "com.dataforge.config.ForgeConfig"}], "properties": [{"name": "dataforge.count", "type": "java.lang.Integer", "description": "要生成的记录数量，默认为10。", "sourceType": "com.dataforge.config.ForgeConfig"}, {"name": "dataforge.fields", "type": "java.util.List<com.dataforge.config.FieldConfigWrapper>", "description": "字段配置列表。", "sourceType": "com.dataforge.config.ForgeConfig"}, {"name": "dataforge.output", "type": "com.dataforge.config.OutputConfig", "description": "输出配置。", "sourceType": "com.dataforge.config.ForgeConfig"}, {"name": "dataforge.seed", "type": "java.lang.Long", "description": "随机种子，用于可重现的数据生成。如果不设置，使用系统时间作为种子。", "sourceType": "com.dataforge.config.ForgeConfig"}, {"name": "dataforge.threads", "type": "java.lang.Integer", "description": "并发线程数，默认为1（单线程）。", "sourceType": "com.dataforge.config.ForgeConfig"}, {"name": "dataforge.validate", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据校验，默认为true。", "sourceType": "com.dataforge.config.ForgeConfig"}], "hints": []}