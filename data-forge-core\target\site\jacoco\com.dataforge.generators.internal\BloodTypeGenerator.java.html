<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BloodTypeGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">BloodTypeGenerator.java</span></div><h1>BloodTypeGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 血型生成器
 * 
 * 支持的参数：
 * - group: 血型组别 (A|B|AB|O|ANY)
 * - rh: Rh因子 (POSITIVE|NEGATIVE|ANY)
 * - distribution: 分布方式 (UNIFORM|WEIGHTED|REALISTIC)
 * - format: 输出格式 (STANDARD|FULL|CODE)
 * - weights: 自定义权重配置 (如 &quot;A:30,B:25,AB:5,O:40&quot;)
 * 
 * <AUTHOR>
 */
<span class="nc" id="L23">public class BloodTypeGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(BloodTypeGenerator.class);</span>
<span class="nc" id="L26">    private static final Random random = new Random();</span>

    // 血型组别枚举
<span class="nc" id="L29">    private enum BloodGroup {</span>
<span class="nc" id="L30">        A, B, AB, O</span>
    }

    // Rh因子枚举
<span class="nc" id="L34">    private enum RhFactor {</span>
<span class="nc" id="L35">        POSITIVE, NEGATIVE</span>
    }

    // 血型信息类
    private static class BloodType {
        final BloodGroup group;
        final RhFactor rh;

<span class="nc" id="L43">        BloodType(BloodGroup group, RhFactor rh) {</span>
<span class="nc" id="L44">            this.group = group;</span>
<span class="nc" id="L45">            this.rh = rh;</span>
<span class="nc" id="L46">        }</span>

        @Override
        public boolean equals(Object obj) {
<span class="nc bnc" id="L50" title="All 2 branches missed.">            if (this == obj)</span>
<span class="nc" id="L51">                return true;</span>
<span class="nc bnc" id="L52" title="All 4 branches missed.">            if (obj == null || getClass() != obj.getClass())</span>
<span class="nc" id="L53">                return false;</span>
<span class="nc" id="L54">            BloodType bloodType = (BloodType) obj;</span>
<span class="nc bnc" id="L55" title="All 4 branches missed.">            return group == bloodType.group &amp;&amp; rh == bloodType.rh;</span>
        }

        @Override
        public int hashCode() {
<span class="nc" id="L60">            return Objects.hash(group, rh);</span>
        }
    }

    // 现实分布权重（基于中国人口血型分布）
<span class="nc" id="L65">    private static final Map&lt;BloodType, Double&gt; REALISTIC_WEIGHTS = new HashMap&lt;&gt;();</span>

    static {
<span class="nc" id="L68">        initializeRealisticWeights();</span>
<span class="nc" id="L69">    }</span>

    private static void initializeRealisticWeights() {
        // 基于中国人口血型分布的近似数据
        // ABO血型分布：A型28%, B型24%, AB型9%, O型39%
        // Rh阳性约99.7%, Rh阴性约0.3%

<span class="nc" id="L76">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.A, RhFactor.POSITIVE), 0.279); // A+ 27.9%</span>
<span class="nc" id="L77">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.A, RhFactor.NEGATIVE), 0.001); // A- 0.1%</span>
<span class="nc" id="L78">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.B, RhFactor.POSITIVE), 0.239); // B+ 23.9%</span>
<span class="nc" id="L79">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.B, RhFactor.NEGATIVE), 0.001); // B- 0.1%</span>
<span class="nc" id="L80">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.AB, RhFactor.POSITIVE), 0.089); // AB+ 8.9%</span>
<span class="nc" id="L81">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.AB, RhFactor.NEGATIVE), 0.001); // AB- 0.1%</span>
<span class="nc" id="L82">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.O, RhFactor.POSITIVE), 0.389); // O+ 38.9%</span>
<span class="nc" id="L83">        REALISTIC_WEIGHTS.put(new BloodType(BloodGroup.O, RhFactor.NEGATIVE), 0.001); // O- 0.1%</span>
<span class="nc" id="L84">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L88">        return &quot;bloodtype&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L93">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取参数
<span class="nc" id="L100">            String group = config.getParam(&quot;group&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L101">            String rh = config.getParam(&quot;rh&quot;, String.class, &quot;ANY&quot;);</span>
<span class="nc" id="L102">            String distribution = config.getParam(&quot;distribution&quot;, String.class, &quot;REALISTIC&quot;);</span>
<span class="nc" id="L103">            String format = config.getParam(&quot;format&quot;, String.class, &quot;STANDARD&quot;);</span>
<span class="nc" id="L104">            String weightsParam = config.getParam(&quot;weights&quot;, String.class, null);</span>

            // 获取可选的血型列表
<span class="nc" id="L107">            List&lt;BloodType&gt; availableBloodTypes = getAvailableBloodTypes(group, rh);</span>

            // 根据分布方式选择血型
<span class="nc" id="L110">            BloodType bloodType = selectBloodType(availableBloodTypes, distribution, weightsParam);</span>

            // 将血型信息存入上下文
<span class="nc" id="L113">            context.put(&quot;blood_group&quot;, bloodType.group.name());</span>
<span class="nc" id="L114">            context.put(&quot;rh_factor&quot;, bloodType.rh.name());</span>

            // 格式化输出
<span class="nc" id="L117">            String result = formatBloodType(bloodType, format);</span>

<span class="nc" id="L119">            logger.debug(&quot;Generated blood type: {}&quot;, result);</span>
<span class="nc" id="L120">            return result;</span>

<span class="nc" id="L122">        } catch (Exception e) {</span>
<span class="nc" id="L123">            logger.error(&quot;Error generating blood type&quot;, e);</span>
<span class="nc" id="L124">            return &quot;A+&quot;;</span>
        }
    }

    private List&lt;BloodType&gt; getAvailableBloodTypes(String group, String rh) {
<span class="nc" id="L129">        List&lt;BloodType&gt; bloodTypes = new ArrayList&lt;&gt;();</span>

        // 确定血型组别
<span class="nc" id="L132">        List&lt;BloodGroup&gt; groups = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(group)) {</span>
<span class="nc" id="L134">            groups.addAll(Arrays.asList(BloodGroup.values()));</span>
        } else {
            try {
<span class="nc" id="L137">                BloodGroup specificGroup = BloodGroup.valueOf(group);</span>
<span class="nc" id="L138">                groups.add(specificGroup);</span>
<span class="nc" id="L139">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L140">                logger.warn(&quot;Unknown blood group: {}. Using all groups.&quot;, group);</span>
<span class="nc" id="L141">                groups.addAll(Arrays.asList(BloodGroup.values()));</span>
<span class="nc" id="L142">            }</span>
        }

        // 确定Rh因子
<span class="nc" id="L146">        List&lt;RhFactor&gt; rhFactors = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L147" title="All 2 branches missed.">        if (&quot;ANY&quot;.equals(rh)) {</span>
<span class="nc" id="L148">            rhFactors.addAll(Arrays.asList(RhFactor.values()));</span>
        } else {
            try {
<span class="nc" id="L151">                RhFactor specificRh = RhFactor.valueOf(rh);</span>
<span class="nc" id="L152">                rhFactors.add(specificRh);</span>
<span class="nc" id="L153">            } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L154">                logger.warn(&quot;Unknown Rh factor: {}. Using all factors.&quot;, rh);</span>
<span class="nc" id="L155">                rhFactors.addAll(Arrays.asList(RhFactor.values()));</span>
<span class="nc" id="L156">            }</span>
        }

        // 组合血型组别和Rh因子
<span class="nc bnc" id="L160" title="All 2 branches missed.">        for (BloodGroup bg : groups) {</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">            for (RhFactor rf : rhFactors) {</span>
<span class="nc" id="L162">                bloodTypes.add(new BloodType(bg, rf));</span>
<span class="nc" id="L163">            }</span>
<span class="nc" id="L164">        }</span>

<span class="nc" id="L166">        return bloodTypes;</span>
    }

    private BloodType selectBloodType(List&lt;BloodType&gt; availableBloodTypes, String distribution, String weightsParam) {
<span class="nc bnc" id="L170" title="All 3 branches missed.">        switch (distribution.toUpperCase()) {</span>
            case &quot;UNIFORM&quot;:
<span class="nc" id="L172">                return availableBloodTypes.get(random.nextInt(availableBloodTypes.size()));</span>

            case &quot;WEIGHTED&quot;:
<span class="nc bnc" id="L175" title="All 4 branches missed.">                if (weightsParam != null &amp;&amp; !weightsParam.isEmpty()) {</span>
<span class="nc" id="L176">                    return selectWithCustomWeights(availableBloodTypes, weightsParam);</span>
                }
                // 如果没有自定义权重，使用现实分布
<span class="nc" id="L179">                return selectWithRealisticWeights(availableBloodTypes);</span>

            case &quot;REALISTIC&quot;:
            default:
<span class="nc" id="L183">                return selectWithRealisticWeights(availableBloodTypes);</span>
        }
    }

    private BloodType selectWithRealisticWeights(List&lt;BloodType&gt; availableBloodTypes) {
        // 计算总权重
<span class="nc" id="L189">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">        for (BloodType bloodType : availableBloodTypes) {</span>
<span class="nc" id="L191">            totalWeight += REALISTIC_WEIGHTS.getOrDefault(bloodType, 0.0);</span>
<span class="nc" id="L192">        }</span>

<span class="nc bnc" id="L194" title="All 2 branches missed.">        if (totalWeight &lt;= 0) {</span>
            // 如果没有权重，使用均匀分布
<span class="nc" id="L196">            return availableBloodTypes.get(random.nextInt(availableBloodTypes.size()));</span>
        }

        // 随机选择
<span class="nc" id="L200">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L201">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L203" title="All 2 branches missed.">        for (BloodType bloodType : availableBloodTypes) {</span>
<span class="nc" id="L204">            currentWeight += REALISTIC_WEIGHTS.getOrDefault(bloodType, 0.0);</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L206">                return bloodType;</span>
            }
<span class="nc" id="L208">        }</span>

        // 默认返回第一个
<span class="nc" id="L211">        return availableBloodTypes.get(0);</span>
    }

    private BloodType selectWithCustomWeights(List&lt;BloodType&gt; availableBloodTypes, String weightsParam) {
<span class="nc" id="L215">        Map&lt;String, Integer&gt; customWeights = parseWeights(weightsParam);</span>

        // 计算总权重
<span class="nc" id="L218">        double totalWeight = 0.0;</span>
<span class="nc bnc" id="L219" title="All 2 branches missed.">        for (BloodType bloodType : availableBloodTypes) {</span>
<span class="nc" id="L220">            String key = bloodType.group.name();</span>
<span class="nc" id="L221">            totalWeight += customWeights.getOrDefault(key, 1);</span>
<span class="nc" id="L222">        }</span>

        // 随机选择
<span class="nc" id="L225">        double randomValue = random.nextDouble() * totalWeight;</span>
<span class="nc" id="L226">        double currentWeight = 0.0;</span>

<span class="nc bnc" id="L228" title="All 2 branches missed.">        for (BloodType bloodType : availableBloodTypes) {</span>
<span class="nc" id="L229">            String key = bloodType.group.name();</span>
<span class="nc" id="L230">            currentWeight += customWeights.getOrDefault(key, 1);</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">            if (randomValue &lt;= currentWeight) {</span>
<span class="nc" id="L232">                return bloodType;</span>
            }
<span class="nc" id="L234">        }</span>

        // 默认返回第一个
<span class="nc" id="L237">        return availableBloodTypes.get(0);</span>
    }

    private Map&lt;String, Integer&gt; parseWeights(String weightsParam) {
<span class="nc" id="L241">        Map&lt;String, Integer&gt; weights = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L244">            String[] pairs = weightsParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">            for (String pair : pairs) {</span>
<span class="nc" id="L246">                String[] parts = pair.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">                if (parts.length == 2) {</span>
<span class="nc" id="L248">                    String bloodGroup = parts[0].trim().toUpperCase();</span>
<span class="nc" id="L249">                    int weight = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L250">                    weights.put(bloodGroup, weight);</span>
                }
            }
<span class="nc" id="L253">        } catch (Exception e) {</span>
<span class="nc" id="L254">            logger.warn(&quot;Failed to parse weights: {}&quot;, weightsParam, e);</span>
<span class="nc" id="L255">        }</span>

<span class="nc" id="L257">        return weights;</span>
    }

    private String formatBloodType(BloodType bloodType, String format) {
<span class="nc bnc" id="L261" title="All 4 branches missed.">        switch (format.toUpperCase()) {</span>
            case &quot;STANDARD&quot;:
                // 标准格式：A+, B-, AB+, O-
<span class="nc bnc" id="L264" title="All 2 branches missed.">                String rhSymbol = bloodType.rh == RhFactor.POSITIVE ? &quot;+&quot; : &quot;-&quot;;</span>
<span class="nc" id="L265">                return bloodType.group.name() + rhSymbol;</span>

            case &quot;FULL&quot;:
                // 完整格式：A型Rh阳性, B型Rh阴性
<span class="nc" id="L269">                String groupName = bloodType.group.name() + &quot;型&quot;;</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">                String rhName = bloodType.rh == RhFactor.POSITIVE ? &quot;Rh阳性&quot; : &quot;Rh阴性&quot;;</span>
<span class="nc" id="L271">                return groupName + rhName;</span>

            case &quot;CODE&quot;:
                // 代码格式：AP, BN, ABP, ON
<span class="nc bnc" id="L275" title="All 2 branches missed.">                String rhCode = bloodType.rh == RhFactor.POSITIVE ? &quot;P&quot; : &quot;N&quot;;</span>
<span class="nc" id="L276">                return bloodType.group.name() + rhCode;</span>

            default:
<span class="nc" id="L279">                logger.warn(&quot;Unknown blood type format: {}. Using STANDARD format.&quot;, format);</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">                String defaultRhSymbol = bloodType.rh == RhFactor.POSITIVE ? &quot;+&quot; : &quot;-&quot;;</span>
<span class="nc" id="L281">                return bloodType.group.name() + defaultRhSymbol;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>