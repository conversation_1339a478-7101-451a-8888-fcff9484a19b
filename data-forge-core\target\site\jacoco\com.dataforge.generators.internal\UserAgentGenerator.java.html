<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserAgentGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">UserAgentGenerator.java</span></div><h1>UserAgentGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.*;

/**
 * User-Agent生成器
 * 
 * &lt;p&gt;
 * 支持生成各种浏览器和操作系统的User-Agent字符串，用于Web应用测试、
 * 爬虫开发、浏览器兼容性测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;browser: 浏览器类型 (CHROME|FIREFOX|SAFARI|EDGE|IE|OPERA|RANDOM) 默认: RANDOM&lt;/li&gt;
 * &lt;li&gt;os: 操作系统 (WINDOWS|MACOS|LINUX|ANDROID|IOS|RANDOM) 默认: RANDOM&lt;/li&gt;
 * &lt;li&gt;device: 设备类型 (DESKTOP|MOBILE|TABLET|RANDOM) 默认: RANDOM&lt;/li&gt;
 * &lt;li&gt;version: 是否包含版本号 默认: true&lt;/li&gt;
 * &lt;li&gt;realistic: 是否生成真实的User-Agent 默认: true&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L32">public class UserAgentGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L34">    private static final Logger logger = LoggerFactory.getLogger(UserAgentGenerator.class);</span>
<span class="fc" id="L35">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 浏览器类型枚举
<span class="fc" id="L38">    public enum BrowserType {</span>
<span class="fc" id="L39">        CHROME, FIREFOX, SAFARI, EDGE, IE, OPERA, RANDOM</span>
    }
    
    // 操作系统类型枚举
<span class="fc" id="L43">    public enum OSType {</span>
<span class="fc" id="L44">        WINDOWS, MACOS, LINUX, ANDROID, IOS, RANDOM</span>
    }
    
    // 设备类型枚举
<span class="fc" id="L48">    public enum DeviceType {</span>
<span class="fc" id="L49">        DESKTOP, MOBILE, TABLET, RANDOM</span>
    }
    
    // Chrome User-Agent模板
<span class="fc" id="L53">    private static final List&lt;String&gt; CHROME_TEMPLATES = Arrays.asList(</span>
        &quot;Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36&quot;,
        &quot;Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Mobile Safari/537.36&quot;
    );
    
    // Firefox User-Agent模板
<span class="fc" id="L59">    private static final List&lt;String&gt; FIREFOX_TEMPLATES = Arrays.asList(</span>
        &quot;Mozilla/5.0 ({os}; rv:{version}) Gecko/20100101 Firefox/{version}&quot;,
        &quot;Mozilla/5.0 (Mobile; rv:{version}) Gecko/{version} Firefox/{version}&quot;
    );
    
    // Safari User-Agent模板
<span class="fc" id="L65">    private static final List&lt;String&gt; SAFARI_TEMPLATES = Arrays.asList(</span>
        &quot;Mozilla/5.0 ({os}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Safari/{webkit_version}&quot;,
        &quot;Mozilla/5.0 ({os}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Mobile/15E148 Safari/{webkit_version}&quot;
    );
    
    // Edge User-Agent模板
<span class="fc" id="L71">    private static final List&lt;String&gt; EDGE_TEMPLATES = Arrays.asList(</span>
        &quot;Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 Edg/{version}&quot;,
        &quot;Mozilla/5.0 ({os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36 EdgA/{version}&quot;
    );
    
    // 操作系统字符串映射
<span class="fc" id="L77">    private static final Map&lt;OSType, List&lt;String&gt;&gt; OS_STRINGS = new HashMap&lt;&gt;();</span>
    
    static {
<span class="fc" id="L80">        OS_STRINGS.put(OSType.WINDOWS, Arrays.asList(</span>
            &quot;Windows NT 10.0; Win64; x64&quot;,
            &quot;Windows NT 10.0; WOW64&quot;,
            &quot;Windows NT 6.3; Win64; x64&quot;,
            &quot;Windows NT 6.1; Win64; x64&quot;,
            &quot;Windows NT 6.1; WOW64&quot;
        ));
        
<span class="fc" id="L88">        OS_STRINGS.put(OSType.MACOS, Arrays.asList(</span>
            &quot;Macintosh; Intel Mac OS X 10_15_7&quot;,
            &quot;Macintosh; Intel Mac OS X 10_14_6&quot;,
            &quot;Macintosh; Intel Mac OS X 10_13_6&quot;,
            &quot;Macintosh; Intel Mac OS X 11_2_3&quot;,
            &quot;Macintosh; Intel Mac OS X 12_1&quot;
        ));
        
<span class="fc" id="L96">        OS_STRINGS.put(OSType.LINUX, Arrays.asList(</span>
            &quot;X11; Linux x86_64&quot;,
            &quot;X11; Ubuntu; Linux x86_64&quot;,
            &quot;X11; Linux i686&quot;,
            &quot;X11; CrOS x86_64&quot;
        ));
        
<span class="fc" id="L103">        OS_STRINGS.put(OSType.ANDROID, Arrays.asList(</span>
            &quot;Linux; Android 11; SM-G991B&quot;,
            &quot;Linux; Android 10; SM-A505F&quot;,
            &quot;Linux; Android 9; SM-G960F&quot;,
            &quot;Linux; Android 12; Pixel 6&quot;,
            &quot;Linux; Android 11; OnePlus 9&quot;
        ));
        
<span class="fc" id="L111">        OS_STRINGS.put(OSType.IOS, Arrays.asList(</span>
            &quot;iPhone; CPU iPhone OS 15_0 like Mac OS X&quot;,
            &quot;iPhone; CPU iPhone OS 14_7_1 like Mac OS X&quot;,
            &quot;iPad; CPU OS 15_0 like Mac OS X&quot;,
            &quot;iPhone; CPU iPhone OS 13_7 like Mac OS X&quot;
        ));
<span class="fc" id="L117">    }</span>

    @Override
    public String getType() {
<span class="fc" id="L121">        return &quot;user_agent&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L126">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取浏览器类型
<span class="fc" id="L133">            String browserStr = getStringParam(config, &quot;browser&quot;, &quot;RANDOM&quot;);</span>
<span class="fc" id="L134">            BrowserType browser = parseBrowserType(browserStr);</span>
            
            // 获取操作系统类型
<span class="fc" id="L137">            String osStr = getStringParam(config, &quot;os&quot;, &quot;RANDOM&quot;);</span>
<span class="fc" id="L138">            OSType os = parseOSType(osStr);</span>
            
            // 获取设备类型
<span class="fc" id="L141">            String deviceStr = getStringParam(config, &quot;device&quot;, &quot;RANDOM&quot;);</span>
<span class="fc" id="L142">            DeviceType device = parseDeviceType(deviceStr);</span>
            
            // 获取其他参数
<span class="fc" id="L145">            boolean includeVersion = getBooleanParam(config, &quot;version&quot;, true);</span>
<span class="fc" id="L146">            boolean realistic = getBooleanParam(config, &quot;realistic&quot;, true);</span>
            
            // 如果是随机类型，则随机选择
<span class="fc bfc" id="L149" title="All 2 branches covered.">            if (browser == BrowserType.RANDOM) {</span>
<span class="fc" id="L150">                BrowserType[] browsers = {BrowserType.CHROME, BrowserType.FIREFOX, BrowserType.SAFARI, BrowserType.EDGE};</span>
<span class="fc" id="L151">                browser = browsers[random.nextInt(browsers.length)];</span>
            }
            
<span class="fc bfc" id="L154" title="All 2 branches covered.">            if (os == OSType.RANDOM) {</span>
<span class="fc" id="L155">                OSType[] oses = {OSType.WINDOWS, OSType.MACOS, OSType.LINUX, OSType.ANDROID, OSType.IOS};</span>
<span class="fc" id="L156">                os = oses[random.nextInt(oses.length)];</span>
            }
            
<span class="fc bfc" id="L159" title="All 2 branches covered.">            if (device == DeviceType.RANDOM) {</span>
<span class="fc" id="L160">                DeviceType[] devices = {DeviceType.DESKTOP, DeviceType.MOBILE, DeviceType.TABLET};</span>
<span class="fc" id="L161">                device = devices[random.nextInt(devices.length)];</span>
            }
            
            // 确保操作系统和设备类型的兼容性
<span class="fc" id="L165">            adjustOSAndDevice(os, device);</span>
            
<span class="fc" id="L167">            return generateUserAgent(browser, os, device, includeVersion, realistic);</span>
            
<span class="nc" id="L169">        } catch (Exception e) {</span>
<span class="nc" id="L170">            logger.error(&quot;Failed to generate user agent&quot;, e);</span>
            // 返回一个默认的Chrome User-Agent作为fallback
<span class="nc" id="L172">            return &quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36&quot;;</span>
        }
    }

    /**
     * 解析浏览器类型
     */
    private BrowserType parseBrowserType(String browserStr) {
        try {
<span class="fc" id="L181">            return BrowserType.valueOf(browserStr.toUpperCase());</span>
<span class="nc" id="L182">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L183">            logger.warn(&quot;Invalid browser type: {}, using RANDOM as default&quot;, browserStr);</span>
<span class="nc" id="L184">            return BrowserType.RANDOM;</span>
        }
    }

    /**
     * 解析操作系统类型
     */
    private OSType parseOSType(String osStr) {
        try {
<span class="fc" id="L193">            return OSType.valueOf(osStr.toUpperCase());</span>
<span class="nc" id="L194">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L195">            logger.warn(&quot;Invalid OS type: {}, using RANDOM as default&quot;, osStr);</span>
<span class="nc" id="L196">            return OSType.RANDOM;</span>
        }
    }

    /**
     * 解析设备类型
     */
    private DeviceType parseDeviceType(String deviceStr) {
        try {
<span class="fc" id="L205">            return DeviceType.valueOf(deviceStr.toUpperCase());</span>
<span class="nc" id="L206">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L207">            logger.warn(&quot;Invalid device type: {}, using RANDOM as default&quot;, deviceStr);</span>
<span class="nc" id="L208">            return DeviceType.RANDOM;</span>
        }
    }

    /**
     * 调整操作系统和设备类型的兼容性
     */
    private void adjustOSAndDevice(OSType os, DeviceType device) {
        // 确保移动操作系统与移动设备匹配
<span class="fc bfc" id="L217" title="All 6 branches covered.">        if ((os == OSType.ANDROID || os == OSType.IOS) &amp;&amp; device == DeviceType.DESKTOP) {</span>
            // 移动操作系统不能是桌面设备，调整为移动设备
<span class="fc" id="L219">            device = DeviceType.MOBILE;</span>
        }
        
<span class="fc bfc" id="L222" title="All 10 branches covered.">        if ((os == OSType.WINDOWS || os == OSType.MACOS || os == OSType.LINUX) &amp;&amp; </span>
            (device == DeviceType.MOBILE || device == DeviceType.TABLET)) {
            // 桌面操作系统通常是桌面设备
<span class="fc" id="L225">            device = DeviceType.DESKTOP;</span>
        }
<span class="fc" id="L227">    }</span>

    /**
     * 生成User-Agent字符串
     */
    private String generateUserAgent(BrowserType browser, OSType os, DeviceType device, 
                                   boolean includeVersion, boolean realistic) {
        
<span class="fc" id="L235">        String osString = getRandomOSString(os);</span>
<span class="fc" id="L236">        String template = getRandomTemplate(browser, device);</span>
        
<span class="fc" id="L238">        Map&lt;String, String&gt; replacements = new HashMap&lt;&gt;();</span>
<span class="fc" id="L239">        replacements.put(&quot;{os}&quot;, osString);</span>
        
<span class="pc bpc" id="L241" title="1 of 2 branches missed.">        if (includeVersion) {</span>
<span class="fc" id="L242">            replacements.put(&quot;{version}&quot;, generateVersion(browser, realistic));</span>
<span class="fc" id="L243">            replacements.put(&quot;{webkit_version}&quot;, generateWebKitVersion(realistic));</span>
<span class="fc" id="L244">            replacements.put(&quot;{chrome_version}&quot;, generateChromeVersion(realistic));</span>
        } else {
<span class="nc" id="L246">            replacements.put(&quot;{version}&quot;, &quot;1.0&quot;);</span>
<span class="nc" id="L247">            replacements.put(&quot;{webkit_version}&quot;, &quot;537.36&quot;);</span>
<span class="nc" id="L248">            replacements.put(&quot;{chrome_version}&quot;, &quot;91.0.4472.124&quot;);</span>
        }
        
<span class="fc" id="L251">        String userAgent = template;</span>
<span class="fc bfc" id="L252" title="All 2 branches covered.">        for (Map.Entry&lt;String, String&gt; entry : replacements.entrySet()) {</span>
<span class="fc" id="L253">            userAgent = userAgent.replace(entry.getKey(), entry.getValue());</span>
<span class="fc" id="L254">        }</span>
        
<span class="fc" id="L256">        return userAgent;</span>
    }

    /**
     * 获取随机操作系统字符串
     */
    private String getRandomOSString(OSType os) {
<span class="fc" id="L263">        List&lt;String&gt; osStrings = OS_STRINGS.get(os);</span>
<span class="pc bpc" id="L264" title="2 of 4 branches missed.">        if (osStrings == null || osStrings.isEmpty()) {</span>
<span class="nc" id="L265">            return &quot;Windows NT 10.0; Win64; x64&quot;;</span>
        }
<span class="fc" id="L267">        return osStrings.get(random.nextInt(osStrings.size()));</span>
    }

    /**
     * 获取随机模板
     */
    private String getRandomTemplate(BrowserType browser, DeviceType device) {
        List&lt;String&gt; templates;
        
<span class="pc bpc" id="L276" title="2 of 5 branches missed.">        switch (browser) {</span>
            case CHROME:
<span class="fc" id="L278">                templates = CHROME_TEMPLATES;</span>
<span class="fc" id="L279">                break;</span>
            case FIREFOX:
<span class="fc" id="L281">                templates = FIREFOX_TEMPLATES;</span>
<span class="fc" id="L282">                break;</span>
            case SAFARI:
<span class="fc" id="L284">                templates = SAFARI_TEMPLATES;</span>
<span class="fc" id="L285">                break;</span>
            case EDGE:
<span class="nc" id="L287">                templates = EDGE_TEMPLATES;</span>
<span class="nc" id="L288">                break;</span>
            default:
<span class="nc" id="L290">                templates = CHROME_TEMPLATES;</span>
                break;
        }
        
        // 根据设备类型选择合适的模板
<span class="pc bpc" id="L295" title="1 of 4 branches missed.">        if (device == DeviceType.MOBILE &amp;&amp; templates.size() &gt; 1) {</span>
<span class="fc" id="L296">            return templates.get(1); // 通常第二个模板是移动版本</span>
        }
        
<span class="fc" id="L299">        return templates.get(0);</span>
    }

    /**
     * 生成浏览器版本号
     */
    private String generateVersion(BrowserType browser, boolean realistic) {
<span class="pc bpc" id="L306" title="1 of 2 branches missed.">        if (!realistic) {</span>
<span class="nc" id="L307">            return &quot;1.0.0&quot;;</span>
        }
        
<span class="pc bpc" id="L310" title="2 of 5 branches missed.">        switch (browser) {</span>
            case CHROME:
<span class="fc" id="L312">                return String.format(&quot;%d.0.%d.%d&quot;, </span>
<span class="fc" id="L313">                    90 + random.nextInt(20), </span>
<span class="fc" id="L314">                    4000 + random.nextInt(1000), </span>
<span class="fc" id="L315">                    100 + random.nextInt(200));</span>
            case FIREFOX:
<span class="fc" id="L317">                return String.format(&quot;%d.0&quot;, 80 + random.nextInt(20));</span>
            case SAFARI:
<span class="fc" id="L319">                return String.format(&quot;%d.%d.%d&quot;, </span>
<span class="fc" id="L320">                    14 + random.nextInt(3), </span>
<span class="fc" id="L321">                    random.nextInt(10), </span>
<span class="fc" id="L322">                    random.nextInt(10));</span>
            case EDGE:
<span class="nc" id="L324">                return String.format(&quot;%d.0.%d.%d&quot;, </span>
<span class="nc" id="L325">                    90 + random.nextInt(20), </span>
<span class="nc" id="L326">                    1000 + random.nextInt(1000), </span>
<span class="nc" id="L327">                    random.nextInt(100));</span>
            default:
<span class="nc" id="L329">                return &quot;1.0.0&quot;;</span>
        }
    }

    /**
     * 生成WebKit版本号
     */
    private String generateWebKitVersion(boolean realistic) {
<span class="pc bpc" id="L337" title="1 of 2 branches missed.">        if (!realistic) {</span>
<span class="nc" id="L338">            return &quot;537.36&quot;;</span>
        }
<span class="fc" id="L340">        return String.format(&quot;537.%d&quot;, 30 + random.nextInt(10));</span>
    }

    /**
     * 生成Chrome版本号（用于Edge等基于Chromium的浏览器）
     */
    private String generateChromeVersion(boolean realistic) {
<span class="pc bpc" id="L347" title="1 of 2 branches missed.">        if (!realistic) {</span>
<span class="nc" id="L348">            return &quot;91.0.4472.124&quot;;</span>
        }
<span class="fc" id="L350">        return String.format(&quot;%d.0.%d.%d&quot;, </span>
<span class="fc" id="L351">            90 + random.nextInt(20), </span>
<span class="fc" id="L352">            4000 + random.nextInt(1000), </span>
<span class="fc" id="L353">            100 + random.nextInt(200));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>