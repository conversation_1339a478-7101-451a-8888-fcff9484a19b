<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SessionTokenGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">SessionTokenGenerator.java</span></div><h1>SessionTokenGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.Random;

/**
 * 会话令牌生成器
 * 
 * 支持的参数：
 * - type: 令牌类型 (RANDOM_STRING|JWT|OAUTH|CSRF)，默认 RANDOM_STRING
 * - length: 令牌长度，默认32
 * - encoding: 编码方式 (BASE64|HEX|ALPHANUMERIC)，默认BASE64
 * - prefix: 令牌前缀，默认无
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
<span class="nc" id="L24">public class SessionTokenGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L26">    private final Random secureRandom = new SecureRandom();</span>

    @Override
    public String getType() {
<span class="nc" id="L30">        return &quot;session_token&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
<span class="nc" id="L35">        String type = getStringParam(config, &quot;type&quot;, &quot;RANDOM_STRING&quot;);</span>
<span class="nc" id="L36">        int length = getIntParam(config, &quot;length&quot;, 32);</span>
<span class="nc" id="L37">        String encoding = getStringParam(config, &quot;encoding&quot;, &quot;BASE64&quot;);</span>
<span class="nc" id="L38">        String prefix = getStringParam(config, &quot;prefix&quot;, &quot;&quot;);</span>

        String token;
<span class="nc bnc" id="L41" title="All 4 branches missed.">        switch (type.toUpperCase()) {</span>
            case &quot;JWT&quot;:
<span class="nc" id="L43">                token = generateJwtToken();</span>
<span class="nc" id="L44">                break;</span>
            case &quot;OAUTH&quot;:
<span class="nc" id="L46">                token = generateOAuthToken(length, encoding);</span>
<span class="nc" id="L47">                break;</span>
            case &quot;CSRF&quot;:
<span class="nc" id="L49">                token = generateCsrfToken(length, encoding);</span>
<span class="nc" id="L50">                break;</span>
            default:
<span class="nc" id="L52">                token = generateRandomString(length, encoding);</span>
                break;
        }

        // 添加前缀
<span class="nc bnc" id="L57" title="All 2 branches missed.">        if (!prefix.isEmpty()) {</span>
<span class="nc" id="L58">            token = prefix + token;</span>
        }

        // 存储到上下文中供其他字段使用
<span class="nc" id="L62">        context.put(&quot;session_token&quot;, token);</span>
<span class="nc" id="L63">        context.put(&quot;token_type&quot;, type);</span>

<span class="nc" id="L65">        return token;</span>
    }

    /**
     * 生成JWT令牌（简化版）
     */
    private String generateJwtToken() {
<span class="nc" id="L72">        String header = &quot;{\&quot;alg\&quot;:\&quot;HS256\&quot;,\&quot;typ\&quot;:\&quot;JWT\&quot;}&quot;;</span>
<span class="nc" id="L73">        String payload = &quot;{\&quot;sub\&quot;:\&quot;user123\&quot;,\&quot;iat\&quot;:&quot; + Instant.now().getEpochSecond() + &quot;}&quot;;</span>
        
<span class="nc" id="L75">        String encodedHeader = Base64.getUrlEncoder().withoutPadding()</span>
<span class="nc" id="L76">            .encodeToString(header.getBytes());</span>
<span class="nc" id="L77">        String encodedPayload = Base64.getUrlEncoder().withoutPadding()</span>
<span class="nc" id="L78">            .encodeToString(payload.getBytes());</span>
        
        // 生成模拟签名
<span class="nc" id="L81">        byte[] signature = new byte[32];</span>
<span class="nc" id="L82">        secureRandom.nextBytes(signature);</span>
<span class="nc" id="L83">        String encodedSignature = Base64.getUrlEncoder().withoutPadding()</span>
<span class="nc" id="L84">            .encodeToString(signature);</span>
        
<span class="nc" id="L86">        return encodedHeader + &quot;.&quot; + encodedPayload + &quot;.&quot; + encodedSignature;</span>
    }

    /**
     * 生成OAuth访问令牌
     */
    private String generateOAuthToken(int length, String encoding) {
<span class="nc" id="L93">        return generateRandomString(Math.max(length, 40), encoding);</span>
    }

    /**
     * 生成CSRF令牌
     */
    private String generateCsrfToken(int length, String encoding) {
<span class="nc" id="L100">        return generateRandomString(length, &quot;HEX&quot;);</span>
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length, String encoding) {
<span class="nc bnc" id="L107" title="All 4 branches missed.">        switch (encoding.toUpperCase()) {</span>
            case &quot;BASE64&quot;:
<span class="nc" id="L109">                return generateBase64String(length);</span>
            case &quot;HEX&quot;:
<span class="nc" id="L111">                return generateHexString(length);</span>
            case &quot;ALPHANUMERIC&quot;:
<span class="nc" id="L113">                return generateAlphanumericString(length);</span>
            default:
<span class="nc" id="L115">                return generateBase64String(length);</span>
        }
    }

    /**
     * 生成Base64编码字符串
     */
    private String generateBase64String(int length) {
<span class="nc" id="L123">        int byteLength = (length * 3 + 3) / 4;</span>
<span class="nc" id="L124">        byte[] bytes = new byte[byteLength];</span>
<span class="nc" id="L125">        secureRandom.nextBytes(bytes);</span>
        
<span class="nc" id="L127">        String encoded = Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">        return encoded.length() &gt; length ? encoded.substring(0, length) : encoded;</span>
    }

    /**
     * 生成十六进制字符串
     */
    private String generateHexString(int length) {
<span class="nc" id="L135">        StringBuilder sb = new StringBuilder(length);</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L137">            sb.append(Integer.toHexString(secureRandom.nextInt(16)));</span>
        }
<span class="nc" id="L139">        return sb.toString();</span>
    }

    /**
     * 生成字母数字字符串
     */
    private String generateAlphanumericString(int length) {
<span class="nc" id="L146">        String chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;;</span>
<span class="nc" id="L147">        StringBuilder sb = new StringBuilder(length);</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="nc" id="L149">            sb.append(chars.charAt(secureRandom.nextInt(chars.length())));</span>
        }
<span class="nc" id="L151">        return sb.toString();</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L156">        return FieldConfig.class;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>