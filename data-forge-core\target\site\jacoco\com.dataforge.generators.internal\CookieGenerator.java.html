<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CookieGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">CookieGenerator.java</span></div><h1>CookieGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Cookie生成器
 * 
 * &lt;p&gt;
 * 支持生成HTTP Cookie字符串，包括Cookie名称、值以及各种属性，
 * 用于Web应用测试、会话管理测试、浏览器兼容性测试等场景。
 * 
 * &lt;p&gt;
 * 支持的参数：
 * &lt;ul&gt;
 * &lt;li&gt;format: 输出格式 (HEADER|JSON|SIMPLE) 默认: HEADER&lt;/li&gt;
 * &lt;li&gt;name: Cookie名称（如果不指定则随机生成）&lt;/li&gt;
 * &lt;li&gt;value: Cookie值（如果不指定则随机生成）&lt;/li&gt;
 * &lt;li&gt;domain: 域名&lt;/li&gt;
 * &lt;li&gt;path: 路径 默认: /&lt;/li&gt;
 * &lt;li&gt;expires_days: 过期天数（从现在开始）&lt;/li&gt;
 * &lt;li&gt;max_age: 最大存活时间（秒）&lt;/li&gt;
 * &lt;li&gt;secure: 是否设置Secure标志 默认: false&lt;/li&gt;
 * &lt;li&gt;http_only: 是否设置HttpOnly标志 默认: false&lt;/li&gt;
 * &lt;li&gt;same_site: SameSite属性 (STRICT|LAX|NONE) 默认: LAX&lt;/li&gt;
 * &lt;li&gt;session: 是否为会话Cookie 默认: false&lt;/li&gt;
 * &lt;li&gt;value_type: 值类型 (STRING|UUID|TOKEN|JSON) 默认: STRING&lt;/li&gt;
 * &lt;li&gt;value_length: 值长度（仅对STRING类型有效）默认: 32&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
<span class="fc" id="L43">public class CookieGenerator extends BaseGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="fc" id="L45">    private static final Logger logger = LoggerFactory.getLogger(CookieGenerator.class);</span>
<span class="fc" id="L46">    private static final SecureRandom random = new SecureRandom();</span>
    
    // 输出格式枚举
<span class="fc" id="L49">    public enum OutputFormat {</span>
<span class="fc" id="L50">        HEADER(&quot;HTTP头格式&quot;),</span>
<span class="fc" id="L51">        JSON(&quot;JSON格式&quot;),</span>
<span class="fc" id="L52">        SIMPLE(&quot;简单格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L56">        OutputFormat(String description) {</span>
<span class="fc" id="L57">            this.description = description;</span>
<span class="fc" id="L58">        }</span>
        
        public String getDescription() {
<span class="nc" id="L61">            return description;</span>
        }
    }
    
    // SameSite属性枚举
<span class="fc" id="L66">    public enum SameSite {</span>
<span class="fc" id="L67">        STRICT, LAX, NONE</span>
    }
    
    // 值类型枚举
<span class="fc" id="L71">    public enum ValueType {</span>
<span class="fc" id="L72">        STRING(&quot;随机字符串&quot;),</span>
<span class="fc" id="L73">        UUID(&quot;UUID格式&quot;),</span>
<span class="fc" id="L74">        TOKEN(&quot;令牌格式&quot;),</span>
<span class="fc" id="L75">        JSON(&quot;JSON格式&quot;);</span>
        
        private final String description;
        
<span class="fc" id="L79">        ValueType(String description) {</span>
<span class="fc" id="L80">            this.description = description;</span>
<span class="fc" id="L81">        }</span>
        
        public String getDescription() {
<span class="nc" id="L84">            return description;</span>
        }
    }
    
    // 常见Cookie名称
<span class="fc" id="L89">    private static final List&lt;String&gt; COMMON_COOKIE_NAMES = Arrays.asList(</span>
        &quot;sessionid&quot;, &quot;JSESSIONID&quot;, &quot;PHPSESSID&quot;, &quot;ASP.NET_SessionId&quot;,
        &quot;auth_token&quot;, &quot;access_token&quot;, &quot;refresh_token&quot;, &quot;csrf_token&quot;,
        &quot;user_id&quot;, &quot;username&quot;, &quot;remember_me&quot;, &quot;language&quot;, &quot;theme&quot;,
        &quot;cart_id&quot;, &quot;visitor_id&quot;, &quot;tracking_id&quot;, &quot;analytics_id&quot;
    );
    
    // 常见域名
<span class="fc" id="L97">    private static final List&lt;String&gt; COMMON_DOMAINS = Arrays.asList(</span>
        &quot;example.com&quot;, &quot;test.com&quot;, &quot;localhost&quot;, &quot;127.0.0.1&quot;,
        &quot;app.example.com&quot;, &quot;api.example.com&quot;, &quot;www.example.com&quot;
    );
    
    // Cookie信息类
    public static class CookieInfo {
        private final String name;
        private final String value;
        private final String domain;
        private final String path;
        private final LocalDateTime expires;
        private final Integer maxAge;
        private final boolean secure;
        private final boolean httpOnly;
        private final SameSite sameSite;
        
        public CookieInfo(String name, String value, String domain, String path,
                         LocalDateTime expires, Integer maxAge, boolean secure,
<span class="fc" id="L116">                         boolean httpOnly, SameSite sameSite) {</span>
<span class="fc" id="L117">            this.name = name;</span>
<span class="fc" id="L118">            this.value = value;</span>
<span class="fc" id="L119">            this.domain = domain;</span>
<span class="fc" id="L120">            this.path = path;</span>
<span class="fc" id="L121">            this.expires = expires;</span>
<span class="fc" id="L122">            this.maxAge = maxAge;</span>
<span class="fc" id="L123">            this.secure = secure;</span>
<span class="fc" id="L124">            this.httpOnly = httpOnly;</span>
<span class="fc" id="L125">            this.sameSite = sameSite;</span>
<span class="fc" id="L126">        }</span>
        
        // Getters
<span class="fc" id="L129">        public String getName() { return name; }</span>
<span class="fc" id="L130">        public String getValue() { return value; }</span>
<span class="fc" id="L131">        public String getDomain() { return domain; }</span>
<span class="fc" id="L132">        public String getPath() { return path; }</span>
<span class="fc" id="L133">        public LocalDateTime getExpires() { return expires; }</span>
<span class="fc" id="L134">        public Integer getMaxAge() { return maxAge; }</span>
<span class="fc" id="L135">        public boolean isSecure() { return secure; }</span>
<span class="fc" id="L136">        public boolean isHttpOnly() { return httpOnly; }</span>
<span class="fc" id="L137">        public SameSite getSameSite() { return sameSite; }</span>
    }

    @Override
    public String getType() {
<span class="fc" id="L142">        return &quot;cookie&quot;;</span>
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="fc" id="L147">        return FieldConfig.class;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 获取输出格式
<span class="fc" id="L154">            String formatStr = getStringParam(config, &quot;format&quot;, &quot;HEADER&quot;);</span>
<span class="fc" id="L155">            OutputFormat format = parseOutputFormat(formatStr);</span>
            
            // 生成Cookie信息
<span class="fc" id="L158">            CookieInfo cookieInfo = generateCookieInfo(config);</span>
            
            // 格式化输出
<span class="fc" id="L161">            return formatCookie(cookieInfo, format);</span>
            
<span class="nc" id="L163">        } catch (Exception e) {</span>
<span class="nc" id="L164">            logger.error(&quot;Failed to generate cookie&quot;, e);</span>
            // 返回一个默认Cookie作为fallback
<span class="nc" id="L166">            return &quot;sessionid=abc123; Path=/&quot;;</span>
        }
    }

    /**
     * 解析输出格式
     */
    private OutputFormat parseOutputFormat(String formatStr) {
        try {
<span class="fc" id="L175">            return OutputFormat.valueOf(formatStr.toUpperCase());</span>
<span class="nc" id="L176">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L177">            logger.warn(&quot;Invalid output format: {}, using HEADER as default&quot;, formatStr);</span>
<span class="nc" id="L178">            return OutputFormat.HEADER;</span>
        }
    }

    /**
     * 生成Cookie信息
     */
    private CookieInfo generateCookieInfo(FieldConfig config) {
        // 生成Cookie名称
<span class="fc" id="L187">        String name = getStringParam(config, &quot;name&quot;, null);</span>
<span class="fc bfc" id="L188" title="All 2 branches covered.">        if (name == null) {</span>
<span class="fc" id="L189">            name = COMMON_COOKIE_NAMES.get(random.nextInt(COMMON_COOKIE_NAMES.size()));</span>
        }
        
        // 生成Cookie值
<span class="fc" id="L193">        String value = getStringParam(config, &quot;value&quot;, null);</span>
<span class="pc bpc" id="L194" title="1 of 2 branches missed.">        if (value == null) {</span>
<span class="fc" id="L195">            String valueTypeStr = getStringParam(config, &quot;value_type&quot;, &quot;STRING&quot;);</span>
<span class="fc" id="L196">            ValueType valueType = parseValueType(valueTypeStr);</span>
<span class="fc" id="L197">            value = generateCookieValue(valueType, config);</span>
        }
        
        // 获取域名
<span class="fc" id="L201">        String domain = getStringParam(config, &quot;domain&quot;, null);</span>
<span class="pc bpc" id="L202" title="1 of 2 branches missed.">        if (domain == null) {</span>
<span class="fc" id="L203">            domain = COMMON_DOMAINS.get(random.nextInt(COMMON_DOMAINS.size()));</span>
        }
        
        // 获取路径
<span class="fc" id="L207">        String path = getStringParam(config, &quot;path&quot;, &quot;/&quot;);</span>
        
        // 计算过期时间
<span class="fc" id="L210">        LocalDateTime expires = null;</span>
<span class="fc" id="L211">        Integer maxAge = null;</span>
<span class="fc" id="L212">        boolean isSession = getBooleanParam(config, &quot;session&quot;, false);</span>
        
<span class="pc bpc" id="L214" title="1 of 2 branches missed.">        if (!isSession) {</span>
<span class="fc" id="L215">            String expiresDaysStr = getStringParam(config, &quot;expires_days&quot;, null);</span>
<span class="fc" id="L216">            String maxAgeStr = getStringParam(config, &quot;max_age&quot;, null);</span>
            
<span class="pc bpc" id="L218" title="1 of 2 branches missed.">            if (expiresDaysStr != null) {</span>
<span class="nc" id="L219">                int expiresDays = Integer.parseInt(expiresDaysStr);</span>
<span class="nc" id="L220">                expires = LocalDateTime.now().plusDays(expiresDays);</span>
<span class="pc bpc" id="L221" title="1 of 2 branches missed.">            } else if (maxAgeStr != null) {</span>
<span class="nc" id="L222">                maxAge = Integer.parseInt(maxAgeStr);</span>
            } else {
                // 默认7天后过期
<span class="fc" id="L225">                expires = LocalDateTime.now().plusDays(7);</span>
            }
        }
        
        // 获取安全属性
<span class="fc" id="L230">        boolean secure = getBooleanParam(config, &quot;secure&quot;, false);</span>
<span class="fc" id="L231">        boolean httpOnly = getBooleanParam(config, &quot;http_only&quot;, false);</span>
        
        // 获取SameSite属性
<span class="fc" id="L234">        String sameSiteStr = getStringParam(config, &quot;same_site&quot;, &quot;LAX&quot;);</span>
<span class="fc" id="L235">        SameSite sameSite = parseSameSite(sameSiteStr);</span>
        
<span class="fc" id="L237">        return new CookieInfo(name, value, domain, path, expires, maxAge, secure, httpOnly, sameSite);</span>
    }

    /**
     * 解析值类型
     */
    private ValueType parseValueType(String valueTypeStr) {
        try {
<span class="fc" id="L245">            return ValueType.valueOf(valueTypeStr.toUpperCase());</span>
<span class="nc" id="L246">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L247">            logger.warn(&quot;Invalid value type: {}, using STRING as default&quot;, valueTypeStr);</span>
<span class="nc" id="L248">            return ValueType.STRING;</span>
        }
    }

    /**
     * 解析SameSite属性
     */
    private SameSite parseSameSite(String sameSiteStr) {
        try {
<span class="fc" id="L257">            return SameSite.valueOf(sameSiteStr.toUpperCase());</span>
<span class="nc" id="L258">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L259">            logger.warn(&quot;Invalid SameSite value: {}, using LAX as default&quot;, sameSiteStr);</span>
<span class="nc" id="L260">            return SameSite.LAX;</span>
        }
    }

    /**
     * 生成Cookie值
     */
    private String generateCookieValue(ValueType valueType, FieldConfig config) {
<span class="pc bpc" id="L268" title="2 of 4 branches missed.">        switch (valueType) {</span>
            case UUID:
<span class="fc" id="L270">                return UUID.randomUUID().toString();</span>
            case TOKEN:
<span class="nc" id="L272">                return generateToken();</span>
            case JSON:
<span class="nc" id="L274">                return generateJsonValue();</span>
            case STRING:
            default:
<span class="fc" id="L277">                int length = getIntParam(config, &quot;value_length&quot;, 32);</span>
<span class="fc" id="L278">                return generateRandomString(length);</span>
        }
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
<span class="fc" id="L286">        String chars = &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;;</span>
<span class="fc" id="L287">        StringBuilder sb = new StringBuilder();</span>
        
<span class="fc bfc" id="L289" title="All 2 branches covered.">        for (int i = 0; i &lt; length; i++) {</span>
<span class="fc" id="L290">            sb.append(chars.charAt(random.nextInt(chars.length())));</span>
        }
        
<span class="fc" id="L293">        return sb.toString();</span>
    }

    /**
     * 生成令牌格式的值
     */
    private String generateToken() {
        // 生成类似JWT的令牌格式
<span class="nc" id="L301">        String header = Base64.getEncoder().encodeToString(&quot;{\&quot;typ\&quot;:\&quot;JWT\&quot;,\&quot;alg\&quot;:\&quot;HS256\&quot;}&quot;.getBytes());</span>
<span class="nc" id="L302">        String payload = Base64.getEncoder().encodeToString(</span>
<span class="nc" id="L303">            String.format(&quot;{\&quot;sub\&quot;:\&quot;user%d\&quot;,\&quot;iat\&quot;:%d}&quot;, </span>
<span class="nc" id="L304">                random.nextInt(10000), </span>
<span class="nc" id="L305">                System.currentTimeMillis() / 1000).getBytes());</span>
<span class="nc" id="L306">        String signature = generateRandomString(43);</span>
        
<span class="nc" id="L308">        return header + &quot;.&quot; + payload + &quot;.&quot; + signature;</span>
    }

    /**
     * 生成JSON格式的值
     */
    private String generateJsonValue() {
<span class="nc" id="L315">        Map&lt;String, Object&gt; jsonData = new HashMap&lt;&gt;();</span>
<span class="nc" id="L316">        jsonData.put(&quot;userId&quot;, random.nextInt(10000));</span>
<span class="nc" id="L317">        jsonData.put(&quot;sessionId&quot;, generateRandomString(16));</span>
<span class="nc" id="L318">        jsonData.put(&quot;timestamp&quot;, System.currentTimeMillis());</span>
<span class="nc" id="L319">        jsonData.put(&quot;preferences&quot;, Map.of(&quot;theme&quot;, &quot;dark&quot;, &quot;language&quot;, &quot;en&quot;));</span>
        
        // 简单的JSON序列化
<span class="nc" id="L322">        StringBuilder json = new StringBuilder(&quot;{&quot;);</span>
<span class="nc" id="L323">        boolean first = true;</span>
<span class="nc bnc" id="L324" title="All 2 branches missed.">        for (Map.Entry&lt;String, Object&gt; entry : jsonData.entrySet()) {</span>
<span class="nc bnc" id="L325" title="All 2 branches missed.">            if (!first) json.append(&quot;,&quot;);</span>
<span class="nc" id="L326">            json.append(&quot;\&quot;&quot;).append(entry.getKey()).append(&quot;\&quot;:&quot;);</span>
            
<span class="nc" id="L328">            Object value = entry.getValue();</span>
<span class="nc bnc" id="L329" title="All 2 branches missed.">            if (value instanceof String) {</span>
<span class="nc" id="L330">                json.append(&quot;\&quot;&quot;).append(value).append(&quot;\&quot;&quot;);</span>
<span class="nc bnc" id="L331" title="All 2 branches missed.">            } else if (value instanceof Map) {</span>
<span class="nc" id="L332">                json.append(&quot;{\&quot;theme\&quot;:\&quot;dark\&quot;,\&quot;language\&quot;:\&quot;en\&quot;}&quot;);</span>
            } else {
<span class="nc" id="L334">                json.append(value);</span>
            }
<span class="nc" id="L336">            first = false;</span>
<span class="nc" id="L337">        }</span>
<span class="nc" id="L338">        json.append(&quot;}&quot;);</span>
        
<span class="nc" id="L340">        return Base64.getEncoder().encodeToString(json.toString().getBytes());</span>
    }

    /**
     * 格式化Cookie
     */
    private String formatCookie(CookieInfo cookieInfo, OutputFormat format) {
<span class="pc bpc" id="L347" title="1 of 4 branches missed.">        switch (format) {</span>
            case HEADER:
<span class="fc" id="L349">                return formatAsHeader(cookieInfo);</span>
            case JSON:
<span class="fc" id="L351">                return formatAsJson(cookieInfo);</span>
            case SIMPLE:
<span class="fc" id="L353">                return formatAsSimple(cookieInfo);</span>
            default:
<span class="nc" id="L355">                return formatAsHeader(cookieInfo);</span>
        }
    }

    /**
     * 格式化为HTTP头格式
     */
    private String formatAsHeader(CookieInfo cookieInfo) {
<span class="fc" id="L363">        StringBuilder cookie = new StringBuilder();</span>
<span class="fc" id="L364">        cookie.append(cookieInfo.getName()).append(&quot;=&quot;).append(cookieInfo.getValue());</span>
        
<span class="pc bpc" id="L366" title="1 of 2 branches missed.">        if (cookieInfo.getDomain() != null) {</span>
<span class="fc" id="L367">            cookie.append(&quot;; Domain=&quot;).append(cookieInfo.getDomain());</span>
        }
        
<span class="pc bpc" id="L370" title="1 of 2 branches missed.">        if (cookieInfo.getPath() != null) {</span>
<span class="fc" id="L371">            cookie.append(&quot;; Path=&quot;).append(cookieInfo.getPath());</span>
        }
        
<span class="pc bpc" id="L374" title="1 of 2 branches missed.">        if (cookieInfo.getExpires() != null) {</span>
<span class="fc" id="L375">            String expiresStr = cookieInfo.getExpires()</span>
<span class="fc" id="L376">                .atZone(ZoneOffset.UTC)</span>
<span class="fc" id="L377">                .format(DateTimeFormatter.RFC_1123_DATE_TIME);</span>
<span class="fc" id="L378">            cookie.append(&quot;; Expires=&quot;).append(expiresStr);</span>
        }
        
<span class="pc bpc" id="L381" title="1 of 2 branches missed.">        if (cookieInfo.getMaxAge() != null) {</span>
<span class="nc" id="L382">            cookie.append(&quot;; Max-Age=&quot;).append(cookieInfo.getMaxAge());</span>
        }
        
<span class="pc bpc" id="L385" title="1 of 2 branches missed.">        if (cookieInfo.isSecure()) {</span>
<span class="nc" id="L386">            cookie.append(&quot;; Secure&quot;);</span>
        }
        
<span class="pc bpc" id="L389" title="1 of 2 branches missed.">        if (cookieInfo.isHttpOnly()) {</span>
<span class="nc" id="L390">            cookie.append(&quot;; HttpOnly&quot;);</span>
        }
        
<span class="pc bpc" id="L393" title="1 of 2 branches missed.">        if (cookieInfo.getSameSite() != null) {</span>
<span class="fc" id="L394">            cookie.append(&quot;; SameSite=&quot;).append(cookieInfo.getSameSite().name());</span>
        }
        
<span class="fc" id="L397">        return cookie.toString();</span>
    }

    /**
     * 格式化为JSON格式
     */
    private String formatAsJson(CookieInfo cookieInfo) {
<span class="fc" id="L404">        StringBuilder json = new StringBuilder(&quot;{&quot;);</span>
<span class="fc" id="L405">        json.append(&quot;\&quot;name\&quot;:\&quot;&quot;).append(cookieInfo.getName()).append(&quot;\&quot;,&quot;);</span>
<span class="fc" id="L406">        json.append(&quot;\&quot;value\&quot;:\&quot;&quot;).append(cookieInfo.getValue()).append(&quot;\&quot;&quot;);</span>
        
<span class="pc bpc" id="L408" title="1 of 2 branches missed.">        if (cookieInfo.getDomain() != null) {</span>
<span class="fc" id="L409">            json.append(&quot;,\&quot;domain\&quot;:\&quot;&quot;).append(cookieInfo.getDomain()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L412" title="1 of 2 branches missed.">        if (cookieInfo.getPath() != null) {</span>
<span class="fc" id="L413">            json.append(&quot;,\&quot;path\&quot;:\&quot;&quot;).append(cookieInfo.getPath()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L416" title="1 of 2 branches missed.">        if (cookieInfo.getExpires() != null) {</span>
<span class="fc" id="L417">            json.append(&quot;,\&quot;expires\&quot;:\&quot;&quot;).append(cookieInfo.getExpires().toString()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="pc bpc" id="L420" title="1 of 2 branches missed.">        if (cookieInfo.getMaxAge() != null) {</span>
<span class="nc" id="L421">            json.append(&quot;,\&quot;maxAge\&quot;:&quot;).append(cookieInfo.getMaxAge());</span>
        }
        
<span class="fc" id="L424">        json.append(&quot;,\&quot;secure\&quot;:&quot;).append(cookieInfo.isSecure());</span>
<span class="fc" id="L425">        json.append(&quot;,\&quot;httpOnly\&quot;:&quot;).append(cookieInfo.isHttpOnly());</span>
        
<span class="pc bpc" id="L427" title="1 of 2 branches missed.">        if (cookieInfo.getSameSite() != null) {</span>
<span class="fc" id="L428">            json.append(&quot;,\&quot;sameSite\&quot;:\&quot;&quot;).append(cookieInfo.getSameSite().name()).append(&quot;\&quot;&quot;);</span>
        }
        
<span class="fc" id="L431">        json.append(&quot;}&quot;);</span>
<span class="fc" id="L432">        return json.toString();</span>
    }

    /**
     * 格式化为简单格式
     */
    private String formatAsSimple(CookieInfo cookieInfo) {
<span class="fc" id="L439">        return cookieInfo.getName() + &quot;=&quot; + cookieInfo.getValue();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>