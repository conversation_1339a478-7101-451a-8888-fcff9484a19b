<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EmailGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">DataForge Core</a> &gt; <a href="index.source.html" class="el_package">com.dataforge.generators.internal</a> &gt; <span class="el_source">EmailGenerator.java</span></div><h1>EmailGenerator.java</h1><pre class="source lang-java linenums">package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 邮箱地址生成器。
 * 
 * &lt;p&gt;
 * 生成符合邮箱格式的电子邮件地址，支持大规模邮箱生成。
 * 支持自定义域名、用户名长度、与姓名关联、权重选择等功能。
 * 通过配置文件管理域名数据，支持生成数十亿唯一邮箱地址。
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
<span class="nc" id="L27">public class EmailGenerator implements DataGenerator&lt;String, FieldConfig&gt; {</span>

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(EmailGenerator.class);</span>

    /**
     * 邮箱域名数据文件路径。
     */
    private static final String EMAIL_DOMAINS_PATH = &quot;data/email-domains.txt&quot;;

    /**
     * 缓存的域名数据。
     */
    private volatile Map&lt;String, DomainInfo&gt; emailDomains;
    private volatile List&lt;String&gt; allDomains;
    private volatile Map&lt;String, List&lt;String&gt;&gt; domainsByType;

    /**
     * Fallback域名数据（当文件加载失败时使用）。
     */
<span class="nc" id="L46">    private static final List&lt;String&gt; FALLBACK_COMMON_DOMAINS = Arrays.asList(</span>
            &quot;qq.com&quot;, &quot;163.com&quot;, &quot;126.com&quot;, &quot;gmail.com&quot;, &quot;hotmail.com&quot;,
            &quot;yahoo.com&quot;, &quot;sina.com&quot;, &quot;sohu.com&quot;, &quot;139.com&quot;, &quot;189.cn&quot;,
            &quot;outlook.com&quot;, &quot;foxmail.com&quot;, &quot;aliyun.com&quot;, &quot;yeah.net&quot;);

    /**
     * 域名信息类。
     */
    private static class DomainInfo {
        // 简化的域名信息类，目前只用作占位符
        // 实际的域名信息通过 Map 的键和分类映射来管理

<span class="nc" id="L58">        DomainInfo(String domain, String type, int weight) {</span>
            // 构造函数保留以维持兼容性
<span class="nc" id="L60">        }</span>
    }

    /**
     * 中文姓名拼音映射（简化版）。
     */
<span class="nc" id="L66">    private static final java.util.Map&lt;String, String&gt; PINYIN_MAP = new java.util.HashMap&lt;&gt;();</span>

    static {
        // 常见姓氏拼音
<span class="nc" id="L70">        PINYIN_MAP.put(&quot;王&quot;, &quot;wang&quot;);</span>
<span class="nc" id="L71">        PINYIN_MAP.put(&quot;李&quot;, &quot;li&quot;);</span>
<span class="nc" id="L72">        PINYIN_MAP.put(&quot;张&quot;, &quot;zhang&quot;);</span>
<span class="nc" id="L73">        PINYIN_MAP.put(&quot;刘&quot;, &quot;liu&quot;);</span>
<span class="nc" id="L74">        PINYIN_MAP.put(&quot;陈&quot;, &quot;chen&quot;);</span>
<span class="nc" id="L75">        PINYIN_MAP.put(&quot;杨&quot;, &quot;yang&quot;);</span>
<span class="nc" id="L76">        PINYIN_MAP.put(&quot;黄&quot;, &quot;huang&quot;);</span>
<span class="nc" id="L77">        PINYIN_MAP.put(&quot;赵&quot;, &quot;zhao&quot;);</span>
<span class="nc" id="L78">        PINYIN_MAP.put(&quot;周&quot;, &quot;zhou&quot;);</span>
<span class="nc" id="L79">        PINYIN_MAP.put(&quot;吴&quot;, &quot;wu&quot;);</span>
<span class="nc" id="L80">        PINYIN_MAP.put(&quot;徐&quot;, &quot;xu&quot;);</span>
<span class="nc" id="L81">        PINYIN_MAP.put(&quot;孙&quot;, &quot;sun&quot;);</span>
<span class="nc" id="L82">        PINYIN_MAP.put(&quot;朱&quot;, &quot;zhu&quot;);</span>
<span class="nc" id="L83">        PINYIN_MAP.put(&quot;马&quot;, &quot;ma&quot;);</span>
<span class="nc" id="L84">        PINYIN_MAP.put(&quot;胡&quot;, &quot;hu&quot;);</span>
<span class="nc" id="L85">        PINYIN_MAP.put(&quot;郭&quot;, &quot;guo&quot;);</span>
<span class="nc" id="L86">        PINYIN_MAP.put(&quot;林&quot;, &quot;lin&quot;);</span>
<span class="nc" id="L87">        PINYIN_MAP.put(&quot;何&quot;, &quot;he&quot;);</span>
<span class="nc" id="L88">        PINYIN_MAP.put(&quot;高&quot;, &quot;gao&quot;);</span>
<span class="nc" id="L89">        PINYIN_MAP.put(&quot;梁&quot;, &quot;liang&quot;);</span>

        // 常见名字拼音
<span class="nc" id="L92">        PINYIN_MAP.put(&quot;伟&quot;, &quot;wei&quot;);</span>
<span class="nc" id="L93">        PINYIN_MAP.put(&quot;强&quot;, &quot;qiang&quot;);</span>
<span class="nc" id="L94">        PINYIN_MAP.put(&quot;磊&quot;, &quot;lei&quot;);</span>
<span class="nc" id="L95">        PINYIN_MAP.put(&quot;军&quot;, &quot;jun&quot;);</span>
<span class="nc" id="L96">        PINYIN_MAP.put(&quot;勇&quot;, &quot;yong&quot;);</span>
<span class="nc" id="L97">        PINYIN_MAP.put(&quot;涛&quot;, &quot;tao&quot;);</span>
<span class="nc" id="L98">        PINYIN_MAP.put(&quot;明&quot;, &quot;ming&quot;);</span>
<span class="nc" id="L99">        PINYIN_MAP.put(&quot;超&quot;, &quot;chao&quot;);</span>
<span class="nc" id="L100">        PINYIN_MAP.put(&quot;辉&quot;, &quot;hui&quot;);</span>
<span class="nc" id="L101">        PINYIN_MAP.put(&quot;华&quot;, &quot;hua&quot;);</span>
<span class="nc" id="L102">        PINYIN_MAP.put(&quot;丽&quot;, &quot;li&quot;);</span>
<span class="nc" id="L103">        PINYIN_MAP.put(&quot;娟&quot;, &quot;juan&quot;);</span>
<span class="nc" id="L104">        PINYIN_MAP.put(&quot;敏&quot;, &quot;min&quot;);</span>
<span class="nc" id="L105">        PINYIN_MAP.put(&quot;静&quot;, &quot;jing&quot;);</span>
<span class="nc" id="L106">        PINYIN_MAP.put(&quot;洁&quot;, &quot;jie&quot;);</span>
<span class="nc" id="L107">        PINYIN_MAP.put(&quot;秀&quot;, &quot;xiu&quot;);</span>
<span class="nc" id="L108">        PINYIN_MAP.put(&quot;兰&quot;, &quot;lan&quot;);</span>
<span class="nc" id="L109">        PINYIN_MAP.put(&quot;红&quot;, &quot;hong&quot;);</span>
<span class="nc" id="L110">        PINYIN_MAP.put(&quot;霞&quot;, &quot;xia&quot;);</span>
<span class="nc" id="L111">    }</span>

    @Override
    public String getType() {
<span class="nc" id="L115">        return &quot;email&quot;;</span>
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        try {
            // 延迟加载数据
<span class="nc" id="L122">            ensureDataLoaded(config);</span>

            // 从参数中获取域名列表
<span class="nc" id="L125">            String domainsParam = getStringParam(config, &quot;domains&quot;, null);</span>
<span class="nc" id="L126">            List&lt;String&gt; domains = parseDomains(domainsParam);</span>

            // 从参数中获取用户名长度范围
<span class="nc" id="L129">            String usernameLengthParam = getStringParam(config, &quot;username_length&quot;, &quot;6,12&quot;);</span>
<span class="nc" id="L130">            int[] lengthRange = parseLengthRange(usernameLengthParam);</span>

            // 从参数中获取是否使用姓名前缀
<span class="nc" id="L133">            boolean prefixName = getBooleanParam(config, &quot;prefix_name&quot;, false);</span>

            // 从参数中获取是否生成有效邮箱
<span class="nc" id="L136">            boolean valid = getBooleanParam(config, &quot;valid&quot;, true);</span>

            // 从参数中获取邮箱类型
<span class="nc" id="L139">            String emailType = getStringParam(config, &quot;type&quot;, &quot;PERSONAL&quot;);</span>

<span class="nc bnc" id="L141" title="All 2 branches missed.">            if (!valid) {</span>
<span class="nc" id="L142">                return generateInvalidEmail();</span>
            }

<span class="nc" id="L145">            return generateValidEmail(domains, lengthRange, prefixName, emailType, context);</span>

<span class="nc" id="L147">        } catch (Exception e) {</span>
<span class="nc" id="L148">            logger.error(&quot;Failed to generate email&quot;, e);</span>
            // 返回一个默认邮箱作为fallback
<span class="nc" id="L150">            return &quot;<EMAIL>&quot;;</span>
        }
    }

    @Override
    public Class&lt;FieldConfig&gt; getConfigClass() {
<span class="nc" id="L156">        return FieldConfig.class;</span>
    }

    /**
     * 确保数据已加载。
     * 
     * @param config 配置
     */
    private void ensureDataLoaded(FieldConfig config) {
<span class="nc bnc" id="L165" title="All 2 branches missed.">        if (emailDomains == null) {</span>
<span class="nc" id="L166">            synchronized (this) {</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">                if (emailDomains == null) {</span>
<span class="nc" id="L168">                    loadData(config);</span>
                }
<span class="nc" id="L170">            }</span>
        }
<span class="nc" id="L172">    }</span>

    /**
     * 加载邮箱域名数据。
     * 
     * @param config 配置
     */
    private void loadData(FieldConfig config) {
        try {
            // 检查是否有自定义数据文件路径
<span class="nc" id="L182">            String customDomainsPath = getStringParam(config, &quot;email_domains_file&quot;, null);</span>

            List&lt;String&gt; lines;
<span class="nc bnc" id="L185" title="All 2 branches missed.">            if (customDomainsPath != null) {</span>
<span class="nc" id="L186">                lines = DataLoader.loadDataFromFile(customDomainsPath);</span>
            } else {
<span class="nc" id="L188">                lines = DataLoader.loadDataFromResource(EMAIL_DOMAINS_PATH);</span>
            }

<span class="nc" id="L191">            emailDomains = new java.util.HashMap&lt;&gt;();</span>
<span class="nc" id="L192">            domainsByType = new java.util.HashMap&lt;&gt;();</span>

<span class="nc bnc" id="L194" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc" id="L195">                String[] parts = line.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">                if (parts.length &gt;= 2) {</span>
<span class="nc" id="L197">                    String domain = parts[0].trim();</span>
<span class="nc" id="L198">                    String type = parts[1].trim();</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">                    int weight = parts.length &gt; 2 ? parseWeight(parts[2].trim()) : 1;</span>

<span class="nc" id="L201">                    DomainInfo info = new DomainInfo(domain, type, weight);</span>
<span class="nc" id="L202">                    emailDomains.put(domain, info);</span>

<span class="nc" id="L204">                    domainsByType.computeIfAbsent(type, k -&gt; new java.util.ArrayList&lt;&gt;()).add(domain);</span>
                }
<span class="nc" id="L206">            }</span>

<span class="nc" id="L208">            allDomains = new java.util.ArrayList&lt;&gt;(emailDomains.keySet());</span>

            // 如果加载失败，使用fallback数据
<span class="nc bnc" id="L211" title="All 2 branches missed.">            if (emailDomains.isEmpty()) {</span>
<span class="nc" id="L212">                initializeFallbackData();</span>
            }

<span class="nc" id="L215">            logger.info(&quot;Email domain data loaded - Total domains: {}, Types: {}&quot;,</span>
<span class="nc" id="L216">                    emailDomains.size(), domainsByType.keySet().size());</span>

<span class="nc" id="L218">        } catch (Exception e) {</span>
<span class="nc" id="L219">            logger.error(&quot;Failed to load email domain data, using fallback&quot;, e);</span>
<span class="nc" id="L220">            initializeFallbackData();</span>
<span class="nc" id="L221">        }</span>
<span class="nc" id="L222">    }</span>

    /**
     * 解析权重值。
     * 
     * @param weightStr 权重字符串
     * @return 权重值
     */
    private int parseWeight(String weightStr) {
        try {
<span class="nc" id="L232">            return Integer.parseInt(weightStr);</span>
<span class="nc" id="L233">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L234">            return 1;</span>
        }
    }

    /**
     * 初始化fallback数据。
     */
    private void initializeFallbackData() {
<span class="nc" id="L242">        emailDomains = new java.util.HashMap&lt;&gt;();</span>
<span class="nc" id="L243">        domainsByType = new java.util.HashMap&lt;&gt;();</span>

        // 添加fallback数据
<span class="nc bnc" id="L246" title="All 2 branches missed.">        for (String domain : FALLBACK_COMMON_DOMAINS) {</span>
<span class="nc" id="L247">            DomainInfo info = new DomainInfo(domain, &quot;PERSONAL&quot;, 1);</span>
<span class="nc" id="L248">            emailDomains.put(domain, info);</span>
<span class="nc" id="L249">            domainsByType.computeIfAbsent(&quot;PERSONAL&quot;, k -&gt; new java.util.ArrayList&lt;&gt;()).add(domain);</span>
<span class="nc" id="L250">        }</span>

<span class="nc" id="L252">        allDomains = new java.util.ArrayList&lt;&gt;(emailDomains.keySet());</span>
<span class="nc" id="L253">    }</span>

    /**
     * 生成有效的邮箱地址。
     * 
     * @param domains     域名列表
     * @param lengthRange 用户名长度范围
     * @param prefixName  是否使用姓名前缀
     * @param emailType   邮箱类型
     * @param context     上下文
     * @return 有效的邮箱地址
     */
    private String generateValidEmail(List&lt;String&gt; domains, int[] lengthRange,
            boolean prefixName, String emailType, DataForgeContext context) {
        // 生成用户名
<span class="nc" id="L268">        String username = generateUsername(lengthRange, prefixName, context);</span>

        // 选择域名
<span class="nc" id="L271">        String domain = selectDomain(domains, emailType);</span>

<span class="nc" id="L273">        return username + &quot;@&quot; + domain;</span>
    }

    /**
     * 生成用户名。
     * 
     * @param lengthRange 长度范围
     * @param prefixName  是否使用姓名前缀
     * @param context     上下文
     * @return 用户名
     */
    private String generateUsername(int[] lengthRange, boolean prefixName, DataForgeContext context) {
<span class="nc" id="L285">        StringBuilder username = new StringBuilder();</span>

        // 如果启用姓名前缀，尝试从上下文获取姓名
<span class="nc bnc" id="L288" title="All 2 branches missed.">        if (prefixName) {</span>
<span class="nc" id="L289">            String namePrefix = extractNamePrefix(context);</span>
<span class="nc bnc" id="L290" title="All 4 branches missed.">            if (namePrefix != null &amp;&amp; !namePrefix.isEmpty()) {</span>
<span class="nc" id="L291">                username.append(namePrefix);</span>
            }
        }

        // 如果用户名还不够长，添加随机部分
<span class="nc" id="L296">        int minLength = lengthRange[0];</span>
<span class="nc" id="L297">        int maxLength = lengthRange[1];</span>
<span class="nc" id="L298">        ThreadLocalRandom random = ThreadLocalRandom.current();</span>
<span class="nc" id="L299">        int targetLength = random.nextInt(maxLength - minLength + 1) + minLength;</span>

        // 如果已有前缀，调整目标长度
<span class="nc bnc" id="L302" title="All 2 branches missed.">        if (username.length() &gt; 0) {</span>
<span class="nc" id="L303">            targetLength = Math.max(targetLength, username.length() + 2);</span>
        }

        // 填充到目标长度
<span class="nc bnc" id="L307" title="All 2 branches missed.">        while (username.length() &lt; targetLength) {</span>
<span class="nc bnc" id="L308" title="All 4 branches missed.">            if (username.length() == 0 || random.nextBoolean()) {</span>
                // 添加字母
<span class="nc" id="L310">                username.append((char) ('a' + random.nextInt(26)));</span>
            } else {
                // 添加数字
<span class="nc" id="L313">                username.append(random.nextInt(10));</span>
            }
        }

        // 确保用户名不以数字开头
<span class="nc bnc" id="L318" title="All 2 branches missed.">        if (Character.isDigit(username.charAt(0))) {</span>
<span class="nc" id="L319">            username.setCharAt(0, (char) ('a' + random.nextInt(26)));</span>
        }

<span class="nc" id="L322">        return username.toString();</span>
    }

    /**
     * 从上下文中提取姓名前缀。
     * 
     * @param context 上下文
     * @return 姓名前缀
     */
    private String extractNamePrefix(DataForgeContext context) {
        // 尝试从上下文获取姓名
<span class="nc" id="L333">        String name = context.get(&quot;name&quot;, String.class).orElse(null);</span>
<span class="nc bnc" id="L334" title="All 4 branches missed.">        if (name == null || name.trim().isEmpty()) {</span>
<span class="nc" id="L335">            return null;</span>
        }

<span class="nc" id="L338">        name = name.trim();</span>

        // 如果是英文姓名
<span class="nc bnc" id="L341" title="All 2 branches missed.">        if (name.matches(&quot;[a-zA-Z\\s]+&quot;)) {</span>
<span class="nc" id="L342">            return extractEnglishNamePrefix(name);</span>
        }

        // 如果是中文姓名
<span class="nc bnc" id="L346" title="All 2 branches missed.">        if (name.matches(&quot;[\\u4e00-\\u9fa5]+&quot;)) {</span>
<span class="nc" id="L347">            return extractChineseNamePrefix(name);</span>
        }

        // 其他情况，取前几个字符
<span class="nc" id="L351">        return name.substring(0, Math.min(name.length(), 6)).toLowerCase();</span>
    }

    /**
     * 提取英文姓名前缀。
     * 
     * @param name 英文姓名
     * @return 前缀
     */
    private String extractEnglishNamePrefix(String name) {
<span class="nc" id="L361">        String[] parts = name.toLowerCase().split(&quot;\\s+&quot;);</span>
<span class="nc bnc" id="L362" title="All 2 branches missed.">        if (parts.length &gt;= 2) {</span>
            // 名.姓 或 名姓
<span class="nc" id="L364">            String firstName = parts[0];</span>
<span class="nc" id="L365">            String lastName = parts[parts.length - 1];</span>

<span class="nc bnc" id="L367" title="All 2 branches missed.">            if (ThreadLocalRandom.current().nextBoolean()) {</span>
<span class="nc" id="L368">                return firstName + &quot;.&quot; + lastName;</span>
            } else {
<span class="nc" id="L370">                return firstName + lastName;</span>
            }
        } else {
<span class="nc" id="L373">            return parts[0];</span>
        }
    }

    /**
     * 提取中文姓名前缀。
     * 
     * @param name 中文姓名
     * @return 前缀
     */
    private String extractChineseNamePrefix(String name) {
<span class="nc" id="L384">        StringBuilder pinyin = new StringBuilder();</span>

<span class="nc bnc" id="L386" title="All 2 branches missed.">        for (char c : name.toCharArray()) {</span>
<span class="nc" id="L387">            String py = PINYIN_MAP.get(String.valueOf(c));</span>
<span class="nc bnc" id="L388" title="All 2 branches missed.">            if (py != null) {</span>
<span class="nc" id="L389">                pinyin.append(py);</span>
            } else {
                // 如果没有找到拼音，使用字符的Unicode值生成
<span class="nc" id="L392">                pinyin.append(&quot;u&quot;).append(Integer.toHexString(c));</span>
            }
        }

<span class="nc" id="L396">        return pinyin.toString();</span>
    }

    /**
     * 选择域名。
     * 
     * @param domains   指定的域名列表
     * @param emailType 邮箱类型
     * @return 域名
     */
    private String selectDomain(List&lt;String&gt; domains, String emailType) {
<span class="nc bnc" id="L407" title="All 4 branches missed.">        if (domains != null &amp;&amp; !domains.isEmpty()) {</span>
<span class="nc" id="L408">            return domains.get(ThreadLocalRandom.current().nextInt(domains.size()));</span>
        }

        // 根据类型选择域名
<span class="nc" id="L412">        List&lt;String&gt; candidateDomains = domainsByType.get(emailType.toUpperCase());</span>
<span class="nc bnc" id="L413" title="All 4 branches missed.">        if (candidateDomains == null || candidateDomains.isEmpty()) {</span>
<span class="nc" id="L414">            candidateDomains = allDomains;</span>
        }

<span class="nc" id="L417">        return candidateDomains.get(ThreadLocalRandom.current().nextInt(candidateDomains.size()));</span>
    }

    /**
     * 生成无效的邮箱地址。
     * 
     * @return 无效的邮箱地址
     */
    private String generateInvalidEmail() {
<span class="nc" id="L426">        int type = ThreadLocalRandom.current().nextInt(5);</span>

<span class="nc bnc" id="L428" title="All 5 branches missed.">        return switch (type) {</span>
<span class="nc" id="L429">            case 0 -&gt; generateEmailWithoutAt();</span>
<span class="nc" id="L430">            case 1 -&gt; generateEmailWithoutDomain();</span>
<span class="nc" id="L431">            case 2 -&gt; generateEmailWithoutUsername();</span>
<span class="nc" id="L432">            case 3 -&gt; generateEmailWithInvalidChars();</span>
<span class="nc" id="L433">            default -&gt; generateEmailWithMultipleAt();</span>
        };
    }

    /**
     * 生成不包含@符号的邮箱。
     * 
     * @return 无效邮箱
     */
    private String generateEmailWithoutAt() {
<span class="nc" id="L443">        return &quot;usernameexample.com&quot;;</span>
    }

    /**
     * 生成没有域名的邮箱。
     * 
     * @return 无效邮箱
     */
    private String generateEmailWithoutDomain() {
<span class="nc" id="L452">        return &quot;username@&quot;;</span>
    }

    /**
     * 生成没有用户名的邮箱。
     * 
     * @return 无效邮箱
     */
    private String generateEmailWithoutUsername() {
<span class="nc" id="L461">        return &quot;@example.com&quot;;</span>
    }

    /**
     * 生成包含非法字符的邮箱。
     * 
     * @return 无效邮箱
     */
    private String generateEmailWithInvalidChars() {
<span class="nc" id="L470">        String[] invalidChars = { &quot; &quot;, &quot;&lt;&quot;, &quot;&gt;&quot;, &quot;[&quot;, &quot;]&quot;, &quot;\\&quot;, &quot;,&quot;, &quot;;&quot;, &quot;:&quot; };</span>
<span class="nc" id="L471">        String invalidChar = invalidChars[ThreadLocalRandom.current().nextInt(invalidChars.length)];</span>
<span class="nc" id="L472">        return &quot;user&quot; + invalidChar + &quot;<EMAIL>&quot;;</span>
    }

    /**
     * 生成包含多个@符号的邮箱。
     * 
     * @return 无效邮箱
     */
    private String generateEmailWithMultipleAt() {
<span class="nc" id="L481">        return &quot;user@<EMAIL>&quot;;</span>
    }

    /**
     * 解析域名参数。
     * 
     * @param domainsParam 域名参数字符串
     * @return 域名列表
     */
    private List&lt;String&gt; parseDomains(String domainsParam) {
<span class="nc bnc" id="L491" title="All 4 branches missed.">        if (domainsParam == null || domainsParam.trim().isEmpty()) {</span>
<span class="nc" id="L492">            return null;</span>
        }

<span class="nc" id="L495">        return Arrays.stream(domainsParam.split(&quot;,&quot;))</span>
<span class="nc" id="L496">                .map(String::trim)</span>
<span class="nc bnc" id="L497" title="All 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="nc" id="L498">                .collect(java.util.stream.Collectors.toList());</span>
    }

    /**
     * 解析长度范围参数。
     * 
     * @param lengthParam 长度参数字符串
     * @return 长度范围数组 [min, max]
     */
    private int[] parseLengthRange(String lengthParam) {
        try {
<span class="nc" id="L509">            String[] parts = lengthParam.split(&quot;,&quot;);</span>
<span class="nc bnc" id="L510" title="All 2 branches missed.">            if (parts.length == 2) {</span>
<span class="nc" id="L511">                int min = Integer.parseInt(parts[0].trim());</span>
<span class="nc" id="L512">                int max = Integer.parseInt(parts[1].trim());</span>
<span class="nc" id="L513">                return new int[] { Math.max(1, min), Math.max(min, max) };</span>
            }
<span class="nc" id="L515">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L516">            logger.warn(&quot;Invalid length range: {}, using default&quot;, lengthParam);</span>
<span class="nc" id="L517">        }</span>

<span class="nc" id="L519">        return new int[] { 6, 12 }; // 默认范围</span>
    }

    /**
     * 从配置中获取字符串参数。
     */
    private String getStringParam(FieldConfig config, String key, String defaultValue) {
<span class="nc bnc" id="L526" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L527">            return defaultValue;</span>
        }

<span class="nc" id="L530">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L531" title="All 2 branches missed.">        return value != null ? value.toString() : defaultValue;</span>
    }

    /**
     * 从配置中获取布尔参数。
     */
    private boolean getBooleanParam(FieldConfig config, String key, boolean defaultValue) {
<span class="nc bnc" id="L538" title="All 4 branches missed.">        if (config == null || config.getParams() == null) {</span>
<span class="nc" id="L539">            return defaultValue;</span>
        }

<span class="nc" id="L542">        Object value = config.getParams().get(key);</span>
<span class="nc bnc" id="L543" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L544">            return defaultValue;</span>
        }

<span class="nc bnc" id="L547" title="All 2 branches missed.">        if (value instanceof Boolean) {</span>
<span class="nc" id="L548">            return (Boolean) value;</span>
        }

<span class="nc" id="L551">        return Boolean.parseBoolean(value.toString());</span>
    }

    /**
     * 获取邮箱域名统计信息。
     * 
     * @return 统计信息
     */
    public String getDomainStats() {
<span class="nc" id="L560">        ensureDataLoaded(null);</span>

<span class="nc" id="L562">        StringBuilder stats = new StringBuilder();</span>
<span class="nc" id="L563">        stats.append(&quot;Total domains: &quot;).append(emailDomains.size()).append(&quot;\n&quot;);</span>

<span class="nc bnc" id="L565" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;String&gt;&gt; entry : domainsByType.entrySet()) {</span>
<span class="nc" id="L566">            stats.append(entry.getKey()).append(&quot;: &quot;).append(entry.getValue().size()).append(&quot; domains\n&quot;);</span>
<span class="nc" id="L567">        }</span>

        // 计算理论组合数（用户名长度6-12位，字母数字组合）
        // 每个域名 × 用户名组合数 = 总组合数
<span class="nc" id="L571">        long usernameVariations = 0;</span>
<span class="nc bnc" id="L572" title="All 2 branches missed.">        for (int len = 6; len &lt;= 12; len++) {</span>
            // 每个位置可以是26个字母或10个数字，但第一位必须是字母
<span class="nc" id="L574">            usernameVariations += 26 * (long) Math.pow(36, len - 1);</span>
        }

<span class="nc" id="L577">        long totalCombinations = (long) emailDomains.size() * usernameVariations;</span>
<span class="nc" id="L578">        stats.append(&quot;\nTotal possible combinations: &quot;).append(String.format(&quot;%,d&quot;, totalCombinations));</span>

<span class="nc" id="L580">        return stats.toString();</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L585">        return &quot;Email address generator - generates email addresses with comprehensive domain and username customization&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>