package com.dataforge.generators.internal;

import com.dataforge.core.DataForgeContext;
import com.dataforge.generators.spi.DataGenerator;
import com.dataforge.model.FieldConfig;
import com.dataforge.util.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 地址生成器
 * 
 * 支持功能：
 * 1. 基于行政区划数据的层级地址生成
 * 2. 与身份证号的地区信息关联
 * 3. 支持不同详细程度的地址
 * 4. 支持自定义街道和小区名称
 * 
 * 参数配置：
 * - country: 国家代码（默认CN）
 * - province: 指定省份名称或代码
 * - city: 指定城市名称或代码
 * - district: 指定区县名称或代码
 * - detail_level: 详细程度 PROVINCE|CITY|DISTRICT|STREET|COMMUNITY|FULL（默认FULL）
 * - include_zipcode: 是否包含邮编（默认true）
 * - link_idcard: 是否关联身份证号（默认true）
 * 
 * 关联字段：
 * - idcard: 从身份证号中提取地区代码
 * - region_code: 从上下文中获取地区代码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AddressGenerator implements DataGenerator<String, FieldConfig> {

    private static final Logger log = LoggerFactory.getLogger(AddressGenerator.class);

    private static final String TYPE = "address";
    private static final String DEFAULT_DETAIL_LEVEL = "FULL";
    private static final boolean DEFAULT_INCLUDE_ZIPCODE = true;
    private static final boolean DEFAULT_LINK_IDCARD = true;

    // 详细程度枚举
    public enum DetailLevel {
        PROVINCE, CITY, DISTRICT, STREET, COMMUNITY, FULL
    }

    // 上下文键名
    private static final String CONTEXT_ID_CARD = "idcard";
    private static final String CONTEXT_REGION_CODE = "region_code";

    // 数据缓存
    private static volatile Map<String, AdministrativeDivision> divisionsCache;
    private static volatile List<String> streetNames;
    private static volatile List<String> communityNames;
    private static volatile List<String> buildingNames;

    // 行政区划数据结构
    public static class AdministrativeDivision {
        private final String code;
        private final String name;
        private final String level;
        private final String parentCode;
        private final String zipCode;

        public AdministrativeDivision(String code, String name, String level, String parentCode, String zipCode) {
            this.code = code;
            this.name = name;
            this.level = level;
            this.parentCode = parentCode;
            this.zipCode = zipCode;
        }

        // Getters
        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public String getLevel() {
            return level;
        }

        public String getParentCode() {
            return parentCode;
        }

        public String getZipCode() {
            return zipCode;
        }
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public Class<FieldConfig> getConfigClass() {
        return FieldConfig.class;
    }

    @Override
    public String generate(FieldConfig config, DataForgeContext context) {
        Map<String, Object> params = config.getParams();

        // 解析配置参数
        String province = getStringParam(params, "province", null);
        String city = getStringParam(params, "city", null);
        String district = getStringParam(params, "district", null);
        String detailLevelStr = getStringParam(params, "detail_level", DEFAULT_DETAIL_LEVEL);
        boolean includeZipcode = getBooleanParam(params, "include_zipcode", DEFAULT_INCLUDE_ZIPCODE);
        boolean linkIdCard = getBooleanParam(params, "link_idcard", DEFAULT_LINK_IDCARD);

        // 解析详细程度
        DetailLevel detailLevel;
        try {
            detailLevel = DetailLevel.valueOf(detailLevelStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid detail level: {}. Using FULL.", detailLevelStr);
            detailLevel = DetailLevel.FULL;
        }

        // 确保数据已加载
        ensureDataLoaded();

        // 确定地区代码
        String regionCode = determineRegionCode(province, city, district, linkIdCard, context);

        // 生成地址
        return generateAddress(regionCode, detailLevel, includeZipcode);
    }

    /**
     * 确保数据已加载
     */
    private void ensureDataLoaded() {
        if (divisionsCache == null) {
            synchronized (AddressGenerator.class) {
                if (divisionsCache == null) {
                    loadAdministrativeDivisions();
                    loadAddressComponents();
                }
            }
        }
    }

    /**
     * 加载行政区划数据
     */
    private void loadAdministrativeDivisions() {
        try {
            List<String> lines = DataLoader.loadDataFromResource("data/administrative-divisions.txt");
            Map<String, AdministrativeDivision> divisions = new HashMap<>();

            for (String line : lines) {
                if (line.trim().isEmpty() || line.startsWith("#")) {
                    continue;
                }

                String[] parts = line.split("\\|");
                if (parts.length >= 4) {
                    String code = parts[0].trim();
                    String name = parts[1].trim();
                    String level = parts[2].trim();
                    String parentCode = parts[3].trim();
                    String zipCode = parts.length > 4 ? parts[4].trim() : "";

                    divisions.put(code, new AdministrativeDivision(code, name, level, parentCode, zipCode));
                }
            }

            divisionsCache = divisions;
            log.info("Administrative division data loaded - Total regions: {}", divisions.size());

        } catch (Exception e) {
            log.error("Failed to load administrative division data", e);
            divisionsCache = new HashMap<>();
        }
    }

    /**
     * 加载地址组件数据
     */
    private void loadAddressComponents() {
        // 加载街道名称
        streetNames = Arrays.asList(
                "人民路", "解放路", "中山路", "建设路", "胜利路", "和平路", "友谊路", "光明路",
                "新华路", "文化路", "学府路", "科技路", "创业路", "发展路", "繁荣路", "幸福路",
                "安康路", "健康路", "长寿路", "吉祥路", "如意路", "顺心路", "美好路", "希望路",
                "未来路", "梦想路", "青春路", "活力路", "朝阳路", "向阳路", "东风路", "春风路",
                "南风路", "西风路", "北风路", "海风路", "山风路", "清风路", "和风路", "暖风路");

        // 加载小区名称
        communityNames = Arrays.asList(
                "阳光花园", "绿色家园", "幸福家园", "温馨家园", "和谐家园", "美好家园", "舒适家园", "宁静家园",
                "春天花园", "夏日花园", "秋韵花园", "冬雪花园", "四季花园", "百花园", "玫瑰园", "牡丹园",
                "桂花园", "梅花园", "兰花园", "菊花园", "荷花园", "樱花园", "桃花园", "杏花园",
                "金桂小区", "银桂小区", "丹桂小区", "月桂小区", "桂花小区", "梧桐小区", "银杏小区", "柳树小区",
                "松柏小区", "竹林小区", "梅园小区", "兰园小区", "菊园小区", "荷园小区", "莲花小区", "水仙小区",
                "紫薇小区", "海棠小区", "茉莉小区", "玉兰小区", "丁香小区", "薰衣草小区", "向日葵小区", "康乃馨小区");

        // 加载建筑名称
        buildingNames = Arrays.asList(
                "栋", "号楼", "座", "幢", "单元", "区", "院", "苑", "轩", "阁", "居", "庭", "府", "邸", "宅", "舍");

        log.info("Address component data loaded - Streets: {}, Communities: {}, Buildings: {}",
                streetNames.size(), communityNames.size(), buildingNames.size());
    }

    /**
     * 确定地区代码
     */
    private String determineRegionCode(String province, String city, String district,
            boolean linkIdCard, DataForgeContext context) {

        // 1. 如果指定了具体地区参数
        if (district != null) {
            String code = findRegionCode(district);
            if (code != null)
                return code;
        }
        if (city != null) {
            String code = findRegionCode(city);
            if (code != null)
                return code;
        }
        if (province != null) {
            String code = findRegionCode(province);
            if (code != null)
                return code;
        }

        // 2. 尝试从身份证号中提取地区代码
        if (linkIdCard) {
            String regionCodeFromIdCard = getRegionCodeFromIdCard(context);
            if (regionCodeFromIdCard != null) {
                log.debug("Using region code from ID card: {}", regionCodeFromIdCard);
                return regionCodeFromIdCard;
            }
        }

        // 3. 从上下文中获取地区代码
        String regionCode = context.get(CONTEXT_REGION_CODE, String.class).orElse(null);
        if (regionCode != null) {
            return regionCode;
        }

        // 4. 随机选择一个地区
        return getRandomRegionCode();
    }

    /**
     * 查找地区代码
     */
    private String findRegionCode(String nameOrCode) {
        if (nameOrCode == null || nameOrCode.trim().isEmpty()) {
            return null;
        }

        // 如果是6位数字，直接作为代码使用
        if (nameOrCode.matches("\\d{6}")) {
            return nameOrCode;
        }

        // 按名称查找
        for (AdministrativeDivision division : divisionsCache.values()) {
            if (division.getName().equals(nameOrCode) || division.getName().contains(nameOrCode)) {
                return division.getCode();
            }
        }

        return null;
    }

    /**
     * 从身份证号中提取地区代码
     */
    private String getRegionCodeFromIdCard(DataForgeContext context) {
        String idCard = context.get(CONTEXT_ID_CARD, String.class).orElse(null);
        if (idCard != null && idCard.length() >= 6) {
            return idCard.substring(0, 6);
        }
        return null;
    }

    /**
     * 随机选择地区代码
     */
    private String getRandomRegionCode() {
        if (divisionsCache.isEmpty()) {
            return "110101"; // 默认北京东城区
        }

        List<String> codes = new ArrayList<>(divisionsCache.keySet());
        ThreadLocalRandom random = ThreadLocalRandom.current();
        return codes.get(random.nextInt(codes.size()));
    }

    /**
     * 生成地址
     */
    private String generateAddress(String regionCode, DetailLevel detailLevel, boolean includeZipcode) {
        StringBuilder address = new StringBuilder();
        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 构建层级地址
        List<String> addressParts = buildAddressParts(regionCode);

        // 根据详细程度添加地址组件
        int maxLevel = Math.min(detailLevel.ordinal() + 1, addressParts.size());
        for (int i = 0; i < maxLevel; i++) {
            if (i > 0)
                address.append("");
            address.append(addressParts.get(i));
        }

        // 添加详细地址组件
        if (detailLevel.ordinal() >= DetailLevel.STREET.ordinal()) {
            String street = streetNames.get(random.nextInt(streetNames.size()));
            int streetNumber = random.nextInt(1, 1000);
            address.append(street).append(streetNumber).append("号");
        }

        if (detailLevel.ordinal() >= DetailLevel.COMMUNITY.ordinal()) {
            String community = communityNames.get(random.nextInt(communityNames.size()));
            address.append(community);
        }

        if (detailLevel == DetailLevel.FULL) {
            int building = random.nextInt(1, 50);
            String buildingName = buildingNames.get(random.nextInt(buildingNames.size()));
            int unit = random.nextInt(1, 6);
            int room = random.nextInt(101, 3999);

            address.append(building).append(buildingName)
                    .append(unit).append("单元")
                    .append(room).append("室");
        }

        // 添加邮编
        if (includeZipcode) {
            String zipCode = getZipCode(regionCode);
            if (zipCode != null && !zipCode.isEmpty()) {
                address.append(" (").append(zipCode).append(")");
            }
        }

        return address.toString();
    }

    /**
     * 构建地址层级部分
     */
    private List<String> buildAddressParts(String regionCode) {
        List<String> parts = new ArrayList<>();

        AdministrativeDivision current = divisionsCache.get(regionCode);
        if (current == null) {
            parts.add("未知地区");
            return parts;
        }

        // 从当前地区向上追溯到省级
        List<AdministrativeDivision> hierarchy = new ArrayList<>();
        while (current != null) {
            hierarchy.add(current);
            current = divisionsCache.get(current.getParentCode());
        }

        // 反转顺序，从省到区县
        Collections.reverse(hierarchy);

        for (AdministrativeDivision division : hierarchy) {
            parts.add(division.getName());
        }

        return parts;
    }

    /**
     * 获取邮编
     */
    private String getZipCode(String regionCode) {
        AdministrativeDivision division = divisionsCache.get(regionCode);
        if (division != null && !division.getZipCode().isEmpty()) {
            return division.getZipCode();
        }

        // 如果没有具体邮编，生成一个合理的邮编
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int baseCode = Integer.parseInt(regionCode.substring(0, 2)) * 10000;
        int offset = random.nextInt(1000, 9999);
        return String.valueOf(baseCode + offset);
    }

    // 工具方法
    private String getStringParam(Map<String, Object> params, String key, String defaultValue) {
        Object value = params.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private boolean getBooleanParam(Map<String, Object> params, String key, boolean defaultValue) {
        Object value = params.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
}